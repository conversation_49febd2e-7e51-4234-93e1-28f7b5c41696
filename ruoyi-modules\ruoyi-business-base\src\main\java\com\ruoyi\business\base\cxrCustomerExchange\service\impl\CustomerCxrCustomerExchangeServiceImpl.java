package com.ruoyi.business.base.cxrCustomerExchange.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.ruoyi.business.api.domain.bo.ISpecDTO;
import com.ruoyi.business.base.api.domain.CxrCustomer;
import com.ruoyi.business.base.api.domain.CxrCustomerAddress;
import com.ruoyi.business.base.api.domain.CxrCustomerStockDetail;
import com.ruoyi.business.base.api.domain.CxrSaleProduct;
import com.ruoyi.business.base.api.domain.vo.BeseEmployeeVo;
import com.ruoyi.business.base.api.domain.vo.CommonExchangeOrderVo;
import com.ruoyi.business.base.api.dubbo.RemoteEmployeeService;
import com.ruoyi.business.base.api.dubbo.RemotelogisticsService;
import com.ruoyi.business.base.api.model.LoginEmployee;
import com.ruoyi.business.base.customer.mapper.CxrCustomerMapper;
import com.ruoyi.business.base.customerAddress.mapper.CxrCustomerAddressMapper;
import com.ruoyi.business.base.customerStockDetai.domain.CxrCustomerStockLockDetail;
import com.ruoyi.business.base.customerStockDetai.mapper.CxrCustomerStockLockDetailMapper;
import com.ruoyi.business.base.customerStockDetai.service.CxrCustomerStockDetailService;
import com.ruoyi.business.base.cxrCustomerExchange.domain.CxrCustomerExchange;
import com.ruoyi.business.base.cxrCustomerExchange.domain.bo.CustomerExchangePageBo;
import com.ruoyi.business.base.cxrCustomerExchange.domain.bo.CxrCustomerExchangeAddBo;
import com.ruoyi.business.base.cxrCustomerExchange.domain.bo.ExchangeProductBo;
import com.ruoyi.business.base.cxrCustomerExchange.domain.vo.*;
import com.ruoyi.business.base.cxrCustomerExchange.mapper.CxrCustomerExchangeMapper;
import com.ruoyi.business.base.cxrCustomerExchange.service.ICustomerCxrCustomerExchangeService;
import com.ruoyi.business.base.exchangeProduct.domain.CxrExchangeProduct;
import com.ruoyi.business.base.exchangeProduct.mapper.CxrExchangeProductMapper;
import com.ruoyi.business.base.saleProduct.mapper.CxrSaleProductMapper;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.PageTableDataInfo;
import com.ruoyi.common.core.enums.CustomerExchangeOrderStatus;
import com.ruoyi.common.core.enums.DeleteStatus;
import com.ruoyi.common.core.enums.SynEnums;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.BeanCollectionUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.enums.AuditStatusEnums;
import com.ruoyi.common.enums.TerminalTypeEnums;
import com.ruoyi.common.satoken.utils.helper.CustomerLoginHelper;
import com.ruoyi.order.api.RemoteOrderService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class CustomerCxrCustomerExchangeServiceImpl extends
    MPJBaseServiceImpl<CxrCustomerExchangeMapper, CxrCustomerExchange>
    implements ICustomerCxrCustomerExchangeService {

    private final CxrCustomerExchangeMapper cxrCustomerExchangeMapper;

    private final CxrCustomerAddressMapper cxrCustomerAddressMapper;

    private final CxrCustomerStockDetailService cxrCustomerStockDetailService;

    private final CxrCustomerStockLockDetailMapper stockLockDetailMapper;

    private final CxrCustomerMapper cxrCustomerMapper;

    private final CxrSaleProductMapper saleProductMapper;

    private final CxrExchangeProductMapper cxrExchangeProductMapper;

    @DubboReference
    private RemoteEmployeeService remoteEmployeeService;
    @DubboReference
    private RemoteOrderService remoteOrderService;

    @DubboReference
    private RemotelogisticsService remotelogisticsService;

    @Override
    public PageTableDataInfo<CustomerExchangePageVo> pageExchange(CustomerExchangePageBo cxrCustomerExchangeBo,
                                                                  PageQuery pageQuery) {

        if (ObjectUtil.isEmpty(pageQuery.getPageSize())) {
            pageQuery.setPageSize(20);
        }
        // 查看自己的提交
        cxrCustomerExchangeBo.setCustomerId(CustomerLoginHelper.getLoginUser().getUserId());
        IPage<CustomerExchangePageVo> exchangePage = cxrCustomerExchangeMapper.pageExchange(cxrCustomerExchangeBo,
            pageQuery.build());

        PageTableDataInfo pageTableDataInfo = new PageTableDataInfo();
        pageTableDataInfo.setRows(exchangePage.getRecords());
        pageTableDataInfo.setTotal(exchangePage.getTotal());
        pageTableDataInfo.setSize(pageQuery.getPageSize());
        pageTableDataInfo.setCurr(pageQuery.getPageNum());
        return pageTableDataInfo;
    }

    @Override
    public Boolean takeDeliverys(Long id) {

        CxrCustomerExchange customerExchange = cxrCustomerExchangeMapper.selectById(id);

        if (customerExchange.getStatus().equals(CustomerExchangeOrderStatus.FINISHED.getValue())) {
            throw new ServiceException("请勿重复收货！");
        }

        if (customerExchange.getStatus().equals(CustomerExchangeOrderStatus.WAITDELIVER.getValue())
        ) {
            throw new ServiceException("商品还未发货！");
        }

        return cxrCustomerExchangeMapper.update(new CxrCustomerExchange(),
            new LambdaUpdateWrapper<CxrCustomerExchange>().eq(CxrCustomerExchange::getId, id)
                .set(CxrCustomerExchange::getUpdateTime, new Date())
                .set(CxrCustomerExchange::getConsigneeTime, new Date())
                .set(CxrCustomerExchange::getStatus, CustomerExchangeOrderStatus.FINISHED.getValue())
        ) > 0;
    }


    @Override
    public Object queryLogistics(String com, String num) {
        return remotelogisticsService.querylogistics(com, num);
    }

    @Override
    public Map queryCustomerStock(Long id) {
        CxrCustomer customer = cxrCustomerMapper.getById(ObjectUtil.isNotEmpty(id) ? id :
            CustomerLoginHelper.getLoginUser().getUserId());
        Map<String, Long> data = new HashMap<>();
        Long customerStock = Long.valueOf(customer.getCustomerStock());
        Long lockStock = ObjectUtil.isNull(customer.getLockStock()) ? 0 : customer.getLockStock();
        Long availableQuantity = customerStock - lockStock;
        data.put("customerStock", customerStock);
        data.put("lockStock", lockStock);
        data.put("availableQuantity", availableQuantity);
        return data;
    }

    @Override
    public CustomerExchangeDateilsVo detail(Long id) {
        CxrCustomerExchange customerExchange = cxrCustomerExchangeMapper.selectById(id);
        CustomerExchangeDateilsVo exchangePageVo = new CustomerExchangeDateilsVo();
        BeanUtils.copyProperties(customerExchange, exchangePageVo);

        if (ObjectUtil.isNotEmpty(exchangePageVo.getSpec())) {
            JSONArray objects = JSONUtil.parseArray(exchangePageVo.getSpec());
            List<ExchangeDTO> lists = JSONUtil.toList(objects, ExchangeDTO.class);
            exchangePageVo.setSpecData(lists);
        }

        CxrExchangeProduct exchangeProduct = cxrExchangeProductMapper.selectById(
            customerExchange.getExchangeProductId());
        CxrCustomer customer = cxrCustomerMapper.getById(CustomerLoginHelper.getLoginUser().getUserId());
        exchangePageVo.setLockStock(ObjectUtil.isEmpty(customer.getLockStock()) ? 0 : customer.getLockStock());
        exchangePageVo.setTips(ObjectUtil.isEmpty(exchangeProduct) ? null : exchangeProduct.getRemark());
        exchangePageVo.setWhetherRefrigeration(exchangeProduct.getWhetherRefrigeration());
        exchangePageVo.setOrderDate(customerExchange.getApplyTime());
        exchangePageVo.setCustomerStock(customer.getCustomerStock());
        return exchangePageVo;
    }


    @Override
    public PageTableDataInfo<ExchangeProductVo> pageProduct(ExchangeProductBo exchangeProductBo, PageQuery pageQuery) {

        if (ObjectUtil.isEmpty(pageQuery.getPageSize())) {
            pageQuery.setPageSize(20);
        }

        // 不加组织id
        Page<CxrExchangeProduct> exchangeProductPage = cxrExchangeProductMapper.selectPage(pageQuery.build(),
            new LambdaQueryWrapper<CxrExchangeProduct>().eq(StringUtils.isNotEmpty(exchangeProductBo.getProductName()),
                    CxrExchangeProduct::getName, exchangeProductBo.getProductName())
                .eq(CxrExchangeProduct::getActive, 1)
                .eq(CxrExchangeProduct::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .orderByDesc(CxrExchangeProduct::getCreateTime)
        );

        List<ExchangeProductVo> exchangeProductVos = BeanCollectionUtils.copyListProperties(
            exchangeProductPage.getRecords(), ExchangeProductVo::new, (a, b) -> {
                if (ObjectUtil.isNotEmpty(a.getSpec())) {
                    JSONArray objects = JSONUtil.parseArray(a.getSpec());
                    List<ISpecDTO> lists = JSONUtil.toList(objects, ISpecDTO.class);
                    b.setSpecData(lists);
                    b.setWhetherRefrigeration(a.getWhetherRefrigeration());
                    //前端要求对规格1、2的数据进行拆分
                    if (lists.size() > 0) {
                        if (ObjectUtil.isNotEmpty(lists.get(0).getSpecName2())) {
                            Map<String, String> specMap2 = lists.stream().collect(
                                Collectors.toMap(c -> c.getSpecName2(), c -> c.getSpecValue2(),
                                    (entity1, entity2) -> entity1));
                            List<ProductSpecDTO> spec2 = dataSpec(specMap2, 2);
                            List<String> collect = lists.stream().map(ISpecDTO::getSpecValue2).distinct()
                                .collect(Collectors.toList());
                            for (String value2 : collect) {
                                ProductSpecDTO productSpecDTO = new ProductSpecDTO();
                                String spec = spec2.get(0).getSpec();
                                String value = spec2.get(0).getValue();
                                if (!value2.equals(value)) {
                                    productSpecDTO.setSpec(spec);
                                    productSpecDTO.setIsSpecLevel(2);
                                    productSpecDTO.setValue(value2);
                                    spec2.add(productSpecDTO);
                                }
                            }
                            b.setSpec2(spec2);
                        }
                        Map<String, String> specMap1 = lists.stream().collect(
                            Collectors.toMap(c -> c.getSpecName1(), c -> c.getSpecValue1(),
                                (entity1, entity2) -> entity1));
                        List<ProductSpecDTO> spec1 = dataSpec(specMap1, 1);
                        List<String> collect1 = lists.stream().map(ISpecDTO::getSpecValue1).distinct()
                            .collect(Collectors.toList());
                        for (String value2 : collect1) {
                            ProductSpecDTO productSpecDTO = new ProductSpecDTO();
                            String spec = spec1.get(0).getSpec();
                            String value = spec1.get(0).getValue();
                            if (!value2.equals(value)) {
                                productSpecDTO.setSpec(spec);
                                productSpecDTO.setValue(value2);
                                productSpecDTO.setIsSpecLevel(1);
                                spec1.add(productSpecDTO);
                            }
                        }
                        b.setSpec1(spec1);
                    }


                }
            });

//        exchangeProductVos
        PageTableDataInfo exchangePage = new PageTableDataInfo();
        exchangePage.setRows(exchangeProductVos);
        exchangePage.setTotal(exchangeProductPage.getTotal());
        exchangePage.setSize(pageQuery.getPageSize());
        exchangePage.setCurr(pageQuery.getPageNum());
        return exchangePage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(CxrCustomerExchangeAddBo cxrCustomerExchangeBo) {

        LoginEmployee loginUser = CustomerLoginHelper.getLoginUser();

        List<CxrCustomerStockLockDetail> stockLockDetails = stockLockDetailMapper.selectList(
            new LambdaQueryWrapper<CxrCustomerStockLockDetail>()
                .select(CxrCustomerStockLockDetail::getLockNum)
                .eq(CxrCustomerStockLockDetail::getCustomerId, loginUser.getUserId())
                .eq(CxrCustomerStockLockDetail::getStockType, 1)
                .eq(CxrCustomerStockLockDetail::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
        );
        Long sum = 0l;
        if (CollUtil.isNotEmpty(stockLockDetails)) {
            sum = stockLockDetails.stream().mapToLong(CxrCustomerStockLockDetail::getLockNum).sum();
        }

        // 校验前端提交值
        if (cxrCustomerExchangeBo.getMilkExchangeSum() <= 0) {
            throw new ServiceException("兑换数不能小于1！");
        }
        CxrCustomer customer = cxrCustomerMapper.selectById(loginUser.getUserId());
        Integer customerStock = customer.getCustomerStock() - Convert.toInt(customer.getLockStock(), 0)
            - Convert.toInt(sum, 0);
        if (cxrCustomerExchangeBo.getMilkExchangeSum() > customerStock) {
            throw new ServiceException("库存不足！");
        }

        CxrCustomerExchange cxrCustomerExchange = new CxrCustomerExchange();

        BeanUtils.copyProperties(cxrCustomerExchangeBo, cxrCustomerExchange);

        /**
         * 1提交后生成一笔待发货的订单，并同步至后台客户兑换模块。
         * 2、提交后，即扣减客户鲜奶库存
         * 3、后台订单模块生成一笔换单，详见后台-订单模块
         */

        //校验商品
        CxrExchangeProduct exchangeProduct = cxrExchangeProductMapper.selectById(
            cxrCustomerExchangeBo.getExchangeProductId());

        if (ObjectUtil.isEmpty(exchangeProduct)) {
            throw new ServiceException("兑换商品不存在");
        }

        CxrCustomerAddress customerAddress = cxrCustomerAddressMapper.selectOne(
            new LambdaQueryWrapper<CxrCustomerAddress>()
                .eq(CxrCustomerAddress::getCxrCustomerId, customer.getId())
                .eq(CxrCustomerAddress::getDefalutAccountAddress, "Y")
                .eq(CxrCustomerAddress::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .last("limit 1")
        );

        BeseEmployeeVo employeeVo = remoteEmployeeService.queryEmployeeByCustomerPhone(customer.getId());
        //一些列的 参数
        cxrCustomerExchange.setCustomerId(customer.getId());
        cxrCustomerExchange.setCustomerName(customer.getName());
        cxrCustomerExchange.setApplyTime(new Date());
        cxrCustomerExchange.setSpec(JSONUtil.toJsonStr(cxrCustomerExchangeBo.getSpecData()));
        cxrCustomerExchange.setStatus(CustomerExchangeOrderStatus.WAITDELIVER.getValue());
        cxrCustomerExchange.setTerminalType(Integer.valueOf(TerminalTypeEnums.customer.getValue()));
        cxrCustomerExchange.setCreateBy(loginUser.getUserId());
        cxrCustomerExchange.setAuditStatus(AuditStatusEnums.Audit.code());
        cxrCustomerExchange.setAuditTime(new Date());
        cxrCustomerExchange.setAuditUserId(loginUser.getUserId());
        cxrCustomerExchange.setProductName(exchangeProduct.getName());
        cxrCustomerExchange.setSysDeptId(employeeVo.getSysDeptId());
        cxrCustomerExchange.setDetailAddress(cxrCustomerExchangeBo.getDetailAddress());
        cxrCustomerExchange.setCxrSiteId(customerAddress.getCxrSiteId());
        cxrCustomerExchange.setCustomerStockInfo(
            customer.getCustomerStock() - cxrCustomerExchangeBo.getMilkExchangeSum().intValue());
        cxrCustomerExchange.setSynType(exchangeProduct.getSynType());
        cxrCustomerExchange.setSupplierId(exchangeProduct.getSupplierId());
        if (ObjectUtil.equals(exchangeProduct.getSynType(), SynEnums.ANY_SUPPLIER.getValue())
            && ObjectUtil.isEmpty(exchangeProduct.getSupplierId())) {
            CxrSaleProduct cxrSaleProduct = saleProductMapper.selectOne(new LambdaQueryWrapper<CxrSaleProduct>()
                .eq(CxrSaleProduct::getProductCode, exchangeProduct.getProductCode())
                .eq(CxrSaleProduct::getDeleteStatus,
                    DeleteStatus.NOT_DELETED.getValue()));
            cxrCustomerExchange.setSupplierId(cxrSaleProduct.getCxrProductSupplierId());
        }

        //提交后生成一笔待发货的订单，并同步至后台客户兑换模块。
        Boolean filg = cxrCustomerExchangeMapper.insert(cxrCustomerExchange) > 0;

        if (!filg) {
            throw new ServiceException("提交失败请重新尝试");
        }

//        Integer currentStockSum= customer.getCustomerStock()-cxrCustomerExchangeBo.getMilkExchangeSum();
//        Boolean stockZero=false;
//        if (currentStockSum==0){
//            stockZero=true;
//        }
//
//        //提交后，即扣减客户鲜奶库存
//        cxrCustomerMapper.update(new CxrCustomer(),new LambdaUpdateWrapper<CxrCustomer>()
//            .eq(CxrCustomer::getId,loginUser.getUserId())
//            .set(CxrCustomer::getCustomerStock,
//                currentStockSum)
//            .set(stockZero,CxrCustomer::getCustomerStockZeroTime,new Date())
//        );

        // 客户库存流水   -- 客户兑换扣减 //提交后，即扣减客户鲜奶库存
        List<CxrCustomerStockDetail> cxrCustomerStockDetailList = new ArrayList<>();
        CxrCustomerStockDetail customerStockDetail = new CxrCustomerStockDetail();
        customerStockDetail.setTerminalType(TerminalTypeEnums.customer.getValue());
        customerStockDetail.setFreshMilkQuantity(-cxrCustomerExchangeBo.getMilkExchangeSum().intValue());
        customerStockDetail.setRemark(StrUtil.format("客户兑换扣减,时间为={},数量={}", LocalDateTime.now().toString(),
            cxrCustomerExchangeBo.getMilkExchangeSum()));
        cxrCustomerStockDetailList.add(customerStockDetail);
        cxrCustomerStockDetailService.addRecord(customer.getId(), cxrCustomerStockDetailList, customer.getRevision());

        // 后台订单模块生成一笔换单，详见后台-订单模块
        //todo 待 后台换单 完成补上
        CommonExchangeOrderVo commonExchangeOrderVo = BeanUtil.copyProperties(cxrCustomerExchangeBo,
            CommonExchangeOrderVo.class);
        commonExchangeOrderVo.setCustomerExchangeId(cxrCustomerExchange.getId());
        commonExchangeOrderVo.setCustomerId(cxrCustomerExchange.getCustomerId());
        commonExchangeOrderVo.setSysDeptId(employeeVo.getSysDeptId());
        commonExchangeOrderVo.setExchangeProductName(cxrCustomerExchange.getProductName());
        // 需求规定要客户的地址而不是要收货人的地址
        commonExchangeOrderVo.setProvince(customer.getProvice());
        commonExchangeOrderVo.setCity(customer.getCity());
        commonExchangeOrderVo.setArea(customer.getArea());

        commonExchangeOrderVo.setCustomerAddress(customerAddress.getDetailDistributionAddress());
        commonExchangeOrderVo.setCustomerName(customer.getName());
        commonExchangeOrderVo.setCustomerPhone(customer.getPhone());
        Long orderId = remoteOrderService.instrtAnyExchangeOrder(commonExchangeOrderVo);

        if (ObjectUtil.isEmpty(orderId)) {
            throw new ServiceException("提交失败请重新尝试");
        }

        cxrCustomerExchangeMapper.update(null, new LambdaUpdateWrapper<CxrCustomerExchange>()
            .eq(CxrCustomerExchange::getId, cxrCustomerExchange.getId())
            .set(CxrCustomerExchange::getUserOrderId, orderId)
        );

        return filg;
    }

    public List<ProductSpecDTO> dataSpec(Map<String, String> specMap, Integer level) {
        //
        List<ProductSpecDTO> specDTOS = new ArrayList<>();
        for (String key : specMap.keySet()) {
            ProductSpecDTO productSpecDTO = new ProductSpecDTO();
            String value = specMap.get(key).toString();
            productSpecDTO.setSpec(key);
            productSpecDTO.setIsSpecLevel(level);
            productSpecDTO.setValue(value);
            specDTOS.add(productSpecDTO);
        }

        return specDTOS;
    }
}
