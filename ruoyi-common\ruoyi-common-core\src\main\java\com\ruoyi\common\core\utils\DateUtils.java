package com.ruoyi.common.core.utils;

import java.lang.management.ManagementFactory;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.time.DateFormatUtils;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {

    public static final String YYYY = "yyyy";

    public static final String YYYY_MM = "yyyy-MM";

    public static final String YYYY_MM_DD = "yyyy-MM-dd";

    public static final String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static String DATE_TIME_PATTERN_YYYY_MM_DD_HH_MM_SS_SSS = "yyyyMMddHHmmssSSS";


    private static final String[] PARSE_PATTERNS = {
        "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
        "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
        "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};
    /**
     * 毫秒换算 - 天数值
     * <p>
     * 1000 * 60 * 60 * 24
     */
    private static final Integer DAY_TIME = 86400000;

    /**
     * 毫秒换算 - 小时值
     * <p>
     * 1000 * 60 * 60
     */
    private static final Integer HOURS_TIME = 3600000;
    /**
     * 毫秒换算 - 分钟值
     * <p>
     * 1000 * 60
     */
    private static final Integer MINUTES_TIME = 60000;
    /**
     * 毫秒换算- 秒值
     */
    private static final Integer SECOND_TIME = 1000;


    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate() {
        return new Date();
    }

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getTomorrowDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }


    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate() {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static String getTime() {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static String dateTimeNow() {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static String dateTimeNow(final String format) {
        return parseDateToStr(format, new Date());
    }

    public static String dateTime(final Date date) {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static String parseDateToStr(final String format, final Date date) {
        return new SimpleDateFormat(format).format(date);
    }

    public static Date dateTime(final String format, final String ts) {
        try {
            return new SimpleDateFormat(format).parse(ts);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static String datePath() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * YYYY-MM_dd
     */
    public static Date formatDateTo(Date date) {
        SimpleDateFormat s = new SimpleDateFormat("yyyy-MM-dd");
        String format = s.format(date);
        try {
            return s.parse(format);
        } catch (ParseException e) {
            throw new RuntimeException("时间转换错误");
        }
    }

    /**
     * YYYY-MM_dd HH:mm:dd  字符串
     */
    public static String formatDateToDateTime(Date date) {
        SimpleDateFormat s = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String format = s.format(date);

        return format;
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static String dateTime() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyyMMdd");
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            return parseDate(str.toString(), PARSE_PATTERNS);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate() {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    /**
     * 计算两个时间差
     */
    public static String getDatePoor(Date endDate, Date nowDate) {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    /**
     * 增加 LocalDateTime ==> Date
     */
    public static Date toDate(LocalDateTime temporalAccessor) {
        ZonedDateTime zdt = temporalAccessor.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    /**
     * 增加 LocalDate ==> Date
     */
    public static Date toDate(LocalDate temporalAccessor) {
        LocalDateTime localDateTime = LocalDateTime.of(temporalAccessor, LocalTime.of(0, 0, 0));
        ZonedDateTime zdt = localDateTime.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    public static String calculateFormat(long millisecond) {
        long days = millisecond / DAY_TIME;
        long hours = (millisecond - days * DAY_TIME) / HOURS_TIME;
        long minutes = (millisecond - days * DAY_TIME - hours * HOURS_TIME) / MINUTES_TIME;
        long second = (millisecond - days * DAY_TIME - hours * HOURS_TIME - minutes * MINUTES_TIME) / SECOND_TIME;
        StringBuilder s = new StringBuilder();
        if (days != 0) {
            s.append(days + "天");
        }
        if (hours != 0) {
            s.append(hours + "小时");
        }
        if (minutes != 0) {
            s.append(minutes + "分");
        }
        if (second != 0) {
            s.append(second + "秒");
        }
        return s.toString();
    }

    /**
     * 时间相减转换为毫秒值
     */
    public static long diffMillisecond(Date d1, Date d2) {
        return d1.getTime() - d2.getTime();
    }


    public static long differenceMillisecond(Date d1, Date d2) {
        return d1.getTime() - d2.getTime();
    }

    /**
     * 获取传递进来的时间 下个月10号的时间
     *
     * @param dataCreateTime
     */
    public static Date nextMonth10day(Date dataCreateTime) {

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dataCreateTime);
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, 10);

        return calendar.getTime();
    }

    /**
     * 获取传递进来的时间 下个月5号的时间
     *
     * @param dataCreateTime
     */
    public static Date nextMonth5day(Date dataCreateTime) {

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dataCreateTime);
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, 3);

        return calendar.getTime();
    }

    /**
     * 获取明天结束时间
     *
     * @return Date() 当前日期
     */
    public static Date getTomorrowDateEnd() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取明天结束时间
     *
     * @return Date() 当前日期
     */
    public static Date getTomorrowDateEnd(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    /**
     * date -> LocalDateTime
     *
     * @param date
     * @return
     */
    public static LocalDateTime getLocalDateTimeFormDate(Date date) {
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        return LocalDateTime.ofInstant(instant, zone);
    }

    /**
     * Date -> LocalDate
     *
     * @param date
     * @return
     */
    public static LocalDate getLocalDateFromDate(Date date) {
        LocalDateTime localDateTime = getLocalDateTimeFormDate(date);
        return localDateTime.toLocalDate();
    }

    public static List<LocalDate> getLocalDateFromDates(List<Date> date) {
        List<LocalDate> dateList = new ArrayList<>();
        for (Date s : date) {
            LocalDateTime localDateTime = getLocalDateTimeFormDate(s);
            dateList.add(localDateTime.toLocalDate());
        }

        return dateList;
    }

    /**
     * 根据date  i  返回大于date i天时间
     *
     * @param date
     * @param i
     * @return
     */
    public static Date dateAdd(Date date, int i) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DATE, i);
        return c.getTime();
    }


    /**
     * 取当月第一天
     *
     * @param date
     * @return
     */
    public static Date MonthOneDate(Date date) {

        Instant instant = date.toInstant();
        LocalDate localDate = instant.atZone(ZoneId.systemDefault()).toLocalDate();

        LocalDate with = localDate.with(TemporalAdjusters.firstDayOfMonth());

        return Date.from(with.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
    }


    /**
     * 将时间转换成   时分秒
     *
     * @param date
     * @return
     */
    public static Date getHours(Date date) {
        SimpleDateFormat f = new SimpleDateFormat("HH:mm:ss");
        String format = f.format(date);
        try {
            return f.parse(format);
        } catch (ParseException e) {
            throw new RuntimeException("时间转换异常");
        }
    }

    public static Date eightHours() {

        Date time = new Date();
        Calendar cal = Calendar.getInstance();
        cal.setTime(time);
        cal.set(Calendar.HOUR_OF_DAY, 20); //时
        cal.set(Calendar.MINUTE, 0); //分
        cal.set(Calendar.SECOND, 0); //秒
        Date time2 = cal.getTime();
        return time2;
    }


    /**
     * 返回区间内的日期  不包含结束日期
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static List<LocalDate> getStartDateUntilEndDate(LocalDate startDate, LocalDate endDate) {

        TimeZone.setDefault(TimeZone.getTimeZone("GMT+8"));
        //日历类  获取的本地时间
        Calendar c = Calendar.getInstance(TimeZone.getTimeZone("GMT+8"));
        //设置开始时间
        c.set(startDate.getYear(), startDate.getMonthValue() - 1, startDate.getDayOfMonth(), 0, 0, 0);

        // 定义时间格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        //转换格式
        LocalDateTime localDateTime = endDate.atStartOfDay();
        Instant instant = localDateTime.atZone(ZoneId.systemDefault()).toInstant();
        Date endTime = Date.from(instant);

        //准备list集合
        List<LocalDate> list = new ArrayList<>();
        //循环将时间搞出来
        while (c.getTime().compareTo(endTime) < 0) {
            String format = sdf.format(c.getTime());
            LocalDate localDate = LocalDate.parse(format);
            list.add(localDate);
            c.add(Calendar.DATE, 1);
        }

        return list;
    }

    //第二天的时间早上6点钟
    public static Date getNextDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 6); //时
        calendar.set(Calendar.MINUTE, 0);      //分
        calendar.set(Calendar.SECOND, 0);      //秒
        calendar.set(Calendar.MILLISECOND, 0); //毫秒
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        return calendar.getTime();
    }

    /*
     * 根据传入的时间获取月1日     日期在6日之前(包括6号)  返回前一月1号   6号之后返回这个月1日
     * */
    public static LocalDate getFirstOfMonthByNow(LocalDate now) {
        if (now.withDayOfMonth(6).compareTo(now) > -1) {
            now = now.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        } else {
            now = now.with(TemporalAdjusters.firstDayOfMonth());
        }
        return now;
    }

    /**
     * 根据传入日期的年月生成 和i  生成那个月的日期
     *
     * @param localDate
     * @param i
     * @return
     */
    public static LocalDate getDayOfMouth(LocalDate localDate, int i) {
        int year = localDate.getYear();
        int monthValue = localDate.getMonthValue();

        String s =
            String.valueOf(year) + "-" + (monthValue > 9 ? monthValue : "0" + monthValue) + "-" + (i > 9 ? i : "0" + i);

        return LocalDate.parse(s);
    }

    public static LocalDate getMinQuitDate(LocalDate now) {
        if (now == null){
            now = LocalDate.now();
        }
        return now.minusMonths(now.getDayOfMonth() >= 21 ? 2 : 3).with(TemporalAdjusters.firstDayOfMonth());
    }

    public static String format(Date date, String pattern) {
        if (date != null) {
            SimpleDateFormat df = new SimpleDateFormat(pattern);
            return df.format(date);
        }
        return null;
    }
}

