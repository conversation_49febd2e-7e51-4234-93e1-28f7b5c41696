package com.ruoyi.order.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.ruoyi.business.base.api.domain.DeliverySiteDTO;
import com.ruoyi.business.base.api.domain.json.BusinessAgent;
import com.ruoyi.common.core.web.domain.CxrBaseEntity;
import com.ruoyi.order.common.typeHandler.TypeHandlerConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 专门存放退订单的表;会把退订单的内容放一份在这个表
 *
 * <AUTHOR> http://www.chiner.pro
 * @date : 2022-8-15
 */
@ApiModel(value = "专门存放退订单的表", description = "会把退订单的内容放一份在这个表")
@TableName(value = "cxr_user_return_order", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = false)
public class CxrUserReturnOrder extends CxrBaseEntity {

    @TableField(exist = false) // 数据库不存在
    @ApiModelProperty("订单类型")
    private final short orderType = 2;

    @ApiModelProperty(name = "编号", notes = "")
    @TableId("id")
    private Long id;

    @ApiModelProperty(name = "主单id", notes = "")
    private Long userOrderId;

    @ApiModelProperty(name = "关联订单id", notes = "")
    private Long orderId;
    /**
     * 订单时间
     */
    @ApiModelProperty(name = "订单时间", notes = "")
    private Date orderDate;
    /**
     * 站点id
     */
    @ApiModelProperty(name = "站点id", notes = "")
    private Long siteId;
    /**
     * 业务代理(已选择的销售代理;可以有多个)
     */
    @ApiModelProperty(name = "业务代理(已选择的销售代理", notes = "可以有多个)")
    @TableField(typeHandler = TypeHandlerConstant.BusinessAgentTypeHandler.class)
    private List<BusinessAgent> businessAgent;

    @ApiModelProperty(name = "客户id", notes = "只取第一个录入的数据")
    private Long customerId;
    /**
     * 客户电话;只取第一个录入的数据
     */
    @ApiModelProperty(name = "客户电话", notes = "只取第一个录入的数据")
    private String customerPhone;
    /**
     * 客户名称
     */
    @ApiModelProperty(name = "客户名称", notes = "")
    private String customerName;
    /**
     * 客户地址;小区
     */
    @ApiModelProperty(name = "客户地址", notes = "小区")
    private String quarters;
    /** 订购数量：鲜奶订购数量 */

    /** 鲜奶赠送数量 */
    /**
     * 客户地址;;只取第一个录入的数据
     */
    @ApiModelProperty(name = "客户地址;", notes = "只取第一个录入的数据")
    private String customerAdress;
    /** 鲜奶已送数量 */
    //    @ApiModelProperty(name = "鲜奶已送数量", notes = "")
    //    private Integer freshMilkSentQuantity;
    /** 鲜奶剩余数量;, 也是客户列表里的 总数量 （鲜奶订购数量 - 鲜奶已送数量） */
    /**
     * 总数量;=;鲜奶订购数量 - 鲜奶已送数量
     */
    @ApiModelProperty(name = "总数量   退订总数量;=", notes = "此处表示的实退订总数量")
    private Integer totalQuantity;
    /**
     * 常温奶已送数量
     */
    @ApiModelProperty(name = "常温奶已送数量", notes = "")
    private Integer longMilkSentQuantity;
    /** 常温奶剩余数量;, 也是客户列表里的 总数量 （鲜奶订购数量 - 鲜奶已送数量） */
    /**
     * 常温奶已送金额\\n\\n\\n销售代理自己输入
     */
    @ApiModelProperty(name = "常温奶已送金额\\n\\n\\n销售代理自己输入", notes = "")
    private BigDecimal longMilkSentAmount;
    /**
     * 客户实际退款的鲜奶盒数
     */
    @ApiModelProperty(name = " 客户退款盒数 (客户实际退款的鲜奶盒数(排除了送的))", notes = "")
    private Integer freshMilkRefundQuantity;
    /**
     * 客户实际退订的鲜奶盒数
     */
    @ApiModelProperty(name = "客户实际退订的鲜奶盒数", notes = "鲜奶退订总数量 = 客户退款盒数 + 鲜奶赠送数（由销售代理手动输入）")
    private Integer freshMilkCancelQuantity;
    /** 客户退订后;账户中的剩余鲜奶数量 */
    /**
     * 客户实际退款的鲜奶盒数
     */
    @ApiModelProperty(name = "客户实际退款的鲜奶盒数", notes = "")
    private Integer longMilkCancelQuantity;
    /**
     * 实际退还给客户的金额
     */
    @ApiModelProperty(name = "实际退还给客户的金额", notes = "")
    private BigDecimal refundAmount;
    /**
     * 补销售代理返利
     */
    @ApiModelProperty(name = "补销售代理返利", notes = "这个需要前端填写")
    private BigDecimal rebates;
    /**
     * 退订总金额;= 客户退款金额 + 常温奶已送金额 + 补业务代理返利 (如果存在多个销售代理的情况，则按人数平均分配)
     */
    @ApiModelProperty(name = "退订总金额", notes = "= 客户退款金额 + 常温奶已送金额 + 补业务代理返利 (如果存在多个销售代理的情况，则按人数平均分配)")
    private BigDecimal amount;

    /**
     * 关联订单的订购金额 常规退订单，由主管自己录入
     */
    private BigDecimal orderMoney;

    /**
     * 开户银行
     */
    @ApiModelProperty(name = "开户银行", notes = "")
    private String bankName;
    /**
     * 账户卡号
     */
    @ApiModelProperty(name = "账户卡号", notes = "")
    private String bankAccountNumber;
    /**
     * 账户名
     */
    @ApiModelProperty(name = "账户名", notes = "")
    private String bankAccountName;
    /**
     * 单据图片
     */
    @ApiModelProperty(name = "单据图片", notes = "")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> images;
    /**
     * 备注
     */
    @ApiModelProperty(name = "备注", notes = "")
    private String remark;

    @ApiModelProperty(name = "单据", notes = "订单单据号")
    private String orderNo;

    @ApiModelProperty("鲜奶赠送总数")
    private Integer freshMilkGiveQuantity;

    @ApiModelProperty("鲜奶已送数量")
    private Integer freshMilkSentQuantity;

    @ApiModelProperty("鲜奶剩余数量")
    private Integer freshMilkRestQuantity;

    @ApiModelProperty("常温奶赠送总数")
    private Integer longMilkGiveQuantity;

    @ApiModelProperty("常温奶剩余数量")
    private Integer longMilkRestQuantity;

    @ApiModelProperty("鲜奶退订后剩余数量 (常温奶没有这个)")
    private Integer freshMilkCancelRestQuantity;

    @ApiModelProperty("退订原因编号")
    private String rrCode;


    /**
     * 打款标识
     */
    private Boolean refundSuccessFlag;

    /**
     * 售后单id
     */
    @ApiModelProperty(value = "售后单id")
    private Long afterSalesId;

    @ApiModelProperty(value = "支付订单id")
    private Long payOrderId;

    @ApiModelProperty(value = "销售代理填写备注")
    private String employeeRemark;


    @ApiModelProperty("配送站点")
    @TableField(exist = false)
    private List<DeliverySiteDTO> deliverySites;
}
