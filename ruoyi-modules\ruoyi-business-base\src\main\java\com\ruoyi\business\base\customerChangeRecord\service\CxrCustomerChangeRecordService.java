package com.ruoyi.business.base.customerChangeRecord.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.business.base.api.domain.CxrCustomerChangeRecord;


/**
 * <AUTHOR>
 * @description 针对表【cxr_customer_change_record(口味数量起送暂停变更记录表)】的数据库操作Service
 * @createDate 2023-03-30 11:28:59
 */
public interface CxrCustomerChangeRecordService extends IService<CxrCustomerChangeRecord> {


    Boolean customerSeparateCchangeAddressSave(CxrCustomerChangeRecord customerChangeRecord);


}
