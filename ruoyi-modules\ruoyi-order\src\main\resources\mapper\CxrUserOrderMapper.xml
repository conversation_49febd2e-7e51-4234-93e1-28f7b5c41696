<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--BindingOrderMapper-->
<mapper namespace="com.ruoyi.order.common.mapper.CxrUserOrderMapper">


    <resultMap type="com.ruoyi.order.common.entity.CxrUserOrder" id="CxrUserOrderResult">
        <result property="id" column="id"/>
        <result property="sysDeptId" column="sys_dept_id"/>
        <result property="revision" column="revision"/>
        <result property="createBy" column="create_by"/>
        <result property="createByName" column="create_by_name"/>
        <result property="createByType" column="create_by_type"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateByName" column="update_by_name"/>
        <result property="updateByType" column="update_by_type"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleteBy" column="delete_by"/>
        <result property="deleteByName" column="delete_by_name"/>
        <result property="deleteByType" column="delete_by_type"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="deleteStatus" column="delete_status"/>
        <result property="sortNum" column="sort_num"/>
        <result property="companyId" column="company_id"/>
        <result property="companyName" column="company_name"/>
        <result property="bigAreaId" column="big_area_id"/>
        <result property="bigAreaName" column="big_area_name"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="area" column="area"/>
        <result property="siteName" column="site_name"/>
        <result property="siteAdress" column="site_adress"/>
        <result property="siteId" column="site_id"/>
        <result property="businessAgent" column="business_agent"
                typeHandler="com.ruoyi.order.common.typeHandler.TypeHandlerConstant$BusinessAgentTypeHandler"/>
        <result property="customerName" column="customer_name"/>
        <result property="customerPhone" column="customer_phone"/>
        <result property="customerAdress" column="customer_adress"/>
        <result property="customerNameSwitch" column="customer_name_switch"/>
        <result property="customerAdressSwitch" column="customer_adress_switch"/>
        <result property="customerPhoneSwitch" column="customer_phone_switch"/>
        <result property="paymentSouce" column="payment_souce"/>
        <result property="orderType" column="order_type"/>
        <result property="zeroQuantityRenewal" column="zero_quantity_renewal"/>
        <result property="conversionType" column="conversion_type"/>
        <result property="orderNo" column="order_no"/>
        <result property="merchantOrderNo" column="merchant_order_no"/>
        <result property="orderQuantity" column="order_quantity"/>
        <result property="freshMilkGiveQuantity" column="fresh_milk_give_quantity"/>
        <result property="longMilkGiveQuantity" column="long_milk_give_quantity"/>
        <result property="excessQuantity" column="excess_quantity"/>
        <result property="freshMilkSentQuantity" column="fresh_milk_sent_quantity"/>
        <result property="longMilkSentQuantity" column="long_milk_sent_quantity"/>
        <result property="conversionQuantity" column="conversion_quantity"/>
        <result property="unitPrice" column="unit_price"/>
        <result property="amount" column="amount"/>
        <result property="creditCardAmount" column="credit_card_amount"/>
        <result property="orderDate" column="order_date"/>
        <result property="remark" column="remark"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="auditTime" column="audit_time"/>
        <result property="auditByName" column="audit_by_name"/>
        <result property="promotionalOrderFlag" column="promotional_order_flag"/>
        <result property="apprenticeOrderFlag" column="apprentice_order_flag"/>
        <result property="freshMilkReturnQuantity" column="fresh_milk_return_quantity"/>

        <result property="fromProductType" column="fromProductType"/>
        <result property="terminalType" column="terminal_type"/>
        <result property="perfectStatus" column="perfect_status"/>
        <result property="customerId" column="customer_id"/>
        <result property="deliverySites" column="delivery_sites"/>
        <result property="refundNoAuditReasons" column="refund_no_audit_reasons"/>

        <result property="newCustomerFlag" column="new_customer_flag"/>
        <!--    <result property="orderImages" column="order_images"/>-->
        <!--    <result property="playImages" column="play_images"/>-->
        <result property="customerInfoList" column="customer_info_list"
                typeHandler="com.ruoyi.order.common.typeHandler.TypeHandlerConstant$CustomerInfoTypeHandler"/>
        <result property="deliverySite" column="deliverySite"/>
    </resultMap>
    <resultMap type="com.ruoyi.order.common.domain.vo.CxrUserReturnOrderVO" id="CxrUserReturnOrderVOResult">
        <result property="businessAgent" column="business_agent"
                typeHandler="com.ruoyi.order.common.typeHandler.TypeHandlerConstant$BusinessAgentTypeHandler"/>
        <result property="customerInfoList" column="customer_info_list"
                typeHandler="com.ruoyi.order.common.typeHandler.TypeHandlerConstant$CustomerInfoTypeHandler"/>
    </resultMap>
    <resultMap type="com.ruoyi.order.common.domain.vo.CxrUserOrderListVo" id="CxrUserOrderListVoResult">
        <result property="id" column="id"/>
        <result property="businessAgent" column="business_agent"
                typeHandler="com.ruoyi.order.common.typeHandler.TypeHandlerConstant$BusinessAgentTypeHandler"/>
        <result property="customerAdress" column="customer_adress"/>
        <result property="customerNameSwitch" column="customer_name_switch"/>
        <result property="customerAdressSwitch" column="customer_adress_switch"/>
        <result property="customerPhoneSwitch" column="customer_phone_switch"/>
        <result property="orderType" column="order_type"/>
        <result property="zeroQuantityRenewal" column="zero_quantity_renewal"/>
        <result property="conversionType" column="conversion_type"/>
        <result property="orderNo" column="order_no"/>
        <result property="merchantOrderNo" column="merchant_order_no"/>
        <result property="orderQuantity" column="order_quantity"/>
        <result property="freshMilkGiveQuantity" column="fresh_milk_give_quantity"/>
        <result property="longMilkGiveQuantity" column="long_milk_give_quantity"/>
        <result property="excessQuantity" column="excess_quantity"/>
        <result property="freshMilkSentQuantity" column="fresh_milk_sent_quantity"/>
        <result property="longMilkSentQuantity" column="long_milk_sent_quantity"/>
        <result property="conversionQuantity" column="conversion_quantity"/>
        <result property="unitPrice" column="unit_price"/>
        <result property="amount" column="amount"/>
        <result property="creditCardAmount" column="credit_card_amount"/>
        <result property="orderDate" column="order_date"/>
        <result property="remark" column="remark"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="auditTime" column="audit_time"/>
        <result property="auditByName" column="audit_by_name"/>
        <result property="promotionalOrderFlag" column="promotional_order_flag"/>
        <result property="apprenticeOrderFlag" column="apprentice_order_flag"/>
        <result property="freshMilkReturnQuantity" column="fresh_milk_return_quantity"/>

        <result property="fromProductType" column="fromProductType"/>
        <result property="terminalType" column="terminal_type"/>
        <result property="customerId" column="customer_id"/>

        <result property="newCustomerFlag" column="new_customer_flag"/>
        <!--    <result property="orderImages" column="order_images"/>-->
        <!--    <result property="playImages" column="play_images"/>-->
        <result property="customerInfoList" column="customer_info_list"
                typeHandler="com.ruoyi.order.common.typeHandler.TypeHandlerConstant$CustomerInfoTypeHandler"/>
        <result property="deliverySite" column="deliverySite"/>
        <result property="deliverySiteId" column="deliverySiteId"/>

        <result property="deliverySites" column="delivery_sites"/>
        <result property="refundNoAuditReasons" column="refund_no_audit_reasons"/>
    </resultMap>
    <resultMap type="com.ruoyi.order.api.domain.vo.UserOrderListVo" id="CxrUserOrderResultVo">
        <result property="id" column="id"/>
        <result property="companyId" column="company_id"/>
        <result property="companyName" column="company_name"/>
        <result property="bigAreaId" column="big_area_id"/>
        <result property="bigAreaName" column="big_area_name"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="area" column="area"/>
        <result property="siteName" column="site_name"/>
        <result property="siteAdress" column="site_adress"/>
        <result property="siteId" column="site_id"/>
        <result property="businessAgent" column="business_agent"
                typeHandler="com.ruoyi.order.common.typeHandler.TypeHandlerConstant$BusinessAgentTypeHandler"/>
        <result property="customerName" column="customer_name"/>
        <result property="customerPhone" column="customer_phone"/>
        <result property="customerAdress" column="customer_adress"/>
        <result property="customerNameSwitch" column="customer_name_switch"/>
        <result property="customerAdressSwitch" column="customer_adress_switch"/>
        <result property="customerPhoneSwitch" column="customer_phone_switch"/>
        <result property="paymentSouce" column="payment_souce"/>
        <result property="orderType" column="order_type"/>
        <result property="zeroQuantityRenewal" column="zero_quantity_renewal"/>
        <result property="conversionType" column="conversion_type"/>
        <result property="orderNo" column="order_no"/>
        <result property="merchantOrderNo" column="merchant_order_no"/>
        <result property="orderQuantity" column="order_quantity"/>
        <result property="freshMilkGiveQuantity" column="fresh_milk_give_quantity"/>
        <result property="longMilkGiveQuantity" column="long_milk_give_quantity"/>
        <result property="excessQuantity" column="excess_quantity"/>
        <result property="freshMilkSentQuantity" column="fresh_milk_sent_quantity"/>
        <result property="longMilkSentQuantity" column="long_milk_sent_quantity"/>
        <result property="conversionQuantity" column="conversion_quantity"/>
        <result property="unitPrice" column="unit_price"/>
        <result property="amount" column="amount"/>
        <result property="creditCardAmount" column="credit_card_amount"/>
        <result property="orderDate" column="order_date"/>
        <result property="remark" column="remark"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="promotionalOrderFlag" column="promotional_order_flag"/>
        <result property="apprenticeOrderFlag" column="apprentice_order_flag"/>
        <result property="freshMilkReturnQuantity" column="fresh_milk_return_quantity"/>
        <result property="terminalType" column="terminal_type"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , sys_dept_id, revision, create_by,create_by_code, create_by_name, create_by_type, create_time, update_by, update_by_name,
        update_by_type, update_time, delete_by, delete_by_name, delete_by_type, delete_time, delete_status, sort_num, spare_id,
        company_id, company_name, big_area_id, big_area_name, province, city, area, site_name, site_adress, site_id, business_agent,
        customer_name, customer_phone, customer_adress, customer_name_switch, customer_adress_switch, customer_phone_switch,
        payment_souce, order_type, zero_quantity_renewal, conversion_type, order_no, merchant_order_no, sqb_sn, order_quantity,
        fresh_milk_give_quantity, long_milk_give_quantity, excess_quantity, fresh_milk_sent_quantity, long_milk_sent_quantity,
        surplus_quantity, conversion_quantity, unit_price, amount,pay_amount,platform_discount, credit_card_amount, order_date, remark, audit_status, audit_by,
        audit_by_name, audit_time, promotional_order_flag, apprentice_order_flag, terminal_type, customer_info_list, order_images,
        play_images, pay_status, perfect_status, fresh_milk_return_quantity, contract_order_ext, order_status, pay_time, contract_total_amount,
        contract_total_order_quantity, exchange_product_id, exchange_sum, milk_exchange_sum, exchange_product_name, spec, is_import,
        activity_give_quantity, activity_sent_quantity, customer_id, activity_type, third_order_no, new_customer_flag,abnormal_tag,
        activity_id,give_gift_list,activity_name,set_meal_price,apportion_money,contract_type_tag,refund_success_flag,payment_failure_reasons,payment_status,pay_order_id,tiktok_order_refund_no,
            accounting_type,product_name,product_type,promotion_commission,product_type_name,delivery_sites,refund_no_audit_reasons,
        customer_info_list->>'$[0].siteName'AS deliverySite,
        JSON_EXTRACT(customer_info_list, '$[0].siteId') AS deliverySiteId
    </sql>

    <select id="count" resultType="java.lang.Long">
        SELECT COUNT(*) FROM ( <include refid="pageSql"></include>)f
    </select>
    <select id="list" resultMap="CxrUserOrderListVoResult">
        <include refid="pageSql"></include>
        <if test="cxrUserOrderBo.id != null">
            AND t.id &lt; #{cxrUserOrderBo.id}
        </if>
        ORDER BY t.id DESC
        LIMIT 200
    </select>
    <select id="page" resultMap="CxrUserOrderResult">
        <include refid="pageSql"></include>
        ORDER BY t.order_date desc
    </select>
    <sql id="pageSql">
        select
        <include refid="Base_Column_List"/>
        from cxr_user_order t
        <if test="cxrUserOrderBo.orderType != null ">
            force index (cxr_user_order_order_date_index_order_type)
        </if>

        <where>

            <if test="cxrUserOrderBo.companyId !=null">
                and t.company_id = #{cxrUserOrderBo.companyId}
            </if>

            <if test="cxrUserOrderBo.bigAreaId !=null">
                AND t.site_id IN (SELECT id FROM cxr_site WHERE cxr_root_region_id = #{cxrUserOrderBo.bigAreaId})
            </if>

            <if test="cxrUserOrderBo.siteId !=null">
                and t.site_id= #{cxrUserOrderBo.siteId}
            </if>

            <if test="cxrUserOrderBo.proxyIds !=null and cxrUserOrderBo.proxyIds.size() >0">
                and JSON_OVERLAPS(t.business_agent -> '$[*].proxyId',CAST(
                #{cxrUserOrderBo.proxyIdsJson,jdbcType=VARCHAR} AS JSON ))
            </if>

            <if test="cxrUserOrderBo.promotionalOrderFlag">
                and t.promotional_order_flag = true
            </if>

            <if test="cxrUserOrderBo.apprenticeOrderFlag">
                and t.apprentice_order_flag = true
            </if>

            <if test="cxrUserOrderBo.abnormalTag !=null ">
                and t.abnormal_tag = #{cxrUserOrderBo.abnormalTag}
            </if>

            <if test="cxrUserOrderBo.orderType != null ">
                <choose>
                    <when test='cxrUserOrderBo.orderType == "0"'>AND (t.order_type = #{cxrUserOrderBo.orderType} OR
                        t.contract_type_tag = #{cxrUserOrderBo.orderType})
                    </when>
                    <when test='cxrUserOrderBo.orderType == "1"'>AND (t.order_type = #{cxrUserOrderBo.orderType} OR
                        t.contract_type_tag = #{cxrUserOrderBo.orderType})
                    </when>
                    <otherwise>AND t.order_type = #{cxrUserOrderBo.orderType}</otherwise>
                </choose>
            </if>

            <if test="cxrUserOrderBo.paymentSouce != null ">
                and t.payment_souce = #{cxrUserOrderBo.paymentSouce}
            </if>

            <if test="cxrUserOrderBo.activityId != null ">
                and t.activity_id = #{cxrUserOrderBo.activityId}
            </if>

            <if test="cxrUserOrderBo.customerPhone != null and cxrUserOrderBo.customerPhone !='' ">
                and t.customer_phone like concat(#{cxrUserOrderBo.customerPhone},'%')
            </if>

            <if test="cxrUserOrderBo.customerPhoneSwitch != null  and cxrUserOrderBo.customerPhoneSwitch !='' ">
                and t.customer_phone_switch like concat(#{cxrUserOrderBo.customerPhoneSwitch},'%')
            </if>

            <if test="cxrUserOrderBo.auditStatus != null ">

                <if test="cxrUserOrderBo.auditStatus ==1 || cxrUserOrderBo.auditStatus ==2 || cxrUserOrderBo.auditStatus ==6">
                    and t.audit_status = #{cxrUserOrderBo.auditStatus}
                </if>
                <if test="cxrUserOrderBo.auditStatus ==3 ">
                    and t.pay_status = #{cxrUserOrderBo.auditStatus}
                </if>

                <if test="cxrUserOrderBo.auditStatus ==4 ">
                    and t.perfect_status = 1
                </if>
                <if test="cxrUserOrderBo.auditStatus ==5 ">
                    and t.pay_status = 1
                </if>
                <if test="cxrUserOrderBo.auditStatus ==7 ">
                    and t.refund_success_flag = 1
                </if>
            </if>

            <if test="cxrUserOrderBo.customerAdress != null ">
                and t.customer_adress like concat(#{cxrUserOrderBo.customerAdress},'%')
            </if>

            <if test="cxrUserOrderBo.orderNo != null ">
                and t.order_no like concat(#{cxrUserOrderBo.orderNo},'%')
            </if>

            <if test="cxrUserOrderBo.merchantOrderNo != null ">
                and t.merchant_order_no like concat(#{cxrUserOrderBo.merchantOrderNo},'%')
            </if>

            <if test="cxrUserOrderBo.startDateTime != null ">
                and t.order_date &gt;= #{cxrUserOrderBo.startDateTime}
            </if>

            <if test="cxrUserOrderBo.endDateTime != null ">
                and t.order_date &lt;=
                #{cxrUserOrderBo.endDateTime}
            </if>

            <if test="cxrUserOrderBo.startUpdateTime != null ">
                and t.update_time &gt;= #{cxrUserOrderBo.startUpdateTime}
            </if>

            <if test="cxrUserOrderBo.endUpdateTime != null ">
                and t.update_time &lt;= #{cxrUserOrderBo.endUpdateTime}
            </if>

            and t.delete_status = '0'

            <if test=" cxrUserOrderBo.auditStatus !=5  ">
                and <![CDATA[t.pay_status <> '1' ]]>
                and <![CDATA[t.pay_status <> '5' ]]>
            </if>
            <if test="cxrUserOrderBo.terminalType !=null">
                and t.terminal_type= #{cxrUserOrderBo.terminalType}
            </if>
            <if test="cxrUserOrderBo.paymentStatus!=null">
                and t.payment_status=#{cxrUserOrderBo.paymentStatus}
            </if>
            <if test="cxrUserOrderBo.accountingType!=null">
                and t.accounting_type=#{cxrUserOrderBo.accountingType}
            </if>
            <if test="cxrUserOrderBo.productName!=null and cxrUserOrderBo.productName!=''">
                and t.product_name like concat ('%',#{cxrUserOrderBo.productName},'%')
            </if>
            <if test="cxrUserOrderBo.productTypeName!=null and cxrUserOrderBo.productTypeName!=''">
                and t.product_type_name like concat ('%',#{cxrUserOrderBo.productTypeName},'%')
            </if>
            <if test="cxrUserOrderBo.promotionCommission!=null">
                and t.promotion_commission=#{cxrUserOrderBo.promotionCommission}
            </if>

            <if test="cxrUserOrderBo.deliverySite!=null and cxrUserOrderBo.deliverySite!=''">
                and (t.delivery_sites like concat ('%',#{cxrUserOrderBo.deliverySite},'%')
                or t.customer_info_list like concat ('%',#{cxrUserOrderBo.deliverySite},'%')
                )
            </if>

        </where>

    </sql>

    <select id="getByTradeNo" resultType="com.ruoyi.order.common.entity.CxrUserOrder">
        select *
        from cxr_user_order
        where order_no = #{tradeNo}
          and delete_status = 0
    </select>

    <select id="getByOrderNo" resultType="com.ruoyi.order.common.entity.CxrUserOrder">
        select *
        from cxr_user_order
        where order_no = #{orderNo}
          and delete_status = 0
    </select>


    <select id="searchOrder" resultMap="CxrUserOrderResult"
            parameterType="com.ruoyi.order.common.domain.vo.OrderSearchReqVo">
        select
        id, sys_dept_id, revision, create_by, create_by_name, create_by_type,
        create_time, update_by, update_by_name, update_by_type, update_time,
        delete_by, delete_by_name, delete_by_type, delete_time, delete_status,
        sort_num, spare_id, company_id, company_name, big_area_id, big_area_name,
        province, city, area, site_name, site_adress, site_id, business_agent,
        customer_name, customer_phone, customer_adress, customer_name_switch,
        customer_adress_switch, customer_phone_switch, payment_souce, order_type,
        zero_quantity_renewal, conversion_type, order_no, merchant_order_no, sqb_sn,
        order_quantity, fresh_milk_give_quantity, long_milk_give_quantity, excess_quantity,
        fresh_milk_sent_quantity, long_milk_sent_quantity, surplus_quantity, conversion_quantity,
        unit_price, amount, credit_card_amount, order_date, remark, audit_status, audit_by,
        audit_time, promotional_order_flag, apprentice_order_flag,
        customer_info_list,customer_info_list
        customerInfos,terminal_type,perfect_status,
        order_images, play_images, pay_status, fresh_milk_return_quantity,

        contract_order_ext, order_status,old_customer,zero15_day_after,
        pay_time, contract_total_amount, contract_total_order_quantity,
        exchange_product_id, exchange_sum, milk_exchange_sum, exchange_product_name,
        spec,new_customer_flag,refund_no_audit_reasons,delivery_sites,
        (CASE
        JSON_LENGTH( business_agent )> 1
        WHEN
        a.create_by = #{orderSearchVo.createBy}
        THEN
        true
        else
        false
        END ) AS promotionalEditFlag,

        (case when
        JSON_LENGTH(business_agent)>1 then amount/JSON_LENGTH(business_agent)
        else
        ifnull(amount,0)
        end ) AS performanceMoney


        from
        cxr_user_order a
        where a.id in
        <foreach collection="orderSearchVo.orderIds" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
        order by create_time desc,id
    </select>


    <select id="searchOrderIds" resultMap="CxrUserOrderResult"
            parameterType="com.ruoyi.order.common.domain.vo.OrderSearchReqVo">
        select distinct a.id
        from cxr_user_order a
        <if test="orderSearchVo.allSiteIds !=null and orderSearchVo.allSiteIds.size() > 0">
            left join (
            select souce_id,site_id
            from cxr_employee_achievement_detail
            where delete_status=0 and order_date >= #{orderSearchVo.subtractTwoMonthDate}
            <if test="orderSearchVo.allSiteIds != null and orderSearchVo.allSiteIds.size() > 0">
                and site_id in
                <foreach collection="orderSearchVo.allSiteIds" item="returnSiteId" open="("
                         close=")" separator=",">
                    #{returnSiteId}
                </foreach>
            </if>
            <if test="orderSearchVo.startDate!=null">
                and order_date &gt;= #{orderSearchVo.startDate}
            </if>
            <if test="orderSearchVo.endDate!=null">
                and order_date &lt;= #{orderSearchVo.endDate}
            </if>
            ) achi_tmp on a.id = achi_tmp.souce_id
        </if>
        <where>
            a.delete_status = '0'
            AND !(a.terminal_type = 1 and a.audit_status != 2)
            <!-- 创建人和销售代理都可以看到订单 -->
            and (
            (
            a.create_by=#{orderSearchVo.createBy, jdbcType=BIGINT} or
            json_contains(a.business_agent ->'$[*].proxyId',
            JSON_ARRAY(${orderSearchVo.createBy}))
            )
            <if test="orderSearchVo.returnSiteIds != null and orderSearchVo.returnSiteIds.size() > 0">
                or (
                achi_tmp.site_id in
                <foreach collection="orderSearchVo.returnSiteIds" item="returnSiteId" open="("
                         close=")" separator=",">
                    #{returnSiteId}
                </foreach>
                and (a.order_type = 2)
                )
            </if>
            )
            <if test="orderSearchVo.siteIds != null and orderSearchVo.siteIds.size() > 0">
                and achi_tmp.site_id in
                <foreach collection="orderSearchVo.siteIds" item="siteId" open="(" separator="," close=")">
                    #{siteId}
                </foreach>
            </if>
            <if test="orderSearchVo.conditionStr!=null and orderSearchVo.conditionStr!=&quot;&quot;">
                and (a.customer_name like concat('%',#{orderSearchVo.conditionStr},'%')
                or a.customer_phone like concat('%',#{orderSearchVo.conditionStr},'%')
                )
            </if>
            and a.id in(
            select a.id
            from cxr_user_order a
            where a.order_date >= #{orderSearchVo.subtractTwoMonthDate}
            <if test="orderSearchVo.orderType!=null">
                and a.order_type= #{orderSearchVo.orderType}
            </if>
            <if test="orderSearchVo.startDate!=null">
                and a.order_date &gt;= #{orderSearchVo.startDate}
            </if>
            <if test="orderSearchVo.endDate!=null">
                and a.order_date &lt;= #{orderSearchVo.endDate}
            </if>
            )
            AND a.is_import IS NULL
        </where>
        ORDER BY a.id desc
    </select>


    <select id="summaryOrders" resultType="com.ruoyi.order.disribution.domain.vo.SummaryOrderVo"
            parameterType="com.ruoyi.order.common.domain.vo.OrderSearchReqVo">
        select
        sum(
        (case when JSON_LENGTH( business_agent)>1 then amount/JSON_LENGTH( business_agent)
        else ifnull( amount,0) end )

        ) AS amount,

        sum( (case when JSON_LENGTH( business_agent)>1 then order_quantity/JSON_LENGTH(
        business_agent)
        else ifnull( order_quantity,0) end ) ) AS orderQuantity,

        sum( (case when JSON_LENGTH( business_agent)>1 then long_milk_give_quantity/JSON_LENGTH(
        business_agent)
        else ifnull( long_milk_give_quantity,0) end ) ) AS longMilkGiveQuantity,

        sum( (case when JSON_LENGTH( business_agent)>1 then fresh_milk_give_quantity/JSON_LENGTH(
        business_agent)
        else ifnull( fresh_milk_give_quantity,0) end ) ) AS freshMilkGiveQuantity

        from
        cxr_user_order a
        <where>

            delete_status = 0
            and (pay_status=3 or audit_status=2)
            -- 对于来自后台的只有 审核通过才展示
            <if test="orderSearchVo.proxyId!=null and orderSearchVo.groupLeaderFlag==false ">
                and json_contains(business_agent, json_object(&apos;proxyId&apos;,
                ${orderSearchVo.proxyId}))
            </if>

            <if test="orderSearchVo.groupLeaderFlag==true">
                and
                (create_by=#{orderSearchVo.createBy} or
                json_contains(business_agent, json_object(&apos;proxyId&apos;,
                ${orderSearchVo.proxyId}))
                )
            </if>

            <if test="orderSearchVo.orderType!=null">
                and order_type= #{orderSearchVo.orderType}
            </if>
            <if test="orderSearchVo.startDate!=null">
                and (
                pay_time &gt;= #{orderSearchVo.startDate}
                or
                audit_time &gt;= #{orderSearchVo.startDate}
                )
            </if>
            <if test="orderSearchVo.endDate!=null">
                and (
                pay_time &lt;= #{orderSearchVo.endDate}
                or
                audit_time &lt;= #{orderSearchVo.endDate}
                )
            </if>
            <if test="orderSearchVo.conditionStr!=null and orderSearchVo.conditionStr!=&quot;&quot;">
                and (customer_name like concat('%',#{orderSearchVo.conditionStr},'%')
                or customer_phone like concat('%',#{orderSearchVo.conditionStr},'%')
                )
            </if>
            and order_date >= #{orderSearchVo.subtractTwoMonthDate}
        </where>
        order by create_time desc
    </select>

    <select id="summaryOrdersAchievement" resultType="java.math.BigDecimal"
            parameterType="com.ruoyi.order.common.domain.vo.OrderSearchReqVo">

        SELECT sum(achievement_value)
        FROM cxr_employee_achievement_detail
        WHERE delete_status = 0 AND employee_id = ${orderSearchVo.proxyId}
        AND souce_id IN (


        select
        a.id
        from
        cxr_user_order a
        <where>

            delete_status = 0
            and (pay_status=3 or audit_status=2)
            -- 对于来自后台的只有 审核通过才展示
            <if test="orderSearchVo.proxyId!=null and orderSearchVo.groupLeaderFlag==false ">
                and json_contains(business_agent, json_object(&apos;proxyId&apos;,
                ${orderSearchVo.proxyId}))
            </if>

            <if test="orderSearchVo.groupLeaderFlag==true">
                and
                (create_by=#{orderSearchVo.createBy} or
                json_contains(business_agent, json_object(&apos;proxyId&apos;,
                ${orderSearchVo.proxyId}))
                )
            </if>

            <if test="orderSearchVo.orderType!=null">
                and order_type= #{orderSearchVo.orderType}
            </if>
            <if test="orderSearchVo.startDate!=null">
                and (
                pay_time &gt;= #{orderSearchVo.startDate}
                or
                audit_time &gt;= #{orderSearchVo.startDate}
                )
            </if>
            <if test="orderSearchVo.endDate!=null">
                and (
                pay_time &lt;= #{orderSearchVo.endDate}
                or
                audit_time &lt;= #{orderSearchVo.endDate}
                )
            </if>
            <if test="orderSearchVo.conditionStr!=null and orderSearchVo.conditionStr!=&quot;&quot;">
                and (customer_name like concat('%',#{orderSearchVo.conditionStr},'%')
                or customer_phone like concat('%',#{orderSearchVo.conditionStr},'%')
                )
            </if>
            and order_date >= #{orderSearchVo.subtractTwoMonthDate}
        </where>

        )
    </select>


    <select id="summaryOrder" resultType="com.ruoyi.order.common.domain.bo.SummaryOrderBo"
            parameterType="com.ruoyi.order.common.domain.vo.OrderSearchReqVo">

        select
        `id`,
        `order_quantity`, -- 订购数量(合计数)
        `amount`, -- 订购金额 ， 退订金额(负数)
        `order_type`, -- 订单类型 退订单7， 算鲜奶合计数
        `province`,
        `city`,
        `area`,
        `audit_status`, -- 审核过了的才算退订
        `long_milk_give_quantity`, -- 鲜奶赠送数量
        `fresh_milk_give_quantity`, -- 常温奶赠送数量，
        `pay_status` , -- 支付状态 支付了才算订购
        `perfect_status`,
        `fresh_milk_return_quantity`, -- 鲜奶退奶数量
        -- 常温奶退奶数量 (无此字段)
        `terminal_type`,
        business_agent
        from cxr_user_order
        <where>
            delete_status = 0
            <if test="orderSearchVo.proxyId!=null">
                and json_contains(business_agent ->'$[*].proxyId',
                JSON_ARRAY(${orderSearchVo.proxyId}))
            </if>
            <if test="orderSearchVo.orderType!=null">
                and order_type= #{orderSearchVo.orderType}
            </if>
            and
            (pay_status = 3 or audit_status = 2 )
            <if test="orderSearchVo.startDate!=null">
                and (
                pay_time &gt;= #{orderSearchVo.startDate}
                or
                audit_time &gt;= #{orderSearchVo.startDate}
                )
            </if>
            <if test="orderSearchVo.endDate!=null">
                and (
                pay_time &lt;= #{orderSearchVo.endDate}
                or
                audit_time &lt;= #{orderSearchVo.endDate}
                )
            </if>
            <if test="orderSearchVo.conditionStr!=null and orderSearchVo.conditionStr!=&quot;&quot;">
                and (customer_name like concat('%',#{orderSearchVo.conditionStr},'%') or
                customer_phone =
                #{orderSearchVo.conditionStr})
            </if>
            and order_date >= #{orderSearchVo.subtractTwoMonthDate}
            AND is_import IS NULL
            -- 要不是审核通过的 要不是已经支付的


        </where>

    </select>
    <select id="sumOrderQuantity"
            resultType="com.ruoyi.business.base.api.domain.dto.DailyIntergerDto">

        select ifnull(sum(t.order_quantity - t.fresh_milk_sent_quantity +
        t.fresh_milk_give_quantity),0) as quantity
        from (
        select order_quantity,fresh_milk_sent_quantity , fresh_milk_give_quantity
        from cxr_user_order
        where
        audit_time between concat(#{day},' 00:00:00') and concat(#{day},' 23:59:59')
        and order_type in
        <foreach collection="list" close=")" open="(" separator="," item="item">
            #{item}
        </foreach>
        and terminal_type = #{man}
        and audit_status=#{audit}
        and customer_phone=#{phone}
        and is_import is null

        union all

        select order_quantity,fresh_milk_sent_quantity , fresh_milk_give_quantity
        from cxr_user_order
        where
        pay_time between concat(#{day},' 00:00:00') and concat(#{day},' 23:59:59')
        and order_type in
        <foreach collection="list" close=")" open="(" separator="," item="item">
            #{item}
        </foreach>
        and terminal_type = #{dis}
        and pay_status=#{pay}
        and customer_phone=#{phone}
        and is_import is null
        ) t
    </select>
    <select id="sumTranInMilk" resultType="com.ruoyi.business.base.api.domain.dto.DailyIntergerDto">
        select ifnull(sum(conversion_quantity),0) as quantity
        from cxr_user_order
        <where>
            audit_time between concat(#{day},' 00:00:00') and concat(#{day},' 23:59:59')
            and order_type=#{orderType}
            and audit_status=#{audit}
            and customer_phone_switch=#{phone}
            and is_import is null
        </where>
    </select>
    <select id="sumTranOutMilk"
            resultType="com.ruoyi.business.base.api.domain.dto.DailyIntergerDto">
        select ifnull(sum(conversion_quantity),0) as quantity
        from cxr_user_order
        <where>
            audit_time between concat(#{day},' 00:00:00') and concat(#{day},' 23:59:59')
            and order_type=#{orderType}
            and audit_status=#{audit}
            and customer_phone=#{phone}
            and is_import is null
        </where>
    </select>
    <select id="sumReturnMilk" resultType="com.ruoyi.business.base.api.domain.dto.DailyIntergerDto">
        select ifnull(sum(t1.fresh_milk_cancel_quantity),0) as quantity
        from cxr_user_order t left join cxr_user_return_order t1 on t1.user_order_id=t.id
        <where>
            t.audit_time between concat(#{day},' 00:00:00') and concat(#{day},' 23:59:59')
            and t.order_type=#{orderType}
            and t.audit_status=#{audit}
            and t.customer_phone=#{phone}
            and t.is_import is null
        </where>
    </select>
    <select id="statisticUserOrder" resultType="java.lang.Integer">
    <![CDATA[
        select ifnull(sum(t2.fresh_milk_sent_quantity), 0)
        from (select t.fresh_milk_sent_quantity
              from cxr_user_order t
              where t.site_id = #{siteId}
                and t.terminal_type = 2
                and t.pay_status = 3
                and t.create_time >= #{startTime}
                and t.create_time <= #{endTime}
                and t.delete_status = '0'
                AND t.is_import IS NULL
              union all
              select t.fresh_milk_sent_quantity
              from cxr_user_order t
              where t.site_id = #{siteId}
                and t.terminal_type = 1
                and t.audit_status = 2
                and t.create_time >= #{startTime}
                and t.create_time <= #{endTime}
                and t.delete_status = '0'
                AND t.is_import IS NULL) t2
        ]]>
    </select>
    <select id="countNewOrder" resultType="com.ruoyi.business.base.api.domain.dto.DailyIntergerDto">
        select count(x.id) as quantity,x.cxrEmployeeId as cxrEmployeeId
        from
        (select id ,t1.cxrEmployeeId as cxrEmployeeId
        from cxr_user_order t,
        json_table(t.business_agent, '$[*]' COLUMNS ( cxrEmployeeId BIGINT path '$.proxyId' )) t1
        <where>
            t.order_type=#{new}
            and t.terminal_type=#{dis}
            and t.pay_status=#{pay}
            and date_format(t.order_date , '%Y-%m-%d')=#{now}
            and cxrEmployeeId =#{id}
        </where>
        union all
        select id ,z1.cxrEmployeeId as cxrEmployeeId
        from cxr_user_order z,
        json_table(z.business_agent, '$[*]' COLUMNS ( cxrEmployeeId BIGINT path '$.proxyId' )) z1
        <where>
            z.order_type=#{new}
            and z.terminal_type=#{man}
            and z.audit_status=#{audit}
            and date_format(z.order_date , '%Y-%m-%d')=#{now}
            and cxrEmployeeId =#{id}
        </where>) as x
        group by x.cxrEmployeeId

    </select>
    <select id="countValidOrder" resultType="java.lang.String">
        select x.quantity
        from
        (select JSON_EXTRACT(any_value(t.business_agent), '$[*].proxyId') AS quantity
        from cxr_user_order t
        <where>
            t.order_type in
            <foreach collection="types" separator="," item="i" open="(" close=")">
                #{i}
            </foreach>
            and (t.pay_status=3 or t.audit_status=2)
            and date_format(t.order_date , '%Y-%m-%d')=#{now}
            and t.amount>=330
            and t.order_quantity>=30
            and json_contains(t.business_agent->'$[*].proxyId',cast(#{id} as json ))
            and delete_status=0
            and is_import is null
            GROUP BY SUBSTRING_INDEX(order_no, '-', 1)
        </where>
        ) as x
    </select>

    <select id="orderStatistics"
            resultType="com.ruoyi.order.api.domain.vo.CustomerOrderStatisticsVo">
        select t.*,
        ifnull( CASE WHEN t.order_type = 2 THEN t.fresh_milk_return_quantity
        ELSE (t.order_quantity+fresh_milk_give_quantity) - fresh_milk_sent_quantity END
        ,0) as orderQuantity,
        ifnull( CASE WHEN t.order_type = 2 THEN t.fresh_milk_return_quantity
        ELSE
        t.order_quantity END ,0) as designQuantity,
        JSON_LENGTH(t.business_agent) businessAgentSize
        ,t.customer_info_list->>'$[0].siteName'AS deliverySite
        ,JSON_EXTRACT(t.customer_info_list, '$[0].siteId') AS deliverySiteId
        from cxr_user_order t
        where
        1=1
        <include refid="orderStatisticsWhere"/>
        and t.is_import is null
        and (t.pay_status=3 or t.audit_status=2)
        and t.delete_status=0
        order by t.order_date desc

    </select>

    <select id="TotalOrderStatistics" resultType="com.ruoyi.order.api.domain.vo.CustomerOrderTotal">
        select

        <if test="bo.siteName!=null and  bo.proxyIds !=null and bo.proxyIds.size() >0 ">
            ifnull(sum(CASE WHEN t.order_type != 2 THEN t.fresh_milk_sent_quantity / JSON_LENGTH(
            t.business_agent )
            ELSE 0 END),0) as totalFreshMilkSentQuantity,
            ifnull(sum(t.excess_quantity / JSON_LENGTH( t.business_agent ) ),0) as
            totalExcessQuantity,

            ifnull(sum(CASE WHEN t.order_type != 2 THEN t.long_milk_give_quantity / JSON_LENGTH(
            t.business_agent ) ELSE
            - abs(long_milk_give_quantity)/ JSON_LENGTH( t.business_agent ) END),0) as
            totalLongMilkGiveQuantity,

            ifnull(sum( CASE WHEN t.order_type != 2 THEN ( t.order_quantity +
            t.fresh_milk_give_quantity ) /
            JSON_LENGTH( t.business_agent ) ELSE 0 END ),0) as totalOrderQuantity,

            ifnull(sum( case when t.order_type=2 then abs(t.order_quantity) else 0 end ),0) as
            returnTotalQuantity,

            ifnull( sum( CASE WHEN t.order_type = 2 THEN (abs( t.fresh_milk_return_quantity ) +
            t.fresh_milk_give_quantity) / JSON_LENGTH( t.business_agent ) ELSE 0 END ), 0 ) AS
            totalFreshMilkReturnQuantityVo,

            ifnull(sum( case when t.order_type!=2 THEN t.order_quantity / JSON_LENGTH(
            t.business_agent ) ELSE -abs(
            t.fresh_milk_return_quantity )/ JSON_LENGTH( t.business_agent ) END ),0) as
            totalDesignQuantity,

            ifnull(sum(CASE WHEN t.order_type != 2 THEN t.fresh_milk_give_quantity / JSON_LENGTH(
            t.business_agent )
            ELSE 0 END),0) as totaFfreshMilkGiveQuantity,


            ifnull( sum( CASE WHEN t.order_type != 2 THEN CASE
            WHEN JSON_LENGTH( t.business_agent )>0 then t.amount / JSON_LENGTH( t.business_agent )
            else t.amount end
            ELSE 0 END ), 0 ) AS totalAmount,


            ifnull( sum( (case when JSON_LENGTH(t.business_agent)>1 and t.order_type = 2 then
            abs(t.amount)/JSON_LENGTH(t.business_agent)

            when t.order_type = 2 then

            ifnull(abs(t.amount),0) end ) ),0) AS returnTotalAmount,

            ifnull( sum( JSON_OVERLAPS(t.business_agent -> '$[*].proxyId',CAST(
            #{bo.proxyIdsJson,jdbcType=VARCHAR} AS JSON )) ), 0 ) AS splitTotal,
        </if>

        <if test="bo.siteName!=null   ">

            ifnull(sum(CASE WHEN (select JSON_LENGTH(JSON_SEARCH(business_agent, 'all',
            #{bo.siteName},null,'$[*].siteName') ) from cxr_user_order where id=t.id)
            != JSON_LENGTH( t.business_agent ) THEN t.fresh_milk_sent_quantity / JSON_LENGTH(
            t.business_agent )
            * ( SELECT JSON_LENGTH( JSON_SEARCH(business_agent, 'all',
            #{bo.siteName},null,'$[*].siteName') ) FROM
            cxr_user_order WHERE id = t.id )
            ELSE
            t.fresh_milk_sent_quantity END),0) as totalFreshMilkSentQuantity,

            ifnull( sum(
            CASE WHEN
            (select JSON_LENGTH(JSON_SEARCH(business_agent, 'all',
            #{bo.siteName},null,'$[*].siteName') ) from
            cxr_user_order where id=t.id)
            != JSON_LENGTH( t.business_agent )
            THEN
            t.excess_quantity / JSON_LENGTH( t.business_agent )
            * ( SELECT JSON_LENGTH( JSON_SEARCH(business_agent, 'all',
            #{bo.siteName},null,'$[*].siteName') ) FROM
            cxr_user_order WHERE id = t.id )
            ELSE
            t.excess_quantity END
            ), 0 ) AS totalExcessQuantity,

            ifnull( sum( CASE WHEN t.order_type != 2 THEN
            CASE
            WHEN (select JSON_LENGTH(JSON_SEARCH(business_agent, 'all',
            #{bo.siteName},null,'$[*].siteName') ) from
            cxr_user_order where id=t.id)
            != JSON_LENGTH( t.business_agent ) THEN t.long_milk_give_quantity/ JSON_LENGTH(
            t.business_agent )
            * ( SELECT JSON_LENGTH( JSON_SEARCH(business_agent, 'all',
            #{bo.siteName},null,'$[*].siteName') ) FROM
            cxr_user_order WHERE id = t.id )
            ELSE t.long_milk_give_quantity
            END
            ELSE
            0 END ), 0 ) AS totalLongMilkGiveQuantity,


            ifnull( sum( CASE WHEN t.order_type = 2 THEN
            CASE
            WHEN (select JSON_LENGTH(JSON_SEARCH(business_agent, 'all',
            #{bo.siteName},null,'$[*].siteName') ) from
            cxr_user_order where id=t.id)
            != JSON_LENGTH( t.business_agent ) THEN - abs( long_milk_give_quantity )/ JSON_LENGTH(
            t.business_agent )
            ELSE - abs( long_milk_give_quantity )
            END
            ELSE
            0 END ), 0 ) AS totalReturnLongMilkGiveQuantity,


            ifnull( sum( CASE WHEN t.order_type != 2 THEN CASE
            WHEN (select JSON_LENGTH(JSON_SEARCH(business_agent, 'all',
            #{bo.siteName},null,'$[*].siteName') ) from
            cxr_user_order where id=t.id)
            != JSON_LENGTH( t.business_agent ) THEN
            ( t.order_quantity + t.fresh_milk_give_quantity ) / JSON_LENGTH( t.business_agent )
            * ( SELECT JSON_LENGTH( JSON_SEARCH(business_agent, 'all',
            #{bo.siteName},null,'$[*].siteName') ) FROM
            cxr_user_order WHERE id = t.id )
            ELSE ( t.order_quantity
            + t.fresh_milk_give_quantity ) END ELSE 0 END ), 0 ) AS totalOrderQuantity,

            ifnull( sum( CASE WHEN t.order_type = 2 THEN abs( t.order_quantity ) ELSE 0 END ), 0 )
            AS
            returnTotalQuantity,

            ifnull( sum( CASE WHEN t.order_type = 2 THEN
            CASE
            WHEN (select JSON_LENGTH(JSON_SEARCH(business_agent, 'all',
            #{bo.siteName},null,'$[*].siteName') ) from
            cxr_user_order where id=t.id)
            != JSON_LENGTH( t.business_agent ) THEN
            abs( t.fresh_milk_return_quantity ) / JSON_LENGTH( t.business_agent )
            * ( SELECT JSON_LENGTH( JSON_SEARCH(business_agent, 'all',
            #{bo.siteName},null,'$[*].siteName') ) FROM
            cxr_user_order WHERE id = t.id )
            ELSE abs( t.fresh_milk_return_quantity
            ) end ELSE 0 END ), 0 ) AS totalFreshMilkReturnQuantityVo,


            ifnull( sum( CASE WHEN t.order_type != 2 THEN CASE
            WHEN (select JSON_LENGTH(JSON_SEARCH(business_agent, 'all',
            #{bo.siteName},null,'$[*].siteName') ) from
            cxr_user_order where id=t.id)
            != JSON_LENGTH( t.business_agent ) THEN t.order_quantity / JSON_LENGTH( t.business_agent
            )
            * ( SELECT JSON_LENGTH( JSON_SEARCH(business_agent, 'all',
            #{bo.siteName},null,'$[*].siteName') ) FROM
            cxr_user_order WHERE id = t.id )
            ELSE
            t.order_quantity END ELSE 0 END ), 0 ) AS totalDesignQuantity,

            ifnull( sum( CASE WHEN t.order_type = 2 THEN CASE
            WHEN (select JSON_LENGTH(JSON_SEARCH(business_agent, 'all',
            #{bo.siteName},null,'$[*].siteName') ) from
            cxr_user_order where id=t.id)
            != JSON_LENGTH( t.business_agent ) THEN - abs( t.fresh_milk_return_quantity ) /
            JSON_LENGTH(
            t.business_agent ) ELSE - abs( t.fresh_milk_return_quantity ) END ELSE 0 END ), 0 ) AS
            totalReturnDesignQuantity,

            ifnull( sum( CASE WHEN t.order_type != 2 THEN CASE
            WHEN (select JSON_LENGTH(JSON_SEARCH(business_agent, 'all',
            #{bo.siteName},null,'$[*].siteName') ) from
            cxr_user_order where id=t.id)
            != JSON_LENGTH( t.business_agent ) THEN t.fresh_milk_give_quantity / JSON_LENGTH(
            t.business_agent ) ELSE
            t.fresh_milk_give_quantity end ELSE 0 END ), 0 ) AS totaFfreshMilkGiveQuantity,

            ifnull( sum( CASE WHEN t.order_type = 2 THEN CASE
            WHEN (select JSON_LENGTH(JSON_SEARCH(business_agent, 'all',
            #{bo.siteName},null,'$[*].siteName') ) from
            cxr_user_order where id=t.id)
            != JSON_LENGTH( t.business_agent ) then - abs( fresh_milk_give_quantity ) / JSON_LENGTH(
            t.business_agent )
            ELSE - abs( fresh_milk_give_quantity ) end ELSE 0 END ), 0 ) AS
            totaRetuenFfreshMilkGiveQuantity,


            ifnull( sum( CASE WHEN t.order_type != 2 THEN CASE
            WHEN (select JSON_LENGTH(JSON_SEARCH(business_agent, 'all',
            #{bo.siteName},null,'$[*].siteName') ) from
            cxr_user_order where id=t.id)
            != JSON_LENGTH( t.business_agent ) then t.amount / JSON_LENGTH( t.business_agent )
            * ( SELECT JSON_LENGTH( JSON_SEARCH(business_agent, 'all',
            #{bo.siteName},null,'$[*].siteName') ) FROM
            cxr_user_order WHERE id = t.id )
            else t.amount end ELSE 0
            END ), 0 ) AS totalAmount,


            ifnull( sum(( CASE WHEN t.order_type = 2 THEN
            CASE
            WHEN (select JSON_LENGTH(JSON_SEARCH(business_agent, 'all',
            #{bo.siteName},null,'$[*].siteName') ) from
            cxr_user_order where id=t.id)
            != JSON_LENGTH( t.business_agent ) then

            ifnull( - abs( t.amount ), 0 ) /JSON_LENGTH( t.business_agent )
            * ( SELECT JSON_LENGTH( JSON_SEARCH(business_agent, 'all',
            #{bo.siteName},null,'$[*].siteName') ) FROM
            cxr_user_order WHERE id = t.id )
            else ifnull( - abs( t.amount ), 0 ) end ELSE
            0 END )), 0 ) returnTotalAmount,

            ifnull( sum( ( select JSON_LENGTH(JSON_SEARCH(business_agent, 'all',
            #{bo.siteName},null,'$[*].siteName')
            ) from cxr_user_order where id=t.id )) , 0 ) AS splitTotal,
        </if>


        <if test="bo.proxyIds !=null and bo.proxyIds.size() >0 ">

            ifnull(sum(CASE WHEN t.order_type != 2 THEN t.fresh_milk_sent_quantity / JSON_LENGTH(
            t.business_agent )
            ELSE 0 END),0) as totalFreshMilkSentQuantity,
            ifnull(sum(t.excess_quantity / JSON_LENGTH( t.business_agent ) ),0) as
            totalExcessQuantity,
            ifnull(sum(CASE WHEN t.order_type != 2 THEN t.long_milk_give_quantity / JSON_LENGTH(
            t.business_agent ) ELSE
            - abs(long_milk_give_quantity)/ JSON_LENGTH( t.business_agent ) END),0) as
            totalLongMilkGiveQuantity,
            ifnull(sum( CASE WHEN t.order_type != 2 THEN ( t.order_quantity +
            t.fresh_milk_give_quantity ) /
            JSON_LENGTH( t.business_agent ) ELSE 0 END ),0) as totalOrderQuantity,
            ifnull(sum( case when t.order_type=2 then abs(t.order_quantity) else 0 end ),0) as
            returnTotalQuantity,
            ifnull( sum( CASE WHEN t.order_type = 2 THEN (abs( t.fresh_milk_return_quantity ) +
            t.fresh_milk_give_quantity) / JSON_LENGTH( t.business_agent ) ELSE 0 END ), 0 ) AS
            totalFreshMilkReturnQuantityVo,
            ifnull(sum( case when t.order_type!=2 THEN t.order_quantity / JSON_LENGTH(
            t.business_agent ) ELSE -abs(
            t.fresh_milk_return_quantity )/ JSON_LENGTH( t.business_agent ) END ),0) as
            totalDesignQuantity,
            ifnull(sum(CASE WHEN t.order_type != 2 THEN t.fresh_milk_give_quantity / JSON_LENGTH(
            t.business_agent )
            ELSE 0 END),0) as totaFfreshMilkGiveQuantity,


            ifnull( sum( CASE WHEN t.order_type != 2 THEN CASE
            WHEN JSON_LENGTH( t.business_agent )>0 then t.amount / JSON_LENGTH( t.business_agent )
            else t.amount end
            ELSE 0 END ), 0 ) AS totalAmount,


            ifnull( sum( (case when JSON_LENGTH(t.business_agent)>1 and t.order_type = 2 then
            abs(t.amount)/JSON_LENGTH(t.business_agent)

            when t.order_type = 2 then

            ifnull(abs(t.amount),0) end ) ),0) AS returnTotalAmount,
            ifnull( sum( JSON_OVERLAPS(t.business_agent -> '$[*].proxyId',CAST(
            #{bo.proxyIdsJson,jdbcType=VARCHAR} AS JSON )) ), 0 ) AS splitTotal,
        </if>


        <if test="bo.proxyIds ==null or bo.proxyIds.size()==0  ">

            ifnull( sum( CASE WHEN t.order_type != 2 THEN t.fresh_milk_sent_quantity ELSE 0 END ), 0
            ) AS
            totalFreshMilkSentQuantity,

            ifnull( sum( t.excess_quantity ), 0 ) AS totalExcessQuantity,

            ifnull(
            sum(
            CASE WHEN t.order_type != 2 THEN
            t.long_milk_give_quantity ELSE - abs( long_milk_give_quantity )
            END
            ),
            0
            ) AS totalLongMilkGiveQuantity,

            ifnull(
            sum(
            CASE

            WHEN t.order_type != 2 THEN
            ( t.order_quantity + t.fresh_milk_give_quantity ) ELSE 0
            END
            ),
            0
            ) AS totalOrderQuantity,
            ifnull( sum( CASE WHEN t.order_type = 2 THEN abs( t.order_quantity ) ELSE 0 END ), 0 )
            AS
            returnTotalQuantity,
            ifnull( sum( CASE WHEN t.order_type = 2 THEN abs( t.fresh_milk_return_quantity ) ELSE 0
            END ), 0 ) AS
            totalFreshMilkReturnQuantityVo,
            ifnull(
            sum(
            CASE

            WHEN t.order_type != 2 THEN
            t.order_quantity ELSE - abs( t.fresh_milk_return_quantity )
            END
            ),
            0
            ) AS totalDesignQuantity,

            ifnull(
            sum(
            CASE

            WHEN t.order_type != 2 THEN
            t.fresh_milk_give_quantity ELSE 0
            END
            ),
            0
            ) AS totaFfreshMilkGiveQuantity,


            ifnull( sum( CASE WHEN t.order_type != 2 THEN t.amount ELSE 0 END ), 0 ) AS totalAmount,

            ifnull( sum((
            CASE WHEN t.order_type = 2 THEN ifnull( - abs(t.amount), 0 ) ELSE 0 END )),0)
            returnTotalAmount,
        </if>

        ifnull(sum(t.unit_price),0) as TotalUnitPriceAmount,
        ifnull(sum(JSON_LENGTH( t.business_agent )),0) as splitTotal

        from cxr_user_order t
        where 1=1
        <if test="bo.startOrderDate != null">
            and t.order_date <![CDATA[ >= ]]> #{bo.startOrderDate}
        </if>

        <if test="bo.endOrderDate != null">
            and t.order_date <![CDATA[ <= ]]> #{bo.endOrderDate}
        </if>

        <!--        <if test="bo.bigAreaName != null and bo.bigAreaName != ''">-->
        <!--            and INSTR(t.big_area_name, #{bo.bigAreaName}) > 0-->
        <!--        </if>-->


        <if test="bo.siteName != null and bo.siteName != ''">
            and INSTR(t.business_agent, #{bo.siteName}) > 0
        </if>

        <if test="bo.employeeName != null">
            and INSTR(t.business_agent, #{bo.employeeName}) > 0
        </if>
        <if test="bo.employeeJobNumber != null">
            and INSTR(t.business_agent, #{bo.employeeJobNumber}) > 0
        </if>

        <if test="bo.orderType != null and bo.orderType != 86 ">
            and t.order_type = #{bo.orderType}
        </if>

        <if test="bo.orderType != null and bo.orderType == 86 ">
            and JSON_LENGTH(t.business_agent)>1
        </if>

        <if test="bo.auditStatus != null and  bo.auditStatus !=''">
            and t.audit_status =#{bo.auditStatus}
        </if>

        <if test="bo.activityId != null and  bo.activityId !=''">
            and t.activity_id =#{bo.activityId}
        </if>

        <if test="bo.companyId != null and  bo.companyId !=''">
            and t.company_id =#{bo.companyId}
        </if>


        <if test="bo.terminalType!=null">
            and t.terminal_type=#{bo.terminalType}
        </if>

        <if test="bo.customerPhone != null ">
            and t.customer_phone like concat(#{bo.customerPhone},'%')
        </if>
        <if test="bo.employeeFlag==true">
            and t.site_id in
            <foreach collection="bo.siteIds" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>

        <if test="bo.customerAddress != null ">
            and t.customer_adress like concat(#{bo.customerAddress},'%')
        </if>
        <if test="bo.proxyIds !=null and bo.proxyIds.size() >0">
            and JSON_OVERLAPS(t.business_agent -> '$[*].proxyId',CAST(
            #{bo.proxyIdsJson,jdbcType=VARCHAR} AS JSON ))
        </if>
        <if test="bo.accountingType!=null">
            and t.accounting_type=#{bo.accountingType}
        </if>
        <if test="bo.productName!=null and bo.productName!=''">
            and t.product_name like concat ('%',#{bo.productName},'%')
        </if>
        <if test="bo.productTypeName!=null and bo.productTypeName!=''">
            and t.product_type_name like concat ('%',#{bo.productTypeName},'%')
        </if>
        <if test="bo.promotionCommission!=null">
            and t.promotion_commission=#{bo.promotionCommission}
        </if>
        <if test="bo.terminalType!=null">
            and t.terminal_type=#{bo.terminalType}
        </if>
        and t.delete_status=0
        and t.is_import is null
        and (t.pay_status=3 or t.audit_status=2)
        ORDER BY t.order_date desc

    </select>
    <select id="countReturnOrder"
            resultType="com.ruoyi.business.base.api.domain.dto.DailyIntergerDto">


        select count(id) as quantity ,z1.cxrEmployeeId as cxrEmployeeId
        from cxr_user_order z,
        json_table(z.business_agent, '$[*]' COLUMNS ( cxrEmployeeId BIGINT path '$.proxyId' )) z1
        <where>
            z.order_type=#{new}
            and z.audit_status=#{audit}
            and date_format(z.order_date , '%Y-%m-%d')=#{now}
        </where>
        group by z1.cxrEmployeeId


    </select>
    <select id="sumGiveFreshMilkNumber"
            resultType="com.ruoyi.business.base.api.domain.dto.DailyIntergerDto">
        SELECT sum(x.fresh_milk_give_quantity) as quantity,sum(x.amount)as money
        ,sum(x.long_milk_give_quantity) as
        LongmilkQuantity, x.cxrSiteId as cxrSiteId
        from
        (select t.fresh_milk_give_quantity,t.amount,t.long_milk_give_quantity ,t.site_id as
        cxrSiteId
        from cxr_user_order t
        <where>
            t.terminal_type=#{dis}
            and t.pay_status=#{pay}
            and date_format(t.order_date , '%Y-%m-%d')=#{now}
            and t.order_type in
            <foreach collection="types" separator="," item="type" close=")" open="(">
                #{type}
            </foreach>
        </where>
        union all
        select z.fresh_milk_give_quantity,z.amount,z.long_milk_give_quantity ,z.site_id as cxrSiteId
        from cxr_user_order z
        <where>
            z.terminal_type=#{man}
            and z.audit_status=#{audit}
            and date_format(z.order_date , '%Y-%m-%d')=#{now}
            and z.order_type in
            <foreach collection="types" separator="," item="type" close=")" open="(">
                #{type}
            </foreach>
        </where>
        ) as x
        group by cxrSiteId
    </select>
    <select id="queryAuditSuccess" resultType="java.lang.Long">
        select id from cxr_user_order
        <where>
            date_format(order_date , '%Y-%m-%d')=#{min}
            and order_type=#{type}
            and audit_status=#{code}
        </where>


    </select>

    <select id="pageOrderTotalStatistics"
            resultType="com.ruoyi.order.api.domain.vo.CustomerOrderTotal">

        select
        ifnull(sum(CASE WHEN tt.order_type != 2 THEN tt.fresh_milk_sent_quantity ELSE
        -tt.fresh_milk_sent_quantity
        END),0) as totalFreshMilkSentQuantity,
        ifnull(sum(tt.excess_quantity),0) as totalExcessQuantity,
        ifnull(sum( CASE WHEN tt.order_type != 2 THEN tt.long_milk_give_quantity ELSE 0 END),0) as
        totalLongMilkGiveQuantity,
        ifnull(sum( CASE WHEN tt.order_type = 2 THEN -tt.fresh_milk_return_quantity ELSE
        (tt.order_quantity +
        tt.fresh_milk_give_quantity) - tt.fresh_milk_sent_quantity END ), 0) AS totalOrderQuantity,
        ifnull(sum( CASE WHEN tt.order_type = 2 THEN -tt.fresh_milk_return_quantity ELSE
        tt.order_quantity END ), 0 ) AS
        totalDesignQuantity,
        ifnull(sum(abs(tt.amount))- sum( ( case when tt.order_type=2 then ifnull( abs(tt.amount) ,0)
        else 0 end )), 0 )
        AS totalAmount,
        ifnull(sum((CASE WHEN tt.order_type = 2 and tt.audit_status=2 THEN ifnull( - abs(tt.amount),
        0 ) ELSE 0 END
        )),0) returnTotalAmount,
        ifnull(sum(tt.unit_price),0) as TotalUnitPriceAmount,
        ifnull(sum(CASE WHEN tt.order_type != 2 THEN tt.fresh_milk_give_quantity ELSE 0 END),0) as
        totaFfreshMilkGiveQuantity,
        ifnull(sum(tt.conversion_quantity),0) as totalConversionQuantity,
        ifnull(sum(tt.fresh_milk_return_quantity),0) as totalFreshMilkReturnQuantity,
        ifnull(sum(tt.exchange_sum),0) as totalExchangeSum,
        ifnull(sum(tt.milk_exchange_sum),0) as totalMilkExchangeSum
        from cxr_user_order tt
        inner join (
        select id from cxr_user_order t
        <where>

            <if test="cxrUserOrderBo.companyId !=null">
                and t.company_id = #{cxrUserOrderBo.companyId}
            </if>

            <if test="cxrUserOrderBo.bigAreaId !=null">
                and t.big_area_id = #{cxrUserOrderBo.bigAreaId}
            </if>

            <if test="cxrUserOrderBo.siteId !=null">
                and t.site_id= #{cxrUserOrderBo.siteId}
            </if>

            <if test="cxrUserOrderBo.proxyIds !=null and cxrUserOrderBo.proxyIds.size() >0">
                and JSON_OVERLAPS(t.business_agent -> '$[*].proxyId',CAST(
                #{cxrUserOrderBo.proxyIdsJson,jdbcType=VARCHAR} AS JSON ))
            </if>

            <if test="cxrUserOrderBo.promotionalOrderFlag">
                and t.promotional_order_flag = true
            </if>

            <if test="cxrUserOrderBo.apprenticeOrderFlag">
                and t.apprentice_order_flag = true
            </if>
            <if test="cxrUserOrderBo.abnormalTag !=null ">
                and t.abnormal_tag = #{cxrUserOrderBo.abnormalTag}
            </if>

            <if test="cxrUserOrderBo.orderType != null ">
                <choose>
                    <when test='cxrUserOrderBo.orderType == "0"'>AND (t.order_type = #{cxrUserOrderBo.orderType} OR
                        t.contract_type_tag = #{cxrUserOrderBo.orderType})
                    </when>
                    <when test='cxrUserOrderBo.orderType == "1"'>AND (t.order_type = #{cxrUserOrderBo.orderType} OR
                        t.contract_type_tag = #{cxrUserOrderBo.orderType})
                    </when>
                    <otherwise>AND t.order_type = #{cxrUserOrderBo.orderType}</otherwise>
                </choose>
            </if>

            <if test="cxrUserOrderBo.paymentSouce != null ">
                and t.payment_souce = #{cxrUserOrderBo.paymentSouce}
            </if>

            <if test="cxrUserOrderBo.customerPhone != null ">
                and t.customer_phone like concat(#{cxrUserOrderBo.customerPhone},'%')
            </if>

            <if test="cxrUserOrderBo.activityId != null ">
                and t.activity_id = #{cxrUserOrderBo.activityId}
            </if>

            <if test="cxrUserOrderBo.customerPhoneSwitch != null ">
                and t.customer_phone_switch like
                concat('%',#{cxrUserOrderBo.customerPhoneSwitch},'%')
            </if>

            <if test="cxrUserOrderBo.auditStatus != null ">

                <if test="cxrUserOrderBo.auditStatus ==1 || cxrUserOrderBo.auditStatus ==2 || cxrUserOrderBo.auditStatus ==6">
                    and t.audit_status = #{cxrUserOrderBo.auditStatus}
                </if>
                <if test="cxrUserOrderBo.auditStatus ==3 ">
                    and t.pay_status = #{cxrUserOrderBo.auditStatus}
                </if>

                <if test="cxrUserOrderBo.auditStatus ==4 ">
                    and t.perfect_status = 1
                </if>
            </if>

            <if test="cxrUserOrderBo.customerAdress != null ">
                and t.customer_adress like concat('%',#{cxrUserOrderBo.customerAdress},'%')
            </if>

            <if test="cxrUserOrderBo.orderNo != null ">
                and t.order_no like concat('%',#{cxrUserOrderBo.orderNo},'%')
            </if>

            <if test="cxrUserOrderBo.merchantOrderNo != null ">
                and t.merchant_order_no like concat(#{cxrUserOrderBo.merchantOrderNo},'%')
            </if>
            <if test="cxrUserOrderBo.startDateTime != null ">
                and t.order_date &gt;= #{cxrUserOrderBo.startDateTime}
            </if>

            <if test="cxrUserOrderBo.endDateTime != null ">
                and t.order_date &lt;=
                #{cxrUserOrderBo.endDateTime}
            </if>

            <if test="cxrUserOrderBo.terminalType !=null">
                and t.terminal_type= #{cxrUserOrderBo.terminalType}
            </if>

            and t.delete_status = 0

            <if test=" cxrUserOrderBo.auditStatus !=4  ">
                AND (t.audit_status = 2 OR t.pay_status = 3)
            </if>

            <if test="cxrUserOrderBo.accountingType!=null">
                and t.accounting_type=#{cxrUserOrderBo.accountingType}
            </if>
            <if test="cxrUserOrderBo.productName!=null and cxrUserOrderBo.productName!=''">
                and t.product_name like concat ('%',#{cxrUserOrderBo.productName},'%')
            </if>
            <if test="cxrUserOrderBo.productTypeName!=null and cxrUserOrderBo.productTypeName!=''">
                and t.product_type_name like concat ('%',#{cxrUserOrderBo.productTypeName},'%')
            </if>
            <if test="cxrUserOrderBo.promotionCommission!=null">
                and t.promotion_commission=#{cxrUserOrderBo.promotionCommission}
            </if>

            <if test="cxrUserOrderBo.startUpdateTime != null ">
                and t.update_time &gt;= #{cxrUserOrderBo.startUpdateTime}
            </if>

            <if test="cxrUserOrderBo.endUpdateTime != null ">
                and t.update_time &lt;= #{cxrUserOrderBo.endUpdateTime}

            </if>

            <if test="cxrUserOrderBo.deliverySite!=null and cxrUserOrderBo.deliverySite!=''">
                and (t.delivery_sites like concat ('%',#{cxrUserOrderBo.deliverySite},'%')
                or t.customer_info_list like concat ('%',#{cxrUserOrderBo.deliverySite},'%')
                )
            </if>
            order by t.order_date desc
        </where>
        ) tt2 on tt2.id = tt.id
        order by tt.order_date desc

    </select>
    <select id="queryLastOrderInfo" resultMap="CxrUserOrderResult">

        select tt.*
        from (select *
              from (select t.*, t.audit_time seq
                    from cxr_user_order t
                    where t.customer_id = #{customerId}
                      and t.order_type in (0, 1, 3, 7)
                      and t.terminal_type = 1
                      and t.audit_status = 2
                    order by t.audit_time desc
                    limit 1) t1
              union all
              select *
              from (select t.*, t.pay_time seq
                    from cxr_user_order t
                    where t.customer_id = #{customerId}
                      and t.order_type in (0, 1, 3, 7)
                      and t.terminal_type = 2
                      and t.pay_status = 3
                    order by t.pay_time desc
                    limit 1) t2) tt
        order by tt.seq desc
        limit 1
    </select>

    <select id="queryFirstOrderInfo" resultMap="CxrUserOrderResult">
        select tt.*
        from (select *
              from (select t.*, t.audit_time seq
                    from cxr_user_order t
                    where t.customer_id = #{customerId}
                      and t.order_type in (0, 1, 3, 7)
                      and t.terminal_type = 1 -- 1=后台录入
                      and t.audit_status = 2  -- 2 已审核
                    order by t.audit_time ASC
                    limit 1) t1
              union all
              select *
              from (select t.*, t.pay_time seq
                    from cxr_user_order t
                    where t.customer_id = #{customerId}
                      and t.order_type in (0, 1, 3, 7)
                      and t.terminal_type in (2, 4) -- 2=配送端录入,4=小程序
                      and t.pay_status = 3          -- 3.支付成功
                    order by t.pay_time ASC
                    limit 1) t2) tt
        order by tt.seq ASC
        limit 1
    </select>

    <select id="queryFirstOrderInfoV2" resultMap="CxrUserOrderResult">
        select tt.*
        from (select *
              from (select t.*, t.audit_time seq
                    from cxr_user_order t
                    where t.delete_status = 0
                      and t.customer_id = #{customerId}
                      and t.order_type in (0, 1, 3, 7)
                      and t.terminal_type = 1 -- 1=后台录入
                      and t.audit_status = 2  -- 2 已审核
                    order by t.audit_time ASC
                    limit 1) t1
              union all
              select *
              from (select t.*, t.pay_time seq
                    from cxr_user_order t
                    where t.delete_status = 0
                      and t.customer_id = #{customerId}
                      and t.order_type in (0, 1, 3, 7)
                      and t.terminal_type in (2, 4) -- 2=配送端录入,4=小程序
                      and t.pay_status = 3          -- 3.支付成功
                    order by t.pay_time ASC
                    limit 1) t2
              union all
              select *
              from (select t.*, t.audit_time seq
                    from cxr_user_order t
                    where t.delete_status = 0
                      and t.customer_id = #{customerId}
                      and t.order_type = 6   -- 6赠送单
                      and t.audit_status = 2 -- 2 已审核
                    order by t.audit_time ASC
                    limit 1) t3
              union all
              select *
              from (select t.*, t.audit_time seq
                    from cxr_user_order t
                    where t.delete_status = 0
                      and t.customer_id_switch = #{customerId}
                      and t.order_type = 4   -- 4转单
                      and t.audit_status = 2 -- 2 已审核
                    order by t.audit_time ASC
                    limit 1) t3) tt
        order by tt.seq ASC
        limit 1
    </select>

    <select id="queryLastOrder" resultMap="CxrUserOrderResult">

        SELECT
        t.id,IFNULL(t.pay_time,IFNULL(t.audit_time,t.order_date))order_date,business_agent,customer_id,order_quantity,order_type,fresh_milk_give_quantity,fresh_milk_sent_quantity,apprentice_order_flag,promotional_order_flag
        FROM cxr_user_order t
        WHERE t.customer_id= #{customerId}
        <if test="orderId != null">
            AND t.id != #{orderId}
            AND t.create_time &lt; (SELECT create_time FROM cxr_user_order WHERE id = #{orderId} )
        </if>
        AND t.order_type in (0,1,3, 7)
        AND ((t.audit_status = 2 ) OR (t.pay_status = 3))
        AND t.delete_status = '0'
        ORDER By order_date DESC
        LIMIT 1
    </select>

    <select id="queryLastOrderInfoNotContinue" resultMap="CxrUserOrderResult">

        SELECT t.id,
               IFNULL(t.pay_time, IFNULL(t.audit_time, t.order_date)) order_date,
               business_agent,
               customer_id
        FROM cxr_user_order t
        WHERE t.customer_id = #{customerId}
          AND t.order_type in (0, 1, 3)
          AND ((t.audit_status = 2) OR (t.pay_status = 3))
          AND t.delete_status = '0'
        ORDER By order_date DESC
        LIMIT 1
    </select>

    <select id="queryLastPromotionalOrderOrder" resultMap="CxrUserOrderResult">

        SELECT
        t.id,IFNULL(t.pay_time,IFNULL(t.audit_time,t.order_date))order_date,business_agent,customer_id,order_quantity,order_type,fresh_milk_give_quantity,fresh_milk_sent_quantity,apprentice_order_flag,promotional_order_flag
        FROM cxr_user_order t
        WHERE t.customer_id= #{customerId}
        <if test="orderId != null">
            AND t.id != #{orderId}
            AND t.create_time &lt; (SELECT create_time FROM cxr_user_order WHERE id = #{orderId} )
        </if>
        AND t.order_type in (0,1,3, 7)
        AND ((t.audit_status = 2 ) OR (t.pay_status = 3))
        and ((t.promotional_order_flag = 1 ) OR (t.apprentice_order_flag = 1))
        AND t.delete_status = '0'
        ORDER By order_date DESC
        LIMIT 1
    </select>

    <select id="queryZeroQuantityRenewal" resultType="java.lang.Long">

        SELECT count(1)
        FROM cxr_user_order t
        WHERE t.customer_id = #{customerId}
          AND t.order_type in (0, 1, 3, 7)
          AND ((t.audit_status = 2) OR (t.pay_status = 3))
          AND t.delete_status = '0'
          and t.zero_quantity_renewal = 1
          and DATE_FORMAT(t.order_date, '%Y-%m-%d') <![CDATA[ > ]]> #{orderDate}

    </select>


    <select id="queryNotContractLastTenDayOrder" resultType="com.ruoyi.order.common.domain.vo.LastUserOrderVo">
        SELECT
        SUM(order_quantity)order_qty,
        SUM(if(pigc_flag = 0 , 0 , order_quantity))order_quantity,
        SUM(if(pigc_flag = 0 , 0 ,fresh_milk_give_quantity))fresh_milk_give_quantity,
        SUM(if(pigc_flag = 0 , 0 ,order_quantity * IF(order_quantity >= 100,10,11)))amount2,
        SUM(if(pigc_flag = 0 , 0 ,amount))amount,
        SUM(if(pigc_flag = 0 , 0 ,long_milk_give_quantity))long_milk_give_quantity
        FROM cxr_user_order t
        WHERE t.customer_id= #{customerId}
        AND t.delete_status = '0'
        AND t.order_type in (0,1,3,7)
        AND ((t.audit_status = 2 ) OR (t.pay_status = 3))
        AND ((t.pay_time >= #{start} AND t.pay_time &lt; #{end}) OR (t.audit_time >= #{start} AND
        t.audit_time &lt;
        #{end}) )
        <if test="orderId != null">
            AND t.id  <![CDATA[<>]]>  #{orderId}
        </if>
    </select>


    <select id="queryNotContractLastTenDayOrderInfo" resultType="com.ruoyi.order.common.domain.vo.LastUserOrderDayInfo">
        SELECT
        t.order_no,t.order_date,t.order_quantity,t.amount,t.fresh_milk_give_quantity,t.long_milk_give_quantity
        FROM cxr_user_order t
        WHERE t.customer_id= #{customerId}
        and t.delete_status= '0'
        AND t.order_type in (0,1,3,7)
        AND ((t.audit_status = 2 ) OR (t.pay_status = 3))
        AND ((t.pay_time >= #{start} AND t.pay_time &lt; #{end}) OR (t.audit_time >= #{start} AND
        t.audit_time &lt;
        #{end}) )
        <if test="orderId != null">
            AND t.id  <![CDATA[<>]]>  #{orderId}
        </if>
    </select>


    <select id="customerOrderStatistics_discard"
            resultType="com.ruoyi.order.api.domain.vo.CustomerOrderStatisticsDTO">
        SELECT SUM(IF(order_type IN (0, 1, 3, 6),
                      IFNULL(order_quantity, 0) + IFNULL(fresh_milk_give_quantity, 0),
                      0))                                                        order_quantity,
               SUM(IF(order_type IN (0, 1, 3, 6), IFNULL(fresh_milk_sent_quantity, 0),
                      0))                                                        fresh_milk_sent_quantity,
               SUM(IF(order_type IN (2), IFNULL(fresh_milk_return_quantity, 0),
                      0))                                                        fresh_milk_return_quantity,
               SUM(IFNULL(milk_exchange_sum, 0))                                 milk_exchange_sum,
               (SELECT SUM(IFNULL(conversion_quantity, 0))
                FROM cxr_user_order
                WHERE order_type = 4
                  AND ((terminal_type = 2 AND pay_status = 3) OR
                       (terminal_type = 1 AND audit_status = 2))
                  AND customer_phone_switch =
                      (SELECT phone FROM cxr_customer WHERE id = #{customerId})) tranInMilk,
               (SELECT SUM(IFNULL(conversion_quantity, 0))
                FROM cxr_user_order
                WHERE order_type = 4
                  AND ((terminal_type = 2 AND pay_status = 3) OR
                       (terminal_type = 1 AND audit_status = 2))
                  AND customer_phone =
                      (SELECT phone FROM cxr_customer WHERE id = #{customerId})) tranOutMilk,
               (SELECT SUM(IFNULL(milk_distribution_total, 0))
                FROM cxr_road_way
                WHERE distribution_status = 1
                  AND cxr_customer_id = #{customerId})                           freshMilkRoadWaySentTotal
        FROM cxr_user_order
        WHERE ((terminal_type = 2 AND pay_status = 3) OR (terminal_type = 1 AND audit_status = 2) OR
               (order_type in (2, 5) AND terminal_type IN (1, 2, 3) AND audit_status = 2))
          AND customer_id = #{customerId}
    </select>

    <select id="customerOrderStatistics"
            resultType="com.ruoyi.order.api.domain.vo.CustomerOrderStatisticsDTO">
        SELECT SUM(IF(order_type IN (0, 1, 3, 6),
                      IFNULL(order_quantity, 0) + IFNULL(fresh_milk_give_quantity, 0),
                      0))                                                        order_quantity,
               SUM(IF(order_type IN (0, 1, 3, 6), IFNULL(fresh_milk_sent_quantity, 0),
                      0))                                                        fresh_milk_sent_quantity,
               SUM(IF(order_type IN (2), IFNULL(fresh_milk_return_quantity, 0),
                      0))                                                        fresh_milk_return_quantity,
               SUM(IFNULL(milk_exchange_sum, 0))                                 milk_exchange_sum,
               (SELECT SUM(IFNULL(conversion_quantity, 0))
                FROM cxr_user_order
                WHERE order_type = 4
                  AND ((terminal_type = 2 AND pay_status = 3) OR
                       (terminal_type = 1 AND audit_status = 2))
                  AND customer_phone_switch =
                      (SELECT phone FROM cxr_customer WHERE id = #{customerId})) tranInMilk,
               (SELECT SUM(IFNULL(conversion_quantity, 0))
                FROM cxr_user_order
                WHERE order_type = 4
                  AND ((terminal_type = 2 AND pay_status = 3) OR
                       (terminal_type = 1 AND audit_status = 2))
                  AND customer_phone =
                      (SELECT phone FROM cxr_customer WHERE id = #{customerId})) tranOutMilk,
               (SELECT IFNULL(sum(total_sum), 0)
                FROM cxr_customer_distribution_list_record
                WHERE cxr_customer_id =
                      #{customerId})                                             freshMilkRoadWaySentTotal
        FROM cxr_user_order
        WHERE ((terminal_type IN (1, 2, 3, 4, 7) AND pay_status = 3)
            OR (terminal_type = 1 AND audit_status = 2)
            OR
               (order_type in (2, 5) AND terminal_type IN (1, 2, 3, 4, 7) AND audit_status = 2))
          AND customer_id = #{customerId}
    </select>

    <select id="userOrderAndReturnCount" resultType="java.lang.Long">
        SELECT COUNT(*) FROM (<include refid="userOrderAndReturnSql"></include>)f
    </select>

    <select id="userOrderAndReturn" resultMap="CxrUserReturnOrderVOResult">
        <include refid="userOrderAndReturnSql"></include>
        ORDER BY tt.id DESC
        LIMIT 100
    </select>

    <sql id="userOrderAndReturnSql">
        select tt.*,
        t2.total_quantity AS returnOrderQuantity,
        t2.fresh_milk_give_quantity AS returnFreshMilkGiveQuantity,
        t2.fresh_milk_sent_quantity AS returnFreshMilkSentQuantity,
        t2.fresh_milk_rest_quantity AS freshMilkRestQuantity,
        t2.long_milk_give_quantity AS returnLongMilkGiveQuantity,
        t2.long_milk_sent_quantity AS returnLongMilkSentQuantity,
        t2.long_milk_rest_quantity AS longMilkRestQuantity,
        t2.long_milk_sent_amount AS longMilkSentAmount,
        t2.long_milk_cancel_quantity AS longMilkCancelQuantity,
        t2.fresh_milk_refund_quantity AS freshMilkRefundQuantity,
        t2.fresh_milk_cancel_quantity AS freshMilkCancelQuantity,
        t2.fresh_milk_cancel_rest_quantity AS freshMilkCancelRestQuantity,
        t2.rebates,
        t2.refund_amount AS refundAmount,
        t2.amount AS returnAmount,
        t2.bank_name,
        t2.bank_account_number,
        t2.bank_account_name,
        t2.rr_code,
        t2.order_money,
        ( select t1.name from cxr_sale_product t
        left join cxr_product_form t1 on t.cxr_product_form_id=t1.id
        where t.name=tt.exchange_product_name
        )as fromProductType
        from cxr_user_order tt
        LEFT JOIN cxr_user_return_order t2 ON t2.user_order_id = tt.id
        inner join (
        select id from cxr_user_order t
        <where>

            <if test="cxrUserOrderBo.companyId !=null">
                and t.company_id = #{cxrUserOrderBo.companyId}
            </if>

            <if test="cxrUserOrderBo.bigAreaId !=null">
                AND t.site_id IN (SELECT id FROM cxr_site WHERE cxr_root_region_id = #{cxrUserOrderBo.bigAreaId})
            </if>

            <if test="cxrUserOrderBo.siteId !=null">
                and t.site_id= #{cxrUserOrderBo.siteId}
            </if>

            <if test="cxrUserOrderBo.proxyIds !=null and cxrUserOrderBo.proxyIds.size() >0">
                and JSON_OVERLAPS(t.business_agent -> '$[*].proxyId',CAST(
                #{cxrUserOrderBo.proxyIdsJson,jdbcType=VARCHAR} AS JSON ))
            </if>

            <if test="cxrUserOrderBo.promotionalOrderFlag">
                and t.promotional_order_flag = true
            </if>

            <if test="cxrUserOrderBo.apprenticeOrderFlag">
                and t.apprentice_order_flag = true
            </if>

            <if test="cxrUserOrderBo.orderType != null ">
                and t.order_type = #{cxrUserOrderBo.orderType}
            </if>

            <if test="cxrUserOrderBo.paymentSouce != null ">
                and t.payment_souce = #{cxrUserOrderBo.paymentSouce}
            </if>

            <if test="cxrUserOrderBo.customerPhone != null ">
                and t.customer_phone like concat(#{cxrUserOrderBo.customerPhone},'%')
            </if>

            <if test="cxrUserOrderBo.customerPhoneSwitch != null ">
                and t.customer_phone_switch like
                concat('%',#{cxrUserOrderBo.customerPhoneSwitch},'%')
            </if>


            <if test="cxrUserOrderBo.auditStatus != null ">

                <if test="cxrUserOrderBo.auditStatus ==1 || cxrUserOrderBo.auditStatus ==2 ">
                    and t.audit_status = #{cxrUserOrderBo.auditStatus}
                </if>
                <if test="cxrUserOrderBo.auditStatus ==3 ">
                    and t.pay_status = #{cxrUserOrderBo.auditStatus}
                </if>

                <if test="cxrUserOrderBo.auditStatus ==4 ">
                    and t.perfect_status = 1
                </if>
                <if test="cxrUserOrderBo.auditStatus ==5 ">
                    and t.pay_status = 1
                </if>
            </if>

            <if test="cxrUserOrderBo.customerAdress != null ">
                and t.customer_adress like concat('%',#{cxrUserOrderBo.customerAdress},'%')
            </if>

            <if test="cxrUserOrderBo.orderNo != null ">
                and t.order_no like concat('%',#{cxrUserOrderBo.orderNo},'%')
            </if>

            <if test="cxrUserOrderBo.merchantOrderNo != null ">
                and t.merchant_order_no like concat(#{cxrUserOrderBo.merchantOrderNo},'%')
            </if>
            and t.order_date &gt;= #{cxrUserOrderBo.startDateTime} and t.order_date &lt;=
            #{cxrUserOrderBo.endDateTime}
            and t.delete_status = 0

            <if test=" cxrUserOrderBo.auditStatus !=5  ">
                and <![CDATA[t.pay_status <> '1' ]]>
                and <![CDATA[t.pay_status <> '5' ]]>
            </if>
            <if test="cxrUserOrderBo.id != null">
                AND t.id &lt; #{cxrUserOrderBo.id}
            </if>

            <if test="cxrUserOrderBo.terminalType !=null">
                and t.terminal_type= #{cxrUserOrderBo.terminalType}
            </if>
            <if test="cxrUserOrderBo.paymentStatus!=null">
                and t.payment_status=#{cxrUserOrderBo.paymentStatus}
            </if>
        </where>
        ) tt2 on tt2.id = tt.id
    </sql>
    <select id="sumGiveFreshMilkNumberBySite"
            resultType="com.ruoyi.business.base.api.domain.dto.DailyIntergerDto">
        SELECT sum(x.fresh_milk_give_quantity) as quantity,sum(x.amount)as money
        ,sum(x.long_milk_give_quantity) as
        LongmilkQuantity, x.cxrSiteId as cxrSiteId
        from
        (select t.fresh_milk_give_quantity,t.amount,t.long_milk_give_quantity ,t.site_id as
        cxrSiteId
        from cxr_user_order t
        <where>
            t.terminal_type=#{dis}
            and t.pay_status=#{pay}
            and date_format(t.order_date , '%Y-%m-%d')=#{now}
            and site_id=#{siteId}
            and t.order_type in
            <foreach collection="types" separator="," item="type" close=")" open="(">
                #{type}
            </foreach>
        </where>
        union all
        select z.fresh_milk_give_quantity,z.amount,z.long_milk_give_quantity ,z.site_id as cxrSiteId
        from cxr_user_order z
        <where>
            z.terminal_type=#{man}
            and z.audit_status=#{audit}
            and date_format(z.order_date , '%Y-%m-%d')=#{now}
            and site_id=#{siteId}
            and z.order_type in
            <foreach collection="types" separator="," item="type" close=")" open="(">
                #{type}
            </foreach>
        </where>
        ) as x
        group by cxrSiteId

    </select>
    <select id="countNewOrderManager" resultType="java.lang.String">
        select cast(z.business_agent->'$[*].proxyId' as json)
        from cxr_user_order z
        <where>
            z.order_type=#{orderType}
            and z.terminal_type=#{man}
            and z.audit_status=#{audit}
            and date_format(z.order_date , '%Y-%m-%d')=#{now}
            and JSON_OVERLAPS(z.business_agent->'$[*].proxyId',cast(#{id} as json))
            and is_import is null
        </where>
    </select>
    <select id="countNewOrderDisribution" resultType="java.lang.String">
        select cast(t.business_agent->'$[*].proxyId' as json)
        from cxr_user_order t
        <where>
            t.order_type=#{orderType}
            and t.terminal_type=#{dis}
            and t.pay_status=#{paYstatus}
            and date_format(t.order_date , '%Y-%m-%d')=#{now}
            and JSON_OVERLAPS(t.business_agent->'$[*].proxyId',cast(#{id} as json))
            and is_import is null
        </where>
    </select>

    <select id="countMergePortNewOrder" resultType="java.lang.String">
        select cast(t.business_agent->'$[*].proxyId' as json)
        from cxr_user_order t
        <where>
            t.order_type=#{orderType}
            and date_format(t.order_date , '%Y-%m-%d')=#{now}
            and JSON_OVERLAPS(t.business_agent->'$[*].proxyId',cast(#{id} as json))
            and (t.pay_status=3 or t.audit_status=2)
            and delete_status=0
            and is_import is null
        </where>
    </select>

    <select id="countMergePortByContractOrderNewOrder" resultType="java.math.BigDecimal">
        select ifnull(sum(JSON_LENGTH( t.business_agent ) / 1
        ),0) as totalOrderQuantity

        from cxr_user_order t
        <where>
            t.order_type=#{orderType}
            and t.contract_type_tag=0
            and date_format(t.order_date , '%Y-%m-%d')=#{now}
            and JSON_OVERLAPS(t.business_agent->'$[*].proxyId',cast(#{id} as json))
            and (t.pay_status=3 or t.audit_status=2)
            and delete_status=0
            and is_import is null
        </where>
    </select>

    <select id="queryCustomerOrder" resultMap="CxrUserOrderResult">
        select id, create_time, order_type, delete_status, customer_id, pay_status, customer_phone
        from cxr_user_order
        where create_time >= #{currentDate}
          AND create_time &lt; #{endTime}
          and (order_type = '0' or order_type = '7')
          and delete_status = '0'
          and pay_status = '3'
    </select>

    <select id="selectListByTime" resultMap="CxrUserOrderResultVo">
        SELECT id,
               company_id,
               site_name,
               site_id,
               business_agent,
               payment_souce,
               order_type,
               zero_quantity_renewal,
               conversion_type,
               order_no,
               order_quantity,
               fresh_milk_give_quantity,
               long_milk_give_quantity,
               excess_quantity,
               fresh_milk_sent_quantity,
               long_milk_sent_quantity,
               surplus_quantity,
               conversion_quantity,
               unit_price,
               amount,
               credit_card_amount,
               order_date,
               audit_status,
               audit_time,
               terminal_type,
               fresh_milk_return_quantity,
               pay_status,
               perfect_status,
               contract_order_ext,
               pay_time,
               contract_total_amount,
               contract_total_order_quantity,
               exchange_sum,
               milk_exchange_sum,
               exchange_product_name,
               is_import,
               customer_id,
               new_customer_flag,
               apportion_money,
               contract_type_tag,
               revision
        FROM cxr_user_order
        WHERE (pay_status = 3 OR audit_status = 2)
          AND order_type IN (0, 1, 3, 6, 7)
          AND order_date >= #{start}
          AND order_date &lt; #{end}
          AND delete_status = '0'
          AND is_import IS NULL
    </select>
    <select id="queryShouldSendNumber" resultType="java.lang.String">
        select customer_phone
        from (select customer_phone
              from cxr_user_order a
              where a.audit_time between concat(#{now}, ' 00:00:00') and concat(#{now}, ' 23:59:59')
                and a.audit_status = #{audit}
                and a.is_import is null
              union
              select customer_phone
              from cxr_user_order aa
              where aa.pay_time between concat(#{now}, ' 00:00:00') and concat(#{now}, ' 23:59:59')
                and aa.pay_status = #{pay}
                and aa.is_import is null
              union
              select customer_phone_switch as customer_phone
              from cxr_user_order aaa
              where aaa.audit_time between concat(#{now}, ' 00:00:00') and concat(#{now}, ' 23:59:59')
                and aaa.order_type = #{type}
                and aaa.audit_status = #{audit}
                and aaa.is_import is null
              union
              select phone as customer_phone
              from cxr_customer x
              where x.id in (select customer_id
                             from cxr_customer_exchange aaaa
                             where DATE_FORMAT(aaaa.audit_time, '%Y-%m-%d') = #{now}
                               and aaaa.delete_status = #{del}
                               and aaaa.audit_status = #{audit})) t
    </select>

    <select id="getDaySumQty" resultType="Integer">
        SELECT SUM(order_quantity) daySumQty
        FROM cxr_user_order
        WHERE (pay_status = 3 OR audit_status = 2)
          AND order_type IN (0, 1, 3, 7)
          AND customer_id = #{customerId}
          AND ((pay_time >= #{start} AND pay_time &lt; #{end}) OR
               (audit_time >= #{start} AND audit_time &lt; #{end}))
          AND delete_status = '0'
          AND is_import IS NULL
    </select>
    <select id="queryById" resultType="com.ruoyi.order.common.entity.CxrUserOrder">
        select company_id,
               company_name,
               big_area_id,
               big_area_name,
               province,
               city,
               area,
               site_name,
               site_adress,
               site_id,
               order_type,
               conversion_quantity,
               terminal_type
        from cxr_user_order
        where id = #{OrderId}

    </select>
    <select id="queryOrderIdByOrderNo" resultType="java.lang.Long">
        select id
        from cxr_user_order
        where order_no = #{orderNo}
    </select>
    <select id="queryOrderDateByOrderNo" resultType="java.util.Date">
        select order_date
        from cxr_user_order
        where order_no = #{orderNo}
          and delete_status = 0
    </select>

    <!-- 退订单是否需要排除后面确认，还有申领了的要排除金额 等字段确认了后面加 -->
    <select id="selectActivitySumAmount" resultType="java.math.BigDecimal">
        SELECT SUM(amount)
        FROM cxr_user_order
        WHERE (pay_status = '3' OR audit_status = '2')
          AND customer_id = #{customerId}
          AND activity_id = #{activityId}
          AND delete_status = '0'
    </select>

    <select id="selectRecentlyOrderDate" resultType="com.ruoyi.order.api.domain.vo.CustomerOrderRecentlyDate">
        SELECT customer_id, MAX(order_date) AS orderDate
        FROM cxr_user_order
        where
        customer_id in
        <foreach collection="cxrCustomerIds" separator="," index="i" open="(" close=")" item="item">
            #{item}
        </foreach>
        and delete_status=0
        GROUP BY customer_id
    </select>

    <select id="selectRecentlyOrderDateByCustomerId"
            resultType="com.ruoyi.order.api.domain.vo.CustomerOrderRecentlyDate">
        SELECT customer_id, MAX(order_date) AS orderDate
        FROM cxr_user_order
        where customer_id = #{customerId}
          and delete_status = 0

    </select>

    <update id="reverseAuditUpdateById">
        UPDATE cxr_user_order
        SET audit_status  = #{bo.auditStatus},
            audit_time    = NULL,
            audit_by      = NULL,
            audit_by_name = NULL
        WHERE id = #{bo.id}
    </update>

    <select id="queryUserOrderByCustomerId" resultType="com.ruoyi.order.common.entity.CxrUserOrder">
        SELECT tt.order_no,
               tt.order_quantity,
               tt.amount,
               tt.fresh_milk_give_quantity,
               tt.conversion_quantity,
               tt.order_type,
               tt.seq orderDate
        FROM ((SELECT t.order_no,
                      t.order_quantity,
                      t.amount,
                      t.fresh_milk_give_quantity,
                      t.conversion_quantity,
                      t.order_type,
                      t.audit_time seq
               FROM cxr_user_order t
               WHERE t.delete_status = 0
                 AND t.customer_id = #{customerId}
                 AND t.order_type in (0, 1, 3, 7)
                 AND t.terminal_type = 1 -- 1=后台录入
                 AND t.audit_status = 2  -- 2 已审核
               ORDER BY t.audit_time DESC)
              UNION ALL
              (SELECT t.order_no,
                      t.order_quantity,
                      t.amount,
                      t.fresh_milk_give_quantity,
                      t.conversion_quantity,
                      t.order_type,
                      t.pay_time seq
               FROM cxr_user_order t
               WHERE t.delete_status = 0
                 AND t.customer_id = #{customerId}
                 AND t.order_type in (0, 1, 3, 7)
                 AND t.terminal_type in (2, 4) -- 2=配送端录入,4=小程序
                 AND t.pay_status = 3          -- 3.支付成功
               ORDER BY t.pay_time DESC)
              UNION ALL
              (SELECT t.order_no,
                      t.order_quantity,
                      t.amount,
                      t.fresh_milk_give_quantity,
                      t.conversion_quantity,
                      t.order_type,
                      t.pay_time seq
               FROM cxr_user_order t
               WHERE t.delete_status = 0
                 AND t.customer_id = #{customerId}
                 AND t.order_type = 6   -- 6赠送单
                 AND t.audit_status = 2 -- 2 已审核
               ORDER BY t.audit_time DESC)
              UNION ALL
              (SELECT t.order_no,
                      t.order_quantity,
                      t.amount,
                      t.fresh_milk_give_quantity,
                      t.conversion_quantity,
                      t.order_type,
                      t.pay_time seq
               FROM cxr_user_order t
               WHERE t.delete_status = 0
                 AND t.customer_id_switch = #{customerId}
                 AND t.order_type = 4   -- 4转单
                 AND t.audit_status = 2 -- 2 已审核
               ORDER BY t.audit_time DESC)) tt
        ORDER BY tt.seq DESC
    </select>
    <select id="returenDailyPerformanceDtos"
            resultType="com.ruoyi.business.base.api.domain.dto.DailyPerformanceDto">
        SELECT e.name cxrEmployeeName,e.job_number,a.achievement_value,a.souce_id,boxs
        ,fresh_milk_sent_quantity,long_milk_give_quantity,fresh_milk_give_quantity
        FROM cxr_employee_achievement_detail a
        LEFT JOIN cxr_employee e ON a.employee_id = e.id
        WHERE a.employee_id != #{userId} AND a.delete_status = '0'
        AND a.souce_id in
        <foreach collection="returnOrderIds" item="value" separator="," open="(" close=")">
            #{value}
        </foreach>
        and a.site_id in
        <foreach collection="returnSiteIds" item="value" separator="," open="(" close=")">
            #{value}
        </foreach>
    </select>

    <select id="countValidNewOrder" resultType="java.lang.String">
        select x.quantity
        from
        (select JSON_EXTRACT(any_value(t.business_agent), '$[*].proxyId') AS quantity
        from cxr_user_order t
        <where>
            t.order_type in
            <foreach collection="types" separator="," item="i" open="(" close=")">
                #{i}
            </foreach>
            and (t.pay_status=3 or t.audit_status=2)
            and date_format(t.order_date , '%Y-%m-%d')=#{now}
            and t.amount>=330
            and t.order_quantity>=30
            and json_contains(t.business_agent->'$[*].proxyId',cast(#{id} as json ))
            and delete_status=0
            and is_import is null
            GROUP BY SUBSTRING_INDEX(order_no, '-', 1)
        </where>
        ) as x
    </select>


    <select id="TotalOrderStatisticsUpgrade" resultType="com.ruoyi.order.api.domain.vo.CustomerOrderTotal">
        SELECT


        ifnull(sum(CASE WHEN order_type != 2 THEN
        IF(proxyIndex &lt; size ,
        freshMilkSentQuantityTag,
        fresh_milk_sent_quantity - freshMilkSentQuantityTag * ( proxyIndex - 1 ))
        ELSE 0 END),0) as totalFreshMilkSentQuantity,

        ifnull(sum(
        IF(proxyIndex &lt; size,
        excessQuantityTag,
        excess_quantity - excessQuantityTag * ( proxyIndex - 1 ))),0) as totalExcessQuantity,

        ifnull(sum(CASE WHEN order_type != 2 THEN
        IF(proxyIndex &lt; size,
        longMilkGiveQuantityTag,
        long_milk_give_quantity - longMilkGiveQuantityTag * ( proxyIndex - 1 ))
        ELSE
        - abs(
        IF(proxyIndex &lt; size,
        longMilkGiveQuantityTag,
        long_milk_give_quantity - longMilkGiveQuantityTag * ( proxyIndex - 1 ))
        )
        END),0) as totalLongMilkGiveQuantity,

        <!--        <choose>-->
        <!--            <when test="bo.proxyIds ==null and bo.proxyIds ==null and bo.siteName ==null">-->
        <!--                ifnull(-->
        <!--                sum(-->
        <!--                CASE-->

        <!--                WHEN order_type != 2 THEN-->
        <!--                ( order_quantity + fresh_milk_give_quantity ) ELSE 0-->
        <!--                END-->
        <!--                ),-->
        <!--                0-->
        <!--                ) AS totalOrderQuantity,-->
        <!--            </when>-->
        <!--            <otherwise>-->
        <!--                ifnull(sum(CASE WHEN order_type != 2 THEN-->
        <!--                IF(proxyIndex &lt;size,-->
        <!--                orderQuantityTag,-->
        <!--                order_quantity - orderQuantityTag * ( proxyIndex - 1 ))-->
        <!--                +-->
        <!--                IF ( proxyIndex &lt; size, freshMilkGiveQuantityTag,-->
        <!--                fresh_milk_give_quantity - freshMilkGiveQuantityTag * ( proxyIndex - 1 ))-->
        <!--                ELSE 0 END),0) as totalOrderQuantity,-->
        <!--            </otherwise>-->
        <!--        </choose>-->

        <!--  第二版
              ifnull(sum(CASE WHEN order_type != 2 THEN
              CASE WHEN integer_par=0 THEN

              IF(proxyIndex &lt;size,
              orderQuantityTag,
              order_quantity - orderQuantityTag * ( proxyIndex - 1 ))
              +
              IF ( proxyIndex &lt; size, freshMilkGiveQuantityTag,
              fresh_milk_give_quantity - freshMilkGiveQuantityTag * ( proxyIndex - 1 ))

              ELSE
              IF
              (
              proxyIndex &lt; size ,
              last_order_quantity_split,
              order_quantity - last_order_quantity_split * ( proxyIndex - 1 ))
              +
              IF
              (
              proxyIndex &lt; size,
              freshMilkGiveQuantityTag,
              fresh_milk_give_quantity - freshMilkGiveQuantityTag * ( proxyIndex - 1 ))
              END
              ELSE 0 END),0) as totalOrderQuantity,-->
        <!--  第三版 -->
        ifnull(sum(CASE
        WHEN order_type != 2 THEN
        IF
        ( proxyIndex &lt;= ( order_quantity - quantityAvg * size )/ 0.01, quantityAvg + 0.01, quantityAvg ) +
        IF
        (
        proxyIndex &lt; size,
        freshMilkGiveQuantityTag,
        fresh_milk_give_quantity - freshMilkGiveQuantityTag * ( proxyIndex - 1 ))

        ELSE 0
        END)

        ,
        0
        ) as totalOrderQuantity,

        ifnull(sum(CASE WHEN order_type != 2 THEN
        IF(proxyIndex &lt; size,
        freshMilkGiveQuantityTag,
        fresh_milk_give_quantity - freshMilkGiveQuantityTag * ( proxyIndex - 1 ))
        ELSE 0 END),0) as totalFreshMilkGiveQuantity,


        ifnull(sum( case when order_type=2 then abs(order_quantity) else 0 end ),0) as
        returnTotalQuantity,

        ifnull(sum(CASE WHEN order_type = 2 THEN
        abs(

        <!--  IF(proxyIndex &lt; size,-->
        <!--  freshMilkGiveQuantityTag,-->
        <!--  fresh_milk_give_quantity - freshMilkGiveQuantityTag * ( proxyIndex - 1 )) +-->

        IF(proxyIndex &lt; size,
        freshMilkReturnQuantityTag,
        fresh_milk_return_quantity - freshMilkReturnQuantityTag * ( proxyIndex - 1 ))

        ) ELSE 0 END),0) as totalFreshMilkReturnQuantityVo,


        ifnull(sum(CASE WHEN order_type != 2 THEN
        IF(proxyIndex &lt; size,
        orderQuantityTag,
        order_quantity - orderQuantityTag * ( proxyIndex - 1 ))
        ELSE
        -abs(IF(proxyIndex &lt; size,
        freshMilkReturnQuantityTag,
        fresh_milk_return_quantity - freshMilkReturnQuantityTag * ( proxyIndex - 1 ))
        )
        END),0) as totalDesignQuantity,


        ifnull(sum(CASE WHEN order_type != 2 THEN
        IF(proxyIndex &lt; size,
        amountTag,
        amount - amountTag * ( proxyIndex - 1 ))
        ELSE 0 END),0) as totalAmount,

        ifnull(
        sum( CASE WHEN order_type != 2 THEN IF ( proxyIndex &lt; size, freshMilkGiveQuantityTag,
        fresh_milk_give_quantity - freshMilkGiveQuantityTag * ( proxyIndex - 1 )) ELSE 0 END ),
        0
        ) AS totaFfreshMilkGiveQuantity,


        ifnull( sum( (case when size>1 and order_type = 2 then
        IF(proxyIndex &lt; size,
        amountTag,
        amount - amountTag * ( proxyIndex - 1 ))
        when order_type = 2 then
        ifnull(abs(amount),0) end ) ),0) AS returnTotalAmount,


        ifnull(sum(unit_price),0) as TotalUnitPriceAmount,
        ifnull( count(id), 0 ) AS splitTotal

        FROM
        (
        SELECT
        a.id,
        proxyId,
        regionId,
        siteName,
        a.order_type,
        a.amount,
        a.fresh_milk_sent_quantity,
        a.excess_quantity,
        a.long_milk_give_quantity,
        a.fresh_milk_give_quantity,
        a.order_quantity,
        a.fresh_milk_return_quantity,
        a.business_agent,
        a.unit_price,
        JSON_LENGTH( business_agent ) size,
        ROUND( amount / JSON_LENGTH( business_agent ), 2 ) amountTag,
        ROUND( fresh_milk_sent_quantity / JSON_LENGTH( business_agent ), 2 ) freshMilkSentQuantityTag,
        ROUND( excess_quantity / JSON_LENGTH( business_agent ), 2 ) excessQuantityTag,
        ROUND( long_milk_give_quantity / JSON_LENGTH( business_agent ), 2 ) longMilkGiveQuantityTag,
        ROUND( fresh_milk_give_quantity / JSON_LENGTH( business_agent ), 2 ) freshMilkGiveQuantityTag,
        ROUND( order_quantity / JSON_LENGTH( business_agent ), 2 ) orderQuantityTag,
        ROUND( fresh_milk_return_quantity / JSON_LENGTH( business_agent ), 2 ) freshMilkReturnQuantityTag,
        (order_quantity - ROUND(order_quantity / JSON_LENGTH( business_agent ), 2) * (JSON_LENGTH( business_agent ) -
        1)) AS last_order_quantity_split,
        ROUND( order_quantity / JSON_LENGTH( business_agent ), 2 ) - FLOOR(ROUND( order_quantity / JSON_LENGTH(
        business_agent ), 2 )) &lt;0.51 integer_par,
        TRUNCATE ( order_quantity / JSON_LENGTH( business_agent ), 2 ) quantityAvg,
        proxyIndex
        FROM
        cxr_user_order a,
        JSON_TABLE (
        a.business_agent,
        '$[*]' COLUMNS ( proxyIndex FOR ORDINALITY, proxyId BIGINT PATH '$.proxyId',
        regionId BIGINT PATH '$.regionId',siteName VARCHAR(64) path '$.siteName' )) AS b
        WHERE
        1=1
        <include refid="TotalOrderStatisticsUpgradeWhere"/>

    </select>

    <select id="queryFirstDayOrder" resultMap="CxrUserOrderResult">

        SELECT
        t.id,IFNULL(t.pay_time,IFNULL(t.audit_time,t.order_date))order_date,business_agent,customer_id,order_quantity,order_type,fresh_milk_give_quantity,fresh_milk_sent_quantity,apprentice_order_flag,promotional_order_flag
        FROM cxr_user_order t
        WHERE t.customer_id= #{customerId}
        <if test="orderId != null">
            AND t.id != #{orderId}
            AND t.create_time &lt; (SELECT create_time FROM cxr_user_order WHERE id = #{orderId} )
        </if>
        AND t.order_type in (0,1,3, 7)
        AND ((t.audit_status = 2 ) OR (t.pay_status = 3))
        and ((t.promotional_order_flag = 1 ) OR (t.apprentice_order_flag = 1))
        AND t.delete_status = '0'
        and date_format(t.order_date , '%Y-%m-%d')=#{date}
        ORDER By order_date ASC
        LIMIT 1
    </select>

    <select id="orderFixationStatistics"
            resultType="com.ruoyi.order.api.domain.vo.CustomerOrderStatisticsVo">
        select t.*,
        ifnull( CASE WHEN t.order_type = 2 THEN t.fresh_milk_return_quantity
        ELSE (t.order_quantity+fresh_milk_give_quantity) - fresh_milk_sent_quantity END
        ,0) as orderQuantity,
        ifnull( CASE WHEN t.order_type = 2 THEN t.fresh_milk_return_quantity
        ELSE
        t.order_quantity END ,0) as designQuantity,
        JSON_LENGTH(t.business_agent) businessAgentSize
        ,t.customer_info_list->>'$[0].siteName'AS deliverySite
        ,JSON_EXTRACT(t.customer_info_list, '$[0].siteId') AS deliverySiteId
        from cxr_user_order_mirror_image t
        where
        1=1
        <include refid="orderStatisticsWhere"/>
        and t.is_import is null
        and (t.pay_status=3 or t.audit_status=2)
        and t.delete_status=0
        <if test="bo.mirrorImageTime != null">
            and t.mirror_image_time=#{bo.mirrorImageTime}
        </if>
        order by t.order_date desc

    </select>


    <select id="TotalOrderFixationStatisticsUpgrade" resultType="com.ruoyi.order.api.domain.vo.CustomerOrderTotal">
        SELECT
        ifnull(sum(CASE WHEN order_type != 2 THEN
        IF(proxyIndex &lt; size ,
        freshMilkSentQuantityTag,
        fresh_milk_sent_quantity - freshMilkSentQuantityTag * ( proxyIndex - 1 ))
        ELSE 0 END),0) as totalFreshMilkSentQuantity,

        ifnull(sum(
        IF(proxyIndex &lt; size,
        excessQuantityTag,
        excess_quantity - excessQuantityTag * ( proxyIndex - 1 ))),0) as totalExcessQuantity,

        ifnull(sum(CASE WHEN order_type != 2 THEN
        IF(proxyIndex &lt; size,
        longMilkGiveQuantityTag,
        long_milk_give_quantity - longMilkGiveQuantityTag * ( proxyIndex - 1 ))
        ELSE
        - abs(
        IF(proxyIndex &lt; size,
        longMilkGiveQuantityTag,
        long_milk_give_quantity - longMilkGiveQuantityTag * ( proxyIndex - 1 ))
        )
        END),0) as totalLongMilkGiveQuantity,


        ifnull(sum(CASE
        WHEN order_type != 2 THEN
        IF
        ( proxyIndex &lt;= ( order_quantity - quantityAvg * size )/ 0.01, quantityAvg + 0.01, quantityAvg ) +
        IF
        (
        proxyIndex &lt; size,
        freshMilkGiveQuantityTag,
        fresh_milk_give_quantity - freshMilkGiveQuantityTag * ( proxyIndex - 1 ))

        ELSE 0
        END)

        ,
        0
        ) as totalOrderQuantity,

        ifnull(sum(CASE WHEN order_type != 2 THEN
        IF(proxyIndex &lt; size,
        freshMilkGiveQuantityTag,
        fresh_milk_give_quantity - freshMilkGiveQuantityTag * ( proxyIndex - 1 ))
        ELSE 0 END),0) as totalFreshMilkGiveQuantity,


        ifnull(sum( case when order_type=2 then abs(order_quantity) else 0 end ),0) as
        returnTotalQuantity,

        ifnull(sum(CASE WHEN order_type = 2 THEN
        abs(


        IF(proxyIndex &lt; size,
        freshMilkReturnQuantityTag,
        fresh_milk_return_quantity - freshMilkReturnQuantityTag * ( proxyIndex - 1 ))

        ) ELSE 0 END),0) as totalFreshMilkReturnQuantityVo,


        ifnull(sum(CASE WHEN order_type != 2 THEN
        IF(proxyIndex &lt; size,
        orderQuantityTag,
        order_quantity - orderQuantityTag * ( proxyIndex - 1 ))
        ELSE
        -abs(IF(proxyIndex &lt; size,
        freshMilkReturnQuantityTag,
        fresh_milk_return_quantity - freshMilkReturnQuantityTag * ( proxyIndex - 1 ))
        )
        END),0) as totalDesignQuantity,


        ifnull(sum(CASE WHEN order_type != 2 THEN
        IF(proxyIndex &lt; size,
        amountTag,
        amount - amountTag * ( proxyIndex - 1 ))
        ELSE 0 END),0) as totalAmount,

        ifnull(
        sum( CASE WHEN order_type != 2 THEN IF ( proxyIndex &lt; size, freshMilkGiveQuantityTag,
        fresh_milk_give_quantity - freshMilkGiveQuantityTag * ( proxyIndex - 1 )) ELSE 0 END ),
        0
        ) AS totaFfreshMilkGiveQuantity,


        ifnull( sum( (case when size>1 and order_type = 2 then
        IF(proxyIndex &lt; size,
        amountTag,
        amount - amountTag * ( proxyIndex - 1 ))
        when order_type = 2 then
        ifnull(abs(amount),0) end ) ),0) AS returnTotalAmount,


        ifnull(sum(unit_price),0) as TotalUnitPriceAmount,
        ifnull( count(id), 0 ) AS splitTotal

        FROM
        (
        SELECT
        a.id,
        proxyId,
        regionId,
        siteName,
        a.order_type,
        a.amount,
        a.fresh_milk_sent_quantity,
        a.excess_quantity,
        a.long_milk_give_quantity,
        a.mirror_image_time,
        a.fresh_milk_give_quantity,
        a.order_quantity,
        a.fresh_milk_return_quantity,
        a.business_agent,
        a.unit_price,
        JSON_LENGTH( business_agent ) size,
        ROUND( amount / JSON_LENGTH( business_agent ), 2 ) amountTag,
        ROUND( fresh_milk_sent_quantity / JSON_LENGTH( business_agent ), 2 ) freshMilkSentQuantityTag,
        ROUND( excess_quantity / JSON_LENGTH( business_agent ), 2 ) excessQuantityTag,
        ROUND( long_milk_give_quantity / JSON_LENGTH( business_agent ), 2 ) longMilkGiveQuantityTag,
        ROUND( fresh_milk_give_quantity / JSON_LENGTH( business_agent ), 2 ) freshMilkGiveQuantityTag,
        ROUND( order_quantity / JSON_LENGTH( business_agent ), 2 ) orderQuantityTag,
        ROUND( fresh_milk_return_quantity / JSON_LENGTH( business_agent ), 2 ) freshMilkReturnQuantityTag,
        (order_quantity - ROUND(order_quantity / JSON_LENGTH( business_agent ), 2) * (JSON_LENGTH( business_agent ) -
        1)) AS last_order_quantity_split,
        ROUND( order_quantity / JSON_LENGTH( business_agent ), 2 ) - FLOOR(ROUND( order_quantity / JSON_LENGTH(
        business_agent ), 2 )) &lt;0.51 integer_par,
        TRUNCATE ( order_quantity / JSON_LENGTH( business_agent ), 2 ) quantityAvg,
        proxyIndex
        FROM
        cxr_user_order_mirror_image a,
        JSON_TABLE (
        a.business_agent,
        '$[*]' COLUMNS ( proxyIndex FOR ORDINALITY, proxyId BIGINT PATH '$.proxyId',
        regionId BIGINT PATH '$.regionId',siteName VARCHAR(64) path '$.siteName' )) AS b
        WHERE
        1=1
        <include refid="TotalOrderStatisticsUpgradeWhere"/>
        <if test="bo.mirrorImageTime != null">
            and c.mirror_image_time=#{bo.mirrorImageTime}
        </if>

    </select>

    <sql id="TotalOrderStatisticsUpgradeWhere">
        <if test="bo.startOrderDate != null">
            and a.order_date <![CDATA[ >= ]]> #{bo.startOrderDate}
        </if>

        <if test="bo.endOrderDate != null">
            and a.order_date <![CDATA[ <= ]]> #{bo.endOrderDate}
        </if>

        <!--        <if test="bo.bigAreaName != null and bo.bigAreaName != ''">-->
        <!--            and INSTR(a.big_area_name, #{bo.bigAreaName}) > 0-->
        <!--        </if>-->

        <if test="bo.regionIds !=null and bo.regionIds.size() >0">
            and JSON_OVERLAPS(a.business_agent -> '$[*].regionId',CAST(
            #{bo.regionIdsJson,jdbcType=VARCHAR} AS JSON ))
        </if>

        <if test="bo.employeeName != null">
            and INSTR(a.business_agent, #{bo.employeeName}) > 0
        </if>
        <if test="bo.employeeJobNumber != null">
            and INSTR(a.business_agent, #{bo.employeeJobNumber}) > 0
        </if>

        <if test="bo.orderType != null and bo.orderType != 86 ">
            and a.order_type = #{bo.orderType}
        </if>

        <if test="bo.orderType != null and bo.orderType == 86 ">
            and JSON_LENGTH(a.business_agent)>1
        </if>

        <if test="bo.auditStatus != null and  bo.auditStatus !=''">
            and a.audit_status =#{bo.auditStatus}
        </if>

        <if test="bo.activityId != null and  bo.activityId !=''">
            and a.activity_id =#{bo.activityId}
        </if>

        <if test="bo.companyId != null and  bo.companyId !=''">
            and a.company_id =#{bo.companyId}
        </if>


        <if test="bo.terminalType!=null">
            and a.terminal_type=#{bo.terminalType}
        </if>

        <if test="bo.customerPhone != null ">
            and a.customer_phone like concat(#{bo.customerPhone},'%')
        </if>
        <if test="bo.employeeFlag==true">
            and a.site_id in
            <foreach collection="bo.siteIds" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>

        <if test="bo.customerAddress != null ">
            and a.customer_adress like concat(#{bo.customerAddress},'%')
        </if>

        <if test="bo.accountingType!=null">
            and a.accounting_type=#{bo.accountingType}
        </if>
        <if test="bo.productName!=null and bo.productName!=''">
            and a.product_name like concat ('%',#{bo.productName},'%')
        </if>
        <if test="bo.productTypeName!=null and bo.productTypeName!=''">
            and a.product_type_name like concat ('%',#{bo.productTypeName},'%')
        </if>
        <if test="bo.promotionCommission!=null">
            and a.promotion_commission=#{bo.promotionCommission}
        </if>
        <if test="bo.terminalType!=null">
            and a.terminal_type=#{bo.terminalType}
        </if>
        and a.delete_status=0
        and a.is_import is null
        and (a.pay_status=3 or a.audit_status=2)
        ORDER BY a.order_date desc
        ) c
        where 1=1

        <if test="bo.proxyIds !=null and bo.proxyIds.size() >0">
            and c.proxyId in
            <foreach collection="bo.proxyIds" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>

        <if test="bo.regionIds !=null and bo.regionIds.size() >0">
            and c.regionId in
            <foreach collection="bo.regionIds" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>

        <if test="bo.siteName != null and bo.siteName != ''">
            and c.siteName=#{bo.siteName}
        </if>
    </sql>

    <sql id="orderStatisticsWhere">
        <if test="bo.startOrderDate != null">
            and t.order_date >= #{bo.startOrderDate}
        </if>

        <if test="bo.endOrderDate != null">
            and t.order_date  <![CDATA[ <= ]]> #{bo.endOrderDate}
        </if>

        <if test="bo.siteName != null and bo.siteName != ''">
            and INSTR(t.business_agent, #{bo.siteName}) > 0
        </if>

        <if test="bo.employeeName != null">
            and INSTR(t.business_agent, #{bo.employeeName}) > 0
        </if>
        <if test="bo.employeeJobNumber != null">
            and INSTR(t.business_agent, #{bo.employeeJobNumber}) > 0
        </if>

        <if test="bo.orderType != null and bo.orderType != 86 ">
            and t.order_type = #{bo.orderType}
        </if>

        <if test="bo.orderType != null and bo.orderType == 86 ">
            and JSON_LENGTH(t.business_agent)>1
        </if>

        <if test="bo.auditStatus != null and  bo.auditStatus !=''">
            and t.audit_status =#{bo.auditStatus}
        </if>

        <if test="bo.activityId != null and  bo.activityId !=''">
            and t.activity_id =#{bo.activityId}
        </if>

        <if test="bo.terminalType!=null">
            and t.terminal_type=#{bo.terminalType}
        </if>
        <!--        <if test="bo.bigAreaName != null and bo.bigAreaName != ''">-->
        <!--            and INSTR(t.big_area_name, #{bo.bigAreaName}) > 0-->
        <!--        </if>-->

        <if test="bo.companyId != null and  bo.companyId !=''">
            and t.company_id =#{bo.companyId}
        </if>

        <if test="bo.customerPhone != null ">
            and t.customer_phone like concat(#{bo.customerPhone},'%')
        </if>

        <if test="bo.proxyIds !=null and bo.proxyIds.size() >0">
            and JSON_OVERLAPS(t.business_agent -> '$[*].proxyId',CAST(
            #{bo.proxyIdsJson,jdbcType=VARCHAR} AS JSON ))
        </if>

        <if test="bo.regionIds !=null and bo.regionIds.size() >0">
            and JSON_OVERLAPS(t.business_agent -> '$[*].regionId',CAST(
            #{bo.regionIdsJson,jdbcType=VARCHAR} AS JSON ))
        </if>

        <if test="bo.customerAddress != null ">
            and t.customer_adress like concat(#{bo.customerAddress},'%')
        </if>

        <if test="bo.employeeFlag==true">
            and t.site_id in
            <foreach collection="bo.siteIds" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="bo.accountingType!=null">
            and t.accounting_type=#{bo.accountingType}
        </if>
        <if test="bo.productName!=null and bo.productName!=''">
            and t.product_name like concat ('%',#{bo.productName},'%')
        </if>
        <if test="bo.productTypeName!=null and bo.productTypeName!=''">
            and t.product_type_name like concat ('%',#{bo.productTypeName},'%')
        </if>
        <if test="bo.promotionCommission!=null">
            and t.promotion_commission=#{bo.promotionCommission}
        </if>
        <if test="bo.terminalType!=null">
            and t.terminal_type=#{bo.terminalType}
        </if>
    </sql>

    <select id="queryCustomerSwitch" resultType="Integer">
        SELECT IFNULL(SUM(IFNULL(conversion_quantity,0)),0) FROM cxr_user_order WHERE order_type = '4' AND customer_id_switch = #{customerId} AND delete_status = '0'
    </select>

</mapper>
