package com.ruoyi.order.common.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.cache.RemovalListener;
import com.google.common.cache.RemovalNotification;
import com.ruoyi.business.base.api.dubbo.RemoteSiteService;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.order.common.config.ExcessGiveCheckWhite;
import com.ruoyi.order.common.domain.dto.ExcessQuantityDTO;
import com.ruoyi.order.common.domain.vo.LastUserOrderVo;
import com.ruoyi.order.disribution.common.ConfigValue;
import com.ruoyi.system.api.RemoteSysConfigService;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Description:
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/8/25 20:28
 */
@Slf4j
@Component
public class ExcessQuantityUtil {

    public static final int KDORDERTYPE = 0;
    public static final int CONTRACTORDERTYPE = 1;
    private static ExcessQuantityUtil excessQuantityUtil;
    private static LoadingCache<String, List<ConfigValue>> cache;
    @DubboReference
    private RemoteSysConfigService remoteSysConfigService;
    @DubboReference
    private RemoteSiteService remoteSiteService;

    @Autowired
    private ExcessGiveCheckWhite excessGiveCheckWhite;
    public static ThreadLocal<Integer> localDaySumQty = new ThreadLocal<>();

    @PostConstruct
    public void init() {
        excessQuantityUtil = this;
        cache =
            CacheBuilder.newBuilder()
                .expireAfterWrite(60, TimeUnit.MINUTES)
                .recordStats()
                .removalListener(
                    new RemovalListener<Object, Object>() {
                        @Override
                        public void onRemoval(RemovalNotification<Object, Object> removalNotification) {
                            log.debug(
                                removalNotification.getKey()
                                    + " was removed, cause is "
                                    + removalNotification.getCause());
                        }
                    })
                .build(
                    new CacheLoader<String, List<ConfigValue>>() {
                        @Override
                        public List<ConfigValue> load(String s) throws Exception {
                            String configValue =
                                excessQuantityUtil.remoteSysConfigService.selectConfigByKey(
                                    "order_excess_quantity_rule");
                            if (StrUtil.isBlank(configValue)) {
                                return new ArrayList<>();
                            }
                            List<ConfigValue> comboList =
                                JSONObject.parseArray(configValue, ConfigValue.class);
                            return comboList;
                        }
                    });
    }

    /**
     * 赠送检查
     *
     * @param varOrderQuantity         订单数量
     * @param varFreshMilkGiveQuantity 鲜奶赠送数量
     * @param varLongMilkGiveQuantity  常温奶赠送数量
     * @param type                     0 快单 1 合订单
     * @throws Exception
     */
    public Integer giveCheck(
        Integer daySumQty,
        Integer varOrderQuantity,
        Integer varFreshMilkGiveQuantity,
        Integer varLongMilkGiveQuantity,
        int type,
        Long siteId) {
        final int orderQuantity = Convert.toInt(varOrderQuantity, 0);
        final int freshMilkGiveQuantity = Convert.toInt(varFreshMilkGiveQuantity, 0);
        final int longMilkGiveQuantity = Convert.toInt(varLongMilkGiveQuantity, 0);

        boolean check = getCheck(siteId);

        List<ConfigValue> configValues = null;
        try {
            configValues = cache.get("order_excess_quantity_rule");
        } catch (ExecutionException e) {
        }
        if (CollectionUtil.isEmpty(configValues)) {
            throw new ServiceException("没有套餐，不能录入鲜奶赠送数和常温奶赠送数，请调整后提交");
        }

        if (KDORDERTYPE == type) {
            //

            if (orderQuantity < 400 && daySumQty < 400 && freshMilkGiveQuantity > 0 && check) {
                throw new ServiceException("订购数量小于400盒，只能赠送常温奶！");
            } else {

                if (orderQuantity >= 100) {
                    ConfigValue min =
                        configValues.stream()
                            .filter(
                                t -> {
                                    return t.getQuantity() <= orderQuantity;
                                })
                            .sorted(Comparator.comparing(ConfigValue::getQuantity).reversed())
                            .findFirst()
                            .orElseThrow(
                                () -> {
                                    return new ServiceException("没有最小赠送数量套餐");
                                });

                    ConfigValue max =
                        configValues.stream()
                            .filter(
                                t -> {
                                    return t.getQuantity() >= orderQuantity
                                        && t.getQuantity() > min.getQuantity();
                                })
                            .findFirst()
                            .orElseThrow(
                                () -> {
                                    return new ServiceException("没有最大赠送数量套餐");
                                });

                    if (freshMilkGiveQuantity + longMilkGiveQuantity < min.getGiveQuantity()) {
                        throw new ServiceException(
                            StrUtil.format("客户合计赠送数量不能少于{}，请重新输入", min.getGiveQuantity()));
                    }

                    if (freshMilkGiveQuantity + longMilkGiveQuantity > max.getGiveQuantity()) {
                        throw new ServiceException(
                            StrUtil.format("客户合计赠送数量不能大于{}，请重新输入", max.getGiveQuantity()));
                    }

                    return freshMilkGiveQuantity + longMilkGiveQuantity - min.getGiveQuantity();
                }
            }
        }

        if (CONTRACTORDERTYPE == type) {
            //
            if (orderQuantity < 100 && longMilkGiveQuantity > 0) {
                throw new ServiceException("总订购数量小于100盒，不能赠送！");
            } else if (orderQuantity < 400 && daySumQty < 400 && freshMilkGiveQuantity > 0 && check) {
                throw new ServiceException("订购数量小于400盒，只能赠送常温奶！");
            } else {

                if (orderQuantity >= 100) {
                    ConfigValue min =
                        configValues.stream()
                            .filter(
                                t -> {
                                    return t.getQuantity() <= orderQuantity;
                                })
                            .sorted(Comparator.comparing(ConfigValue::getQuantity).reversed())
                            .findFirst()
                            .orElseThrow(
                                () -> {
                                    return new ServiceException("没有最小赠送数量套餐");
                                });

                    ConfigValue max =
                        configValues.stream()
                            .filter(
                                t -> {
                                    return t.getQuantity() >= orderQuantity
                                        && t.getQuantity() > min.getQuantity();
                                })
                            .findFirst()
                            .orElseThrow(
                                () -> {
                                    return new ServiceException("没有最大赠送数量套餐");
                                });

                    if (freshMilkGiveQuantity + longMilkGiveQuantity < min.getGiveQuantity()) {
                        throw new ServiceException(
                            StrUtil.format("客户合计赠送数量不能少于{}，请重新输入", min.getGiveQuantity()));
                    }

                    if (freshMilkGiveQuantity + longMilkGiveQuantity > max.getGiveQuantity()) {
                        throw new ServiceException(
                            StrUtil.format("客户合计赠送数量不能大于{}，请重新输入", max.getGiveQuantity()));
                    }

                    return freshMilkGiveQuantity + longMilkGiveQuantity - min.getGiveQuantity();
                }
            }
        }
        return 0;
    }


    public ExcessQuantityDTO getExcessQuantityNotLimit(Integer varOrderQuantity,
            Integer varFreshMilkGiveQuantity,
            Integer varLongMilkGiveQuantity) {
        final int orderQuantity = Convert.toInt(varOrderQuantity, 0);
        final int freshMilkGiveQuantity = Convert.toInt(varFreshMilkGiveQuantity, 0);
        final int longMilkGiveQuantity = Convert.toInt(varLongMilkGiveQuantity, 0);

        List<ConfigValue> configValues = null;
        try {
            configValues = cache.get("order_excess_quantity_rule");
        } catch (ExecutionException e) {
        }
        if (CollectionUtil.isEmpty(configValues)) {
            throw new ServiceException("没有套餐，不能录入鲜奶赠送数和常温奶赠送数，请调整后提交");
        }

        ConfigValue min = configValues.stream().filter(t -> t.getQuantity() <= orderQuantity)
                .sorted(Comparator.comparing(ConfigValue::getQuantity).reversed())
                .findFirst().orElseGet(() -> {
                    ConfigValue configValue = new ConfigValue();
                    configValue.setQuantity(0);
                    configValue.setGiveQuantity(0);
                    configValue.setId(0);
                    return configValue;
                });
       int excessQuantity = freshMilkGiveQuantity + longMilkGiveQuantity - min.getGiveQuantity();

        ExcessQuantityDTO excessQuantityDTO = new ExcessQuantityDTO();
        excessQuantityDTO.setGiveMin(0);
        excessQuantityDTO.setGiveMax(-1);
        excessQuantityDTO.setMin(min.getQuantity());
        excessQuantityDTO.setMax(-1);
        excessQuantityDTO.setExcessQuantity(excessQuantity > 0 ? excessQuantity : 0);
        return excessQuantityDTO;
    }

    private boolean getCheck(Long siteId) {
        Long cxrRegionIdBySiteId = remoteSiteService.getCxrRegionIdBySiteId(siteId);
        if (CollectionUtil.isEmpty(excessGiveCheckWhite.getWhites())
            || !excessGiveCheckWhite.getWhites().contains(Convert.toLong(cxrRegionIdBySiteId))) {
            return true;
        }
        return false;
    }

    public void giveCheck(
        Integer daySumQty,
        Integer orderQuantity,
        Integer varFreshMilkGiveQuantity,
        Integer varLongMilkGiveQuantity,
        int type,
        LastUserOrderVo userOrder,
        Long siteId) {
        final int freshMilkGiveQuantity = Convert.toInt(varFreshMilkGiveQuantity, 0);
        final int longMilkGiveQuantity = Convert.toInt(varLongMilkGiveQuantity, 0);

        List<ConfigValue> configValues = null;
        try {
            configValues = cache.get("order_excess_quantity_rule");
        } catch (ExecutionException e) {
        }
        if (CollectionUtil.isEmpty(configValues)) {
            throw new ServiceException("没有套餐，不能录入鲜奶赠送数和常温奶赠送数，请调整后提交");
        }
        boolean check = getCheck(siteId);
        // 上一张订单赠送数量
        int lastGiveQty = userOrder.getFreshMilkGiveQuantity() + userOrder.getLongMilkGiveQuantity();

        // 当前订单赠送数量
        int currentGiveQty = freshMilkGiveQuantity + longMilkGiveQuantity;

        int sumQty = orderQuantity + userOrder.getOrderQuantity();
        if (KDORDERTYPE == type) {
            //

            if ((sumQty < 400 && daySumQty < 400) && freshMilkGiveQuantity > 0 && check) {
                throw new ServiceException("合计订购数量小于400盒，只能赠送常温奶！");
            } else {

                if (sumQty >= 100) {
                    ConfigValue min =
                        configValues.stream()
                            .filter(
                                t -> {
                                    return t.getQuantity() <= sumQty;
                                })
                            .sorted(Comparator.comparing(ConfigValue::getQuantity).reversed())
                            .findFirst()
                            .orElseThrow(
                                () -> {
                                    return new ServiceException("没有最小赠送数量套餐");
                                });

                    ConfigValue max =
                        configValues.stream()
                            .filter(
                                t -> {
                                    return t.getQuantity() >= sumQty && t.getQuantity() > min.getQuantity();
                                })
                            .findFirst()
                            .orElseThrow(
                                () -> {
                                    return new ServiceException("没有最大赠送数量套餐");
                                });

                    int minGiveQty = min.getGiveQuantity() > lastGiveQty ? min.getGiveQuantity() - lastGiveQty : 0;
                    if (currentGiveQty < minGiveQty) {
                        throw new ServiceException(StrUtil.format("本单最小可赠送数：{}", minGiveQty));
                    }

                    int maxGiveQty = max.getGiveQuantity() > lastGiveQty ? max.getGiveQuantity() - lastGiveQty : 0;
                    if (currentGiveQty > maxGiveQty) {
                        throw new ServiceException(StrUtil.format("本单最大可赠送数：{}",  maxGiveQty));
                    }
                }
            }
        }

        if (CONTRACTORDERTYPE == type) {
            //
            if (sumQty < 100 && longMilkGiveQuantity > 0) {
                throw new ServiceException("总订购数量小于100盒，不能赠送！");
            } else if (sumQty < 400 && daySumQty < 400 && freshMilkGiveQuantity > 0 && check) {
                throw new ServiceException("订购数量小于400盒，只能赠送常温奶！");
            } else {

                if (sumQty >= 100) {
                    ConfigValue min =
                        configValues.stream()
                            .filter(
                                t -> {
                                    return t.getQuantity() <= sumQty;
                                })
                            .sorted(Comparator.comparing(ConfigValue::getQuantity).reversed())
                            .findFirst()
                            .orElseThrow(
                                () -> {
                                    return new ServiceException("没有最小赠送数量套餐");
                                });

                    ConfigValue max =
                        configValues.stream()
                            .filter(
                                t -> {
                                    return t.getQuantity() >= sumQty && t.getQuantity() > min.getQuantity();
                                })
                            .findFirst()
                            .orElseThrow(
                                () -> {
                                    return new ServiceException("没有最大赠送数量套餐");
                                });

                    int minGiveQty = min.getGiveQuantity() > lastGiveQty ? min.getGiveQuantity() - lastGiveQty : 0;
                    if (currentGiveQty < minGiveQty) {
                        throw new ServiceException(StrUtil.format("本单最小可赠送数：{}", minGiveQty));
                    }
                    int maxGiveQty = max.getGiveQuantity() > lastGiveQty ? max.getGiveQuantity() - lastGiveQty : 0;
                    if (currentGiveQty > maxGiveQty) {
                        throw new ServiceException(StrUtil.format("本单最大可赠送数：{}", maxGiveQty));
                    }
                }
            }
        }
    }

    /**
     * 获取超送数量
     *
     * @param varOrderQuantity         订单数量
     * @param varFreshMilkGiveQuantity 鲜奶赠送数量
     * @param varLongMilkGiveQuantity  常温奶赠送数量
     * @throws Exception
     */
    public ExcessQuantityDTO getExcessQuantity(
        Integer varOrderQuantity, Integer varFreshMilkGiveQuantity, Integer varLongMilkGiveQuantity) {
        final int orderQuantity = Convert.toInt(varOrderQuantity, 0);
        final int freshMilkGiveQuantity = Convert.toInt(varFreshMilkGiveQuantity, 0);
        final int longMilkGiveQuantity = Convert.toInt(varLongMilkGiveQuantity, 0);

        ExcessQuantityDTO excessQuantityDTO = new ExcessQuantityDTO();
        List<ConfigValue> configValues = null;
        try {
            configValues = cache.get("order_excess_quantity_rule");
        } catch (ExecutionException e) {
        }

        if (CollectionUtil.isEmpty(configValues)) {
            throw new ServiceException("没有套餐，不能录入鲜奶赠送数和常温奶赠送数，请调整后提交");
        }

        if (orderQuantity >= 100) {

            ConfigValue min =
                configValues.stream()
                    .filter(
                        t -> {
                            return t.getQuantity() <= orderQuantity;
                        })
                    .sorted(Comparator.comparing(ConfigValue::getQuantity).reversed())
                    .findFirst()
                    .orElseThrow(
                        () -> {
                            return new ServiceException("没有最小赠送数量套餐");
                        });

            ConfigValue max =
                configValues.stream()
                    .filter(
                        t -> {
                            return t.getQuantity() >= orderQuantity && t.getQuantity() > min.getQuantity();
                        })
                    .findFirst()
                    .orElseThrow(
                        () -> {
                            return new ServiceException("没有最大赠送数量套餐");
                        });

            //

            if (freshMilkGiveQuantity + longMilkGiveQuantity < min.getGiveQuantity()) {
                throw new ServiceException(StrUtil.format("最少赠送{}盒奶，请重新输入！", min.getGiveQuantity()));
            }

            if (freshMilkGiveQuantity + longMilkGiveQuantity > max.getGiveQuantity()) {
                throw new ServiceException(StrUtil.format("最多赠送{}盒奶，请重新输入", max.getGiveQuantity()));
            }

            excessQuantityDTO.setExcessQuantity(
                freshMilkGiveQuantity + longMilkGiveQuantity - min.getGiveQuantity());
            excessQuantityDTO.setMin(min.getQuantity());
            excessQuantityDTO.setGiveMin(min.getGiveQuantity());
            excessQuantityDTO.setMax(max.getQuantity());
            excessQuantityDTO.setGiveMax(max.getGiveQuantity());
            return excessQuantityDTO;
        }

        excessQuantityDTO.setExcessQuantity(0);
        excessQuantityDTO.setMin(0);
        excessQuantityDTO.setGiveMin(0);
        excessQuantityDTO.setMax(0);
        excessQuantityDTO.setGiveMax(0);
        return excessQuantityDTO;
    }

    /**
     * 获取超送数量
     *
     * @param varOrderQuantity         订单数量
     * @param varFreshMilkGiveQuantity 鲜奶赠送数量
     * @param varLongMilkGiveQuantity  常温奶赠送数量
     * @throws Exception
     */
    public ExcessQuantityDTO getExcessQuantity(
        Integer varOrderQuantity,
        Integer varFreshMilkGiveQuantity,
        Integer varLongMilkGiveQuantity,
        LastUserOrderVo userOrder) {

        final int orderQuantity = Convert.toInt(varOrderQuantity, 0);
        final int freshMilkGiveQuantity = Convert.toInt(varFreshMilkGiveQuantity, 0);
        final int longMilkGiveQuantity = Convert.toInt(varLongMilkGiveQuantity, 0);

        ExcessQuantityDTO excessQuantityDTO = new ExcessQuantityDTO();
        List<ConfigValue> configValues = null;
        try {
            configValues = cache.get("order_excess_quantity_rule");
        } catch (ExecutionException e) {
        }

        if (CollectionUtil.isEmpty(configValues)) {
            throw new ServiceException("没有套餐，不能录入鲜奶赠送数和常温奶赠送数，请调整后提交");
        }

        // 上一张订单赠送数量
        int lastGiveQty = userOrder.getFreshMilkGiveQuantity() + userOrder.getLongMilkGiveQuantity();

        // 当前订单赠送数量
        int currentGiveQty = freshMilkGiveQuantity + longMilkGiveQuantity;

        int sumQty = orderQuantity + userOrder.getOrderQuantity();

        if (sumQty >= 100) {

            ConfigValue min =
                configValues.stream()
                    .filter(
                        t -> {
                            return t.getQuantity() <= sumQty;
                        })
                    .sorted(Comparator.comparing(ConfigValue::getQuantity).reversed())
                    .findFirst()
                    .orElseThrow(
                        () -> {
                            return new ServiceException("没有最小赠送数量套餐");
                        });

            ConfigValue max =
                configValues.stream()
                    .filter(
                        t -> {
                            return t.getQuantity() >= sumQty && t.getQuantity() > min.getQuantity();
                        })
                    .findFirst()
                    .orElseThrow(
                        () -> {
                            return new ServiceException("没有最大赠送数量套餐");
                        });

            //

            int minGiveQty = min.getGiveQuantity() > lastGiveQty ? min.getGiveQuantity() - lastGiveQty : 0;
            if (currentGiveQty < minGiveQty) {
                throw new ServiceException(StrUtil.format("本单最小可赠送数：{}", minGiveQty));
            }

            int maxGiveQty = max.getGiveQuantity() > lastGiveQty ? max.getGiveQuantity() - lastGiveQty : 0;
            if (currentGiveQty > maxGiveQty) {
                throw new ServiceException(StrUtil.format("本单最大可赠送数：{}",  maxGiveQty));
            }

            excessQuantityDTO.setExcessQuantity(freshMilkGiveQuantity + longMilkGiveQuantity - minGiveQty);
            excessQuantityDTO.setMin(min.getQuantity());
            excessQuantityDTO.setGiveMin(minGiveQty);
            excessQuantityDTO.setMax(max.getQuantity());
            excessQuantityDTO.setGiveMax(maxGiveQty);
            return excessQuantityDTO;
        }

        excessQuantityDTO.setExcessQuantity(0);
        excessQuantityDTO.setMin(0);
        excessQuantityDTO.setGiveMin(0);
        excessQuantityDTO.setMax(0);
        excessQuantityDTO.setGiveMax(0);
        return excessQuantityDTO;
    }

    public ExcessQuantityDTO getExcessQuantityNotLimit(Integer orderQuantity, Integer freshMilkGiveQuantity, Integer longMilkGiveQuantity, LastUserOrderVo userOrder) {

        ExcessQuantityDTO excessQuantityDTO = new ExcessQuantityDTO();
        List<ConfigValue> configValues = null;
        try {
            configValues = cache.get("order_excess_quantity_rule");
        } catch (ExecutionException e) {
        }

        if (CollectionUtil.isEmpty(configValues)) {
            throw new ServiceException("没有套餐，不能录入鲜奶赠送数和常温奶赠送数，请调整后提交");
        }

        // 上一张订单赠送数量
        int lastGiveQty = userOrder.getFreshMilkGiveQuantity() + userOrder.getLongMilkGiveQuantity();

        int sumQty = orderQuantity + userOrder.getOrderQuantity();

        ConfigValue min = configValues.stream().filter(t -> t.getQuantity() <= sumQty)
                .sorted(Comparator.comparing(ConfigValue::getQuantity).reversed())
                .findFirst().orElseGet(() -> {
                    ConfigValue configValue = new ConfigValue();
                    configValue.setQuantity(0);
                    configValue.setGiveQuantity(0);
                    configValue.setId(0);
                    return configValue;
                });


        int minGiveQty = min.getGiveQuantity() - lastGiveQty;
        if (minGiveQty < 0) {
            minGiveQty = 0;
        }
        int excessQuantity = freshMilkGiveQuantity + longMilkGiveQuantity - minGiveQty;
        excessQuantityDTO.setExcessQuantity(excessQuantity < 0 ? 0 : excessQuantity);
        excessQuantityDTO.setMin(min.getQuantity());
        excessQuantityDTO.setGiveMin(minGiveQty);
        excessQuantityDTO.setMax(-1);
        excessQuantityDTO.setGiveMax(-1);
        return excessQuantityDTO;
    }
}
