package com.ruoyi.job.common.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.business.base.api.domain.CxrEmployeeAchievementDetail;
import com.ruoyi.job.common.domain.EmployeeLevel;
import com.ruoyi.job.common.domain.CxrRegionStaitemDailyReport;

import java.time.LocalDate;
import java.time.YearMonth;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>Description: </p>
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/8/10 17:52
 **/
@DS("ry_cloud")
@Mapper
public interface CxrEmployeeAchievementDetailMapper extends BaseMapper<CxrEmployeeAchievementDetail> {

    List<CxrEmployeeAchievementDetail> getMouthPerformance(@Param("ids") List<Long> employeeIds,
                                                           @Param("currentYearMonth") YearMonth currentYearMonth);
    @Select("<script>"
        +"select ifnull(sum(l1EmployeePerformance), 0.00) l1EmployeePerformance,\n"
        + "       ifnull(sum(l2EmployeePerformance), 0.00) l2EmployeePerformance,\n"
        + "       ifnull(sum(l3EmployeePerformance), 0.00) l3EmployeePerformance,\n"
        + "       ifnull(sum(l4EmployeePerformance), 0.00) l4EmployeePerformance,\n"
        + "       ifnull(sum(l5EmployeePerformance), 0.00) l5EmployeePerformance,\n"
        + "       ifnull(sum(l6EmployeePerformance), 0.00) l6EmployeePerformance\n"
        + "from (select\n"
        + "    case when employee_id in  "
        + "<foreach item='item' index='index' collection='em.l1EmployeeIds'  separator=',' open='(' close=')'>\n"
        + "#{item}"
        + "</foreach>"
        + " then achievement_value end as l1EmployeePerformance,\n"
        + "    case when employee_id in "
        + "<foreach item='item' index='index' collection='em.l2EmployeeIds'  separator=',' open='(' close=')'>\n"
        + "#{item}"
        + "</foreach>"
        + " then  achievement_value end as l2EmployeePerformance,\n"
        + "    case when employee_id in "
        + "<foreach item='item' index='index' collection='em.l3EmployeeIds'  separator=',' open='(' close=')'>\n"
        + "#{item}"
        + "</foreach>"
        + " then achievement_value end as l3EmployeePerformance,\n"
        + "    case when employee_id in "
        + "<foreach item='item' index='index' collection='em.l4EmployeeIds'  separator=',' open='(' close=')'>\n"
        + "#{item}"
        + "</foreach>"
        + " then  achievement_value end l4EmployeePerformance,\n"
        + "    case when employee_id in "
        + "<foreach item='item' index='index' collection='em.l5EmployeeIds'  separator=',' open='(' close=')'>\n"
        + "#{item}"
        + "</foreach>"
        + " then  achievement_value end l5EmployeePerformance,\n"
        + "    case when employee_id in "
        + "<foreach item='item' index='index' collection='em.l6EmployeeIds'  separator=',' open='(' close=')'>\n"
        + "#{item}"
        + "</foreach>"
        + " then  achievement_value end l6EmployeePerformance\n"
        + "from cxr_employee_achievement_detail a,\n"
        + "     (select @l1 := 0, @l2 := 0, @l3 := 0, @l4 := 0, @l5 := 0, @l6 := 0)t\n"
        + "where order_date between concat(#{month},'-01') and last_day(concat(#{month},'-01'))"
        + "and  delete_status = 0 \n"
        + "  and employee_id in"
        + "<foreach item='item' index='index' collection='allEmIds'  separator=',' open='(' close=')'>"
        + "#{item}"
        + "</foreach>) t "
        + "</script>")
    EmployeeLevel queryLevelPerformance(@Param("month") String month,
        @Param("em") EmployeeLevel employeeLevel, @Param("allEmIds") List<Long> allEmIds);

    /**
     * 按销售代理聚合查询业绩数据（类似 pageGroupEmp 的实现）
     * @param date 查询日期
     * @return 聚合后的报表数据列表
     */
    List<CxrRegionStaitemDailyReport> selectAggregatedByEmployee(@Param("date") LocalDate date);
}
