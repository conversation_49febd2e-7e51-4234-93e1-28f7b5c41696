package com.ruoyi.calculate.api.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 区域统计报表维度类型枚举
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@AllArgsConstructor
@Getter
public enum RegionStatisticsDimensionType {

    /**
     * 销售代理维度
     */
    AGENT("AGENT", "销售代理"),

    /**
     * 站点维度
     */
    SITE("SITE", "站点"),

    /**
     * 区域维度
     */
    REGION("REGION", "区域"),

    /**
     * 大区维度
     */
    ROOT_REGION("ROOT_REGION", "大区"),

    /**
     * 公司维度
     */
    COMPANY("COMPANY", "公司");

    private  String code;
    private  String description;



    /**
     * 根据代码获取枚举
     *
     * @param code 代码
     * @return 枚举值
     */
    public static RegionStatisticsDimensionType fromCode(String code) {
        for (RegionStatisticsDimensionType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的区域统计报表维度类型: " + code);
    }

    /**
     * 判断是否为有效的维度类型代码
     *
     * @param code 代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        for (RegionStatisticsDimensionType type : values()) {
            if (type.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }

}
