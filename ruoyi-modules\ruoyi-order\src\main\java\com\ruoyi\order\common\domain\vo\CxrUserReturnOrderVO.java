package com.ruoyi.order.common.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.ruoyi.business.base.api.domain.DeliverySiteDTO;
import com.ruoyi.business.base.api.domain.json.BusinessAgent;
import com.ruoyi.common.enums.TerminalTypeEnums;
import com.ruoyi.core.base.domain.vo.CxrOrderAfterSaleVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Description: 退订单vo
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/8/15 17:34
 */
@Data
public class CxrUserReturnOrderVO extends CxrUserOrderListVo implements Serializable {

    @ApiModelProperty(value = "编号", notes = "")
    private Long id;

    @ApiModelProperty(value = "主单id", required = true)
    private Long userOrderId;
    /**
     * 订单时间
     */
    @DateTimeFormat("yyyy-MM-dd")
    @ApiModelProperty(value = "订单时间", required = true)
    private Date orderDate;
    /**
     * 站点id
     */
    @ApiModelProperty(value = "站点id", required = true)
    private Long siteId;
    /**
     * 业务代理(已选择的销售代理;可以有多个)
     */
    @ApiModelProperty(value = "业务代理(已选择的销售代理", required = true)
    private List<BusinessAgent> businessAgent;

    @ApiModelProperty(value = "客户id", required = true)
    private Long customerId;

    /**
     * 客户电话;只取第一个录入的数据
     */
    @ApiModelProperty(value = "客户电话", notes = "只取第一个录入的数据")
    private String customerPhone;
    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称", notes = "")
    private String customerName;
    /**
     * 客户地址;小区
     */
    @ApiModelProperty(value = "客户地址", notes = "小区")
    private String quarters;
    /**
     * 客户地址;;只取第一个录入的数据
     */
    @ApiModelProperty(value = "客户地址;", notes = "只取第一个录入的数据")
    private String customerAdress;
    /**
     * 订购数量：鲜奶订购数量
     */
    @ApiModelProperty(value = "订购数量：鲜奶订购数量", required = true)
    private Integer orderQuantity;
    /**
     * 鲜奶赠送数量
     */
    @ApiModelProperty(value = "鲜奶赠送数量", required = true)
    private Integer freshMilkGiveQuantity;
    /**
     * 总数量;=;鲜奶订购数量 - 鲜奶已送数量
     */
    @ApiModelProperty(value = "总数量;=", notes = "鲜奶订购数量 - 鲜奶已送数量", required = true)
    private Integer totalQuantity;
    /**
     * 鲜奶已送数量
     */
    @ApiModelProperty(value = "鲜奶已送数量", notes = "", required = true)
    private Integer freshMilkSentQuantity;
    /**
     * 鲜奶剩余数量;, 也是客户列表里的 总数量 （鲜奶订购数量 - 鲜奶已送数量）
     */
    @ApiModelProperty(value = "鲜奶剩余数量", notes = ", 也是客户列表里的 总数量 （鲜奶订购数量 - 鲜奶已送数量）", required = true)
    private Integer surplusQuantity;
    /**
     * 常温奶赠送数量
     */
    @ApiModelProperty(value = "常温奶赠送数量", notes = "", required = true)
    private Integer longMilkGiveQuantity;
    /**
     * 常温奶已送数量
     */
    @ApiModelProperty(value = "常温奶已送数量", notes = "", required = true)
    private Integer longMilkSentQuantity;
    /**
     * 常温奶已送金额\\n\\n\\n销售代理自己输入
     */
    @ApiModelProperty(value = "常温奶已送金额\\n\\n\\n销售代理自己输入", notes = "", required = true)
    @ExcelProperty(value = "常温奶已送金额", index = 40)
    private BigDecimal longMilkSentAmount;
    /**
     * 常温奶剩余数量;, 也是客户列表里的 总数量 （鲜奶订购数量 - 鲜奶已送数量）
     */
    @ApiModelProperty(value = "常温奶剩余数量", notes = ", 也是客户列表里的 总数量 （鲜奶订购数量 - 鲜奶已送数量）", required = true)
    private Integer longMilkSurplusQuantity;
    /**
     * 客户实际退款的鲜奶盒数
     */
    @ApiModelProperty(value = "客户退款盒数 (客户实际退款的鲜奶盒数(排除了送的))", notes = "", required = true)
    @ExcelProperty(value = "客户退款盒数", index = 41)
    private Integer freshMilkRefundQuantity;
    /**
     * 客户实际退订的鲜奶盒数
     */
    @ApiModelProperty(value = "客户实际退订的鲜奶盒数", notes = "", required = true)
    @ExcelProperty(value = "鲜奶退订总数量", index = 42)
    private Integer freshMilkCancelQuantity;

    @ApiModelProperty("常温奶剩余数量")
    @ExcelProperty(value = "常温奶剩余数量", index = 43)
    private Integer longMilkRestQuantity;

    @ApiModelProperty("鲜奶退订后剩余数量 (常温奶没有这个)")
    @ExcelProperty(value = "鲜奶退订剩余数量", index = 44)
    private Integer freshMilkCancelRestQuantity;

    /**
     * 客户实际退款的鲜奶盒数
     */
    @ApiModelProperty(value = "客户实际退款的鲜奶盒数", notes = "", required = true)
    @ExcelProperty(value = "常温奶退订数量", index = 45)
    private Integer longMilkCancelQuantity;
    /**
     * 客户退订后;账户中的剩余鲜奶数量
     */
    @ApiModelProperty(value = "客户退订后", notes = "账户中的剩余鲜奶数量", required = true)
    @ExcelProperty(value = "鲜奶剩余数量", index = 46)
    private Integer freshMilkRestQuantity;
    /**
     * 实际退还给客户的金额
     */
    @ApiModelProperty(value = "实际退还给客户的金额", notes = "", required = true)
    @ExcelProperty(value = "客户退订金额", index = 47)
    private BigDecimal refundAmount;
    /**
     * 补销售代理返利
     */
    @ApiModelProperty(value = "补销售代理返利", notes = "", required = true)
    @ExcelProperty(value = "补销售代理返利", index = 48)
    private BigDecimal rebates;
    /**
     * 退订总金额;= 客户退款金额 + 常温奶已送金额 + 补业务代理返利 (如果存在多个销售代理的情况，则按人数平均分配)
     */
    @ApiModelProperty(
        value = "退订总金额",
        notes = "= 客户退款金额 + 常温奶已送金额 + 补业务代理返利 (如果存在多个销售代理的情况，则按人数平均分配)",
        required = true)
    private BigDecimal amount;
    /**
     * 开户银行
     */
    @ApiModelProperty(value = "开户银行", notes = "", required = true)
    @ExcelProperty(value = "开户银行", index = 49)
    private String bankName;
    /**
     * 账户卡号
     */
    @ApiModelProperty(value = "账户卡号", notes = "", required = true)
    @ExcelProperty(value = "银行账号", index = 50)
    private String bankAccountNumber;
    /**
     * 账户名
     */
    @ApiModelProperty(value = "账户名", notes = "", required = true)
    @ExcelProperty(value = "账户姓名", index = 51)
    private String bankAccountName;
    /**
     * 单据图片
     */
    @ApiModelProperty(value = "单据图片", notes = "")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> images;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", notes = "")
    private String remark;

    @ApiModelProperty(value = "单据", notes = "订单单据号")
    private String orderNo;

    /**
     * 来源 1 后端 2 配送端
     */
    @ApiModelProperty(value = "终端类型")
    private Short terminalType = TerminalTypeEnums.manager.getValue();

    /**
     * 订单类型
     */
    @ApiModelProperty(value = "订单类型")
    private Short orderType;
    /**
     * 省
     */
    @ApiModelProperty("省")
    private String province;

    /**
     * 市
     */
    @ApiModelProperty("市")
    private String city;

    /**
     * 区
     */
    @ApiModelProperty("区")
    private String area;

    @ApiModelProperty("1 待审核 、 2 已审核 、 3.已拒绝")
    private Integer auditStatus;

    @ApiModelProperty("站点名称")
    private String siteName;

    /**
     * 金额：该笔订单的总金额
     */
    @ApiModelProperty("金额：该笔订单的总金额")
    @ExcelProperty(value = "退订总金额", index = 52)
    private BigDecimal returnAmount;


    /**
     * 常温奶已送数量
     */
    @ApiModelProperty("常温奶已送数量")
    @ExcelProperty(value = "常温奶已送数量", index = 53)
    private Integer returnLongMilkSentQuantity;


    /**
     * 常温奶赠送数量
     */
    @ApiModelProperty("常温奶赠送数量")
    @ExcelProperty(value = "常温奶赠送总数", index = 54)
    private Integer returnLongMilkGiveQuantity;


    @ApiModelProperty("退订原因编号")
    private String rrCode;

    @ApiModelProperty("退订原因")
    @ExcelProperty(value = "退订原因", index = 55)
    private String rrName;

    /**
     * 售后单id
     */
    @ApiModelProperty("售后单id")
    private Long afterSalesId;

    /**
     * 售后单编号
     */
    @ApiModelProperty("售后单编号")
    private String afterSalesNo;

    /**
     * 关联订单的订购金额
     */
    @ApiModelProperty("订购金额")
    @ExcelProperty(value = "订购金额", index = 56)
    private BigDecimal orderMoney;

    @ApiModelProperty("售后单")
    private CxrOrderAfterSaleVo cxrOrderAfterSaleVo;


    @ApiModelProperty(value = "打款状态:0.未打款、1.退款中 2.已打款、3.打款失败")
    @ExcelProperty(value = "打款状态", index = 57)
    private Integer paymentStatus;

    @ApiModelProperty(value = "打款失败原因")
    @ExcelProperty(value = "打款失败原因", index = 58)
    private String paymentFailureReasons;


    // 常温奶id
    private Long longMilkId;
    //常温奶数量
    private Integer longMilkQuantity;

    //已申领
    private Integer applyNum;
    //未申领
    private Integer surplusNum;

    //已过期
    private Integer overdueNum;

    private List<OrderHistoryReturnVo> orderHistoryList;
    // 退款合计obj
    private HistoryStatisticsVo historyStatisticsVo;

    private String orderImage;

    private String goodsName;
    @ApiModelProperty("上方订购数量")
    private Integer orderInfoQuantity;

    @ApiModelProperty("上方金额：该笔订单的总金额")
    private BigDecimal InfoAmount;

    @ApiModelProperty("上方实际支付金额")
    private BigDecimal practicalAmount;
    @ApiModelProperty("优惠金额")
    private BigDecimal couponAmount;

    @ApiModelProperty("上方常温奶赠送数量")
    private Integer infoLongMilkGiveQuantity;

    @ApiModelProperty("上方鲜奶赠送数量")
    private Integer infoFreshMilkGiveQuantity;

    private Map<String, Object> checkCustomerPhoneMap;

    private String specifications;


    @ApiModelProperty(value = "销售代理填写备注")
    private String employeeRemark;

    @ApiModelProperty("抖音订单号")
    private String tiktokOrderNo;

    @ApiModelProperty(value = "异常订单状态  1：异常")
    private Integer abnormalTag;

    @ApiModelProperty("配送站点")
    private String deliverySites;

    @ApiModelProperty("退款审核不通过原因")
    private String refundNoAuditReasons;

    @ApiModelProperty("配送站点")
    private List<DeliverySiteDTO> deliverySiteDTOS;


}
