<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.calculate.orderCount.mapper.CxrEmployeeOrderCountMapper">

    <select id="list" resultType="com.ruoyi.core.base.domain.vo.CxrEmployeeOrderCountVo">
        SELECT s.id,s.name
        siteName,s.site_abbreviation,s.current_dept_id,s.cxr_root_region_id,s.cxr_root_region_name,s.site_mark,s.provice,s.city,s.area,d.dept_name
        companyName,ifnull(detail.achievement_value,0) as achievement_value
        FROM cxr_site s
        LEFT JOIN sys_dept d ON s.current_dept_id = d.dept_id
        left join (
            SELECT sum(IFNULL(detail.achievement_value,0)) as achievement_value,detail.site_id
            FROM cxr_employee_achievement_detail detail
            WHERE detail.delete_status = '0'
            AND detail.order_date >= #{bo.startDate}
            AND detail.order_date &lt; #{bo.endDate}
            group by detail.site_id
        ) detail on s.id = detail.site_id
        <include refid="whereSql"></include>
        ORDER BY achievement_value desc
    </select>
    <sql id="whereSql">
        WHERE delete_status = '0'
        <if test="bo.siteId != null">
            AND s.id = #{bo.siteId}
        </if>
        <if test="bo.companyId != null">
            AND s.current_dept_id = #{bo.companyId}
        </if>
        <if test="bo.cxrRootRegionName != null and bo.cxrRootRegionName != ''">
            AND s.cxr_root_region_name LIKE CONCAT('%',#{bo.cxrRootRegionName},'%')
        </if>
        <if test="bo.provice != null and bo.provice != ''">
            AND s.provice LIKE CONCAT('%',#{bo.provice},'%')
        </if>
        <if test="bo.city != null and bo.city != ''">
            AND s.city LIKE CONCAT('%', #{bo.city} ,'%')
        </if>
        <if test="bo.area != null and bo.area != ''">
            AND s.area LIKE CONCAT('%',#{bo.area},'%')
        </if>
        <if test="bo.siteName != null and bo.siteName != ''">
            AND s.name LIKE CONCAT('%',#{bo.siteName},'%')
        </if>
        <if test="bo.siteIds != null and bo.siteIds.size() > 0">
            AND s.id IN
            <foreach collection="bo.siteIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </sql>
    <select id="count" resultType="java.lang.Long">
        SELECT COUNT(*) FROM cxr_site s
        <include refid="whereSql"></include>
    </select>

    <select id="queryCountAndAchievement" resultType="com.ruoyi.core.base.domain.vo.CxrEmployeeOrderCountVo">
        SELECT site_id,
        SUM(IF(order_type = 0 OR order_type = 70,order_count,0)) newOrderCount,
        SUM(IF(order_type = 0 OR order_type = 70,achievement_value,0)) newOrderAchievement,
        SUM(IF(order_type = 1 OR order_type = 71,achievement_value,0))continueOrderAchievement,
        SUM(IF(order_type = 1 OR order_type = 71,order_count,0))continueOrderCount,
        SUM(IF(order_type = 2,achievement_value,0))returnOrderAchievement,
        SUM(IF(order_type = 2,order_count,0))returnOrderCount,
        SUM(IF(order_type = 3,achievement_value,0))increaseOrderAchievement,
        SUM(IF(order_type = 3,order_count,0))increaseOrderCount
        FROM cxr_employee_achievement_detail
        WHERE delete_status = '0'
        AND site_id IN
        <foreach collection="ids" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        <if test="bo.startDate != null">
            AND order_date >= #{bo.startDate}
        </if>
        <if test="bo.endDate != null">
            AND order_date &lt; #{bo.endDate}
        </if>
        GROUP BY site_id
    </select>


    <sql id="PageBaseSql">
        SELECT a_detail.employee_id,a_detail.site_id,site.cxr_root_region_id as cxr_region_id,
        site.current_dept_id as current_dept_id,a_detail.employee_name,
        IF(a_detail.order_type = 0 OR a_detail.order_type = 70,a_detail.order_count,0) newOrderCount,
        IF(a_detail.order_type = 0 OR a_detail.order_type = 70,a_detail.achievement_value,0) newOrderAchievement,
        IF(a_detail.order_type = 1 OR a_detail.order_type = 71,a_detail.achievement_value,0) continueOrderAchievement,
        IF(a_detail.order_type = 1 OR a_detail.order_type = 71,a_detail.order_count,0) continueOrderCount,
        IF(a_detail.order_type = 2,a_detail.achievement_value,0) returnOrderAchievement,
        IF(a_detail.order_type = 2,a_detail.order_count,0) returnOrderCount,
        IF(a_detail.order_type = 3,a_detail.achievement_value,0) increaseOrderAchievement,
        IF(a_detail.order_type = 3,a_detail.order_count,0)increaseOrderCount
        FROM cxr_employee_achievement_detail a_detail
        left join cxr_site site on a_detail.site_id=site.id
        WHERE a_detail.delete_status = '0'
        <if test="bo.companyId != null">
            AND site.current_dept_id = #{bo.companyId}
        </if>
        <if test="bo.siteIds != null and bo.siteIds.size() > 0">
            AND a_detail.site_id IN
            <foreach collection="bo.siteIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="bo.employeeOccupationStatus != null">
            AND EXISTS (
                SELECT 1 FROM cxr_employee e WHERE e.id = a_detail.employee_id
                <if test="bo.employeeOccupationStatus == 1">
                    AND e.occupation_status = '2'
                </if>
                <if test="bo.employeeOccupationStatus == 2">
                    AND e.occupation_status != '2'
                </if>
            )
        </if>
        <if test="bo.employeeName != null and bo.employeeName != ''">
            AND a_detail.employee_name LIKE CONCAT('%',#{bo.employeeName},'%')
        </if>
        <if test="(bo.siteName != null and bo.siteName != '')
                or (bo.provice != null and bo.provice != '')
                or (bo.city != null and bo.city != '')
                or (bo.area != null and bo.area != '')">
            AND EXISTS (
                SELECT 1 FROM cxr_site s
                WHERE s.id = a_detail.site_id
                <if test="bo.siteName != null and bo.siteName != ''">
                    AND s.name LIKE CONCAT('%', #{bo.siteName}, '%')
                </if>
                <if test="bo.provice != null and bo.provice != ''">
                    AND s.provice LIKE CONCAT('%', #{bo.provice}, '%')
                </if>
                <if test="bo.city != null and bo.city != ''">
                    AND s.city LIKE CONCAT('%', #{bo.city}, '%')
                </if>
                <if test="bo.area != null and bo.area != ''">
                    AND s.area LIKE CONCAT('%', #{bo.area}, '%')
                </if>
            )
        </if>
        <if test="bo.cxrRegionName != null and bo.cxrRegionName != ''">
            AND EXISTS (
                SELECT 1 FROM cxr_region r
                WHERE r.id = site.cxr_root_region_id
                AND r.name LIKE CONCAT('%', #{bo.cxrRegionName}, '%')
            )
        </if>
        <if test="bo.startDate != null">
            AND a_detail.order_date >= #{bo.startDate}
        </if>
        <if test="bo.endDate != null">
            AND a_detail.order_date &lt;= #{bo.endDate}
        </if>
    </sql>
    <select id="pageGroupEmp" resultType="com.ruoyi.core.base.domain.vo.CxrEmployeeOrderCountVo">
        select a_detail.employee_id,a_detail.employee_name,a_detail.site_id,a_detail.cxr_region_id,a_detail.current_dept_id,
        SUM(a_detail.newOrderCount) newOrderCount,
        SUM(a_detail.newOrderAchievement) newOrderAchievement,
        SUM(a_detail.continueOrderAchievement) continueOrderAchievement,
        SUM(a_detail.continueOrderCount)continueOrderCount,
        SUM(a_detail.returnOrderAchievement)returnOrderAchievement,
        SUM(a_detail.returnOrderCount)returnOrderCount,
        SUM(a_detail.increaseOrderAchievement)increaseOrderAchievement,
        SUM(a_detail.increaseOrderCount)increaseOrderCount,
        SUM(a_detail.newOrderCount + a_detail.continueOrderCount + a_detail.increaseOrderCount - a_detail.returnOrderCount) totalOrderCount,
        SUM(a_detail.newOrderAchievement + a_detail.continueOrderAchievement + a_detail.increaseOrderAchievement + a_detail.returnOrderAchievement) totalOrderAchievement
        from
            (
                select a_detail.employee_id,a_detail.employee_name,a_detail.site_id,a_detail.cxr_region_id,a_detail.current_dept_id,
                a_detail.newOrderCount,a_detail.newOrderAchievement,a_detail.continueOrderAchievement,a_detail.continueOrderCount,
                a_detail.returnOrderAchievement,a_detail.returnOrderCount,a_detail.increaseOrderAchievement,a_detail.increaseOrderCount
                from (<include refid="PageBaseSql"/>) a_detail
                union all
                select emp.id as employee_id,emp.name as employee_name,site.id as site_id,site.cxr_region_id as cxr_region_id,site.current_dept_id as current_dept_id,
                0 as newOrderCount,0 as newOrderAchievement,0 as continueOrderAchievement,0 as continueOrderCount,
                0 as returnOrderAchievement,0 as returnOrderCount,0 as increaseOrderAchievement,0 as increaseOrderCount
                from cxr_site site
                inner join cxr_employee emp on site.id = emp.cxr_site_id
                <where>
                    <if test="bo.companyId != null">
                        AND site.current_dept_id = #{bo.companyId}
                    </if>
                    <if test="bo.startDate != null">
                        AND ifnull(emp.quit_time,'2099-1-1') >= DATE_SUB(#{bo.startDate}, INTERVAL 3 MONTH)
                    </if>
                    <if test="bo.employeeName != null and bo.employeeName != ''">
                        AND emp.name LIKE CONCAT('%',#{bo.employeeName},'%')
                    </if>
                    <if test="bo.siteName != null and bo.siteName != ''">
                        AND site.name LIKE CONCAT('%',#{bo.siteName},'%')
                    </if>
                    <if test="bo.provice != null and bo.provice != ''">
                        AND site.provice LIKE CONCAT('%', #{bo.provice}, '%')
                    </if>
                    <if test="bo.city != null and bo.city != ''">
                        AND site.city LIKE CONCAT('%', #{bo.city}, '%')
                    </if>
                    <if test="bo.area != null and bo.area != ''">
                        AND site.area LIKE CONCAT('%', #{bo.area}, '%')
                    </if>
                    <if test="bo.employeeOccupationStatus != null">
                        <if test="bo.employeeOccupationStatus == 1">
                            AND emp.occupation_status = '2'
                        </if>
                        <if test="bo.employeeOccupationStatus == 2">
                            AND emp.occupation_status != '2'
                        </if>
                    </if>
                    <if test="bo.cxrRegionName != null and bo.cxrRegionName != ''">
                        AND EXISTS (
                            SELECT 1 FROM cxr_region r
                            WHERE r.id = site.cxr_region_id
                            AND r.name LIKE CONCAT('%', #{bo.cxrRegionName}, '%')
                        )
                    </if>
                </where>

            ) a_detail
        group by a_detail.employee_id,a_detail.site_id,a_detail.cxr_region_id,a_detail.current_dept_id,a_detail.employee_name
        order by a_detail.employee_id
    </select>

    <select id="pageGroupRegion" resultType="com.ruoyi.core.base.domain.vo.CxrEmployeeOrderCountVo">
        select a_detail.cxr_region_id,a_detail.current_dept_id,
        SUM(a_detail.newOrderCount) newOrderCount,
        SUM(a_detail.newOrderAchievement) newOrderAchievement,
        SUM(a_detail.continueOrderAchievement) continueOrderAchievement,
        SUM(a_detail.continueOrderCount)continueOrderCount,
        SUM(a_detail.returnOrderAchievement)returnOrderAchievement,
        SUM(a_detail.returnOrderCount)returnOrderCount,
        SUM(a_detail.increaseOrderAchievement)increaseOrderAchievement,
        SUM(a_detail.increaseOrderCount)increaseOrderCount,
        SUM(a_detail.newOrderCount + a_detail.continueOrderCount + a_detail.increaseOrderCount - a_detail.returnOrderCount) totalOrderCount,
        SUM(a_detail.newOrderAchievement + a_detail.continueOrderAchievement + a_detail.increaseOrderAchievement + a_detail.returnOrderAchievement) totalOrderAchievement
        from
            (
                select a_detail.cxr_region_id,a_detail.current_dept_id,
                a_detail.newOrderCount,a_detail.newOrderAchievement,a_detail.continueOrderAchievement,a_detail.continueOrderCount,
                a_detail.returnOrderAchievement,a_detail.returnOrderCount,a_detail.increaseOrderAchievement,a_detail.increaseOrderCount
                from (<include refid="PageBaseSql"/>) a_detail
                union all
                select region.id as cxr_region_id,region.current_dept_id,
                0 as newOrderCount,0 as newOrderAchievement,0 as continueOrderAchievement,0 as continueOrderCount,
                0 as returnOrderAchievement,0 as returnOrderCount,0 as increaseOrderAchievement,0 as increaseOrderCount
                from cxr_region region
                where region.delete_status = '0'
                <if test="bo.companyId != null">
                    AND region.current_dept_id = #{bo.companyId}
                </if>
                <if test="bo.regionIds != null and bo.regionIds.size() > 0">
                    AND region.id IN
                    <foreach collection="bo.regionIds" open="(" close=")" separator="," item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="(bo.siteName != null and bo.siteName != '')
                        or (bo.provice != null and bo.provice != '')
                        or (bo.city != null and bo.city != '')
                        or (bo.area != null and bo.area != '')">
                    AND EXISTS (
                        SELECT 1 FROM cxr_site s
                        WHERE s.cxr_root_region_id = region.id
                        <if test="bo.siteName != null and bo.siteName != ''">
                            AND s.name LIKE CONCAT('%', #{bo.siteName}, '%')
                        </if>
                        <if test="bo.provice != null and bo.provice != ''">
                            AND s.provice LIKE CONCAT('%', #{bo.provice}, '%')
                        </if>
                        <if test="bo.city != null and bo.city != ''">
                            AND s.city LIKE CONCAT('%', #{bo.city}, '%')
                        </if>
                        <if test="bo.area != null and bo.area != ''">
                            AND s.area LIKE CONCAT('%', #{bo.area}, '%')
                        </if>
                    )
                </if>
                <if test="bo.cxrRegionName != null and bo.cxrRegionName != ''">
                    AND region.name LIKE CONCAT('%',#{bo.cxrRegionName},'%')
                </if>
            ) a_detail
        group by a_detail.cxr_region_id,a_detail.current_dept_id
        order by a_detail.cxr_region_id
    </select>

    <select id="pageGroupSite" resultType="com.ruoyi.core.base.domain.vo.CxrEmployeeOrderCountVo">
        select a_detail.site_id,a_detail.current_dept_id,a_detail.cxr_region_id,
        SUM(a_detail.newOrderCount) newOrderCount,
        SUM(a_detail.newOrderAchievement) newOrderAchievement,
        SUM(a_detail.continueOrderAchievement) continueOrderAchievement,
        SUM(a_detail.continueOrderCount)continueOrderCount,
        SUM(a_detail.returnOrderAchievement)returnOrderAchievement,
        SUM(a_detail.returnOrderCount)returnOrderCount,
        SUM(a_detail.increaseOrderAchievement)increaseOrderAchievement,
        SUM(a_detail.increaseOrderCount)increaseOrderCount,
        SUM(a_detail.newOrderCount + a_detail.continueOrderCount + a_detail.increaseOrderCount - a_detail.returnOrderCount) totalOrderCount,
        SUM(a_detail.newOrderAchievement + a_detail.continueOrderAchievement + a_detail.increaseOrderAchievement + a_detail.returnOrderAchievement) totalOrderAchievement
        from
            (
                select a_detail.site_id,a_detail.current_dept_id,a_detail.cxr_region_id,
                a_detail.newOrderCount,a_detail.newOrderAchievement,a_detail.continueOrderAchievement,a_detail.continueOrderCount,
                a_detail.returnOrderAchievement,a_detail.returnOrderCount,a_detail.increaseOrderAchievement,a_detail.increaseOrderCount
                from (<include refid="PageBaseSql"/>) a_detail
                union all
                select site.id as site_id,site.current_dept_id as current_dept_id,site.cxr_root_region_id  as cxr_region_id,
                0 as newOrderCount,0 as newOrderAchievement,0 as continueOrderAchievement,0 as continueOrderCount,
                0 as returnOrderAchievement,0 as returnOrderCount,0 as increaseOrderAchievement,0 as increaseOrderCount
                from cxr_site site
                where site.delete_status = '0'
                <if test="bo.companyId != null">
                    AND site.current_dept_id = #{bo.companyId}
                </if>
                <if test="bo.siteIds != null and bo.siteIds.size() > 0">
                    AND site.id IN
                    <foreach collection="bo.siteIds" open="(" close=")" separator="," item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="bo.cxrRegionName != null and bo.cxrRegionName != ''">
                    AND EXISTS (
                        SELECT 1 FROM cxr_region r
                        WHERE r.id = site.cxr_root_region_id
                        AND r.name LIKE CONCAT('%', #{bo.cxrRegionName}, '%')
                    )
                </if>
                <if test="bo.siteName != null and bo.siteName != ''">
                    AND site.name LIKE CONCAT('%',#{bo.siteName},'%')
                </if>
                <if test="bo.provice != null and bo.provice != ''">
                    AND site.provice LIKE CONCAT('%', #{bo.provice}, '%')
                </if>
                <if test="bo.city != null and bo.city != ''">
                    AND site.city LIKE CONCAT('%', #{bo.city}, '%')
                </if>
                <if test="bo.area != null and bo.area != ''">
                    AND site.area LIKE CONCAT('%', #{bo.area}, '%')
                </if>
                <if test="bo.regionIds != null and bo.regionIds.size() > 0">
                    AND site.cxr_root_region_id IN
                    <foreach collection="bo.regionIds" open="(" close=")" separator="," item="item">
                        #{item}
                    </foreach>
                </if>
            ) a_detail
        group by a_detail.site_id,a_detail.current_dept_id,a_detail.cxr_region_id
        order by a_detail.site_id
    </select>

    <select id="sum2" resultType="com.ruoyi.core.base.domain.vo.CxrEmployeeOrderCountVo">
        select sum(a_detail.newOrderCount) newOrderCount,
        sum(a_detail.newOrderAchievement) newOrderAchievement,
        sum(a_detail.continueOrderAchievement) continueOrderAchievement,
        sum(a_detail.continueOrderCount)continueOrderCount,
        sum(a_detail.returnOrderAchievement)returnOrderAchievement,
        sum(a_detail.returnOrderCount)returnOrderCount,
        sum(a_detail.increaseOrderAchievement)increaseOrderAchievement,
        sum(a_detail.increaseOrderCount)increaseOrderCount,
        sum(a_detail.newOrderCount + a_detail.continueOrderCount + a_detail.increaseOrderCount - a_detail.returnOrderCount) totalOrderCount,
        sum(a_detail.newOrderAchievement + a_detail.continueOrderAchievement + a_detail.increaseOrderAchievement + a_detail.returnOrderAchievement) totalOrderAchievement
        from (<include refid="PageBaseSql"/>) a_detail
    </select>


    <select id="queryCountAndAchievementSum" resultType="com.ruoyi.core.base.domain.vo.CxrEmployeeOrderCountVo">
        SELECT
        SUM(IF(order_type = 0 OR order_type = 70,order_count,0)) newOrderCount,
        SUM(IF(order_type = 0 OR order_type = 70,achievement_value,0)) newOrderAchievement,
        SUM(IF(order_type = 1 OR order_type = 71,achievement_value,0))continueOrderAchievement,
        SUM(IF(order_type = 1 OR order_type = 71,order_count,0))continueOrderCount,
        SUM(IF(order_type = 2,achievement_value,0))returnOrderAchievement,
        SUM(IF(order_type = 2,order_count,0))returnOrderCount,
        SUM(IF(order_type = 3,achievement_value,0))increaseOrderAchievement,
        SUM(IF(order_type = 3,order_count,0))increaseOrderCount
        FROM cxr_employee_achievement_detail
        WHERE delete_status = '0'
        AND site_id IN
        <foreach collection="ids" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        <if test="bo.startDate != null">
            AND order_date >= #{bo.startDate}
        </if>
        <if test="bo.endDate != null">
            AND order_date &lt; #{bo.endDate}
        </if>
    </select>


    <select id="regionList" resultType="com.ruoyi.core.base.domain.vo.CxrEmployeeOrderCountVo">
        SELECT region.id,region.name cxrRootRegionName,ifnull(detail.achievement_value,0)  as achievement_value
        FROM cxr_region region
        left join
        (
            SELECT sum(IFNULL(detail.achievement_value,0)) as achievement_value,site.cxr_region_id
            FROM cxr_employee_achievement_detail detail
            left join cxr_site site on detail.site_id = site.id
            WHERE detail.delete_status = '0'
            AND detail.order_date >= #{bo.startDate}
            AND detail.order_date &lt; #{bo.endDate}
            GROUP BY cxr_region_id
        ) detail on detail.cxr_region_id = region.id
        WHERE region.delete_status = '0'
        <if test="bo.cxrRootRegionName != null and bo.cxrRootRegionName != ''">
            AND region.name LIKE CONCAT('%',#{bo.cxrRootRegionName},'%')
        </if>
        <if test="bo.regionIds != null and bo.regionIds.size() > 0">
            AND region.id IN
            <foreach collection="bo.regionIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        order by achievement_value desc

    </select>

    <select id="siteList" resultType="com.ruoyi.core.base.domain.vo.CxrEmployeeOrderCountVo">
        SELECT id,cxr_root_region_id
        FROM cxr_site
        WHERE delete_status = '0'
        AND cxr_root_region_id IN
        <foreach collection="ids" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="siteIds" resultType="java.lang.Long">
        SELECT s.id FROM cxr_site s
        LEFT JOIN cxr_region r ON r.id = s.cxr_root_region_id
        WHERE s.delete_status = '0'
        AND s.cxr_root_region_id IN
        <foreach collection="ids" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>

        <if test="bo.cxrRootRegionName != null and bo.cxrRootRegionName != ''">
            AND r.name LIKE CONCAT('%',#{bo.cxrRootRegionName},'%')
        </if>

    </select>

    <select id="employeeList_bak" resultType="com.ruoyi.core.base.domain.vo.CxrEmployeeOrderCountVo">
        SELECT e.id,e.name employeeName,IFNULL(a.site_id,e.cxr_site_id)site_id,a.*
        FROM cxr_employee e
        LEFT JOIN (
        SELECT employee_id,site_id,any_value(employee_name)employee_name
        SUM(IF(order_type = 0 OR order_type = 70,order_count,0)) newOrderCount,
        SUM(IF(order_type = 0 OR order_type = 70,achievement_value,0)) newOrderAchievement,
        SUM(IF(order_type = 1 OR order_type = 71,achievement_value,0))continueOrderAchievement,
        SUM(IF(order_type = 1 OR order_type = 70,order_count,0))continueOrderCount,
        SUM(IF(order_type = 2,achievement_value,0))returnOrderAchievement,
        SUM(IF(order_type = 2,order_count,0))returnOrderCount,
        SUM(IF(order_type = 3,achievement_value,0))increaseOrderAchievement,
        SUM(IF(order_type = 3,order_count,0))increaseOrderCount
        FROM cxr_employee_achievement_detail a
        WHERE delete_status = '0'
        <if test="bo.siteId != null">
            AND a.siteId = #{bo.siteId}
        </if>
        <if test="bo.startDate != null">
            AND order_date >= #{bo.startDate}
        </if>
        <if test="bo.endDate != null">
            AND order_date &lt; #{bo.endDate}
        </if>
        AND (
        site_id IN
        <foreach collection="bo.siteIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        <if test="bo.groupIds != null and bo.groupIds.size() > 0">
            OR group_id IN
            <foreach collection="bo.groupIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        )

        GROUP BY employee_id,site_id
        )a ON e.id = a.employee_id
        WHERE e.delete_status = '0'
        <if test="bo.employeeName != null and bo.employeeName != ''">
            AND (e.name = CONCAT(#{bo.employeeName},'%') OR a.employee_name = CONCAT(#{bo.employeeName},'%'))
        </if>

    </select>

    <select id="employeeList2" resultType="com.ruoyi.core.base.domain.vo.CxrEmployeeOrderCountVo">
        SELECT * FROM (
        (SELECT id employee_id,cxr_site_id site_id,cxr_group_id group_id,name employee_name
        FROM cxr_employee
        WHERE delete_status = '0'
        AND (
        cxr_site_id IN
        <foreach collection="bo.siteIds" open="(" close=")" separator="," item="item">#{item}
        </foreach>
        <if test="bo.groupIds != null and bo.groupIds.size() > 0">OR cxr_group_id IN
            <foreach collection="bo.groupIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        )
        <if test="bo.siteId != null">
            AND cxr_site_id = #{bo.siteId}
        </if>
        <if test="bo.employeeName != null and bo.employeeName != ''">
            AND name LIKE CONCAT('%',#{bo.employeeName},'%')
        </if>
        <if test="bo.groupId != null">
            AND cxr_group_id = #{bo.groupId}
        </if>

        )
        UNION
        (
        SELECT employee_id,site_id,group_id,any_value(employee_name)employee_name
        FROM cxr_employee_achievement_detail a
        WHERE delete_status = '0'
        <if test="bo.startDate != null">AND order_date >= #{bo.startDate}
        </if>
        <if test="bo.endDate != null">AND order_date &lt; #{bo.endDate}
        </if>
        GROUP BY employee_id,site_id,group_id
        )
        )f LEFT JOIN (
        SELECT employee_id,site_id,group_id,
        SUM(IF(order_type = 0 OR order_type = 70,order_count,0)) newOrderCount,
        SUM(IF(order_type = 0 OR order_type = 70,achievement_value,0)) newOrderAchievement,
        SUM(IF(order_type = 1 OR order_type = 71,achievement_value,0))continueOrderAchievement,
        SUM(IF(order_type = 1 OR order_type = 70,order_count,0))continueOrderCount,
        SUM(IF(order_type = 2,achievement_value,0))returnOrderAchievement,
        SUM(IF(order_type = 2,order_count,0))returnOrderCount,
        SUM(IF(order_type = 3,achievement_value,0))increaseOrderAchievement,
        SUM(IF(order_type = 3,order_count,0))increaseOrderCount
        FROM cxr_employee_achievement_detail a
        WHERE delete_status = '0'
        <if test="bo.startDate != null">
            AND order_date >= #{bo.startDate}
        </if>
        <if test="bo.endDate != null">
            AND order_date &lt; #{bo.endDate}
        </if>
        GROUP BY employee_id,site_id,group_id

        )g ON f.employee_id = g.employee_id AND f.site_id = g.site_id
    </select>
    <select id="employeeList" resultType="com.ruoyi.core.base.domain.vo.CxrEmployeeOrderCountVo">
        select emp.employee_id,emp.site_id,emp.group_id, sum(ifnull(emp.achievement_value, 0)) as achievement_value
        from
        (
            (
                SELECT id employee_id,cxr_site_id site_id,cxr_group_id group_id,0 as achievement_value
                FROM cxr_employee
                WHERE delete_status = '0' AND (occupation_status = '2' OR (
                quit_time >= #{bo.startDate} AND quit_time &lt;= #{bo.endDate}
                ))
                AND (
                cxr_site_id IN
                <foreach collection="bo.siteIds" open="(" close=")" separator="," item="item">#{item}
                </foreach>
                <if test="bo.groupIds != null and bo.groupIds.size() > 0">OR cxr_group_id IN
                    <foreach collection="bo.groupIds" open="(" close=")" separator="," item="item">
                        #{item}
                    </foreach>
                </if>
                )
                <if test="bo.siteId != null">
                    AND cxr_site_id = #{bo.siteId}
                </if>
                <if test="bo.andSiteIds != null and bo.andSiteIds.size() > 0">
                    AND cxr_site_id IN
                    <foreach collection="bo.andSiteIds" open="(" close=")" separator="," item="item">#{item}</foreach>
                </if>
                <if test="bo.siteName != null">
                    AND site_name LIKE CONCAT('%',#{bo.siteName},'%')
                </if>
                <if test="bo.employeeName != null and bo.employeeName != ''">
                    AND name LIKE CONCAT('%',#{bo.employeeName},'%')
                </if>
                <if test="bo.groupId != null">
                    AND cxr_group_id = #{bo.groupId}
                </if>
            )
            UNION
            (
                SELECT employee_id,site_id,group_id,sum(IFNULL(achievement_value, 0)) as achievement_value
                FROM cxr_employee_achievement_detail
                WHERE delete_status = '0'
                AND (
                site_id IN
                <foreach collection="bo.siteIds" open="(" close=")" separator="," item="item">#{item}
                </foreach>
                <if test="bo.groupIds != null and bo.groupIds.size() > 0">OR group_id IN
                    <foreach collection="bo.groupIds" open="(" close=")" separator="," item="item">
                        #{item}
                    </foreach>
                </if>
                )

                <if test="bo.andSiteIds != null and bo.andSiteIds.size() > 0">
                    AND site_id IN
                    <foreach collection="bo.andSiteIds" open="(" close=")" separator="," item="item">#{item}</foreach>
                </if>
                <if test="bo.siteId != null">
                    AND site_id = #{bo.siteId}
                </if>
                <if test="bo.startDate != null">AND order_date >= #{bo.startDate}
                </if>
                <if test="bo.endDate != null">AND order_date &lt; #{bo.endDate}
                </if>
                <if test="bo.employeeName != null and bo.employeeName != ''">
                    AND employee_name LIKE CONCAT('%',#{bo.employeeName},'%')
                </if>
                <if test="bo.groupId != null">
                    AND group_id = #{bo.groupId}
                </if>
                GROUP BY employee_id,site_id,group_id
            )
        ) emp
        GROUP BY
        emp.employee_id,
        emp.site_id,
        emp.group_id
        order by
        achievement_value desc
    </select>

    <select id="getEmployeeName" resultType="com.ruoyi.business.base.api.domain.CxrEmployee">
        SELECT id,real_name FROM cxr_employee WHERE (delete_status = '0' AND id IN
        <foreach collection="ids" open="(" close=")" separator="," item="item">#{item}</foreach>
        )
    </select>

    <select id="getGroupName" resultType="com.ruoyi.business.base.api.domain.CxrGroup">
        SELECT id,name FROM cxr_group WHERE (delete_status = '0' AND id IN
        <foreach collection="ids" open="(" close=")" separator="," item="item">#{item}</foreach>
        )
    </select>

    <select id="queryAchievement" resultType="com.ruoyi.core.base.domain.vo.CxrEmployeeOrderCountVo">
        SELECT CONCAT_WS('',employee_id,site_id,group_id) keyStr,
        SUM(IF(order_type = 0 OR order_type = 70,order_count,0)) newOrderCount,
        SUM(IF(order_type = 0 OR order_type = 70,achievement_value,0)) newOrderAchievement,
        SUM(IF(order_type = 1 OR order_type = 71,achievement_value,0))continueOrderAchievement,
        SUM(IF(order_type = 1 OR order_type = 70,order_count,0))continueOrderCount,
        SUM(IF(order_type = 2,achievement_value,0))returnOrderAchievement,
        SUM(IF(order_type = 2,order_count,0))returnOrderCount,
        SUM(IF(order_type = 3,achievement_value,0))increaseOrderAchievement,
        SUM(IF(order_type = 3,order_count,0))increaseOrderCount
        FROM cxr_employee_achievement_detail a
        WHERE delete_status = '0'
        AND (
        site_id IN
        <foreach collection="bo.siteIds" open="(" close=")" separator="," item="item">#{item}
        </foreach>
        <if test="bo.groupIds != null and bo.groupIds.size() > 0">OR group_id IN
            <foreach collection="bo.groupIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        )
        <if test="bo.startDate != null">
            AND order_date >= #{bo.startDate}
        </if>
        <if test="bo.endDate != null">
            AND order_date &lt; #{bo.endDate}
        </if>
        GROUP BY employee_id,site_id,group_id
    </select>

</mapper>
