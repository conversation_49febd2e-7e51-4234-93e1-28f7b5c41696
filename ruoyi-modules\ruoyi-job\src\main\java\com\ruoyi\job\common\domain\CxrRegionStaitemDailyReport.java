package com.ruoyi.job.common.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 区域单数统计明细日报表
 * @TableName cxr_region_staitem_daily_report
 */
@TableName(value ="cxr_region_staitem_daily_report")
@Data
public class CxrRegionStaitemDailyReport {
    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 统计日期
     */
    private Date reportDate;

    /**
     * 星期
     */
    private String weekDay;

    /**
     * 维度类型：AGENT-代理,SITE-站点,REGION-区域,ROOT_REGION-大区,COMPANY-公司
     */
    private String dimensionType;

    /**
     * 公司ID
     */
    private Long companyId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 大区ID
     */
    private Long rootRegionId;

    /**
     * 大区名称
     */
    private String rootRegionName;

    /**
     * 区域ID
     */
    private Long regionId;

    /**
     * 区域名称
     */
    private String regionName;

    /**
     * 区域编号
     */
    private String regionCode;

    /**
     * 站点ID
     */
    private Long siteId;

    /**
     * 站点名称
     */
    private String siteName;

    /**
     * 站点编号
     */
    private String siteCode;

    /**
     * 销售代理ID
     */
    private Long agentId;

    /**
     * 销售代理姓名
     */
    private String agentName;

    /**
     * 代理编号
     */
    private String agentCode;

    /**
     * 代理等级
     */
    private String agentLevel;

    /**
     * 新订单数
     */
    private BigDecimal newOrderCount;

    /**
     * 续订单数
     */
    private BigDecimal continueOrderCount;

    /**
     * 增订单数
     */
    private BigDecimal increaseOrderCount;

    /**
     * 退订单数
     */
    private BigDecimal returnOrderCount;

    /**
     * 总订单数
     */
    private BigDecimal totalOrderCount;

    /**
     * 新订单业绩
     */
    private BigDecimal newOrderAchievement;

    /**
     * 续订单业绩
     */
    private BigDecimal continueOrderAchievement;

    /**
     * 增订单业绩
     */
    private BigDecimal increaseOrderAchievement;

    /**
     * 退订单业绩
     */
    private BigDecimal returnOrderAchievement;

    /**
     * 总业绩
     */
    private BigDecimal totalAchievement;

    /**
     * 新订单占比(%)
     */
    private BigDecimal newOrderRatio;

    /**
     * 续订单占比(%)
     */
    private BigDecimal continueOrderRatio;

    /**
     * 增订单占比(%)
     */
    private BigDecimal increaseOrderRatio;

    /**
     * 退订单占比(%)
     */
    private BigDecimal returnOrderRatio;

    /**
     * 新订单客单价
     */
    private BigDecimal newOrderUnitPrice;

    /**
     * 续订单客单价
     */
    private BigDecimal continueOrderUnitPrice;

    /**
     * 增订单客单价
     */
    private BigDecimal increaseOrderUnitPrice;

    /**
     * 总平均客单价
     */
    private BigDecimal avgUnitPrice;

    /**
     * 新订单数同比(%)
     */
    private BigDecimal newOrderCountYoy;

    /**
     * 续订单数同比(%)
     */
    private BigDecimal continueOrderCountYoy;

    /**
     * 增订单数同比(%)
     */
    private BigDecimal increaseOrderCountYoy;

    /**
     * 退订单数同比(%)
     */
    private BigDecimal returnOrderCountYoy;

    /**
     * 总订单数同比(%)
     */
    private BigDecimal totalOrderCountYoy;

    /**
     * 新订单业绩同比(%)
     */
    private BigDecimal newOrderAchievementYoy;

    /**
     * 续订单业绩同比(%)
     */
    private BigDecimal continueOrderAchievementYoy;

    /**
     * 增订单业绩同比(%)
     */
    private BigDecimal increaseOrderAchievementYoy;

    /**
     * 退订单业绩同比(%)
     */
    private BigDecimal returnOrderAchievementYoy;

    /**
     * 总业绩同比(%)
     */
    private BigDecimal totalAchievementYoy;

    /**
     * 新订单数环比(%)
     */
    private BigDecimal newOrderCountMom;

    /**
     * 续订单数环比(%)
     */
    private BigDecimal continueOrderCountMom;

    /**
     * 增订单数环比(%)
     */
    private BigDecimal increaseOrderCountMom;

    /**
     * 退订单数环比(%)
     */
    private BigDecimal returnOrderCountMom;

    /**
     * 总订单数环比(%)
     */
    private BigDecimal totalOrderCountMom;

    /**
     * 新订单业绩环比(%)
     */
    private BigDecimal newOrderAchievementMom;

    /**
     * 续订单业绩环比(%)
     */
    private BigDecimal continueOrderAchievementMom;

    /**
     * 增订单业绩环比(%)
     */
    private BigDecimal increaseOrderAchievementMom;

    /**
     * 退订单业绩环比(%)
     */
    private BigDecimal returnOrderAchievementMom;

    /**
     * 总业绩环比(%)
     */
    private BigDecimal totalAchievementMom;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除状态(0-正常,2-删除)
     */
    private String deleteStatus;
}
