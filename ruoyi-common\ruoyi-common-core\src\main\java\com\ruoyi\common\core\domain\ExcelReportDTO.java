package com.ruoyi.common.core.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class ExcelReportDTO implements Serializable {

    /**
     * 文件代码
     */
    private String htmlName;

    /**
     * 文件目录
     */
    private String fileDir;

    /**
     * 文件后缀
     */
    private String fileNameSuffix;

    /**
     * 导出数据数量
     */
    private Long dataTotal;

    /**
     * 创建人
     */
    private Long createById;

    /**
     * 创建类型
     */
    private String createByType;

    /**
     * 创建人名字
     */
    private String createByName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 唯一标识
     */
    private String uniqueMark;

    /**
     * 增量数据
     */
    private Long incrementData;

    private String errMessage;

    /**
     * 类型 0 保存excel导出  1 保存增量导出  2 异常
     */
    private int type = 0;

    private String lockKey;

    private Integer retainStatus;
}
