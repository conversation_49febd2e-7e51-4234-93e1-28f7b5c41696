package com.ruoyi.calculate.customerOrderStatistics.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.business.base.api.domain.CxrEmployee;
import com.ruoyi.business.base.api.domain.CxrRegion;
import com.ruoyi.business.base.api.domain.CxrSite;
import com.ruoyi.business.base.api.domain.json.BusinessAgent;
import com.ruoyi.business.base.api.dubbo.RemoteCxrReportFormsExportService;
import com.ruoyi.business.base.api.dubbo.RemoteEmployeeService;
import com.ruoyi.business.base.api.dubbo.RemoteSiteService;
import com.ruoyi.business.base.api.enums.OrderTypeEnums;
import com.ruoyi.calculate.common.mapper.CxrRegionMapper;
import com.ruoyi.calculate.common.mapper.SysDeptMapper;
import com.ruoyi.calculate.customerOrderStatistics.service.CustomerOrderStatisticsService;
import com.ruoyi.common.core.domain.ExcelReportDTO;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.enums.DeleteStatus;
import com.ruoyi.common.core.enums.UserType;
import com.ruoyi.common.excel.utils.ExcelExportObj;
import com.ruoyi.common.excel.utils.ExcelMqUtil;
import com.ruoyi.common.satoken.utils.helper.LoginHelper;
import com.ruoyi.core.base.domain.vo.ActivityGoodsVo;
import com.ruoyi.order.api.RemoteOrderService;
import com.ruoyi.order.api.domain.bo.CustomerOrderStatisticsBo;
import com.ruoyi.order.api.domain.vo.CustomerOrderStatisticsExportVo;
import com.ruoyi.order.api.domain.vo.CustomerOrderStatisticsVo;
import com.ruoyi.order.api.domain.vo.CustomerOrderTotal;
import com.ruoyi.order.api.enums.OrderAuditStatusEnums;
import com.ruoyi.system.api.domain.SysDept;
import com.ruoyi.system.api.model.LoginUser;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

@GlobalTransactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
@Service
public class CustomerOrderStatisticsServiceImpl implements CustomerOrderStatisticsService {

    @DubboReference
    private RemoteOrderService remoteOrderService;
    @DubboReference
    private RemoteSiteService remoteSiteService;
    @DubboReference
    private RemoteEmployeeService remoteEmployeeService;

    @DubboReference
    private RemoteCxrReportFormsExportService remoteExportService;

    private final SysDeptMapper sysDeptMapper;

    private final CxrRegionMapper regionMapper;

    @Override
    public Object orderStatistics(CustomerOrderStatisticsBo bo, PageQuery pageQuery) {

        if (ObjectUtil.isEmpty(pageQuery.getPageSize())) {
            pageQuery.setPageSize(20);
        }

        LoginUser loginUser = LoginHelper.getLoginUser();
        queryParameter(loginUser, bo);

        IPage<CustomerOrderStatisticsVo> statisticsVoIPage = remoteOrderService.orderStatistics(bo, pageQuery.build());

        List<CustomerOrderStatisticsVo> records = oredrStatisticsComputer(
            statisticsVoIPage.getRecords(), bo);

        /**
         *  第一版
         * CustomerOrderTotal statistics = remoteOrderService.TotalOrderStatistics(bo);
         */
        CustomerOrderTotal statistics = remoteOrderService.TotalOrderStatisticsUpgrade(bo);
        handleTotalOrderStatistics(statistics, bo);

        Map<String, Object> data = new HashMap<>();

        data.put("page", statisticsVoIPage.getPages());
        int recordSum = records.size() - (int) statisticsVoIPage.getTotal();

        data.put("total", statisticsVoIPage.getTotal());
        data.put("splitTotal", statistics.getSplitTotal());

        if (ObjectUtil.isNotEmpty(bo.getProxyIds())) {
            int sum = (int) statisticsVoIPage.getTotal() - records.size();
            data.put("total", statisticsVoIPage.getTotal() - sum);
        }
        data.put("sum", recordSum);
        data.put("data", records);
        data.put("statistics", statistics);
        data.put("size", statisticsVoIPage.getSize());

        return data;
    }

    @Override
    public void orderStatisticsExport(CustomerOrderStatisticsBo bo, HttpServletResponse response) {
        //😁😁😁
        int pageSize = 1000;
        long pageCount = 1;
        LoginUser loginUser = null;
        if (ObjectUtil.isEmpty(bo.getRetainStatus())) {
            loginUser = LoginHelper.getLoginUser();
            queryParameter(loginUser, bo);
        }

        CustomerOrderTotal statistics = remoteOrderService.TotalOrderStatisticsUpgrade(bo);
        List<CustomerOrderStatisticsVo> records = new ArrayList<>();

        Long splitTotal = statistics.getSplitTotal();
        ExcelReportDTO excelReportDTO = null;
        if (ObjectUtil.isNotEmpty(bo.getRetainStatus()) && bo.getRetainStatus() == 1) {
            excelReportDTO = ExcelMqUtil.excelSendRecordPermanent(bo.getHtmlName(), splitTotal, 0L,
                UserType.SYS_USER.getUserType(), "自动导出");
        } else {
            excelReportDTO = ExcelMqUtil.excelSendRecord(bo.getHtmlName(), splitTotal, loginUser.getUserId(),
                loginUser.getUserType(), loginUser.getUserName());
        }

        for (int current = 1; current <= pageCount; current++) {
            PageQuery query = new PageQuery();
            query.setPageNum(current);
            query.setPageSize(pageSize);
            IPage<CustomerOrderStatisticsVo> statisticsVoIPage = remoteOrderService.orderStatistics(bo,
                query.build());
            long total = splitTotal;
            pageCount = total % pageSize > 0 ? (total / pageSize + 1) : total / pageSize;

            List<CustomerOrderStatisticsVo> computer = oredrStatisticsComputer(
                statisticsVoIPage.getRecords(), bo);
            records.addAll(computer);
            //提取配送站点ID
            List<Long> deliverySiteIds = records.stream()
                .map(CustomerOrderStatisticsVo::getDeliverySiteId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(deliverySiteIds)) {
                List<CxrSite> cxrSites = remoteSiteService.queryIds(deliverySiteIds);
                Map<Long, CxrSite> deliverySiteMap = cxrSites.stream()
                    .collect(Collectors.toMap(CxrSite::getId, Function.identity()));
                records.forEach(record -> {
                    CxrSite cxrSite = deliverySiteMap.get(record.getDeliverySiteId());
                    if (cxrSite != null) {
                        record.setDeliverySiteNo(cxrSite.getSiteMark());
                    }
                });
            }
            ExcelMqUtil.excelSendIncrementData(excelReportDTO.getUniqueMark(),
                Long.valueOf(computer.size()), null);
        }
        excelExportOredrStatistics(records, response, excelReportDTO);
    }

    @Override
    public Object orderFixationStatistics(CustomerOrderStatisticsBo bo, PageQuery pageQuery) {
        if (ObjectUtil.isEmpty(pageQuery.getPageSize())) {
            pageQuery.setPageSize(20);
        }

        LoginUser loginUser = LoginHelper.getLoginUser();
        queryParameter(loginUser, bo);

        IPage<CustomerOrderStatisticsVo> statisticsVoIPage = remoteOrderService.orderFixationStatistics(bo, pageQuery.build());

        List<CustomerOrderStatisticsVo> records = oredrStatisticsComputer(
            statisticsVoIPage.getRecords(), bo);

        CustomerOrderTotal statistics = remoteOrderService.TotalOrderFixationStatisticsUpgrade(bo);
        handleTotalOrderStatistics(statistics, bo);

        Map<String, Object> data = new HashMap<>();

        data.put("page", statisticsVoIPage.getPages());
        int recordSum = records.size() - (int) statisticsVoIPage.getTotal();

        data.put("total", statisticsVoIPage.getTotal());
        data.put("splitTotal", statistics.getSplitTotal());

        if (ObjectUtil.isNotEmpty(bo.getProxyIds())) {
            int sum = (int) statisticsVoIPage.getTotal() - records.size();
            data.put("total", statisticsVoIPage.getTotal() - sum);
        }
        data.put("sum", recordSum);
        data.put("data", records);
        data.put("statistics", statistics);
        data.put("size", statisticsVoIPage.getSize());

        return data;
    }

    @Override
    public void orderFixationStatisticsExport(CustomerOrderStatisticsBo bo, HttpServletResponse response) {
//😁😁😁
        int pageSize = 1000;
        long pageCount = 1;
        LoginUser loginUser = LoginHelper.getLoginUser();
        queryParameter(loginUser, bo);

        CustomerOrderTotal statistics = remoteOrderService.TotalOrderFixationStatisticsUpgrade(bo);
        List<CustomerOrderStatisticsVo> records = new ArrayList<>();

        Long splitTotal = statistics.getSplitTotal();

        ExcelReportDTO excelReportDTO = ExcelMqUtil.excelSendRecord(bo.getHtmlName(), splitTotal, loginUser.getUserId(),
            loginUser.getUserType(), loginUser.getUserName());
        for (int current = 1; current <= pageCount; current++) {
            PageQuery query = new PageQuery();
            query.setPageNum(current);
            query.setPageSize(pageSize);
            IPage<CustomerOrderStatisticsVo> statisticsVoIPage = remoteOrderService.orderFixationStatistics(bo,
                query.build());
            long total = splitTotal;
            pageCount = total % pageSize > 0 ? (total / pageSize + 1) : total / pageSize;

            List<CustomerOrderStatisticsVo> computer = oredrStatisticsComputer(
                statisticsVoIPage.getRecords(), bo);
            records.addAll(computer);
            //提取配送站点ID
            List<Long> deliverySiteIds = records.stream()
                .map(CustomerOrderStatisticsVo::getDeliverySiteId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(deliverySiteIds)) {
                List<CxrSite> cxrSites = remoteSiteService.queryIds(deliverySiteIds);
                Map<Long, CxrSite> deliverySiteMap = cxrSites.stream()
                    .collect(Collectors.toMap(CxrSite::getId, Function.identity()));
                records.forEach(record -> {
                    CxrSite cxrSite = deliverySiteMap.get(record.getDeliverySiteId());
                    if (cxrSite != null) {
                        record.setDeliverySiteNo(cxrSite.getSiteMark());
                    }
                });
            }
            ExcelMqUtil.excelSendIncrementData(excelReportDTO.getUniqueMark(),
                Long.valueOf(computer.size()), null);
        }
        excelExportOredrStatistics(records, response, excelReportDTO);
    }

    @Override
    public List<LocalDateTime> getMirrorImageTime() {
        return remoteOrderService.getMirrorImageTime();
    }


    private List<CustomerOrderStatisticsVo> oredrStatisticsComputer(List<CustomerOrderStatisticsVo> data,
                                                                    CustomerOrderStatisticsBo bo) {
        CxrEmployee cxrEmployee = new CxrEmployee();
        List<CustomerOrderStatisticsVo> records = new ArrayList<>();
        if (data.size() > 0) {
            for (CustomerOrderStatisticsVo item : data) {
                String giveGiftList = item.getGiveGiftList();
                boolean jsonFlag = JSONUtil.isTypeJSON(giveGiftList);
                if (StrUtil.isNotEmpty(giveGiftList) && jsonFlag) {

                    List<ActivityGoodsVo> goodsVos = JSONUtil.toList(giveGiftList, ActivityGoodsVo.class);
                    if (CollUtil.isNotEmpty(goodsVos)) {
                        String goodsName = goodsVos.stream().map(ActivityGoodsVo::getGoodsName)
                            .collect(Collectors.joining(","));
                        item.setActivityGiftName(goodsName);
                        String giftsum = goodsVos.stream().map(x -> {
                            return x.getGiftSum().toString();
                        }).collect(Collectors.joining(","));
                        item.setActivityGiftSum(giftsum);
                    }
                }
                if (item.getOrderType() == OrderTypeEnums.RETURN_ORDER.getValue()) {
                    item.setFreshMilkSentQuantity(new BigDecimal(0));
                }

                List<BusinessAgent> businessAgents = JSONUtil.toList(item.getBusinessAgent(), BusinessAgent.class);
                if (businessAgents.size() > 1) { // 拆单系列
                    int size = businessAgents.size();
                    BigDecimal Amount = BigDecimal.valueOf(0); // 拆分后
                    BigDecimal ApportionMoney = BigDecimal.valueOf(0); // 拆分后

                    BigDecimal ExcessQuantity = BigDecimal.valueOf(0);// 拆分后
                    BigDecimal OrderQuantity = BigDecimal.valueOf(0);// 拆分后

                    BigDecimal DesignQuantity = BigDecimal.valueOf(0);// 拆分后
                    BigDecimal FreshMilkGiveQuantity = BigDecimal.valueOf(0);// 拆分后
                    BigDecimal LongMilkGiveQuantity = BigDecimal.valueOf(0);// 拆分后
                    BigDecimal FreshMilkSentQuantity = BigDecimal.valueOf(0);// 拆分后
                    BigDecimal LongMilkSentQuantity = BigDecimal.valueOf(0);// 拆分后

                    BigDecimal amount = item.getAmount();
                    BigDecimal apportionMoney = Convert.toBigDecimal(item.getApportionMoney(), BigDecimal.ZERO);
                    BigDecimal excessQuantity = item.getExcessQuantity();
                    BigDecimal orderQuantity = item.getOrderQuantity();
                    BigDecimal designQuantity = item.getDesignQuantity();
                    BigDecimal freshMilkGiveQuantity = item.getFreshMilkGiveQuantity();
                    BigDecimal longMilkGiveQuantity = item.getLongMilkGiveQuantity();
                    BigDecimal freshMilkSentQuantity = item.getFreshMilkSentQuantity();
                    BigDecimal longMilkSentQuantity = item.getLongMilkSentQuantity();
                    List<Long> regionIds = bo.getRegionIds();

                    String itemStr = JSONObject.toJSONString(item);
                    for (BusinessAgent businessAgent : businessAgents) {

                        item = JSONObject.parseObject(itemStr, CustomerOrderStatisticsVo.class);
                        if (ObjectUtil.isNotEmpty(bo.getProxyIds())) { // 销售代理模糊查询
                            if (bo.getProxyIds().size() > 1) {
                                List<Long> proxyIds = bo.getProxyIds();
                                if (proxyIds.contains(businessAgent.getProxyId())) {
                                    continue;
                                } else {
                                    break;
                                }
                            }
                        }

                        if (businessAgents.get(size - 1).getProxyId()
                            .equals(businessAgent.getProxyId())) {  // 最后一条数据处理
                            item.setBusinessAgent(JSONUtil.toJsonStr(Arrays.asList(businessAgents.get(size - 1))));
                            BusinessAgent businessAgent1 = businessAgents.get(size - 1);
                            if (ObjectUtil.isEmpty(businessAgent1.getSiteName()) || !businessAgent1.getSiteName()
                                .equals(item.getSiteName())) {
//                                CxrSite site = remoteSiteService.selectOneByEmployeeId(businessAgent.getProxyId());
                                CxrSite site = remoteSiteService.selectOneByEmployee(businessAgent);
                                if (ObjectUtil.isNotEmpty(site)) {
                                    handleOtherSite(site, item);
                                } else {
                                    item.setSiteName(businessAgent.getSiteName());
                                }
                            }
                            if (ObjectUtil.isNotEmpty(businessAgent.getRegionName())) {
                                item.setBigAreaName(businessAgent.getRegionName());
                            }
                            item.setLongMilkSentQuantity(longMilkSentQuantity);
                            item.setFreshMilkSentQuantity(freshMilkSentQuantity);
                            item.setFreshMilkGiveQuantity(freshMilkGiveQuantity);
                            item.setLongMilkGiveQuantity(longMilkGiveQuantity);
                            item.setOrderQuantity(orderQuantity);
                            item.setDesignQuantity(designQuantity);
                            item.setExcessQuantity(excessQuantity);
                            item.setAmount(amount);
                            item.setApportionMoney(apportionMoney);
                            if (item.getOrderType() == OrderTypeEnums.CHANGE_ORDER.getValue()) {
                                item.setRemark(
                                    StrUtil.format("由{}转到{},其中{}为客户地址信息", item.getCustomerName(),
                                        item.getCustomerNameSwitch(), item.getCustomerAdressSwitch()));
                            }

                            if (ObjectUtil.isNotEmpty(bo.getSiteName())) {
                                List<BusinessAgent> agents = JSONUtil.toList(item.getBusinessAgent(),
                                    BusinessAgent.class);
                                BusinessAgent agent = agents.get(0);
                                if (bo.getSiteName().equals(agent.getSiteName())) {
                                    if (CollUtil.isNotEmpty(regionIds)) {
                                        if (ObjectUtil.isNotEmpty(agent.getRegionId()) && regionIds.contains(agent.getRegionId())) {
                                            records.add(item);
                                        }
                                    } else {
                                        records.add(item);
                                    }
                                }
                            } else {
                                if (CollUtil.isNotEmpty(regionIds)) {
                                    if (ObjectUtil.isNotEmpty(businessAgent.getRegionId()) && regionIds.contains(businessAgent.getRegionId())) {
                                        records.add(item);
                                    }
                                } else {
                                    records.add(item);
                                }
                            }
                        } else {

                            if (ObjectUtil.isNotEmpty(bo.getProxyIds()) && bo.getProxyIds().size() == 1) {
                                cxrEmployee = remoteEmployeeService.getById(bo.getProxyIds().get(0));
                            }

                            CustomerOrderStatisticsVo customerOrderStatisticsVo = new CustomerOrderStatisticsVo();
                            BeanUtils.copyProperties(item, customerOrderStatisticsVo);
                            customerOrderStatisticsVo.setBusinessAgent(
                                JSONUtil.toJsonStr(Arrays.asList(businessAgent)));

                            if (ObjectUtil.isEmpty(businessAgent.getSiteName()) || !businessAgent.getSiteName()
                                .equals(item.getSiteName())) {
//                                CxrSite site = remoteSiteService.selectOneByEmployeeId(businessAgent.getProxyId());
                                CxrSite site = remoteSiteService.selectOneByEmployee(businessAgent);
                                if (ObjectUtil.isNotEmpty(site)) {
                                    handleOtherSite(site, customerOrderStatisticsVo);
                                } else {
                                    customerOrderStatisticsVo.setSiteName(businessAgent.getSiteName());
                                }
                            }
                            // 代理站点
                            if (ObjectUtil.isNotEmpty(item.getLongMilkSentQuantity())) {
                                if (LongMilkSentQuantity.compareTo(new BigDecimal(0)) == 0) {

                                    LongMilkSentQuantity = NumberUtil.div(item.getLongMilkSentQuantity(), size, 2);
                                }

                                customerOrderStatisticsVo.setLongMilkSentQuantity(LongMilkSentQuantity);
                            }

                            if (ObjectUtil.isNotEmpty(item.getFreshMilkSentQuantity())) {
                                if (FreshMilkSentQuantity.compareTo(new BigDecimal(0)) == 0) {

                                    FreshMilkSentQuantity = NumberUtil.div(item.getFreshMilkSentQuantity(), size,
                                        2);
                                }
                                customerOrderStatisticsVo.setFreshMilkSentQuantity(FreshMilkSentQuantity);
                            }

                            if (ObjectUtil.isNotEmpty(item.getLongMilkGiveQuantity())) {
                                if (LongMilkGiveQuantity.compareTo(new BigDecimal(0)) == 0) {

                                    LongMilkGiveQuantity = NumberUtil.div(item.getLongMilkGiveQuantity(), size,
                                        2);// , RoundingMode.DOWN
                                }
                                customerOrderStatisticsVo.setLongMilkGiveQuantity(LongMilkGiveQuantity);
                            }

                            if (ObjectUtil.isNotEmpty(item.getFreshMilkGiveQuantity())) {
                                if (FreshMilkGiveQuantity.compareTo(new BigDecimal(0)) == 0) {

                                    FreshMilkGiveQuantity = NumberUtil.div(item.getFreshMilkGiveQuantity(), size,
                                        2);
                                }
                                customerOrderStatisticsVo.setFreshMilkGiveQuantity(FreshMilkGiveQuantity);
                            }
                            if (ObjectUtil.isNotEmpty(item.getOrderQuantity())) {
                                if (OrderQuantity.compareTo(new BigDecimal(0)) == 0) {

                                    OrderQuantity = NumberUtil.div(item.getOrderQuantity(), size, 2);
                                }
                                customerOrderStatisticsVo.setOrderQuantity(OrderQuantity);
                            }
                            if (ObjectUtil.isNotEmpty(item.getDesignQuantity())) {
                                if (DesignQuantity.compareTo(new BigDecimal(0)) == 0) {

                                    DesignQuantity = NumberUtil.div(item.getDesignQuantity(), size, 2);
                                }
                                customerOrderStatisticsVo.setDesignQuantity(DesignQuantity);
                            }

                            if (ObjectUtil.isNotEmpty(item.getExcessQuantity())) {
                                if (ExcessQuantity.compareTo(new BigDecimal(0)) == 0) {

                                    ExcessQuantity = NumberUtil.div(item.getExcessQuantity(), size, 2);
                                }
                                customerOrderStatisticsVo.setExcessQuantity(ExcessQuantity);
                            }
                            if (ObjectUtil.isNotEmpty(item.getAmount())) {
                                if (Amount.compareTo(new BigDecimal(0)) == 0) {

                                    Amount = NumberUtil.div(item.getAmount(), size, 2);
                                }
                                customerOrderStatisticsVo.setAmount(Amount);
                            }

                            if (ObjectUtil.isNotEmpty(item.getApportionMoney())) {
                                if (ApportionMoney.compareTo(new BigDecimal(0)) == 0) {

                                    ApportionMoney = NumberUtil.div(item.getApportionMoney(), size, 2);
                                }
                                customerOrderStatisticsVo.setApportionMoney(ApportionMoney);
                            }

                            amount = amount.subtract(Amount);
                            apportionMoney = apportionMoney.subtract(ApportionMoney);
                            excessQuantity = excessQuantity.subtract(ExcessQuantity);
                            orderQuantity = orderQuantity.subtract(OrderQuantity);

                            designQuantity = designQuantity.subtract(DesignQuantity);
                            freshMilkGiveQuantity = freshMilkGiveQuantity.subtract(FreshMilkGiveQuantity);
                            longMilkGiveQuantity = longMilkGiveQuantity.subtract(LongMilkGiveQuantity);
                            freshMilkSentQuantity = freshMilkSentQuantity.subtract(FreshMilkSentQuantity);
                            longMilkSentQuantity = longMilkSentQuantity.subtract(LongMilkSentQuantity);

                            if (ObjectUtil.isNotEmpty(businessAgent.getRegionName())) {
                                item.setBigAreaName(businessAgent.getRegionName());
                            }

                            if (item.getOrderType() == OrderTypeEnums.CHANGE_ORDER.getValue()) {
                                customerOrderStatisticsVo.setRemark(
                                    StrUtil.format("由{}转到{},其中{}为客户地址信息", item.getCustomerName(),
                                        item.getCustomerNameSwitch(), item.getCustomerAdressSwitch()));
                            }
                            // 销售代理模糊查询
                            if (ObjectUtil.isNotEmpty(bo.getProxyIds()) && bo.getProxyIds().size() == 1) {
                                if (bo.getProxyIds().get(0).equals(Long.valueOf(businessAgent.getProxyId()))) {
                                    if (CollUtil.isNotEmpty(regionIds)) {
                                        if (ObjectUtil.isNotEmpty(businessAgent.getRegionId()) && regionIds.contains(businessAgent.getRegionId())) {
                                            records.add(customerOrderStatisticsVo);
                                        }
                                    } else {
                                        records.add(customerOrderStatisticsVo);
                                    }
                                }
                            } else if (ObjectUtil.isNotEmpty(bo.getSiteName())) {
                                List<BusinessAgent> agents = JSONUtil.toList(
                                    customerOrderStatisticsVo.getBusinessAgent(), BusinessAgent.class);
                                BusinessAgent agent = agents.get(0);
                                if (bo.getSiteName().equals(agent.getSiteName())) {
                                    if (CollUtil.isNotEmpty(regionIds)) {
                                        if (ObjectUtil.isNotEmpty(businessAgent.getRegionId()) && regionIds.contains(businessAgent.getRegionId())) {
                                            records.add(customerOrderStatisticsVo);
                                        }
                                    } else {
                                        records.add(customerOrderStatisticsVo);
                                    }
                                }
                            } else {
                                if (CollUtil.isNotEmpty(regionIds)) {
                                    if (ObjectUtil.isNotEmpty(businessAgent.getRegionId()) && regionIds.contains(businessAgent.getRegionId())) {
                                        records.add(customerOrderStatisticsVo);
                                    }
                                } else {
                                    records.add(customerOrderStatisticsVo);
                                }
                            }
                        }
                    }

                } else {

                    BusinessAgent businessAgent = businessAgents.get(0);
                    if (ObjectUtil.isEmpty(businessAgent.getSiteName()) || !businessAgent.getSiteName()
                        .equals(item.getSiteName())) {
//                        CxrSite site = remoteSiteService.selectOneByEmployeeId(businessAgent.getProxyId());
                        CxrSite site = remoteSiteService.selectOneByEmployee(businessAgent);
                        if (ObjectUtil.isNotEmpty(site)) {
                            handleOtherSite(site, item);
                        } else {
                            item.setSiteName(businessAgent.getSiteName());
                        }
                    }

                    if (ObjectUtil.isNotEmpty(businessAgent.getRegionName())) {
                        item.setBigAreaName(businessAgent.getRegionName());
                    }

                    if (item.getOrderType() == OrderTypeEnums.CHANGE_ORDER.getValue()) {
                        item.setRemark(StrUtil.format("由{}转到{},其中{}为客户地址信息", item.getCustomerName(),
                            item.getCustomerNameSwitch(), item.getCustomerAdressSwitch()));
                    }
                    records.add(item);
                }
            }
            List<CustomerOrderStatisticsVo> recordnew = new ArrayList<>();
            if (records.size() > 0 && ObjectUtil.isNotEmpty(bo.getProxyIds()) && bo.getProxyIds().size() == 1) {
                if (ObjectUtil.isEmpty(cxrEmployee.getName())) {
                    cxrEmployee = remoteEmployeeService.getById(bo.getProxyIds().get(0));
                }
                for (CustomerOrderStatisticsVo record : records) {
                    List<BusinessAgent> businessAgents = JSONUtil.toList(record.getBusinessAgent(),
                        BusinessAgent.class);
                    BusinessAgent businessAgent = businessAgents.get(0);
                    if (businessAgent.getProxyNo().equals(cxrEmployee.getJobNumber())) {
                        recordnew.add(record);
                    }
                }
                records = recordnew;
            }

        }
        return records;
    }

    private void excelExportOredrStatistics(List<CustomerOrderStatisticsVo> records, HttpServletResponse response,
                                            ExcelReportDTO excelReportDTO) {

        List<List> listList = records.stream().map(x -> {
            List<Object> list = new ArrayList<>();
            list.add(x.getTerminalType());
            list.add(x.getOrderDate());
            Short orderType = x.getOrderType();
            list.add(x.getCompanyName());
            list.add(x.getBigAreaName());
            list.add(x.getProvince());
            list.add(x.getCity());
            list.add(x.getArea());
            list.add(x.getSiteName());
            List<BusinessAgent> businessAgents = JSONUtil.toList(x.getBusinessAgent(), BusinessAgent.class);
            BusinessAgent businessAgent = businessAgents.get(0);
            list.add(businessAgent.getProxyName());//名称
            list.add(businessAgent.getProxyNo());//销售代理编号
            list.add(OrderTypeEnums.getType(x.getOrderType()).getDesc());
            list.add(businessAgent.getLevel());//等级
            list.add(x.getDeliverySite());
            list.add(x.getDeliverySiteNo());
            list.add(x.getCustomerName());
            list.add(x.getCustomerPhone());
            list.add(x.getCustomerAdress());
            list.add(OrderAuditStatusEnums.getdesc(x.getAuditStatus()));// '', '待审核', '已审核', '拒绝', '不需要审核'

            if (NumberUtil.equals(orderType, OrderTypeEnums.RETURN_ORDER.getValue())) {
                list.add(x.getOrderQuantity().negate());
                list.add(0);// order==2
                list.add(x.getLongMilkGiveQuantity().compareTo(new BigDecimal(0)) == 1 ?
                    x.getLongMilkGiveQuantity().negate() : 0);//
                // order==2
                list.add(x.getDesignQuantity().negate());
                list.add(x.getExcessQuantity());
                list.add(0);
                list.add(x.getUnitPrice());
                list.add(x.getAmount().compareTo(new BigDecimal(0)) == 1 ?
                    x.getAmount().negate() : 0);  // order==2
            } else {
                list.add(x.getOrderQuantity());
                list.add(x.getFreshMilkGiveQuantity());// order==2
                list.add(x.getLongMilkGiveQuantity());// order==2
                list.add(x.getDesignQuantity());
                list.add(x.getExcessQuantity());
                list.add(x.getFreshMilkSentQuantity());
                list.add(x.getUnitPrice());
                list.add(x.getAmount());  // order==2
            }
            list.add(x.getAccountingType());
            list.add(x.getProductName());
            list.add(x.getProductTypeName());
            list.add(x.getPromotionCommission());
            list.add(x.getActivityName());
            list.add(x.getActivityGiftName());
            list.add(x.getActivityGiftSum());
            list.add(x.getSetMealPrice());
            list.add(x.getApportionMoney());
            list.add(x.getRemark());

//            list.add(TerminalTypeEnums.fromValue(x.getTerminalType()).getDesc());
            return list;
        }).collect(toList());
        ExcelExportObj excelExportObj = new ExcelExportObj(excelReportDTO.getFileDir(), "客户订单统计.xlsx",
            CustomerOrderStatisticsExportVo.class);

//        ExcelExportObj excelExportObj = new ExcelExportObj(excelReportDTO.getFileDir(), "客户订单统计.xlsx", heads);
        excelExportObj.writeManySheet(listList);
        excelExportObj.pathClose();
    }

    private void handleOtherSite(CxrSite site, CustomerOrderStatisticsVo item) {
        item.setArea(site.getArea());
        item.setSiteName(site.getName());
        item.setCity(site.getCity());
        item.setProvince(site.getProvice());
        item.setBigAreaName(site.getCxrRootRegionName());
        if (!ObjectUtil.equals(item.getCompanyId(), site.getCurrentDeptId())) {
            SysDept dept = sysDeptMapper.selectById(item.getCompanyId());
            if (ObjectUtil.isNotEmpty(dept)) {
                item.setCompanyName(dept.getDeptName());
            }
        }
    }

    private void handleTotalOrderStatistics(CustomerOrderTotal statistics, CustomerOrderStatisticsBo bo) {
        if (ObjectUtil.isNotEmpty(statistics.getReturnTotalQuantity())) {
            BigDecimal returnTotal = statistics.getReturnTotalQuantity()
                .add(statistics.getTotalFreshMilkReturnQuantityVo());
            statistics.setTotalOrderQuantity(
                statistics.getTotalOrderQuantity().subtract(returnTotal).setScale(2, BigDecimal.ROUND_DOWN));
        }

        if (ObjectUtil.isNotEmpty(statistics.getTotalLongMilkGiveQuantity())) {
            statistics.setTotalLongMilkGiveQuantity(
                statistics.getTotalLongMilkGiveQuantity().add(statistics.getTotalReturnLongMilkGiveQuantity())
                    .setScale(2, BigDecimal.ROUND_DOWN));
        }

        if (ObjectUtil.isNotEmpty(statistics.getTotaFfreshMilkGiveQuantity())) {
            statistics.setTotaFfreshMilkGiveQuantity(
                statistics.getTotaFfreshMilkGiveQuantity()
//                    .add(statistics.getTotaRetuenFfreshMilkGiveQuantity())
                    .setScale(2, BigDecimal.ROUND_DOWN));
        }

        if (ObjectUtil.isNotEmpty(statistics.getTotalFreshMilkSentQuantity())) {
            statistics.setTotalFreshMilkSentQuantity(
                statistics.getTotalFreshMilkSentQuantity().setScale(2, BigDecimal.ROUND_DOWN));
            statistics.setTotalOrderQuantity(
                statistics.getTotalOrderQuantity().subtract(statistics.getTotalFreshMilkSentQuantity())
                    .setScale(2, BigDecimal.ROUND_DOWN));
        }

        if (ObjectUtil.isNotEmpty(statistics.getTotalDesignQuantity())) {
            statistics.setTotalDesignQuantity(
                statistics.getTotalDesignQuantity().add(statistics.getTotalReturnDesignQuantity())
                    .setScale(2, BigDecimal.ROUND_DOWN));
        }

        if (ObjectUtil.isNotEmpty(statistics.getTotalExcessQuantity())) {
            statistics.setTotalExcessQuantity(statistics.getTotalExcessQuantity().setScale(2, BigDecimal.ROUND_DOWN));
        }

        if (ObjectUtil.isNotEmpty(statistics.getReturnTotalAmount()) && ObjectUtil.isNotEmpty(bo.getOrderType()) &&
            bo.getOrderType() == OrderTypeEnums.RETURN_ORDER.getValue()) {
            if (!statistics.getReturnTotalAmount().toString().contains("-")) {
                statistics.setReturnTotalAmount(statistics.getReturnTotalAmount().negate());
            }
            statistics.setTotalAmount(ObjectUtil.isEmpty(statistics.getReturnTotalAmount()) ? new BigDecimal(0)
                : statistics.getReturnTotalAmount().setScale(2, BigDecimal.ROUND_UP));
        } else {
            BigDecimal amountMoney = new BigDecimal(0);
            BigDecimal returnAmountMoney = new BigDecimal(0);
            if (ObjectUtil.isEmpty(statistics.getTotalAmount())) {
                amountMoney = new BigDecimal(0);
            } else {
                amountMoney = statistics.getTotalAmount().setScale(2, BigDecimal.ROUND_UP);
            }

            if (ObjectUtil.isEmpty(statistics.getReturnTotalAmount())) {
                returnAmountMoney = new BigDecimal(0);
            } else {
                returnAmountMoney = statistics.getReturnTotalAmount().setScale(2, BigDecimal.ROUND_UP);
            }
            BigDecimal decimal = new BigDecimal(0);
            if (statistics.getReturnTotalAmount().toString().contains("-")) {
                decimal = amountMoney.subtract(returnAmountMoney.negate());
            } else {
                decimal = amountMoney.subtract(returnAmountMoney);
            }

            statistics.setTotalAmount(
                ObjectUtil.isNotEmpty(statistics.getTotalAmount()) ? decimal.setScale(2, BigDecimal.ROUND_UP)
                    : new BigDecimal(0)
            );

        }
    }

    private void queryParameter(LoginUser loginUser, CustomerOrderStatisticsBo bo) {
        bo.setEmployeeFlag(loginUser.getEmployeeFlag());
        bo.setSiteIds(loginUser.getSiteIds());

        if (ObjectUtil.isNotEmpty(bo.getBigAreaName())) {
            List<CxrRegion> cxrRegions = regionMapper.selectList(new LambdaQueryWrapper<CxrRegion>()
                .select(CxrRegion::getId)
                .eq(CxrRegion::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .like(CxrRegion::getName, bo.getBigAreaName())
            );
            if (CollUtil.isNotEmpty(cxrRegions)) {
                bo.setRegionIds(cxrRegions.stream().map(CxrRegion::getId).collect(toList()));
            } else {
                bo.setRegionIds(Arrays.asList(0L));
            }
            bo.setRegionIdsJson(JSONUtil.toJsonStr(bo.getRegionIds()));
        }
    }
}
