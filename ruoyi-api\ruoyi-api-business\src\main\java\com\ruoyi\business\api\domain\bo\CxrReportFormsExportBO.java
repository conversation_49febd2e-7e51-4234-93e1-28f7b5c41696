package com.ruoyi.business.api.domain.bo;

import com.ruoyi.common.core.domain.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@ApiModel
public class CxrReportFormsExportBO extends PageQuery implements Serializable {

    @ApiModelProperty(value = "页面名称")
    private String htmlName;


    @ApiModelProperty(value = "操作人Id")
    private Long createById;

    @ApiModelProperty(value = "操作人")
    private String createByName;


    @ApiModelProperty(value = "操作开始时间")
    private LocalDateTime startTime;


    @ApiModelProperty(value = "操作结束时间")
    private LocalDateTime endTime;


}
