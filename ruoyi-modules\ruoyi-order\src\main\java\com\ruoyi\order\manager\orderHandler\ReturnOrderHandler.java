package com.ruoyi.order.manager.orderHandler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.business.base.api.domain.*;
import com.ruoyi.business.base.api.domain.json.BusinessAgent;
import com.ruoyi.business.base.api.dubbo.*;
import com.ruoyi.business.base.api.enums.OrderTypeEnums;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.service.DictService;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.enums.AuditStatusEnums;
import com.ruoyi.common.enums.SourceEnums;
import com.ruoyi.common.enums.TerminalTypeEnums;
import com.ruoyi.common.redis.utils.RedisLockUtils;
import com.ruoyi.common.rocketmq.constant.message.SendMessageReturnOrderConstant;
import com.ruoyi.common.rocketmq.utils.MqUtil;
import com.ruoyi.common.security.handler.ConstantExceptionCode;
import com.ruoyi.core.base.domain.CxrOrderAfterSale;
import com.ruoyi.core.base.mapper.CxrOrderAfterSaleMapper;
import com.ruoyi.order.api.RemoteOrderService;
import com.ruoyi.order.api.enums.OrderAuditStatusEnums;
import com.ruoyi.order.api.enums.RefundStatusEnums;
import com.ruoyi.order.common.domain.bo.CxrUserReturnOrderBO;
import com.ruoyi.order.common.domain.bo.ReturnOrderMsgBo;
import com.ruoyi.order.common.domain.vo.CxrUserOrderVO;
import com.ruoyi.order.common.entity.CxrUserOrder;
import com.ruoyi.order.common.entity.CxrUserReturnOrder;
import com.ruoyi.order.common.mapper.CxrCustomerMapper;
import com.ruoyi.order.common.mapper.CxrEmployeePostMapper;
import com.ruoyi.order.common.utils.OrderUtil;
import com.ruoyi.order.extend.mapper.CxrEmployeeMapper;
import com.ruoyi.order.manager.domain.entity.MallOrderEntity;
import com.ruoyi.order.manager.domain.entity.MallOrderGoodsEntity;
import com.ruoyi.order.manager.mapper.MallOrderGoodsMapper;
import com.ruoyi.order.manager.mapper.MallOrderMapper;
import com.ruoyi.order.manager.service.CxrUserOrderService;
import com.ruoyi.order.manager.service.CxrUserReturnOrderService;
import com.ruoyi.order.manager.strategy.AbstractOrderHandler;
import com.ruoyi.system.api.RemoteAreaService;
import com.ruoyi.system.api.RemoteDeptService;
import com.ruoyi.system.api.domain.SysDept;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: 退订单处理类
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/8/15 14:14
 */
@Slf4j
@RequiredArgsConstructor
@GlobalTransactional(rollbackFor = Exception.class)
@Component("convert_return_order_to_user_order")
public class ReturnOrderHandler extends AbstractOrderHandler {

    private final IdentifierGenerator identifierGenerator;
    @Lazy
    @Autowired
    private CxrUserOrderService cxrUserOrderService;
    @Lazy
    @Autowired
    private CxrUserReturnOrderService cxrUserReturnOrderService;
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;
    @DubboReference
    private RemoteSiteService remoteSiteServicel;
    @DubboReference
    private RemoteDeptService remoteDeptService;
    @DubboReference
    private RemoteRegionService remoteRegionService;
    @DubboReference
    private RemoteCustomerService remoteCustomerService;
    @DubboReference
    private RemoteCustomerStockDetailService remoteCustomerStockDetailService;
    @DubboReference
    private RemoteAreaService remoteAreaService;
    @DubboReference
    private RemoteCustomerStockDetailService customerStockDetailService;
    @DubboReference
    private RemoteEmployeeService employeeService;

    @DubboReference
    private RemoteEmployeeAchievementDetailService remoteEmployeeAchievementDetailService;

    @DubboReference
    private RemoteLongMilkStockService remoteLongMilkStockService;

    private final CxrOrderAfterSaleMapper cxrOrderAfterSaleMapper;

    private final CxrEmployeeMapper cxrEmployeeMapper;

    private final CxrEmployeePostMapper cxrEmployeePostMapper;
    private final RemoteOrderService remoteOrderService;

    @Autowired
    private MallOrderMapper mallOrderMapper;
    @Autowired
    private MqUtil mqUtil;
    @Autowired
    private MallOrderGoodsMapper mallOrderGoodsMapper;
    @Autowired
    private CxrCustomerMapper cxrCustomerMapper;

    private final DictService dictService;

    /**
     * 第一次新增时要转换成 CxrUserOrder 用于添加
     *
     * @param obj
     * @return
     */
    @Override
    @Transactional
    public CxrUserOrder createCxrUserOrder(Object obj) {

        return RedisLockUtils.tryLockException(
            OrderTypeEnums.RETURN_ORDER.getLockName(),
            () -> {
                CxrUserReturnOrderBO cxrUserReturnOrderBO = (CxrUserReturnOrderBO) obj;

                CxrUserOrder returnCxrUserOrder = new CxrUserOrder();

                // 查询站点
                Long siteId = cxrUserReturnOrderBO.getSiteId();
                if (ObjectUtil.isEmpty(siteId)) {
                    throw new ServiceException("请选择开单站点",
                        ConstantExceptionCode.BUSSINESS_EXCEPTION_CODE);
                }
                CxrSite cxrSite = remoteSiteServicel.queryId(siteId);

                // 设置所属公司 id
                returnCxrUserOrder.setCompanyId(cxrSite.getCurrentDeptId());

                SysDept sysDept = remoteDeptService.queryById(cxrSite.getCurrentDeptId());
                if (ObjectUtil.isNull(sysDept)) {

                    throw new ServiceException("站点没有关联所属部门");
                }
                // 所属公司名称
                returnCxrUserOrder.setCompanyName(sysDept.getDeptName());

                // 站点数据设置
                returnCxrUserOrder.setBigAreaId(cxrSite.getCxrRootRegionId());
                returnCxrUserOrder.setBigAreaName(cxrSite.getCxrRootRegionName());
                returnCxrUserOrder.setProvince(cxrSite.getProvice());
                returnCxrUserOrder.setCity(cxrSite.getCity());
                returnCxrUserOrder.setArea(cxrSite.getArea());
                returnCxrUserOrder.setSiteName(cxrSite.getName());
                returnCxrUserOrder.setSiteAdress(cxrSite.getDetailAddress());
                returnCxrUserOrder.setSiteId(cxrSite.getId());

                if (CollectionUtil.isEmpty(cxrUserReturnOrderBO.getBusinessAgent())) {
                    throw new ServiceException("请选择销售代理",
                        ConstantExceptionCode.BUSSINESS_EXCEPTION_CODE);
                }

                returnCxrUserOrder.setBusinessAgent(cxrUserReturnOrderBO.getBusinessAgent());
                if (CollUtil.isNotEmpty(cxrUserReturnOrderBO.getDeliverySites())) {
                    returnCxrUserOrder.setDeliverySites(JSONUtil.toJsonStr(cxrUserReturnOrderBO.getDeliverySites()));
                }
                Long customerId = cxrUserReturnOrderBO.getCustomerId();
                if (ObjectUtil.isNull(customerId)) {
                    throw new ServiceException("用户信息不存在!");
                }

                Long afterSalesId = cxrUserReturnOrderBO.getAfterSalesId();
                if (afterSalesId != null) {
                    CxrOrderAfterSale cxrOrderAfterSale = cxrOrderAfterSaleMapper.selectById(afterSalesId);
                    if (cxrOrderAfterSale == null) {
                        throw new ServiceException("售后单不存在");
                    }
                    if (!customerId.equals(cxrOrderAfterSale.getCustomerId())) {
                        throw new ServiceException("当前退订单和售后工单客户手机号不一致!");
                    }
                }

                CustomerInfoDTO customerInfoDTO = remoteCustomerService.queryById(customerId);
                // 客户信息
                returnCxrUserOrder.setCustomerName(customerInfoDTO.getName());
                returnCxrUserOrder.setCustomerPhone(customerInfoDTO.getPhone());
                returnCxrUserOrder.setCustomerAdress(customerInfoDTO.getAdress());
                returnCxrUserOrder.setCustomerId(customerInfoDTO.getCustomerId());
                log.info("退单的客户id{}", customerInfoDTO.getCustomerId());

                // 鲜奶数量设置

                returnCxrUserOrder
                    .setSurplusQuantity(cxrUserReturnOrderBO.getFreshMilkCancelQuantity());
                returnCxrUserOrder.setOrderQuantity(0);
                returnCxrUserOrder.setFreshMilkGiveQuantity(0);
                returnCxrUserOrder.setLongMilkGiveQuantity(
                    cxrUserReturnOrderBO.getLongMilkCancelQuantity());
                returnCxrUserOrder.setFreshMilkSentQuantity(0);
                returnCxrUserOrder.setLongMilkSentQuantity(0);
                returnCxrUserOrder.setSurplusQuantity(0);
                returnCxrUserOrder.setFreshMilkReturnQuantity(
                    cxrUserReturnOrderBO.getFreshMilkCancelQuantity());
                returnCxrUserOrder.setOrderNo(
                    OrderUtil.getOrderNo(cxrUserReturnOrderBO.getCustomerPhone()));
                //            returnCxrUserOrder.setAmount(cxrUserReturnOrderBO.getRefundAmount());
                returnCxrUserOrder.setAmount(cxrUserReturnOrderBO.getAmount());
                returnCxrUserOrder.setOrderDate(cxrUserReturnOrderBO.getOrderDate());
//                if (ObjectUtil.isEmpty(cxrUserReturnOrderBO.getFreshMilkCancelQuantity())
//                    || cxrUserReturnOrderBO.getFreshMilkCancelQuantity().equals(0)) {
//                    throw new ServiceException("鲜奶退订数量不能为0");
//                }

                // 可按0数量 退款优化
                if (ObjectUtil.isEmpty(cxrUserReturnOrderBO.getFreshMilkCancelQuantity())
                    || cxrUserReturnOrderBO.getFreshMilkCancelQuantity().equals(0)) {
                    returnCxrUserOrder.setUnitPrice(new BigDecimal(0));
                } else {
                    BigDecimal unitPrice =
                        NumberUtil.div(
                            cxrUserReturnOrderBO.getRefundAmount(),
                            cxrUserReturnOrderBO.getFreshMilkCancelQuantity(),
                            2);

                    returnCxrUserOrder.setUnitPrice(unitPrice);
                }

                int terminalType = cxrUserReturnOrderBO.getTerminalType();
                if (terminalType == TerminalTypeEnums.manager.getValue()
                    || terminalType == TerminalTypeEnums.tiktok.getValue()) {
                    returnCxrUserOrder.setAuditStatus(OrderAuditStatusEnums.WAIT_AUDIT.getValue());
                } else {
                    returnCxrUserOrder.setAuditStatus(OrderAuditStatusEnums.AUDITED.getValue());
                }

                returnCxrUserOrder.setTerminalType((short) cxrUserReturnOrderBO.getTerminalType());

                long id = identifierGenerator.nextId(null).longValue();
                // 保存 退订单信息

                CxrUserReturnOrder cxrUserReturnOrder =
                    BeanUtil.copyProperties(cxrUserReturnOrderBO, CxrUserReturnOrder.class);
                cxrUserReturnOrder.setUserOrderId(id);
                cxrUserReturnOrder.setOrderDate(cxrUserReturnOrderBO.getOrderDate());
                cxrUserReturnOrderService.getBaseMapper().insert(cxrUserReturnOrder);
                // 添加备注
                if (OrderTypeEnums.RETURN_ORDER.getValue() == cxrUserReturnOrderBO.getOrderType()) {
                    returnCxrUserOrder.setRemark(cxrUserReturnOrder.getRemark());
                }

                if (ObjectUtil.isEmpty(returnCxrUserOrder.getAmount())) {
                    returnCxrUserOrder.setAmount(new BigDecimal(0));
                }

                returnCxrUserOrder.setId(id);
                returnCxrUserOrder.setOrderType(OrderTypeEnums.RETURN_ORDER.getValue());

                if (ObjectUtil.isNotEmpty(cxrUserReturnOrderBO.getPayOrderId())) {
                    returnCxrUserOrder.setPayOrderId(cxrUserReturnOrderBO.getPayOrderId());
                    Long payOrderId = cxrUserReturnOrderBO.getPayOrderId();
                    CxrUserOrder cxrUserOrder = cxrUserOrderService.getById(payOrderId);

                    List<CxrUserOrder> userOrders = cxrUserOrderService.getBaseMapper()
                        .selectList(new LambdaQueryWrapper<CxrUserOrder>()
                            .select(CxrUserOrder::getPayOrderId, CxrUserOrder::getAmount)
                            .eq(CxrUserOrder::getPayOrderId, payOrderId)
                            .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.not_deleted)
                            .in(CxrUserOrder::getAuditStatus, AuditStatusEnums.Audit.code(),
                                AuditStatusEnums.ToAudit.code()
                            )
                        );

                    BigDecimal totalAmount = returnCxrUserOrder.getAmount();
                    if (CollUtil.isNotEmpty(userOrders)) {
                        BigDecimal reduce = userOrders.stream().map(CxrUserOrder::getAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        totalAmount.add(reduce);
                    }
                    returnCxrUserOrder.setPaymentStatus(RefundStatusEnums.REFUND_WAIT.getValue());
                    if (totalAmount.compareTo(cxrUserOrder.getAmount()) == 1) {
                        throw new ServiceException("退款总金额大于支付金额!");
                    }
                }
                setChannelValue(returnCxrUserOrder, cxrUserReturnOrder.getOrderId(),
                    cxrUserReturnOrder.getPayOrderId());
                boolean save = cxrUserOrderService.save(returnCxrUserOrder);

                if (save) {
                    CxrOrderAfterSale cxrOrderAfterSale = null;
                    if (afterSalesId != null) {
                        cxrOrderAfterSale = cxrOrderAfterSaleMapper.selectById(afterSalesId);
                    }
                    if (cxrOrderAfterSale != null && cxrOrderAfterSale.getGoodsType() == 3) {
                        //鲜奶+售后.退款动作完成后会修改为已完成状态
                        cxrOrderAfterSaleMapper.update(null, Wrappers.lambdaUpdate(CxrOrderAfterSale.class)
                            .set(CxrOrderAfterSale::getRefundOrderId, id)
                            .eq(CxrOrderAfterSale::getId, afterSalesId));
                    } else {
                        cxrOrderAfterSaleMapper.update(null, Wrappers.lambdaUpdate(CxrOrderAfterSale.class)
                            .set(CxrOrderAfterSale::getSaleStatus, SaleStatus.PENDING.getValue())
                            .set(CxrOrderAfterSale::getRefundOrderId, id)
                            .eq(CxrOrderAfterSale::getId, afterSalesId));
                    }

                    //来源小程序的鲜奶订单 生成售后数据
                    //下单的主单
                    CxrUserOrder cxrUserOrder = cxrUserOrderService.getByPayOrderId(returnCxrUserOrder.getPayOrderId());
                    if (ObjectUtil.isNotEmpty(cxrUserOrder) && ObjectUtil.isNotEmpty(returnCxrUserOrder.getPayOrderId())
                        && cxrUserOrder.getTerminalType().equals(TerminalTypeEnums.xcx.getValue())) {
                        CxrOrderAfterSale orderAfterSale = addAfterSaleOrderDate(cxrUserReturnOrder, cxrUserOrder);
                        Boolean a = cxrOrderAfterSaleMapper.insert(orderAfterSale) > 0;
                        if (!a) {
                            throw new ServiceException("售后退订单数据生成失败!!!");
                        }
                        boolean b = cxrUserReturnOrderService.getBaseMapper().update(null,
                            new LambdaUpdateWrapper<CxrUserReturnOrder>()
                                .eq(CxrUserReturnOrder::getUserOrderId, orderAfterSale.getRefundOrderId())
                                .set(CxrUserReturnOrder::getAfterSalesId, orderAfterSale.getId())
                        ) > 0;
                        if (!b) {
                            throw new ServiceException("修改退订里面售后id失败!!!");
                        }
                    }

                }

                return save ? returnCxrUserOrder : null;
            });
    }

    private void setChannelValue(CxrUserOrder returnCxrUserOrder, Long orderId, Long payOrderId) {
        if (orderId == null && payOrderId != null) {
            orderId = payOrderId;
        }
        if (orderId != null) {
            CxrUserOrder order = cxrUserOrderService.getById(orderId);
            if (order != null) {
                returnCxrUserOrder.setChannel(order.getChannel());
                returnCxrUserOrder.setProductType(order.getProductType());
                returnCxrUserOrder.setProductName(order.getProductName());
                returnCxrUserOrder.setAccountingType(order.getAccountingType());
                returnCxrUserOrder.setPromotionCommission(order.getPromotionCommission());
                returnCxrUserOrder.setCommissionConfigId(order.getCommissionConfigId());
                returnCxrUserOrder.setProductTypeName(order.getProductTypeName());
                returnCxrUserOrder.setTerminalType(order.getTerminalType());
            }
        }
    }

    private CxrOrderAfterSale addAfterSaleOrderDate(CxrUserReturnOrder returnCxrUserOrder, CxrUserOrder cxrUserOrder) {
        CxrOrderAfterSale afterSale = new CxrOrderAfterSale();
        //退订单的主单
        CxrUserOrder order = cxrUserOrderService.getByPayOrderId(returnCxrUserOrder.getUserOrderId());
        //1.生成售后订单的数据
        //2.退订单回填 售后订单id
        MallOrderEntity mallOrder = mallOrderMapper.queryById(cxrUserOrder.getOrderNo());
        if (ObjectUtil.isNotEmpty(mallOrder)) {
            String orderNo = "SH" + StringUtils.generateOrderNumber();
            afterSale.setOrderNo(cxrUserOrder.getOrderNo());
            afterSale.setAfterSaleNo(orderNo);
            afterSale.setRefundOrderId(order.getId());
            afterSale.setSaleStatus(SaleStatus.PENDING.getValue());
            //对应退款的类型 退款类型
            afterSale.setSaleType(1);
            //鲜奶类型
            afterSale.setGoodsType(1);
            afterSale.setBsDate(cxrUserOrder.getPayTime());
            afterSale.setOrderDate(cxrUserOrder.getOrderDate());
            //拒绝原因
            if (ObjectUtil.isNotEmpty(returnCxrUserOrder.getRrCode())) {
                afterSale.setReturnReason(
                    dictService.getDictLabel("order_refund_reason", returnCxrUserOrder.getRrCode(), ","));
            }
            //公司信息//客户
            afterSale.setSysDeptName(cxrUserOrder.getCompanyName());
            afterSale.setCity(cxrUserOrder.getCity());
            afterSale.setProvice(cxrUserOrder.getProvince());
            afterSale.setArea(cxrUserOrder.getArea());
            afterSale.setSiteId(cxrUserOrder.getSiteId());
            afterSale.setSiteName(cxrUserOrder.getSiteName());
            afterSale.setAddressDetail(returnCxrUserOrder.getCustomerAdress());
            afterSale.setCustomerId(returnCxrUserOrder.getCustomerId());
            afterSale.setCustomerPhone(returnCxrUserOrder.getCustomerPhone());
            afterSale.setCustomerName(returnCxrUserOrder.getCustomerName());
            afterSale.setApplyTime(new Date());
            //大区
            SysDept sysDept = remoteDeptService.queryById(cxrUserOrder.getBigAreaId());
            if (ObjectUtil.isNotEmpty(sysDept)) {
                SysDept dept = remoteDeptService.queryById(sysDept.getParentId());
                if (ObjectUtil.isNotEmpty(dept)) {
                    afterSale.setCxrRootRegionName(dept.getDeptName());
                }
            }
            afterSale.setSourceType(DeviceType.PC.getDevice());
            afterSale.setReturnStatus(RefundStatusEnums.REFUND_WAIT.getValue());
            afterSale.setCreateTime(new Date());
            afterSale.setCreateBy(mallOrder.getUserId());
            afterSale.setCreateByName(cxrUserOrder.getCustomerName());
            afterSale.setCreateByType(DeviceType.PC.getDevice());
            afterSale.setReturnFreshMilkFlag(0); //退鲜奶标识
            afterSale.setReturnAllFinishFlag(0); //是否全部退完

            afterSale.setSubmitGoodsNum(returnCxrUserOrder.getFreshMilkRefundQuantity());
            afterSale.setPredictReturnAmount(returnCxrUserOrder.getAmount());
            afterSale.setReturnAmount(returnCxrUserOrder.getAmount());
            afterSale.setActReturnNums(returnCxrUserOrder.getFreshMilkRefundQuantity());
            afterSale.setOrderQuantity(mallOrder.getOrderQuantity());
            //afterSale.setLongMilkGiveQuantity(returnCxrUserOrder.getLongMilkGiveQuantity());
            // afterSale.setFreshMilkGiveQuantity(returnCxrUserOrder.getFreshMilkGiveQuantity());
            afterSale.setDiscountsAmount(mallOrder.getCouponPrice());

            afterSale.setTotalAmount(mallOrder.getOrderPrice());
            afterSale.setGoodsAmount(mallOrder.getOrderPrice());
            afterSale.setPayAmount(mallOrder.getActualPrice());
            afterSale.setDeleteStatus(DeleteStatus.NOT_DELETED.getValue());
            MallOrderGoodsEntity orderGoods = mallOrderGoodsMapper.queryByOrderId(cxrUserOrder.getOrderNo());
            if (ObjectUtil.isNotEmpty(orderGoods)) {
                afterSale.setGoodsImgs(orderGoods.getListPicUrl());
                afterSale.setGoodsName(orderGoods.getGoodsName());
                afterSale.setSpecifications(orderGoods.getGoodsSpecifitionNameValue());
                afterSale.setOrderQuantity(orderGoods.getNumber());
            }
//                boolean b = mallOrderMapper.updateStatus(cxrUserOrder.getOrderNo());
//                if (!b) {
//                    throw new ServiceException("小程序状态修改失败!!!");
//                }
        }
        return afterSale;
    }


    /**
     * 生成 要更新的 CxrUserOrder
     *
     * @param obj
     * @return
     */
    @Override
    public CxrUserOrder createCxrUserOrderUpdate(Object obj) {

        CxrUserReturnOrderBO bo = (CxrUserReturnOrderBO) obj;

        // 获取用户订单信息
        CxrUserOrderService cxrUserOrderService = SpringUtils.getBean(CxrUserOrderService.class);
        CxrUserOrder cxrUserOrder = cxrUserOrderService.getById(bo.getUserOrderId());
        cxrUserOrder.setUpdateBy(null);
        cxrUserOrder.setUpdateByType(null);
        cxrUserOrder.setUpdateByName(null);
        cxrUserOrder.setUpdateTime(null);

        if (ObjectUtil.isEmpty(bo.getAmount())) {
            bo.setAmount(new BigDecimal(0));
        }

        if (ObjectUtil.isNotEmpty(cxrUserOrder.getPayOrderId()) && !ObjectUtil.equals(bo.getAmount(),
            cxrUserOrder.getAmount())) {
            Long payOrderId = cxrUserOrder.getPayOrderId();
            CxrUserOrder cxrUserOrderPay = cxrUserOrderService.getById(payOrderId);

            List<CxrUserOrder> userOrders = cxrUserOrderService.getBaseMapper()
                .selectList(new LambdaQueryWrapper<CxrUserOrder>()
                    .select(CxrUserOrder::getPayOrderId, CxrUserOrder::getAmount)
                    .eq(CxrUserOrder::getPayOrderId, payOrderId)
                    .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.not_deleted)
                    .in(CxrUserOrder::getAuditStatus, AuditStatusEnums.Audit.code(),
                        AuditStatusEnums.ToAudit.code()
                    )
                    .notIn(CxrUserOrder::getId, bo.getUserOrderId())
                );

            BigDecimal totalAmount = bo.getAmount();
            if (CollUtil.isNotEmpty(userOrders)) {
                BigDecimal reduce = userOrders.stream().map(CxrUserOrder::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                totalAmount.add(reduce);
            }

            if (totalAmount.compareTo(cxrUserOrderPay.getAmount()) == 1) {
                throw new ServiceException("退款总金额大于支付金额!");
            }
        }

        if (CollUtil.isNotEmpty(bo.getDeliverySites())) {
            cxrUserOrder.setDeliverySites(JSONUtil.toJsonStr(bo.getDeliverySites()));
        }


        if (!bo.getSiteId().equals(cxrUserOrder.getSiteId())) {
            CxrSite cxrSite = remoteSiteServicel.getCxrSite(bo.getSiteId());
            cxrUserOrder.setBigAreaId(cxrSite.getCxrRootRegionId());
            cxrUserOrder.setBigAreaName(cxrSite.getCxrRootRegionName());
            cxrUserOrder.setProvince(cxrSite.getProvice());
            cxrUserOrder.setCity(cxrSite.getCity());
            cxrUserOrder.setArea(cxrSite.getArea());
            cxrUserOrder.setSiteName(cxrSite.getName());
            cxrUserOrder.setSiteAdress(cxrSite.getDetailAddress());
        }

        //        //设置客户信息 不予 修改
        //        bo.setCustomerAdress(cxrUserOrder.getCustomerAdress());
        //        bo.setCustomerName(cxrUserOrder.getCustomerName());
        //        bo.setCustomerPhone(cxrUserOrder.getCustomerPhone());
        // copy    bo对象

        CxrUserReturnOrder returnOrder = cxrUserReturnOrderService.lambdaQuery()
            .eq(CxrUserReturnOrder::getUserOrderId, bo.getUserOrderId())
            .eq(CxrUserReturnOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()).one();
        Long afterSalesId = bo.getAfterSalesId();
        Long salesId = returnOrder.getAfterSalesId();
        boolean updateSales = false;
        CxrOrderAfterSale cxrOrderAfterSale = null;
        if (ObjectUtil.isEmpty(cxrUserOrder.getPayOrderId())) {
            if (afterSalesId != null) {

                cxrOrderAfterSale = cxrOrderAfterSaleMapper.selectById(afterSalesId);
                if (cxrOrderAfterSale == null) {
                    throw new ServiceException("售后单不存在");
                }
                if (!cxrUserOrder.getCustomerId().equals(cxrOrderAfterSale.getCustomerId())) {
                    throw new ServiceException("当前退订单和售后工单客户手机号不一致!");
                }

                if (!cxrOrderAfterSale.getSaleStatus().equals(SaleStatus.IN_APPLICATION.getValue())) {
                    throw new ServiceException("售后工单不是申请中！");
                }

                if (salesId != null && !afterSalesId.equals(salesId)) {

                    if (cxrUserOrder.getRefundSuccessFlag()) {
                        throw new ServiceException("订单已经打款，不能修改关联的售后单");
                    }
                    // TODO 修改原来的售后单状态
                    updateSales = true;
                }
            } else {
                if (salesId != null) {

                    if (cxrUserOrder.getRefundSuccessFlag()) {
                        throw new ServiceException("订单已经打款，不能修改关联的售后单");
                    }

                    // TODO 修改原来的售后单状态
                    updateSales = true;
                }
            }
        }

        //来源为在线退款
        if (ObjectUtil.isNotEmpty(cxrUserOrder.getPayOrderId())) {
            CxrOrderAfterSale orderAfterSale = cxrOrderAfterSaleMapper.selectOne(
                new LambdaQueryWrapper<CxrOrderAfterSale>()
                    .eq(CxrOrderAfterSale::getRefundOrderId, cxrUserOrder.getId())
                    .eq(CxrOrderAfterSale::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            );
            if (ObjectUtil.isNotEmpty(orderAfterSale)) {
                CxrOrderAfterSale afterSale = updateAfterSaleOrderDate(bo);
                if (ObjectUtil.isNotEmpty(afterSale)) {
                    afterSale.setId(orderAfterSale.getId());
                    cxrOrderAfterSaleMapper.updateById(afterSale);
                }
            }
        }

        // 如果已经审核,判断该订单是否超过下个月10号
        if (OrderAuditStatusEnums.AUDITED.getValue() == cxrUserOrder.getAuditStatus().intValue()) {
            // 常温奶赠送总数
            cxrUserOrder.setLongMilkGiveQuantity(bo.getLongMilkGiveQuantity());
            // 常温奶已送
            cxrUserOrder.setLongMilkSentQuantity(bo.getLongMilkSentQuantity());
            cxrUserOrder.setAmount(bo.getAmount());
            cxrUserOrder.setOrderImages(bo.getImages());
            cxrUserOrder.setBusinessAgent(bo.getBusinessAgent());
            cxrUserOrder.setFreshMilkReturnQuantity(bo.getFreshMilkCancelQuantity());
            cxrUserOrder.setRemark(bo.getRemark());
            //            Date dataCreateTime = cxrUserOrder.getCreateTime();
            //            Date nextMonth5day = DateUtils.nextMonth5day(dataCreateTime);
            //            if (System.currentTimeMillis() > nextMonth5day.getTime()) {
            //                throw new ServiceException("该单号已经超过修改时间范围,不允许修改");
            //            }

            returnOrderUpdateCustomerStock(cxrUserOrder, bo);

            cxrUserReturnOrderService
                .getBaseMapper()
                .update(
                    null,
                    new LambdaUpdateWrapper<CxrUserReturnOrder>()
                        .eq(CxrUserReturnOrder::getUserOrderId, bo.getUserOrderId())
                        .set(null != bo.getSiteId(), CxrUserReturnOrder::getSiteId, bo.getSiteId())
                        .set(
                            CxrUserReturnOrder::getBusinessAgent,
                            JSONUtil.toJsonStr(bo.getBusinessAgent()))
                        // 开户银行字段不为null&&未审核    修改字段
                        .set(
                            StringUtils.isNotBlank(bo.getBankName()),
                            CxrUserReturnOrder::getBankName,
                            bo.getBankName())
                        // 账户名称字段不为null&&未审核    修改字段
                        .set(
                            StringUtils.isNotBlank(bo.getBankAccountName()),
                            CxrUserReturnOrder::getBankAccountName,
                            bo.getBankAccountName())
                        // 账户卡号字段不为null&&未审核    修改字段
                        .set(
                            StringUtils.isNotBlank(bo.getBankAccountNumber()),
                            CxrUserReturnOrder::getBankAccountNumber,
                            bo.getBankAccountNumber())
                        .set(CxrUserReturnOrder::getImages, JSONUtil.toJsonStr(bo.getImages()))
                        //   修改备注
                        .set(CxrUserReturnOrder::getRemark, bo.getRemark())
                        .set(
                            ObjectUtil.isNotEmpty(bo.getLongMilkSentQuantity()),
                            CxrUserReturnOrder::getLongMilkSentQuantity,
                            bo.getLongMilkSentQuantity())
                        .set(
                            ObjectUtil.isNotEmpty(bo.getLongMilkGiveQuantity()),
                            CxrUserReturnOrder::getLongMilkGiveQuantity,
                            bo.getLongMilkGiveQuantity())
                        .set(
                            ObjectUtil.isNotEmpty(bo.getAmount()),
                            CxrUserReturnOrder::getAmount,
                            bo.getAmount())
                        .set(StringUtils.isNotBlank(bo.getRrCode()), CxrUserReturnOrder::getRrCode, bo.getRrCode())
                        .set(
                            CollectionUtil.isNotEmpty(bo.getImages()),
                            CxrUserReturnOrder::getImages,
                            JSONUtil.toJsonStr(bo.getImages()))
                        .set(CollectionUtil.isEmpty(bo.getImages()), CxrUserReturnOrder::getImages,
                            null)

                        // 鲜奶实际退订数量
                        .set(
                            bo.getFreshMilkCancelQuantity() != null,
                            CxrUserReturnOrder::getFreshMilkCancelQuantity,
                            bo.getFreshMilkCancelQuantity())
                        // 退订后剩余数量 = 剩余数量  - 退订数量
                        .set(
                            bo.getFreshMilkCancelRestQuantity() != null,
                            CxrUserReturnOrder::getFreshMilkCancelRestQuantity,
                            bo.getFreshMilkCancelRestQuantity())
                        // 退款盒数
                        .set(
                            bo.getFreshMilkRefundQuantity() != null,
                            CxrUserReturnOrder::getFreshMilkRefundQuantity,
                            bo.getFreshMilkRefundQuantity())
                        // 退款金额
                        .set(
                            bo.getRefundAmount() != null,
                            CxrUserReturnOrder::getRefundAmount,
                            bo.getRefundAmount())

                        // 常温奶实际退订数量
                        .set(
                            bo.getLongMilkCancelQuantity() != null,
                            CxrUserReturnOrder::getLongMilkCancelQuantity,
                            bo.getLongMilkCancelQuantity())
                        // 常温奶剩余数量
                        .set(
                            bo.getLongMilkRestQuantity() != null,
                            CxrUserReturnOrder::getLongMilkRestQuantity,
                            bo.getLongMilkRestQuantity())
                        // 常温奶已送金额
                        .set(
                            bo.getLongMilkSentAmount() != null,
                            CxrUserReturnOrder::getLongMilkSentAmount,
                            bo.getLongMilkSentAmount())


                        .set(StringUtils.isNotBlank(bo.getRrCode()), CxrUserReturnOrder::getRrCode, bo.getRrCode())
                        .set(CxrUserReturnOrder::getAfterSalesId, bo.getAfterSalesId())
                        .set(CxrUserReturnOrder::getOrderMoney, bo.getOrderMoney())
                        .set(CxrUserReturnOrder::getEmployeeRemark, bo.getEmployeeRemark())
                        // 销售代理返利
                        .set(bo.getRebates() != null, CxrUserReturnOrder::getRebates,
                            bo.getRebates()));
        } else {
            BeanUtils.copyProperties(bo, cxrUserOrder);
            cxrUserOrder.setBusinessAgent(bo.getBusinessAgent());
            cxrUserOrder.setFreshMilkReturnQuantity(bo.getFreshMilkCancelQuantity());
            //             更新退单子表数据      不是审核状态的话
            cxrUserReturnOrderService
                .getBaseMapper()
                .update(
                    null,
                    new LambdaUpdateWrapper<CxrUserReturnOrder>()
                        .eq(CxrUserReturnOrder::getUserOrderId, bo.getUserOrderId())
                        .set(
                            CxrUserReturnOrder::getBusinessAgent,
                            JSONUtil.toJsonStr(bo.getBusinessAgent()))
                        // 开户银行字段不为null&&未审核    修改字段
                        .set(
                            StringUtils.isNotBlank(bo.getBankName()),
                            CxrUserReturnOrder::getBankName,
                            bo.getBankName())
                        // 账户名称字段不为null&&未审核    修改字段
                        .set(
                            StringUtils.isNotBlank(bo.getBankAccountName()),
                            CxrUserReturnOrder::getBankAccountName,
                            bo.getBankAccountName())
                        // 账户卡号字段不为null&&未审核    修改字段
                        .set(
                            StringUtils.isNotBlank(bo.getBankAccountNumber()),
                            CxrUserReturnOrder::getBankAccountNumber,
                            bo.getBankAccountNumber())
                        //   修改备注
                        .set(CxrUserReturnOrder::getRemark, bo.getRemark())
                        // 总数量
                        .set(
                            null != bo.getTotalQuantity(),
                            CxrUserReturnOrder::getTotalQuantity,
                            bo.getTotalQuantity())
                        // 站点Id
                        .set(null != bo.getSiteId(), CxrUserReturnOrder::getSiteId, bo.getSiteId())
                        .set(
                            null != bo.getRefundAmount(),
                            CxrUserReturnOrder::getRefundAmount,
                            bo.getRefundAmount())
                        .set(null != bo.getAmount(), CxrUserReturnOrder::getAmount, bo.getAmount())
                        .set(CxrUserReturnOrder::getImages, JSONUtil.toJsonStr(bo.getImages()))
                        .set(
                            null != bo.getLongMilkSentQuantity(),
                            CxrUserReturnOrder::getLongMilkSentQuantity,
                            bo.getLongMilkSentQuantity())
                        .set(
                            null != bo.getLongMilkSentAmount(),
                            CxrUserReturnOrder::getLongMilkSentAmount,
                            bo.getLongMilkSentAmount())
                        .set(
                            null != bo.getFreshMilkRefundQuantity(),
                            CxrUserReturnOrder::getFreshMilkRefundQuantity,
                            bo.getFreshMilkRefundQuantity())
                        .set(
                            null != bo.getFreshMilkCancelQuantity(),
                            CxrUserReturnOrder::getFreshMilkCancelQuantity,
                            bo.getFreshMilkCancelQuantity())
                        .set(
                            null != bo.getLongMilkCancelQuantity(),
                            CxrUserReturnOrder::getLongMilkCancelQuantity,
                            bo.getLongMilkCancelQuantity())
                        .set(
                            StringUtils.isNotBlank(bo.getOrderNo()),
                            CxrUserReturnOrder::getOrderNo,
                            bo.getOrderNo())
                        .set(
                            null != bo.getFreshMilkGiveQuantity(),
                            CxrUserReturnOrder::getFreshMilkGiveQuantity,
                            bo.getFreshMilkGiveQuantity())
                        .set(
                            null != bo.getFreshMilkSentQuantity(),
                            CxrUserReturnOrder::getFreshMilkSentQuantity,
                            bo.getFreshMilkSentQuantity())
                        .set(
                            null != bo.getFreshMilkRestQuantity(),
                            CxrUserReturnOrder::getFreshMilkRestQuantity,
                            bo.getFreshMilkRestQuantity())
                        .set(
                            null != bo.getLongMilkGiveQuantity(),
                            CxrUserReturnOrder::getLongMilkGiveQuantity,
                            bo.getLongMilkGiveQuantity())
                        .set(
                            null != bo.getLongMilkRestQuantity(),
                            CxrUserReturnOrder::getLongMilkRestQuantity,
                            bo.getLongMilkRestQuantity())
                        .set(
                            null != bo.getFreshMilkCancelRestQuantity(),
                            CxrUserReturnOrder::getFreshMilkCancelRestQuantity,
                            bo.getFreshMilkCancelRestQuantity())
                        .set(null != bo.getRebates(), CxrUserReturnOrder::getRebates,
                            bo.getRebates())
                        .set(StringUtils.isNotBlank(bo.getRrCode()), CxrUserReturnOrder::getRrCode, bo.getRrCode())
                        .set(CxrUserReturnOrder::getAfterSalesId, bo.getAfterSalesId())
                        .set(CxrUserReturnOrder::getOrderMoney, bo.getOrderMoney())
                        .set(CxrUserReturnOrder::getCustomerId, bo.getCustomerId())
                        .set(CxrUserReturnOrder::getCustomerAdress, bo.getCustomerAdress())
                        .set(CxrUserReturnOrder::getCustomerPhone, bo.getCustomerPhone())
                        .set(CxrUserReturnOrder::getEmployeeRemark, bo.getEmployeeRemark())
                        .set(CxrUserReturnOrder::getCustomerName, bo.getCustomerName()));
        }

        if (afterSalesId != null) {
            cxrOrderAfterSale = cxrOrderAfterSaleMapper.selectById(afterSalesId);
            if (cxrOrderAfterSale.getGoodsType() == 3) {

            } else {
                cxrOrderAfterSaleMapper.update(null, Wrappers.lambdaUpdate(CxrOrderAfterSale.class)
                    .set(CxrOrderAfterSale::getSaleStatus, SaleStatus.PENDING.getValue())
                    .set(CxrOrderAfterSale::getRefundOrderId, bo.getUserOrderId())
                    .eq(CxrOrderAfterSale::getId, afterSalesId));
            }
        }

        if (updateSales) {
            cxrOrderAfterSale = cxrOrderAfterSaleMapper.selectById(salesId);
            if (cxrOrderAfterSale.getGoodsType() == 3) {

            } else {
                cxrOrderAfterSaleMapper.update(null, Wrappers.lambdaUpdate(CxrOrderAfterSale.class)
                    .set(CxrOrderAfterSale::getSaleStatus, SaleStatus.IN_APPLICATION.getValue())
                    .set(CxrOrderAfterSale::getRefundOrderId, null)
                    .eq(CxrOrderAfterSale::getId, salesId));
            }
        }

        cxrUserOrder.setLongMilkGiveQuantity(bo.getLongMilkCancelQuantity());

        return cxrUserOrder;
    }

    private CxrOrderAfterSale updateAfterSaleOrderDate(CxrUserReturnOrderBO returnCxrUserOrder) {
        CxrOrderAfterSale afterSale = new CxrOrderAfterSale();
        //退订单的主单
        CxrUserOrder order = cxrUserOrderService.getByPayOrderId(returnCxrUserOrder.getUserOrderId());
        //下单的主单
        if (ObjectUtil.isNotEmpty(order)) {
            CxrUserOrder cxrUserOrder = cxrUserOrderService.getByPayOrderId(order.getPayOrderId());
            if (ObjectUtil.isNotEmpty(cxrUserOrder)) {
                //1.生成售后订单的数据
                //2.退订单回填 售后订单id
                afterSale.setOrderDate(cxrUserOrder.getOrderDate());
                //拒绝原因
                if (ObjectUtil.isNotEmpty(returnCxrUserOrder.getRrCode())) {
                    afterSale.setReturnReason(
                        dictService.getDictLabel("order_refund_reason", returnCxrUserOrder.getRrCode(), ","));
                }
                //公司信息//客户

                afterSale.setSiteId(returnCxrUserOrder.getSiteId());
                afterSale.setSiteName(returnCxrUserOrder.getSiteName());
                afterSale.setSubmitGoodsNum(returnCxrUserOrder.getFreshMilkRefundQuantity());
                afterSale.setPredictReturnAmount(returnCxrUserOrder.getAmount());
                afterSale.setReturnAmount(returnCxrUserOrder.getAmount());
                afterSale.setActReturnNums(returnCxrUserOrder.getFreshMilkRefundQuantity());
                afterSale.setDeleteStatus(DeleteStatus.NOT_DELETED.getValue());
            }
        }
        return afterSale;
    }

    public void returnOrderUpdateCustomerStock(CxrUserOrder cxrUserOrder, CxrUserReturnOrderBO bo) {

        CxrUserReturnOrder cxrUserReturnOrder =
            cxrUserReturnOrderService
                .getBaseMapper()
                .selectOne(
                    new LambdaQueryWrapper<CxrUserReturnOrder>()
                        .eq(CxrUserReturnOrder::getUserOrderId, cxrUserOrder.getId())
                        .eq(CxrUserReturnOrder::getDeleteStatus,
                            DeleteStatus.NOT_DELETED.getValue()));

        int longMilkQty =
            bo.getLongMilkCancelQuantity() - cxrUserReturnOrder.getLongMilkCancelQuantity();

        int freshMilkQty =
            cxrUserReturnOrder.getFreshMilkCancelQuantity() - bo.getFreshMilkCancelQuantity();

        // 常温奶：负数表示要加库存，正数减库存
        // 鲜羊奶：负数表示减库存，正数加库存
        if (longMilkQty != 0 || freshMilkQty != 0) {
            List<CxrCustomerStockDetail> cxrCustomerStockDetailList = new ArrayList<>();
            CxrCustomerStockDetail customerStockDetail = new CxrCustomerStockDetail();
            customerStockDetail.setRemark(
                StrUtil.format(
                    "退订单修改库存，订单id={},订单类型 ={}",
                    cxrUserOrder.getId(),
                    OrderTypeEnums.getType(cxrUserOrder.getOrderType()).getDesc()));
            customerStockDetail.setSourceId(cxrUserOrder.getId());
            customerStockDetail.setSourceType(
                SourceEnums.getType(cxrUserOrder.getOrderType()).getValue());
            customerStockDetail.setTerminalType(cxrUserOrder.getTerminalType());

            customerStockDetail.setFreshMilkQuantity(freshMilkQty);
            customerStockDetail.setOrderQuantity(0);
            customerStockDetail.setFreshMilkGiveQuantity(0);
            customerStockDetail.setFreshMilkSentQuantity(0);
            customerStockDetail.setExchangeUseFreshMilkQuantity(0);
            customerStockDetail.setDisributionFreshMilkQuantity(0);
            customerStockDetail.setTemporaryAddMilkQuantity(0);
            customerStockDetail.setLongMilkGiveQuantity(longMilkQty);
            customerStockDetail.setLongMilkGiveQuantity(0);
            customerStockDetail.setLongMilkSentQuantity(0);

            cxrCustomerStockDetailList.add(customerStockDetail);
            log.info("退订单修改常温奶库存：{}", JSONObject.toJSONString(cxrCustomerStockDetailList));
            Long customerId = cxrUserOrder.getCustomerId();
            remoteCustomerStockDetailService.add(customerId, cxrCustomerStockDetailList);

            if (longMilkQty != 0) {
                CxrLongMilkStockDTO cxrLongMilkStockDTO = new CxrLongMilkStockDTO();
                cxrLongMilkStockDTO.setUpdateBy(cxrUserOrder.getUpdateBy());
                cxrLongMilkStockDTO.setUpdateTime(new Date());
                cxrLongMilkStockDTO.setUpdateByType(cxrUserOrder.getUpdateByType());
                cxrLongMilkStockDTO.setUpdateByName(cxrUserOrder.getUpdateByName());
                cxrLongMilkStockDTO.setUnregNum(longMilkQty);
                cxrLongMilkStockDTO.setCxrCustomerId(customerId);
                remoteLongMilkStockService.returnOrderLongMilkAddAndSubStock(cxrLongMilkStockDTO);
                log.info("退订单修改,常温奶库存：{}", JSONObject.toJSONString(cxrLongMilkStockDTO));
            }
        }
    }

    /**
     * 订单审核之后的 再次编辑时 处理的逻辑
     *
     * @param obj
     * @return
     */
    @Override
    public void auditAfterChange(Object obj) {
        CxrUserReturnOrderBO bo = (CxrUserReturnOrderBO) obj;
        List<BusinessAgent> businessAgent = bo.getBusinessAgent();
        Long userOrderId = bo.getUserOrderId();
        // 废弃之前的业绩
        remoteEmployeeAchievementDetailService.achievemenDiscard(userOrderId, "订单修改废弃审核的订单业绩");

        cxrUserOrderService.orderAfterEmployeeHandler(userOrderId);
    }

    @Override
    public CxrUserOrder disributionOrderUpdate(Object obj) {

        boolean b = this.checkType(obj);
        if (!b) {
            throw new ServiceException("订单类型不准确!");
        }
        CxrUserReturnOrderBO bo = (CxrUserReturnOrderBO) obj;

        CxrUserOrder cxrUserOrder = cxrUserOrderService.getById(bo.getId());
        if (ObjectUtil.isEmpty(cxrUserOrder)) {
            throw new ServiceException("该订单已不存在!");
        }
        if (NumberUtil.equals(cxrUserOrder.getAuditStatus(), AuditStatusEnums.Audit.code())) {
            throw new ServiceException("订单已经审核不能编辑");
        }

        Function<Object, Integer> function = null;
        CxrUserReturnOrder returnOrder = cxrUserReturnOrderService.lambdaQuery()
            .eq(CxrUserReturnOrder::getUserOrderId, bo.getId())
            .eq(CxrUserReturnOrder::getDeleteStatus, DeleteStatus.not_deleted).one();
        Long salesId = returnOrder.getAfterSalesId();
        Long afterSalesId = bo.getAfterSalesId();
        if (afterSalesId != null) {
            CxrOrderAfterSale cxrOrderAfterSale = cxrOrderAfterSaleMapper.selectById(afterSalesId);
            if (cxrOrderAfterSale == null) {
                throw new ServiceException("售后单不存在");
            }
            if (!cxrUserOrder.getCustomerId().equals(cxrOrderAfterSale.getCustomerId())) {
                throw new ServiceException("当前退订单和售后工单客户手机号不一致!");
            }

            if (!cxrOrderAfterSale.getSaleStatus().equals(SaleStatus.IN_APPLICATION.getValue())) {
                throw new ServiceException("售后单不是待处理状态");
            }

            if (salesId != null && !afterSalesId.equals(salesId)) {
                function = (a) -> cxrOrderAfterSaleMapper.update(null, new LambdaUpdateWrapper<CxrOrderAfterSale>()
                    .set(CxrOrderAfterSale::getRefundOrderId, null)
                    .set(cxrOrderAfterSale.getGoodsType() != 3, CxrOrderAfterSale::getSaleStatus,
                        SaleStatus.IN_APPLICATION.getValue())
                    .eq(CxrOrderAfterSale::getId, salesId));
            }
        } else {
            if (salesId != null) {
                CxrOrderAfterSale cxrOrderAfterSale = cxrOrderAfterSaleMapper.selectById(salesId);
                function = (a) -> cxrOrderAfterSaleMapper.update(null, new LambdaUpdateWrapper<CxrOrderAfterSale>()
                    .set(CxrOrderAfterSale::getRefundOrderId, null)
                    .set(cxrOrderAfterSale.getGoodsType() != 3, CxrOrderAfterSale::getSaleStatus,
                        SaleStatus.IN_APPLICATION.getValue())
                    .eq(CxrOrderAfterSale::getId, salesId));
            }
        }

        if (ObjectUtil.isNotEmpty(bo.getUpdateBy())) {
            cxrUserOrder.setUpdateBy(bo.getUpdateBy());
            cxrUserOrder.setUpdateByName(bo.getUpdateByName());
        }
        cxrUserOrder.setUpdateTime(new Date());
        cxrUserOrder.setLongMilkGiveQuantity(bo.getLongMilkCancelQuantity());
        cxrUserOrder.setDeliverySites(CollUtil.isNotEmpty(bo.getDeliverySites()) ? JSONUtil.toJsonStr(bo.getDeliverySites()) : null);
        // 未审核
        if (!NumberUtil.equals(cxrUserOrder.getAuditStatus(), AuditStatusEnums.Audit.code())) {
            cxrUserOrder.setCustomerPhone(bo.getCustomerPhone());
            cxrUserOrder.setCustomerAdress(bo.getCustomerAdress());
            cxrUserOrder.setCustomerName(bo.getCustomerName());
            cxrUserOrder.setCustomerId(bo.getCustomerId());
            cxrUserOrder.setFreshMilkReturnQuantity(bo.getFreshMilkCancelQuantity());
            cxrUserOrder.setAmount(bo.getAmount());
            cxrUserOrder.setOrderImages(bo.getImages());
            cxrUserOrder.setRemark(bo.getRemark());
            cxrUserOrder.setBusinessAgent(bo.getBusinessAgent());

            cxrUserReturnOrderService
                .getBaseMapper()
                .update(
                    null,
                    new LambdaUpdateWrapper<CxrUserReturnOrder>()
                        .eq(CxrUserReturnOrder::getUserOrderId, bo.getId())
                        .set(CxrUserReturnOrder::getBusinessAgent, JSONObject.toJSONString(bo.getBusinessAgent()))
                        .set(CxrUserReturnOrder::getLongMilkSentAmount, bo.getLongMilkSentAmount())
                        // 开户银行字段不为null&&未审核    修改字段
                        .set(
                            StringUtils.isNotBlank(bo.getBankName()),
                            CxrUserReturnOrder::getBankName,
                            bo.getBankName())
                        // 账户名称字段不为null&&未审核    修改字段
                        .set(
                            StringUtils.isNotBlank(bo.getBankAccountName()),
                            CxrUserReturnOrder::getBankAccountName,
                            bo.getBankAccountName())
                        // 账户卡号字段不为null&&未审核    修改字段
                        .set(
                            StringUtils.isNotBlank(bo.getBankAccountNumber()),
                            CxrUserReturnOrder::getBankAccountNumber,
                            bo.getBankAccountNumber())
                        .set(
                            ObjectUtil.isNotEmpty(bo.getFreshMilkRefundQuantity()),
                            CxrUserReturnOrder::getFreshMilkRefundQuantity,
                            bo.getFreshMilkRefundQuantity())
                        .set(
                            ObjectUtil.isNotEmpty(bo.getFreshMilkCancelQuantity()),
                            CxrUserReturnOrder::getFreshMilkCancelQuantity,
                            bo.getFreshMilkCancelQuantity())
                        .set(
                            ObjectUtil.isNotEmpty(bo.getRebates()),
                            CxrUserReturnOrder::getRebates,
                            bo.getRebates())
                        .set(
                            ObjectUtil.isNotEmpty(bo.getRefundAmount()),
                            CxrUserReturnOrder::getRefundAmount,
                            bo.getRefundAmount())
                        .set(
                            ObjectUtil.isNotEmpty(bo.getLongMilkCancelQuantity()),
                            CxrUserReturnOrder::getLongMilkCancelQuantity,
                            bo.getLongMilkCancelQuantity())
                        .set(
                            ObjectUtil.isNotEmpty(bo.getFreshMilkCancelRestQuantity()),
                            CxrUserReturnOrder::getFreshMilkCancelRestQuantity,
                            bo.getFreshMilkCancelRestQuantity())
                        //   修改备注
                        .set(CxrUserReturnOrder::getRemark, bo.getRemark())
                        .set(CxrUserReturnOrder::getCustomerPhone, bo.getCustomerPhone())
                        .set(CxrUserReturnOrder::getCustomerAdress, bo.getCustomerAdress())
                        .set(CxrUserReturnOrder::getCustomerName, bo.getCustomerName())
                        .set(CxrUserReturnOrder::getCustomerId, bo.getCustomerId())
                        .set(CxrUserReturnOrder::getEmployeeRemark, bo.getEmployeeRemark())
                        .set(
                            ObjectUtil.isNotEmpty(bo.getAmount()),
                            CxrUserReturnOrder::getAmount,
                            bo.getAmount())
                        .set(
                            CollectionUtil.isNotEmpty(bo.getImages()),
                            CxrUserReturnOrder::getImages,
                            JSONUtil.toJsonStr(bo.getImages()))
                        .set(StringUtils.isNotBlank(bo.getRrCode()), CxrUserReturnOrder::getRrCode, bo.getRrCode())
                        .set(CxrUserReturnOrder::getAfterSalesId, bo.getAfterSalesId())
                        .set(CxrUserReturnOrder::getOrderMoney, bo.getOrderMoney())
                        .set(
                            CollectionUtil.isEmpty(bo.getImages()), CxrUserReturnOrder::getImages,
                            null));

            // 售后单关联退订单
            if (afterSalesId != null && !afterSalesId.equals(salesId)) {
                CxrOrderAfterSale cxrOrderAfterSale = cxrOrderAfterSaleMapper.selectById(afterSalesId);
                cxrOrderAfterSaleMapper.update(null, new LambdaUpdateWrapper<CxrOrderAfterSale>()
                    .set(CxrOrderAfterSale::getRefundOrderId, cxrUserOrder.getId())
                    .set(cxrOrderAfterSale.getGoodsType() != 3, CxrOrderAfterSale::getSaleStatus,
                        SaleStatus.PENDING.getValue())
                    .eq(CxrOrderAfterSale::getId, afterSalesId));
            }
            if (function != null) {
                Integer apply = function.apply(null);
                log.info("更新售后单号：{}", apply);
            }
        }
        return cxrUserOrder;
    }

    /**
     * 检查类型是否匹配
     *
     * @param obj@return
     */
    @Override
    public boolean checkType(Object obj) {
        return obj instanceof CxrUserReturnOrderBO;
    }

    /**
     * 订单审核处理
     *
     * @param id
     */
    @Override
    public void executeCxrUserOrderAudit(Long id) {
        CxrUserOrder cxrUserOrder = cxrUserOrderService.getById(id);

        CxrUserReturnOrder cxrUserReturnOrder =
            cxrUserReturnOrderService.getOne(
                new LambdaQueryWrapper<CxrUserReturnOrder>()
                    .eq(CxrUserReturnOrder::getUserOrderId, id));

        // 用户库存 + 流水明细
        SourceEnums type = SourceEnums.getType(cxrUserOrder.getOrderType());
        CxrCustomer customer = remoteCustomerService.queryByPhone(cxrUserOrder.getCustomerPhone());

        List<CxrCustomerStockDetail> cxrCustomerStockDetailList = new ArrayList<>();
        CxrCustomerStockDetail customerStockDetail = new CxrCustomerStockDetail();
        customerStockDetail.setRemark(
            StrUtil.format(
                "订单id={},订单类型 ={}",
                cxrUserOrder.getId(),
                OrderTypeEnums.getType(cxrUserOrder.getOrderType()).getDesc()));
        customerStockDetail.setSourceId(cxrUserOrder.getId());
        customerStockDetail
            .setSourceType(SourceEnums.getType(cxrUserOrder.getOrderType()).getValue());
        customerStockDetail.setTerminalType(cxrUserOrder.getTerminalType());

        customerStockDetail.setFreshMilkQuantity(
            -Convert.toInt(cxrUserReturnOrder.getFreshMilkCancelQuantity(), 0));
        customerStockDetail.setOrderQuantity(0);
        customerStockDetail.setFreshMilkGiveQuantity(0);
        customerStockDetail.setFreshMilkSentQuantity(0);
        customerStockDetail.setExchangeUseFreshMilkQuantity(0);
        customerStockDetail.setDisributionFreshMilkQuantity(0);
        customerStockDetail.setTemporaryAddMilkQuantity(0);
        customerStockDetail.setLongMilkQuantity(
            -Convert.toInt(cxrUserReturnOrder.getLongMilkCancelQuantity()));
        customerStockDetail.setLongMilkGiveQuantity(0);
        customerStockDetail.setLongMilkSentQuantity(0);
        cxrCustomerStockDetailList.add(customerStockDetail);

        customerStockDetailService.add(customer.getId(), cxrCustomerStockDetailList);

        // 常温奶同步减库存
        remoteOrderService.returnOrderLongMilkStock(id);

        // 退奶通知
        sendMessageReturnOrder(cxrUserOrder
        );

    }

    /**
     * 订单审核处理
     *
     * @param id
     */
    @Override
    @GlobalTransactional
    public void cxrUserOrderAudit(Long id, Boolean subLockFlag) {
        CxrUserOrder cxrUserOrder = cxrUserOrderService.getById(id);

        CxrUserReturnOrder cxrUserReturnOrder =
            cxrUserReturnOrderService.getOne(
                new LambdaQueryWrapper<CxrUserReturnOrder>()
                    .eq(CxrUserReturnOrder::getUserOrderId, id));

        // 用户库存 + 流水明细
        SourceEnums type = SourceEnums.getType(cxrUserOrder.getOrderType());
        CxrCustomer customer = remoteCustomerService.getById(cxrUserOrder.getCustomerId());

        List<CxrCustomerStockDetail> cxrCustomerStockDetailList = new ArrayList<>();
        CxrCustomerStockDetail customerStockDetail = new CxrCustomerStockDetail();
        customerStockDetail.setRemark(
            StrUtil.format(
                "订单id={},订单类型 ={}",
                cxrUserOrder.getId(),
                OrderTypeEnums.getType(cxrUserOrder.getOrderType()).getDesc()));
        customerStockDetail.setSourceId(cxrUserOrder.getId());
        customerStockDetail
            .setSourceType(SourceEnums.getType(cxrUserOrder.getOrderType()).getValue());
        customerStockDetail.setTerminalType(cxrUserOrder.getTerminalType());

        customerStockDetail.setFreshMilkQuantity(
            -Convert.toInt(cxrUserReturnOrder.getFreshMilkCancelQuantity(), 0));
        customerStockDetail.setOrderQuantity(0);
        customerStockDetail.setFreshMilkGiveQuantity(0);
        customerStockDetail.setFreshMilkSentQuantity(0);
        customerStockDetail.setExchangeUseFreshMilkQuantity(0);
        customerStockDetail.setDisributionFreshMilkQuantity(0);
        customerStockDetail.setTemporaryAddMilkQuantity(0);
        customerStockDetail.setLongMilkQuantity(
            -Convert.toInt(cxrUserReturnOrder.getLongMilkCancelQuantity()));
        customerStockDetail.setLongMilkGiveQuantity(0);
        customerStockDetail.setLongMilkSentQuantity(0);
        customerStockDetail.setSubLock(subLockFlag);
        cxrCustomerStockDetailList.add(customerStockDetail);
        //提前减
        if (subLockFlag) {
            Long lockStock = customer.getLockStock();
            long newLockStock = lockStock + customerStockDetail.getFreshMilkQuantity();
            if (newLockStock < 0) {
                throw new ServiceException("锁定库存异常");
            }
            cxrCustomerMapper.update(null, new LambdaUpdateWrapper<CxrCustomer>()
                .eq(CxrCustomer::getId, customer.getId())
                .set(CxrCustomer::getLockStock, newLockStock)
            );
        }
        customerStockDetailService.add(customer.getId(), cxrCustomerStockDetailList);

    }

    public void executeCxrUserOrderReverseAudit(CxrUserOrder cxrUserOrder) {

        Long id = cxrUserOrder.getId();
        CxrUserReturnOrder returnOrder = cxrUserReturnOrderService.getOne(
            new LambdaQueryWrapper<CxrUserReturnOrder>()
                .eq(CxrUserReturnOrder::getUserOrderId, id));

        if (returnOrder.getRefundSuccessFlag()) {
            throw new ServiceException("订单已经退款成功不能反审核");
        }

        Long customerId = cxrUserOrder.getCustomerId();
        OrderTypeEnums type = OrderTypeEnums.getType(cxrUserOrder.getOrderType());

        List<CxrCustomerStockDetail> cxrCustomerStockDetailList = new ArrayList<>();
        CxrCustomerStockDetail customerStockDetail = new CxrCustomerStockDetail();
        customerStockDetail.setRemark(StrUtil.format("订单id={},订单类型 ={}", id, type.getDesc()));
        customerStockDetail.setSourceId(id);
        customerStockDetail.setSourceType(SourceEnums.RETURN_ORDER_REVERSE.getValue());
        customerStockDetail.setTerminalType(cxrUserOrder.getTerminalType());

        customerStockDetail.setFreshMilkQuantity(Convert.toInt(returnOrder.getFreshMilkCancelQuantity(), 0));
        customerStockDetail.setOrderQuantity(0);
        customerStockDetail.setFreshMilkGiveQuantity(0);
        customerStockDetail.setFreshMilkSentQuantity(0);
        customerStockDetail.setExchangeUseFreshMilkQuantity(0);
        customerStockDetail.setDisributionFreshMilkQuantity(0);
        customerStockDetail.setTemporaryAddMilkQuantity(0);

        Integer longMilkCancelQuantity = returnOrder.getLongMilkCancelQuantity();
        customerStockDetail.setLongMilkQuantity(Convert.toInt(longMilkCancelQuantity));
        customerStockDetail.setLongMilkGiveQuantity(0);
        customerStockDetail.setLongMilkSentQuantity(0);

        cxrCustomerStockDetailList.add(customerStockDetail);

        customerStockDetailService.add(customerId, cxrCustomerStockDetailList);

        //退订单反审核加常温奶库存
        CxrLongMilkStockDTO cxrLongMilkStockDTO = new CxrLongMilkStockDTO();
        cxrLongMilkStockDTO.setUpdateBy(cxrUserOrder.getUpdateBy());
        cxrLongMilkStockDTO.setUpdateTime(new Date());
        cxrLongMilkStockDTO.setUpdateByType(cxrUserOrder.getUpdateByType());
        cxrLongMilkStockDTO.setUpdateByName(cxrUserOrder.getUpdateByName());
        cxrLongMilkStockDTO.setUnregNum(0 - longMilkCancelQuantity);
        cxrLongMilkStockDTO.setCxrCustomerId(customerId);
        remoteLongMilkStockService.returnOrderLongMilkAddAndSubStock(cxrLongMilkStockDTO);
        log.info("退订单修改,常温奶库存：{}", JSONObject.toJSONString(cxrLongMilkStockDTO));

        // 废弃之前的业绩,废弃后不用重新计算
        remoteEmployeeAchievementDetailService.achievemenDiscard(id, "订单修改废弃审核的订单业绩");

        //去掉锁库存代码
//        //反审核时锁定库存
//        Long afterSalesId = returnOrder.getAfterSalesId();
//        if (afterSalesId != null){
//            CxrOrderAfterSale cxrOrderAfterSale = cxrOrderAfterSaleMapper.selectById(afterSalesId);
//            if (cxrOrderAfterSale.getLockFlag()){
//                JSONObject map = new JSONObject();
//                map.put("orderId",returnOrder.getOrderId());
//                map.put("afterSalesId",afterSalesId);
//                map.put("customerId",returnOrder.getCustomerId());
//                map.put("qty",returnOrder.getFreshMilkCancelQuantity());
//                remoteCustomerService.orderRefundUpdateLockStock(map);
//            }
//        }
    }

    /**
     * 查询详情
     *
     * @param id
     * @return
     */
    @Override
    public CxrUserOrderVO executeOrderDetail(Long id) {
        return null;
    }

    @Override
    public void todoCxrActivity(Long orderId, Object obj) {

    }

    private void sendMessageReturnOrder(CxrUserOrder cxrUserOrder
    ) {
        ReturnOrderMsgBo msgBo = new ReturnOrderMsgBo();
        BigDecimal amount = cxrUserOrder.getAmount();
        Set<Long> proxyIds = cxrUserOrder.getBusinessAgent().stream().map(BusinessAgent::getProxyId)
            .collect(Collectors.toSet());

        List<CxrEmployee> cxrEmployees = cxrEmployeeMapper.selectBatchIds(proxyIds);
        Set<Long> siteIds = cxrEmployees.stream().map(CxrEmployee::getCxrSiteId).collect(Collectors.toSet());
        List<CxrEmployeePost> cxrEmployeePosts = cxrEmployeePostMapper.selectList(
            new LambdaQueryWrapper<CxrEmployeePost>()
                .in(CxrEmployeePost::getCxrSiteId, siteIds)
                .eq(CxrEmployeePost::getChargeStatus, ChargeStatus.IN_PROGRESS.getValue())
                .eq(CxrEmployeePost::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .apply("JSON_OVERLAPS(JSON_EXTRACT(cxr_post_id,'$.value'),{0})", PostType.DIRECTOR.getValue()));

        if (CollUtil.isNotEmpty(cxrEmployeePosts)) {
            Set<Long> employeeIds = cxrEmployeePosts.stream().map(CxrEmployeePost::getCxrEmployeeId)
                .collect(Collectors.toSet());
            cxrEmployees.addAll(cxrEmployeeMapper.selectBatchIds(employeeIds));
        }

        Set<String> openIds = cxrEmployees.stream().filter(cxrEmployee -> cxrEmployee.getWxOpenid() != null)
            .map(CxrEmployee::getWxOpenid).collect(
                Collectors.toSet());
        msgBo.setCustomerPhone(cxrUserOrder.getCustomerPhone());
        msgBo.setReturnOrderId(cxrUserOrder.getId());
        msgBo.setCustomerAddress(cxrUserOrder.getCustomerAdress());
        msgBo.setApplyTime(cxrUserOrder.getCreateTime());
        msgBo.setReturnAmount(amount);
        log.info(StrUtil.format("发送人员：{}", openIds));
        for (String openId : openIds) {
            msgBo.setOpenId(openId);
            mqUtil.sendSyncMessage(SendMessageReturnOrderConstant.MESSAGE_CENTER_CXR_CUSTOMER_RETURN_ORDER_TOPIC,
                SendMessageReturnOrderConstant.CUSTOMER_RETURN_ORDER_MSG_TAG,
                JSONUtil.toJsonStr(msgBo)
            );
        }


    }

}
