<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.business.base.customer.mapper.CxrCustomerMapper">

    <resultMap type="com.ruoyi.business.base.api.domain.CxrCustomer" id="CxrCustomerResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="genderType" column="gender_type"/>
        <result property="phone" column="phone"/>
        <result property="birthday" column="birthday"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="sourceType" column="source_type"/>
        <result property="wxOpenId" column="wx_open_id"/>
        <result property="wxUnionId" column="wx_union_id"/>
        <result property="wxHeadPortrait" column="wx_head_portrait"/>
        <result property="wxNickname" column="wx_nickname"/>
        <result property="sysDeptId" column="sys_dept_id"/>
        <result property="revision" column="revision"/>
        <result property="createBy" column="create_by"/>
        <result property="createByName" column="create_by_name"/>
        <result property="createByType" column="create_by_type"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateByName" column="update_by_name"/>
        <result property="updateByType" column="update_by_type"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleteBy" column="delete_by"/>
        <result property="deleteByName" column="delete_by_name"/>
        <result property="deleteByType" column="delete_by_type"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="deleteStatus" column="delete_status"/>
        <result property="sortNum" column="sort_num"/>
        <result property="remark" column="remark"/>
        <result property="spareId" column="spare_id"/>
        <result property="shouldSend" column="should_send"/>
        <result property="originStock" column="origin_stock"/>
    </resultMap>

    <sql id="BaseColumnList">
        id
        , name, gender_type, phone, birthday, audit_status, source_type, wx_open_id, wx_union_id, wx_head_portrait,
        wx_nickname, sys_dept_id, revision, create_by, create_by_name, create_by_type, create_time, update_by,
        update_by_name, update_by_type, update_time, delete_by, delete_by_name, delete_by_type, delete_time, delete_status, sort_num,
            remark, spare_id, provice, city, area, detail_distribution_address, customer_stock, customer_stock_zero_time, fresh_milk_order_total,
            fresh_milk_give_total, fresh_milk_sent_total, exchange_use_fresh_milk_total, distribution_fresh_milk_total, temporary_add_milk_total,
            long_milk_give_total, long_milk_sent_total,should_send,fresh_milk_road_way_sent_total,origin_stock,lock_stock

    </sql>
    <update id="updateShouldSendBatch">
        update cxr_customer
        set should_send=should_send + #{i},
            update_time = now()
        where id = #{id}

    </update>
    <update id="updateCustomerFreshMilkRoadWaySentAdd">
        UPDATE cxr_customer t
            INNER JOIN (SELECT t.cxr_customer_id,
                               sum(t.daily_boxs) quantity
                        FROM cxr_customer_way_number t
                        WHERE t.daily_date = #{min}
                        GROUP BY t.cxr_customer_id) tt
            ON t.id = tt.cxr_customer_id
        SET t.fresh_milk_road_way_sent_total = IFNULL(tt.quantity, 0) + fresh_milk_road_way_sent_total;
    </update>
    <update id="updateRoadWayChangeRecord">
        update cxr_road_way_change_record
        set sync_status ='Y',
            update_time= now()
        WHERE STATUS = 'Y'
          and sync_status = 'N';
    </update>

    <update id="updateFreshMilkRoadWayChange">
        <foreach collection="List" close="" open="" separator=";" item="item">
            update cxr_customer set fresh_milk_road_way_sent_total
            =#{item.freshMilkRoadWaySentTotal},revision=#{item.revision}+1
            where id=#{item.id} and revision=#{item.revision}
        </foreach>
    </update>

    <select id="getById" resultType="com.ruoyi.business.base.api.domain.CxrCustomer">
        select <include refid="BaseColumnList"></include> from cxr_customer
        where id = #{id} and delete_status = '0'
    </select>


    <select id="customerStockTotal" resultType="java.lang.Long">
        select
        ifnull( sum(t.customer_stock), 0 )
        from cxr_customer t
        left join cxr_customer_address t1 on t.id = t1.cxr_customer_id and t1.defalut_account_address="Y"
        LEFT join cxr_site t2 on t1.cxr_site_id = t2.id
        left join cxr_residential_quarters t3 on t1.cxr_residential_quarters_id = t3.id
        left join cxr_employee t4 on t1.cxr_employee_id =t4.id
        <where>
            t.delete_status = #{del}
            <if test="bo.provice !=null and bo.provice !=''">
                and t2.provice like concat('%',#{bo.provice},'%')
            </if>

            <if test="bo.name!=null and bo.name!=''">
                and t.name like concat('%',#{bo.name},'%')
            </if>
            <if test="bo.city !=null and bo.city !=''">
                and t2.city like concat('%',#{bo.city},'%')
            </if>
            <if test="bo.area !=null and bo.area !=''">
                and t2.area like concat('%',#{bo.area},'%')
            </if>
            <if test="bo.bigAreaName !=null and bo.bigAreaName !=''">
                and t2.cxr_root_region_name like concat('%',#{bo.bigAreaName},'%')
            </if>

            <if test="bo.receiverName!=null and bo.receiverName !=''">
                and t1.receiver_name like concat('%', #{bo.receiverName},'%')
            </if>

            <if test="bo.customerAdress!=null and  bo.customerAdress!=''">
                and t1.detail_distribution_address like concat('%', #{bo.customerAdress},'%')
            </if>

            <if test="bo.receiverPhone !=null and bo.receiverPhone!=''">
                and t1.receiver_phone like concat('%',#{bo.receiverPhone},'%')
            </if>

            <if test="bo.phone !=null and bo.phone !=''">
                and t.phone like concat('%',#{bo.phone},'%')
            </if>

            <if test="bo.addressPhone !=null and bo.addressPhone !=''">
                and t1.receiver_phone like concat('%',#{bo.addressPhone},'%')
            </if>

            <if test="bo.cxrSiteName !=null and bo.cxrSiteName !=''">
                and t2.name like concat('%',#{bo.cxrSiteName},'%')
            </if>
            <if test="bo.id !=null">
                and t.id =#{bo.id}
            </if>

            <if test="bo.employeeName !=null and bo.employeeName !=''">
                and t4.name like concat('%',#{bo.employeeName},'%')
            </if>
            <if test="bo.wxBind !=null and bo.wxBind">
                <![CDATA[ and t.wx_open_id <> '' ]]>
            </if>

            <if test="bo.wxBind !=null and !bo.wxBind">
                <![CDATA[ and t.wx_open_id = '' ]]>
            </if>
            order by t.create_time desc ,t1.create_time desc
        </where>


    </select>

    <select id="selectByCustomerId" resultType="com.ruoyi.business.base.customer.domain.vo.CxrCustomerCountVo">
        select t.name
             , t.gender_type
             , t.phone
             , t.remark
             , ifnull(t.fresh_milk_order_total, 0) as freshMilkOrderTotal
             , ifnull(t.fresh_milk_give_total, 0)  as freshMilkGiveTotal
             , ifnull(t.customer_stock, 0)         as freshMilkStock
        from cxr_customer t
        where t.id = #{id}
          and t.delete_status = #{del}
    </select>
    <select id="queryDistributionMilk" resultType="java.lang.Long">

        SELECT t2.id
        FROM cxr_customer t2
        WHERE t2.id IN (
        SELECT t.cxr_customer_id
        FROM cxr_customer_address t
        INNER JOIN (
        SELECT id
        FROM cxr_customer_address
        <where>
            <if test="employeeIds !=null and employeeIds.size() >0 ">
                AND cxr_employee_id in
                <foreach collection="employeeIds" item="value" separator="," open="(" close=")">
                    #{value}
                </foreach>
            </if>

            <if test="siteIds !=null and siteIds.size() >0 ">
                AND cxr_site_id in
                <foreach collection="siteIds" item="value" separator="," open="(" close=")">
                    #{value}
                </foreach>
            </if>
        </where>
        ) tt ON tt.id = t.id
        ) and t2.customer_stock > 0 order by t2.id asc

    </select>

    <select id="queryStockByid" resultType="java.lang.Integer">
        select ifnull(customer_stock, 0)
        from cxr_customer
        where id = #{id}
    </select>


    <select id="queryByPhone" resultType="com.ruoyi.business.base.api.domain.CxrCustomer">
        select id
        from cxr_customer
        where phone = #{phone}
          and delete_status = #{del}
    </select>

    <select id="queryStockGTZero" resultType="com.ruoyi.business.base.api.domain.CxrCustomer">
        SELECT *
        FROM cxr_customer t2
        where t2.customer_stock > 0
    </select>
    <select id="customerPageTwo" resultType="com.ruoyi.business.base.customer.domain.vo.CxrCustomerListVo">
        select id
        , origin_stock
        , name
        , phone
        , customer_stock
        , fresh_milk_order_total
        , fresh_milk_give_total
        , fresh_milk_sent_total
        , long_milk_give_total
        , wx_open_id
        , remark
        , fresh_milk_road_way_sent_total
        , labeled
        , create_time
        , update_time
        , update_by_name,
        create_by_type
        from
        cxr_customer
        INNER JOIN
        (
        select id from cxr_customer
        <where>
            <if test="bo.stockZore !=null and bo.stockZore ==true">
                and customer_stock=0
            </if>
            <if test="bo.phone !=null and bo.phone !=''">
                and phone like concat(#{bo.phone},'%')
            </if>
            <if test="bo.name !=null and bo.name !=''">
                and name like concat('%',#{bo.name},'%')
            </if>
            <if test="bo.wxBind !=null and !bo.wxBind">
                <![CDATA[ and wx_open_id  is  null  ]]>
            </if>
            <if test="bo.wxBind !=null and bo.wxBind">
                <![CDATA[ and wx_open_id  is  not  null ]]>
            </if>

            <if test="bo.createByType !=null and bo.createByType !=''">
                and create_by_type=#{bo.createByType}
            </if>

            <if test="bo.labeledIds !=null and bo.labeledIds.size() > 0">
                AND JSON_OVERLAPS(JSON_EXTRACT(labeled,'$[*].id'), JSON_ARRAY(
                    <foreach collection="bo.labeledIds" open="" close="" index="i" separator="," item="labelId">
                        #{labelId}
                    </foreach>
                ))
            </if>

            and delete_status = #{notDel}
            <if test="(siteIds!=null  and siteIds.size()>0)  or (bo.addressPhone!=null and  bo.addressPhone!='') ">
                and id in
                (select cxr_customer_id as id
                from cxr_customer_address
                <where>
                    <if test="bo.addressPhone == null">
                        defalut_account_address ='Y'
                    </if>
                    <if test="siteIds!=null  and siteIds.size()>0">
                        and cxr_site_id in
                        <foreach collection="siteIds" close=")" open="(" item="i" separator=",">
                            #{i}
                        </foreach>
                    </if>
                    <if test="bo.addressPhone!=null and  bo.addressPhone!=''">
                        and receiver_phone like concat(#{bo.addressPhone},'%')
                    </if>
                </where>
                )
            </if>
        </where>
        limit #{offset},#{size}
        ) temp using (id)

    </select>
    <select id="countCustomerTwo" resultType="com.ruoyi.business.base.customer.domain.dto.CountAndSumQuantity">

        select
        ifnull(sum(customer_stock),0) as sumQuantity,
        ifnull(count(id),0) as countQuantity,
        ifnull(sum(origin_stock),0) as originStockCountTotal,
        ifnull(sum(fresh_milk_sent_total),0) as orderCountTotal,
        ifnull(sum(fresh_milk_sent_total),0) as freshMilkSentTotal,
        ifnull(sum(fresh_milk_road_way_sent_total),0) as freshMilkRoadWaySentTotal
        from
        cxr_customer
        <where>
            <if test="bo.stockZore !=null and bo.stockZore ==true">
                and customer_stock=0
            </if>
            <if test="bo.phone !=null and bo.phone !=''">
                and phone like concat(#{bo.phone},'%')
            </if>

            <if test="bo.createByType !=null and bo.createByType !=''">
                and create_by_type=#{bo.createByType}
            </if>

            <if test="bo.name !=null and bo.name !=''">
                and name like concat('%',#{bo.name},'%')
            </if>

            <if test="bo.wxBind !=null and !bo.wxBind">
                <![CDATA[ and wx_open_id  is  null ]]>
            </if>

            <if test="bo.wxBind !=null and bo.wxBind">
                <![CDATA[ and wx_open_id  is  not  null ]]>
            </if>

            <if test="bo.labeledIds !=null and bo.labeledIds.size() > 0">
                AND JSON_OVERLAPS(JSON_EXTRACT(labeled,'$[*].id'), JSON_ARRAY(
                    <foreach collection="bo.labeledIds" open="" close="" index="i" separator="," item="labelId">
                        #{labelId}
                    </foreach>
                ))
            </if>

            and delete_status = #{notDel}
            <if test="siteIds!=null  and siteIds.size()>0  or bo.addressPhone!=null and  bo.addressPhone!='' ">
                and id in
                (select cxr_customer_id as id
                from cxr_customer_address
                <where>
                    <if test="bo.addressPhone ==null">
                        defalut_account_address ='Y'
                    </if>
                    <if test="siteIds!=null  and siteIds.size()>0">
                        and cxr_site_id in
                        <foreach collection="siteIds" close=")" open="(" item="i" separator=",">
                            #{i}
                        </foreach>
                    </if>
                    <if test="bo.addressPhone!=null and  bo.addressPhone!=''">
                        and receiver_phone like concat(#{bo.addressPhone},'%')
                    </if>
                </where>
                )
            </if>
        </where>

    </select>

    <select id="countCustomerTwoV2" resultType="com.ruoyi.business.base.customer.domain.dto.CountAndSumQuantity">

        select
        ifnull(sum(customer_stock),0) as sumQuantity,
        ifnull(count(id),0) as countQuantity
        from
        cxr_customer
        <where>
            <if test="bo.stockZore !=null and bo.stockZore ==true">
                and customer_stock=0
            </if>
            <if test="bo.phone !=null and bo.phone !=''">
                and phone like concat(#{bo.phone},'%')
            </if>

            <if test="bo.createByType !=null and bo.createByType !=''">
                and create_by_type=#{bo.createByType}
            </if>

            <if test="bo.name !=null and bo.name !=''">
                and name like concat('%',#{bo.name},'%')
            </if>

            <if test="bo.wxBind !=null and !bo.wxBind">
                <![CDATA[ and wx_open_id  is  null ]]>
            </if>

            <if test="bo.wxBind !=null and bo.wxBind">
                <![CDATA[ and wx_open_id  is  not  null ]]>
            </if>

            <if test="bo.labeledIds !=null and bo.labeledIds.size() > 0">
                AND JSON_OVERLAPS(JSON_EXTRACT(labeled,'$[*].id'), JSON_ARRAY(
                    <foreach collection="bo.labeledIds" open="" close="" index="i" separator="," item="labelId">
                        #{labelId}
                    </foreach>
                ))
            </if>

            and delete_status = #{notDel}
            <if test="siteIds!=null  and siteIds.size()>0  or bo.addressPhone!=null and  bo.addressPhone!='' ">
                and id in
                (select cxr_customer_id as id
                from cxr_customer_address
                <where>
                    <if test="bo.addressPhone ==null">
                        defalut_account_address ='Y'
                    </if>
                    <if test="siteIds!=null  and siteIds.size()>0">
                        and cxr_site_id in
                        <foreach collection="siteIds" close=")" open="(" item="i" separator=",">
                            #{i}
                        </foreach>
                    </if>
                    <if test="bo.addressPhone!=null and  bo.addressPhone!=''">
                        and receiver_phone like concat(#{bo.addressPhone},'%')
                    </if>
                </where>
                )
            </if>
        </where>

    </select>

    <select id="huiBoRenewalPortraitStock" resultType="com.ruoyi.business.base.customer.domain.vo.HuiBoRenewalPortVo">

        select t1.phone,
               t1.customer_stock                                      as surplusStock,
               t1.id                                                  as cxr_customer_id,
               t1.create_time,
               (t1.fresh_milk_order_total + t1.fresh_milk_give_total) as freshMilkOrderTotal
        from cxr_customer t1
        where t1.id in (select t.customer_id
                        from cxr_customer_stock_detail t
                        WHERE t.update_time >= #{startTime}
                          and t.update_time &lt;= #{endTime}
                        GROUP BY t.customer_id)
        order by t1.id asc

    </select>

    <select id="huiBoRenewalPortraitStockById"
            resultType="com.ruoyi.business.base.customer.domain.vo.HuiBoRenewalPortVo">

        select t1.phone,
               t1.customer_stock                                      as surplusStock,
               t1.id                                                  as cxr_customer_id,
               t1.create_time,
               (t1.fresh_milk_order_total + t1.fresh_milk_give_total) as freshMilkOrderTotal
        from cxr_customer t1
        where t1.id = #{id}
    </select>


    <select id="getDateRoadWays" resultType="com.ruoyi.business.base.api.domain.CxrRoadWay">

        SELECT any_value(cxr_customer_id)   AS cxrCustomerId,
               cxr_customer_address_id,
               distribution_date,
               sum(milk_distribution_total) as milkDistributionTotal
        FROM cxr_road_way
        WHERE delete_status = 0
          AND `status` = 1
          AND distribution_date = #{now}
          and adjustment_milk_create = 0
        GROUP BY cxr_customer_address_id

    </select>
    <select id="queryById" resultType="com.ruoyi.business.base.api.domain.CxrCustomer">

        select phone,name,id from cxr_customer
        where phone in
        <foreach collection="phones" separator="," item="i" open="(" close=")">
            #{i}
        </foreach>


    </select>
    <select id="selectActiveyCustomer" resultType="java.lang.Long">
        select t.id
        from cxr_customer t
        where t.id in (select cxr_customer_id from cxr_gift_module where cxr_customer_id is not null)
          and t.phone like concat(#{phone}, '%')

    </select>


    <select id="getModifyCustomerPhoneVo"
            resultType="com.ruoyi.business.base.CxrCustomerPhoneModify.domain.vo.ModifyCustomerPhoneVo">
        select id               as userId,
               phone            as account,
               name             as userName,
               wx_open_id       as wxOpenId,
               wx_union_id      as wxUnionid,
               wx_head_portrait as wxHeadPortrait,
               wx_nickname      as wxNickname
        from cxr_customer
        where delete_status = 0
          and id = #{userId}
    </select>

    <select id="selectCustomerWarningJobUpdateExpirationTime"
            resultType="com.ruoyi.business.base.api.domain.CxrCustomer">

        SELECT t.id,
               t.customer_stock,
               t.create_time,
               t.customer_stock_zero_time,
               t.expiration_time,
               t.phone,
               t.origin_stock
        FROM cxr_customer t
                 INNER JOIN (select id
                             from cxr_customer
                             WHERE (delete_status = '0' and (customer_stock > 0 or customer_stock_zero_time >=
                                                                                   DATE_ADD(NOW(), interval -1 MONTH)))
                             ORDER BY id ASC) as a on a.id = t.id

    </select>

    <select id="selectCustomerWarningJobUpdateExpirationTimeByIds"
            resultType="com.ruoyi.business.base.api.domain.CxrCustomer">

        SELECT t.id,
        t.customer_stock,
        t.create_time,
        t.customer_stock_zero_time,
        t.expiration_time,
        t.phone,
        t.origin_stock
        FROM cxr_customer t where t.id in
        <foreach collection="ids" open="(" close=")" index="i" separator="," item="id">
            #{id}
        </foreach>
    </select>

    <select id="selectCustomerWarningJobUpdateExpirationTimePageIds"
            resultType="com.ruoyi.business.base.api.domain.CxrCustomer">
        select idx.id
        from (select id
              from cxr_customer
              where customer_stock > 0
                and delete_status = '0'
              union
              select id
              from cxr_customer
              where customer_stock_zero_time >= DATE_ADD(NOW(), INTERVAL - 1 MONTH)
                and delete_status = '0') idx
        ORDER BY idx.id
    </select>

    <select id="queryCustomerIdByPhone" resultType="java.lang.Long">
        select id
        from cxr_customer
        where phone = #{phone}
          and delete_status = '0'
    </select>

    <update id="updateBatchExpirationTime">
        <foreach collection="cxrCustomers" index="i" item="bo" separator=";">
            update cxr_customer
            <set>
                expiration_time =#{bo.expirationTime}
            </set>
            where id=#{bo.id}
        </foreach>


    </update>

    <select id="queryPageByPhone" resultType="com.ruoyi.business.base.api.domain.CxrCustomer">
        SELECT `id`,
               `name`,
               `gender_type`,
               `phone`,
               `birthday`,
               `audit_status`,
               `source_type`,
               `wx_open_id`,
               `wx_union_id`,
               `wx_head_portrait`,
               `wx_nickname`,
               `sys_dept_id`,
               `revision`,
               `create_by`,
               `create_by_name`,
               `create_by_type`,
               `create_time`,
               `update_by`,
               `update_by_name`,
               `update_by_type`,
               `update_time`,
               `delete_by`,
               `delete_by_name`,
               `delete_by_type`,
               `delete_time`,
               `delete_status`,
               `sort_num`,
               `remark`,
               `spare_id`,
               `provice`,
               `city`,
               `area`,
               `detail_distribution_address`,
               `customer_stock`,
               `customer_stock_zero_time`,
               `fresh_milk_order_total`,
               `fresh_milk_give_total`,
               `fresh_milk_sent_total`,
               `exchange_use_fresh_milk_total`,
               `distribution_fresh_milk_total`,
               `temporary_add_milk_total`,
               `long_milk_give_total`,
               `long_milk_sent_total`,
               `expiration_time`,
               `should_send`,
               `fresh_milk_road_way_sent_total`,
               `labeled`,
               `origin_stock`,
               `is_import`
        FROM cxr_customer
        WHERE phone = #{bo.receiverName}
          AND delete_status = '0'
          AND id IN (SELECT cxr_customer_id FROM cxr_customer_address WHERE cxr_site_id IN (${bo.siteIds}))
    </select>

    <update id="orderRefundUpdateLockStock">
        UPDATE cxr_customer SET lock_stock = lock_stock + #{qty} WHERE id = #{customerId}
        <if test="qty &lt; 0">
            AND lock_stock + #{qty} >= 0
        </if>
    </update>

    <update id="del">
        UPDATE cxr_customer
        SET phone          = CONCAT_WS('', phone, '_', #{num}),
            delete_status  = '2',
            delete_time    = NOW(),
            delete_by      = #{loginUser.userId},
            delete_by_name = #{loginUser.userName},
            delete_by_type = #{loginUser.userType}
        WHERE id = #{id}
    </update>

    <select id="getCustomerBy" resultType="com.ruoyi.business.base.api.model.TagCustomerVo">
        SELECT
        c.id id,
        c.wx_union_id wxUnionId,
        c.wx_open_id wxOpenId,
        c.phone phone,
        c.customer_stock customerStock,
        c.customer_stock_zero_time customerStockZeroTime
        FROM cxr_customer c
        WHERE c.delete_status = 0
        <if test="customerIds!=null and customerIds.size>0">
            AND c.id in
            <foreach collection="customerIds" open="(" close=")" index="i" separator="," item="customerId">
                #{customerId}
            </foreach>
        </if>
    </select>

    <select id="selectAllBy" resultType="com.ruoyi.business.base.api.model.TagCustomerVo">
        SELECT
        c.id id,
        c.wx_union_id wxUnionId,
        c.wx_open_id wxOpenId,
        c.phone phone,
        c.customer_stock customerStock
        FROM cxr_customer c
        WHERE c.delete_status = 0
        <if test="maxCustomerId!=null and pageSize!=null">
            and c.id >= #{maxCustomerId} LIMIT #{pageSize}
        </if>
    </select>

    <select id="loadCustomerIdsBy" resultType="java.lang.Long">
        SELECT
        c.id id
        FROM cxr_customer c
        WHERE c.delete_status = 0
        <if test="maxCustomerId!=null and pageSize!=null">
            and c.id >= #{maxCustomerId} LIMIT #{pageSize}
        </if>
    </select>


    <select id="violationQueryCustomer"
            resultType="com.ruoyi.business.base.violationOrderUserWarning.domian.vo.ViolationCustomerVo">

        select t.id as customerId,t.phone as customerPhone,t.name as customerName FROM cxr_customer t
        <where>
            and t.delete_status="0"
            and t.phone like concat( '%',#{customerPhone}, '%')
        </where>
        limit #{limit}
    </select>

    <select id="customerStockInfo" resultType="com.ruoyi.business.base.customer.domain.vo.CustomerStockSumVo">
        SELECT (SELECT ifnull(
                                       sum(ifnull(t1.order_quantity, 0)) -
                                       sum(ifnull(t1.fresh_milk_sent_quantity, 0)) +
                                       sum(ifnull(t1.fresh_milk_give_quantity, 0)) -
                                       sum(ifnull(t1.milk_exchange_sum, 0)) -
                                       sum(ifnull(t1.conversion_quantity, 0)) -
                                       sum(ifnull(t1.fresh_milk_return_quantity, 0))
                           , 0)
                FROM cxr_user_order t1
                WHERE t1.customer_id = #{customerId}
                  and (t1.pay_status = 3 or t1.audit_status = 2)
                  AND t1.delete_status = 0)                  AS totalOrderQuantity,

               (SELECT ifnull(sum(t2.total_sum), 0)
                FROM cxr_customer_distribution_list_record t2
                WHERE t2.cxr_customer_id = #{customerId}) AS totalFreshMilkSentQuantity,
               (SELECT ifnull(sum(t2.milk_distribution_total), 0)
                FROM cxr_road_way t2
                WHERE t2.cxr_customer_id = #{customerId}
                  and t2.status = 0
                  and t2.change_status = -1
                  AND t2.delete_status = 0)                  AS totalNotSentQuantity,
               lock_stock,
               ifnull(t.customer_stock, 0)                   as totalSurplusQuantity
        FROM cxr_customer t
        where t.id = #{customerId}
    </select>
    <select id="customerStockSumInfo" resultType="com.ruoyi.business.base.customer.domain.vo.CustomerStockSumVo">


        SELECT * FROM (
                    SELECT customer_stock totalSurplusQuantity FROM cxr_customer WHERE id = #{customerId}
                      )c JOIN(
                          SELECT
                              SUM(IFNULL(order_quantity, 0))order_quantity,
                              SUM(IFNULL(fresh_milk_sent_quantity, 0)) orderFreshMilkSentQuantity,
                              SUM(IFNULL(fresh_milk_give_quantity, 0))fresh_milk_give_quantity,
                              SUM(IF(order_type = '5',IFNULL(milk_exchange_sum, 0),0))exchangeMilkTotal,
                              SUM(IF(order_type = '4',IFNULL(conversion_quantity, 0),0))transferOutTotal,
                              SUM(IF(order_type = '2',IFNULL(fresh_milk_return_quantity, 0),0))fresh_milk_return_quantity
                          FROM cxr_user_order o
                          WHERE customer_id = #{customerId}
                            AND (pay_status = '3' OR audit_status = '2')
                            AND delete_status = '0'
                      )a JOIN
                      (SELECT
                           SUM(IFNULL(conversion_quantity, 0))transferInTotal
                       FROM cxr_user_order o
                       WHERE customer_id_switch = #{customerId}
                         AND order_type = '4'
                         AND audit_status = '2'
                         AND delete_status = '0'
                      ) b JOIN  (
            SELECT sum(t2.milk_distribution_total)totalNotSentQuantity
            FROM cxr_road_way t2
            WHERE t2.cxr_customer_id = #{customerId}
              and t2.status = 0
              and t2.change_status = -1
              AND t2.delete_status = 0
        )d JOIN (
            SELECT sum(t2.total_sum)freshMilkSentQuantity
            FROM cxr_customer_distribution_list_record t2
            WHERE t2.cxr_customer_id = #{customerId})f
    </select>
</mapper>
