package com.ruoyi.business.cxrSiteInventory.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.ruoyi.business.base.api.domain.*;
import com.ruoyi.business.base.api.domain.bo.CxrShaftAuditBo;
import com.ruoyi.business.base.api.domain.json.GoodsInfo;
import com.ruoyi.business.base.api.dubbo.RemoteCxrSaleProductService;
import com.ruoyi.business.base.api.dubbo.RemoteSiteService;
import com.ruoyi.business.base.api.model.LoginEmployee;
import com.ruoyi.business.cxrEmployeeDimissionApply.mapper.CxrSiteMapper;
import com.ruoyi.business.cxrSiteInventory.domain.CxrSiteInventory;
import com.ruoyi.business.cxrSiteInventory.domain.bo.CxrSiteInventoryAddEditBo;
import com.ruoyi.business.cxrSiteInventory.domain.bo.CxrSiteInventoryBo;
import com.ruoyi.business.cxrSiteInventory.domain.bo.CxrSiteInventoryPageBo;
import com.ruoyi.business.cxrSiteInventory.domain.vo.*;
import com.ruoyi.business.cxrSiteInventory.mapper.CxrSiteInventoryMapper;
import com.ruoyi.business.cxrSiteInventory.mapper.CxrSiteStockStatisticsMapper;
import com.ruoyi.business.cxrSiteInventory.service.ICxrSiteInventoryService;
import com.ruoyi.business.cxrYangMlik.domain.vo.YangMilkTotal;
import com.ruoyi.common.core.domain.LoginInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.PageTableDataInfo;
import com.ruoyi.common.core.enums.DeleteStatus;
import com.ruoyi.common.core.enums.ShaftAuditStatus;
import com.ruoyi.common.core.enums.SiteOperationType;
import com.ruoyi.common.core.enums.SiteStockStatisticsType;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.JsonUtils;
import com.ruoyi.common.enums.AuditStatusEnums;
import com.ruoyi.common.satoken.utils.LoginUtil;
import com.ruoyi.common.satoken.utils.helper.StaffLoginHelper;
import com.ruoyi.core.base.mapper.CxrFileMapper;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 盘点Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-10-28
 */
@RequiredArgsConstructor
@Service
@Slf4j
@GlobalTransactional(rollbackFor = Exception.class)
public class CxrSiteInventoryServiceImpl
    extends MPJBaseServiceImpl<CxrSiteInventoryMapper, CxrSiteInventory>
    implements ICxrSiteInventoryService {

    private final CxrSiteInventoryMapper baseMapper;

    private final CxrSiteMapper cxrSiteMapper;


    private final CxrSiteStockStatisticsMapper siteStockStatisticsMapper;
    @DubboReference
    private RemoteCxrSaleProductService remoteCxrSaleProductService;
    @DubboReference
    private RemoteSiteService remoteSiteService;

    @Autowired
    @Qualifier("futureThreadPool")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private CxrFileMapper cxrFileMapper;


    @Override
    public PageTableDataInfo<CxrSiteInventoryListVo> staffpage(
        CxrSiteInventoryBo cxrSiteInventoryBo, PageQuery pageQuery) {
        return getPageTableDataInfo(cxrSiteInventoryBo, pageQuery);
    }

    @Override
    public PageTableDataInfo<CxrSiteInventoryListVo> staffAuditpage(
        CxrSiteInventoryBo cxrSiteInventoryBo, PageQuery pageQuery) {
        return getPageTableDataInfo(cxrSiteInventoryBo, pageQuery);
    }

    @Override
    public SiteInventoryPageVo page(CxrSiteInventoryPageBo cxrSiteInventoryBo, PageQuery pageQuery) {
        if (pageQuery.getPageSize() == 0) {
            pageQuery.setPageSize(20);
        }
        LoginInfo loginUser = LoginUtil.getLoginUser();
        Long deptId = loginUser.getDeptId();
        cxrSiteInventoryBo.setSysDeptId(deptId);

        IPage<CxrSiteInventoryPageVo> voIPage = baseMapper.page(cxrSiteInventoryBo, pageQuery.build());
        List<CxrSaleProduct> cxrSaleProducts = remoteCxrSaleProductService.queryProcuctAllForYXN();
        List<CxrSiteInventoryPageVo> records = voIPage.getRecords();

        // 鲜羊奶
        List<CxrSaleProduct> saleProducts =
            cxrSaleProducts.stream()
                .filter(
                    a ->
                        a.getCxrProductFormId()
                            .equals(remoteCxrSaleProductService.queryMilkProcuctId(deptId)))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(records)) {
            List<CxrSiteInventoryPageVo> newRecords = new ArrayList<>();

            for (CxrSiteInventoryPageVo record : records) {
                // 1. 处理盘点库存记录
                List<GoodsInfo> goodsInfos = JSONUtil.toList(record.getGoodsInfo(), GoodsInfo.class);
                Map<Long, GoodsInfo> goodsInfoMap =
                    goodsInfos.stream()
                        .collect(
                            Collectors.toMap(GoodsInfo::getProductId, Function.identity(), (v1, v2) -> v2));
                List<GoodsInfo> returnGoodsInfo = new ArrayList<>();

                for (CxrSaleProduct cxrSaleProduct : saleProducts) {
                    GoodsInfo goodsInfo = new GoodsInfo();
                    goodsInfo.setProductId(cxrSaleProduct.getId());
                    goodsInfo.setProductName(cxrSaleProduct.getName());
                    goodsInfo.setProductAlias(cxrSaleProduct.getProductAlias());
                    goodsInfo.setQuantity(0L);
                    GoodsInfo temp = goodsInfoMap.get(cxrSaleProduct.getId());
                    if (ObjectUtil.isNotEmpty(temp)) {
                        goodsInfo.setQuantity(temp.getQuantity());
                    }
                    returnGoodsInfo.add(goodsInfo);
                }
                record.setGoodsInfoList(returnGoodsInfo);
                record.setType(1);  // 设置为盘点库存类型
                newRecords.add(record);

                // 2. 创建系统库存记录
                CxrSiteInventoryPageVo systemRecord = BeanUtil.copyProperties(record, CxrSiteInventoryPageVo.class);
                systemRecord.setType(2);  // 系统库存类型
                CxrSiteStockStatistics milk = this.getMilk(record.getSiteId(), record.getInventoryTime());

                // 获取系统库存的商品Map
                Map<Long, GoodsInfo> systemGoodsMap = milk != null ?
                    milk.getShaftMilkDistributionInfo().stream()
                        .collect(Collectors.toMap(GoodsInfo::getProductId, Function.identity())) :
                    new HashMap<>();

                // 使用saleProducts构建完整的商品列表
                List<GoodsInfo> systemGoodsInfo = new ArrayList<>();
                for (CxrSaleProduct cxrSaleProduct : saleProducts) {
                    GoodsInfo goodsInfo = new GoodsInfo();
                    goodsInfo.setProductId(cxrSaleProduct.getId());
                    goodsInfo.setProductName(cxrSaleProduct.getName());
                    goodsInfo.setProductAlias(cxrSaleProduct.getProductAlias());

                    // 如果系统库存中有该商品，使用系统库存数量，否则设为0
                    GoodsInfo systemGoods = systemGoodsMap.get(cxrSaleProduct.getId());
                    goodsInfo.setQuantity(systemGoods != null ? systemGoods.getQuantity() : 0L);

                    systemGoodsInfo.add(goodsInfo);
                }
                systemRecord.setGoodsInfo(JSONUtil.toJsonStr(systemGoodsInfo));
                systemRecord.setGoodsInfoList(systemGoodsInfo);
                systemRecord.setInventoryTotalSum(milk != null ? milk.getMilkTotal() : 0L);
                newRecords.add(systemRecord);

                // 3. 创建差异记录
                CxrSiteInventoryPageVo diffRecord = BeanUtil.copyProperties(record, CxrSiteInventoryPageVo.class);
                diffRecord.setType(3);  // 差异数类型
                List<GoodsInfo> diffGoodsInfo = new ArrayList<>();

                for (GoodsInfo inventoryGoods : returnGoodsInfo) {
                    GoodsInfo diffInfo = BeanUtil.copyProperties(inventoryGoods, GoodsInfo.class);
                    GoodsInfo systemGoods = systemGoodsMap.getOrDefault(inventoryGoods.getProductId(), new GoodsInfo());
                    diffInfo.setQuantity(
                        inventoryGoods.getQuantity() - ObjectUtil.defaultIfNull(systemGoods.getQuantity(), 0L));
                    diffGoodsInfo.add(diffInfo);
                }
                diffRecord.setGoodsInfo(JSONUtil.toJsonStr(diffGoodsInfo));
                diffRecord.setGoodsInfoList(diffGoodsInfo);
                diffRecord.setInventoryTotalSum(
                    record.getInventoryTotalSum() - (milk != null ? milk.getMilkTotal() : 0L));
                newRecords.add(diffRecord);
            }

            // 处理类型筛选
            if (ObjectUtil.isNotNull(cxrSiteInventoryBo.getType())) {
                newRecords.removeIf(record -> !record.getType().equals(cxrSiteInventoryBo.getType()));
            }
            voIPage.setRecords(newRecords);
        }

        SiteInventoryPageVo cxrSiteInventoryPageVo = new SiteInventoryPageVo();
        PageTableDataInfo<CxrSiteInventoryPageVo> tableDataInfo = PageTableDataInfo.build(voIPage);
        cxrSiteInventoryPageVo.setCxrSaleProducts(saleProducts);
        cxrSiteInventoryPageVo.setDataInfo(tableDataInfo);

        return cxrSiteInventoryPageVo;
    }

    @Override
    public Boolean add(CxrSiteInventoryAddEditBo cxrSiteInventoryBo) {
        LoginEmployee loginUser = StaffLoginHelper.getLoginUser();
        CxrSiteInventory cxrSiteInventory = new CxrSiteInventory();

        long sum =
            cxrSiteInventoryBo.getGoodsInfoList().stream().mapToLong(GoodsInfo::getQuantity).sum();
        cxrSiteInventory.setInventoryTotalSum(sum);

        cxrSiteInventory.setStatus(AuditStatusEnums.ToAudit.code());
        cxrSiteInventory.setInventoryTime(cxrSiteInventoryBo.getInventoryTime());
//        cxrSiteInventory.setInventoryTime(new Date());
        cxrSiteInventory.setGoodsInfo(JsonUtils.toJsonString(cxrSiteInventoryBo.getGoodsInfoList()));
        cxrSiteInventory.setSiteId(cxrSiteInventoryBo.getSiteId());
        cxrSiteInventory.setCreateBy(loginUser.getUserId());
        cxrSiteInventory.setRemark(cxrSiteInventoryBo.getRemark());
        cxrSiteInventory.setFile(cxrSiteInventoryBo.getFile());
        return baseMapper.insert(cxrSiteInventory) > 0;
    }

    @Override
    public Boolean edit(CxrSiteInventoryAddEditBo cxrSiteInventoryBo) {

        CxrSiteInventory cxrSiteInventory = baseMapper.selectById(cxrSiteInventoryBo.getId());

        if (ObjectUtil.isEmpty(cxrSiteInventory)) {
            throw new ServiceException("找不到该数据");
        }

        if (cxrSiteInventory.getStatus().equals(AuditStatusEnums.Audit.code())) {
            throw new ServiceException("已审核单不能修改！");
        }

        long sum =
            cxrSiteInventoryBo.getGoodsInfoList().stream().mapToLong(GoodsInfo::getQuantity).sum();

        return baseMapper.update(
            null,
            new LambdaUpdateWrapper<CxrSiteInventory>()
                .eq(CxrSiteInventory::getId, cxrSiteInventoryBo.getId())
                .set(
                    ObjectUtil.isNotEmpty(cxrSiteInventoryBo.getSiteId()),
                    CxrSiteInventory::getSiteId,
                    cxrSiteInventoryBo.getSiteId())
                .set(
                    ObjectUtil.isNotEmpty(cxrSiteInventoryBo.getInventoryTime()),
                    CxrSiteInventory::getInventoryTime,
                    cxrSiteInventoryBo.getInventoryTime())
                .set(
                    ObjectUtil.isNotEmpty(cxrSiteInventoryBo.getRemark()),
                    CxrSiteInventory::getRemark,
                    cxrSiteInventoryBo.getRemark())
                .set(
                    ObjectUtil.isNotEmpty(cxrSiteInventoryBo.getGoodsInfoList()),
                    CxrSiteInventory::getGoodsInfo,
                    JsonUtils.toJsonString(cxrSiteInventoryBo.getGoodsInfoList()))
                .set(
                    ObjectUtil.isNotNull(cxrSiteInventoryBo.getFile()),
                    CxrSiteInventory::getFile,
                    JSONUtil.toJsonStr(cxrSiteInventoryBo.getFile()))
                .set(CxrSiteInventory::getInventoryTotalSum, sum))
            > 0;
    }

    @Override
    public Boolean applyPass(Long id) {
        CxrSiteInventory siteInventory = baseMapper.selectById(id);
        if (ObjectUtil.isEmpty(siteInventory)) {
            throw new ServiceException("审核数据未找到！");
        }

        if (siteInventory.getStatus().equals(AuditStatusEnums.Audit.code())) {
            throw new ServiceException("不能重复提交！");
        }

        return baseMapper.update(
            null,
            new LambdaUpdateWrapper<CxrSiteInventory>()
                .eq(CxrSiteInventory::getId, id)
                .set(CxrSiteInventory::getStatus, AuditStatusEnums.Audit.code())
                .set(CxrSiteInventory::getAuditTime, new Date())
                .set(CxrSiteInventory::getAuditUserId, StaffLoginHelper.getLoginUser().getUserId()))
            > 0;
    }

    @Override
    public Boolean applyRefuse(Long id, String refuse) {
        CxrSiteInventory siteInventory = baseMapper.selectById(id);
        if (ObjectUtil.isEmpty(siteInventory)) {
            throw new ServiceException("审核数据未找到！");
        }
        if (siteInventory.getStatus().equals(AuditStatusEnums.Audit.code())) {
            throw new ServiceException("已审核通过该单不能再次审核！");
        }

        if (siteInventory.getStatus().equals(AuditStatusEnums.Refuse.code())) {
            throw new ServiceException("不能重复提交！");
        }

        return baseMapper.update(
            null,
            new LambdaUpdateWrapper<CxrSiteInventory>()
                .eq(CxrSiteInventory::getId, id)
                .set(CxrSiteInventory::getStatus, AuditStatusEnums.Refuse.code())
                .set(CxrSiteInventory::getRefusalCause, refuse)
                .set(CxrSiteInventory::getAuditTime, new Date())
                .set(CxrSiteInventory::getAuditUserId, StaffLoginHelper.getLoginUser().getUserId()))
            > 0;
    }

    @Override
    public CxrSiteInventoryVo detail(Long id) {
        CxrSiteInventory siteInventory = baseMapper.selectById(id);
        if (ObjectUtil.isEmpty(siteInventory)) {
            throw new ServiceException("数据未找到！");
        }
        CxrSiteInventoryVo siteInventoryVo =
            BeanUtil.copyProperties(siteInventory, CxrSiteInventoryVo.class);

        String name = cxrSiteMapper.selectById(siteInventory.getSiteId()).getName();
        // siteName
        siteInventoryVo.setSiteName(name);

        List<CxrSiteInventoryGoodsInfo> recordGoodsInfoList = JSONUtil.toList(siteInventoryVo.getGoodsInfo(),
            CxrSiteInventoryGoodsInfo.class);
        CxrSiteStockStatistics milk = this.getMilk(siteInventory.getSiteId(), siteInventory.getInventoryTime());
        //合并两个产品id再循环
        Map<Long, CxrSiteInventoryGoodsInfo> cxrSiteInventoryProductMap =
            recordGoodsInfoList.stream().collect(Collectors.toMap(CxrSiteInventoryGoodsInfo::getProductId, Function.identity()));
        if (ObjectUtil.isNotEmpty(milk)) {
            Map<Long, GoodsInfo> siteStockProductMap =
                milk.getShaftMilkDistributionInfo().stream().collect(Collectors.toMap(GoodsInfo::getProductId, Function.identity()));
            List<Long> mergedProductIds = new ArrayList<>();
            mergedProductIds.addAll(cxrSiteInventoryProductMap.keySet());
            mergedProductIds.addAll(siteStockProductMap.keySet());
            // 去重
            mergedProductIds = mergedProductIds.stream().distinct().collect(Collectors.toList());
            for (Long productId : mergedProductIds) {
                CxrSiteInventoryGoodsInfo cxrSiteInventoryGoodsInfo = cxrSiteInventoryProductMap.get(productId);
                GoodsInfo siteStockGoodsInfo = ObjectUtil.defaultIfNull(siteStockProductMap.get(productId), new GoodsInfo());
                if (cxrSiteInventoryGoodsInfo == null) {
                    cxrSiteInventoryGoodsInfo = new CxrSiteInventoryGoodsInfo();
                    cxrSiteInventoryGoodsInfo.setProductId(siteStockGoodsInfo.getProductId());
                    cxrSiteInventoryGoodsInfo.setQuantity(0L);
                    cxrSiteInventoryGoodsInfo.setProductAlias(siteStockGoodsInfo.getProductAlias());
                    cxrSiteInventoryGoodsInfo.setProductName(siteStockGoodsInfo.getProductName());
                    recordGoodsInfoList.add(cxrSiteInventoryGoodsInfo);
                }
                cxrSiteInventoryGoodsInfo.setSystemQuantity(siteStockGoodsInfo.getQuantity());
                cxrSiteInventoryGoodsInfo.setQuantityVariance(cxrSiteInventoryGoodsInfo.getQuantity() - siteStockGoodsInfo.getQuantity());
            }
            List<CxrSiteInventoryGoodsInfoStr> goodsInfoStrs = recordGoodsInfoList.stream().map(x -> {
                CxrSiteInventoryGoodsInfoStr goodsInfoStr = BeanUtil.copyProperties(x, CxrSiteInventoryGoodsInfoStr.class);
                goodsInfoStr.setProductId(x.getProductId().toString());
                return goodsInfoStr;
            }).collect(Collectors.toList());
            siteInventoryVo.setGoodsInfo(JSONUtil.toJsonStr(goodsInfoStrs));
        }
        return siteInventoryVo;
    }

    @Override
    public CxrSiteInventoryDateilVo detailInventory(Long id) {
        CxrSiteInventoryPageVo dateilVo = baseMapper.detailInventory(id);
        if (ObjectUtil.isEmpty(dateilVo)) {
            throw new ServiceException("数据未找到！");
        }

        List<CxrSaleProduct> saleProducts = remoteCxrSaleProductService.queryProcuctAllForYXN();
        CxrSiteInventoryDateilVo inventoryDateilVo = new CxrSiteInventoryDateilVo();

        List<GoodsInfo> goodsInfos = JSONUtil.toList(dateilVo.getGoodsInfo(), GoodsInfo.class);

        Map<Long, GoodsInfo> goodsInfoMap =
            goodsInfos.stream()
                .collect(
                    Collectors.toMap(GoodsInfo::getProductId, Function.identity(), (v1, v2) -> v2));
        List<GoodsInfo> returnGoodsInfo = new ArrayList<>();

        for (CxrSaleProduct cxrSaleProduct : saleProducts) {
            GoodsInfo goodsInfo = new GoodsInfo();
            goodsInfo.setProductId(cxrSaleProduct.getId());
            goodsInfo.setProductName(cxrSaleProduct.getName());
            goodsInfo.setProductAlias(cxrSaleProduct.getProductAlias());
            goodsInfo.setQuantity(0L);
            GoodsInfo temp = goodsInfoMap.get(cxrSaleProduct.getId());
            if (ObjectUtil.isNotEmpty(temp)) {
                goodsInfo.setQuantity(temp.getQuantity());
            }
            returnGoodsInfo.add(goodsInfo);
        }
        dateilVo.setGoodsInfoList(returnGoodsInfo);

        Long milkTotal = getMilkTotal(dateilVo.getSiteId(), dateilVo.getInventoryTime());
        dateilVo.setSystemTotalSum(
            milkTotal);
        dateilVo.setQuantityVariance(dateilVo.getInventoryTotalSum() - milkTotal);

        inventoryDateilVo.setCxrSaleProducts(saleProducts);

        String fileStr = dateilVo.getFile();
        if (StrUtil.isNotEmpty(fileStr)) {
            dateilVo.setFileContain(getFileContain(fileStr));
        }

        inventoryDateilVo.setCxrSiteInventoryPageVo(dateilVo);
        return inventoryDateilVo;
    }

    private List<FileContain> getFileContain(String fileStr) {
        List<FileContain> fileContains = JSONUtil.toList(fileStr, FileContain.class);
        if (CollUtil.isNotEmpty(fileContains)) {
            for (FileContain fileContain : fileContains) {
                String url = fileContain.getUrl();
                String newFileId = cxrFileMapper.queryNewFileId(url);
                if (StringUtils.isNotBlank(newFileId)) {
                    fileContain.setUrl(newFileId);
                }
            }
        }
        return fileContains;
    }


    private Long getMilkTotal(Long siteId, Date date) {
        LocalDate inventoryTime = date.toInstant()
            .atZone(ZoneId.systemDefault())
            .toLocalDate();
        CxrSiteStockStatistics siteStockStatistics = siteStockStatisticsMapper.selectOne(
            new LambdaQueryWrapper<CxrSiteStockStatistics>()
                .select(CxrSiteStockStatistics::getMilkTotal)
                .eq(CxrSiteStockStatistics::getCxrSiteId, siteId)
                .eq(CxrSiteStockStatistics::getDataDate, inventoryTime)
                .eq(CxrSiteStockStatistics::getShaft, SiteStockStatisticsType.CURRENT_STOCK.getValue())
                .eq(CxrSiteStockStatistics::getDeleteStatus, DeleteStatus.not_deleted)
        );

//        Long totalSum = 0l;
//        if (ObjectUtil.isEmpty(siteStockStatistics)) {
//            CxrSite cxrSite = cxrSiteMapper.selectById(siteId);
//            List<GoodsInfo> stockInfo = cxrSite.getStockInfo();
//            if (CollUtil.isNotEmpty(stockInfo)) {
//                totalSum = stockInfo.stream().mapToLong(GoodsInfo::getQuantity).sum();
//            }
//        } else {
//            totalSum = siteStockStatistics.getMilkTotal();
//        }

        return siteStockStatistics == null ? 0l : siteStockStatistics.getMilkTotal();
    }

    private CxrSiteStockStatistics getMilk(Long siteId, Date date) {
        LocalDate inventoryTime = date.toInstant()
            .atZone(ZoneId.systemDefault())
            .toLocalDate();
        CxrSiteStockStatistics siteStockStatistics = siteStockStatisticsMapper.selectOne(
            new LambdaQueryWrapper<CxrSiteStockStatistics>()
                .eq(CxrSiteStockStatistics::getCxrSiteId, siteId)
                .eq(CxrSiteStockStatistics::getDataDate, inventoryTime)
                .eq(CxrSiteStockStatistics::getShaft, SiteStockStatisticsType.CURRENT_STOCK.getValue())
                .eq(CxrSiteStockStatistics::getDeleteStatus, DeleteStatus.not_deleted)
        );

//        Long totalSum = 0l;
//        if (ObjectUtil.isEmpty(siteStockStatistics)) {
//            CxrSite cxrSite = cxrSiteMapper.selectById(siteId);
//            List<GoodsInfo> stockInfo = cxrSite.getStockInfo();
//            if (CollUtil.isNotEmpty(stockInfo)) {
//                totalSum = stockInfo.stream().mapToLong(GoodsInfo::getQuantity).sum();
//            }
//        } else {
//            totalSum = siteStockStatistics.getMilkTotal();
//        }

        return siteStockStatistics;
    }

    private PageTableDataInfo getPageTableDataInfo(CxrSiteInventoryBo cxrSiteInventoryBo, PageQuery pageQuery) {

        if (pageQuery.getPageSize() == 0) {
            pageQuery.setPageSize(20);
        }

        LoginEmployee loginUser = StaffLoginHelper.getLoginUser();
        // 管辖站点的数据查询
        if (ObjectUtil.isEmpty(cxrSiteInventoryBo.getSiteId())) {
            List<Long> longs = loginUser.getAuthSiteIds().stream().collect(Collectors.toList());
            cxrSiteInventoryBo.setSiteIds(longs);
        }
        IPage<CxrSiteInventoryListVo> inventoryListVoIPage =
            baseMapper.staffpage(cxrSiteInventoryBo, pageQuery.build());
        List<CxrSiteInventoryListVo> records = inventoryListVoIPage.getRecords();
        if (CollUtil.isNotEmpty(records)) {

            for (CxrSiteInventoryListVo record : records) {
                CxrSiteStockStatistics milk = this.getMilk(record.getSiteId(), record.getInventoryTime());
                if (milk == null) continue;
                List<CxrSiteInventoryGoodsInfo> recordGoodsInfoList = JSONUtil.toList(record.getGoodsInfo(),
                    CxrSiteInventoryGoodsInfo.class);
                //合并两个产品id再循环
                Map<Long, CxrSiteInventoryGoodsInfo> cxrSiteInventoryProductMap =
                    recordGoodsInfoList.stream().collect(Collectors.toMap(CxrSiteInventoryGoodsInfo::getProductId, Function.identity()));
                Map<Long, GoodsInfo> siteStockProductMap =
                    milk.getShaftMilkDistributionInfo().stream().collect(Collectors.toMap(GoodsInfo::getProductId, Function.identity()));
                List<Long> mergedProductIds = new ArrayList<>();
                mergedProductIds.addAll(cxrSiteInventoryProductMap.keySet());
                mergedProductIds.addAll(siteStockProductMap.keySet());
                // 去重
                mergedProductIds = mergedProductIds.stream().distinct().collect(Collectors.toList());
                for (Long productId : mergedProductIds) {
                    CxrSiteInventoryGoodsInfo cxrSiteInventoryGoodsInfo = cxrSiteInventoryProductMap.get(productId);
                    GoodsInfo siteStockGoodsInfo = ObjectUtil.defaultIfNull(siteStockProductMap.get(productId), new GoodsInfo());
                    if (cxrSiteInventoryGoodsInfo == null) {
                        cxrSiteInventoryGoodsInfo = new CxrSiteInventoryGoodsInfo();
                        cxrSiteInventoryGoodsInfo.setProductId(siteStockGoodsInfo.getProductId());
                        cxrSiteInventoryGoodsInfo.setQuantity(0L);
                        cxrSiteInventoryGoodsInfo.setProductAlias(siteStockGoodsInfo.getProductAlias());
                        cxrSiteInventoryGoodsInfo.setProductName(siteStockGoodsInfo.getProductName());
                        recordGoodsInfoList.add(cxrSiteInventoryGoodsInfo);
                    }
                    cxrSiteInventoryGoodsInfo.setSystemQuantity(siteStockGoodsInfo.getQuantity());
                    cxrSiteInventoryGoodsInfo.setQuantityVariance(cxrSiteInventoryGoodsInfo.getQuantity() - siteStockGoodsInfo.getQuantity());
                }
                record.setSystemTotalSum(
                    milk.getMilkTotal());
                record.setQuantityVariance(record.getInventoryTotalSum() - milk.getMilkTotal());
                record.setGoodsInfo(JSONUtil.toJsonStr(recordGoodsInfoList));
            }

        }
        PageTableDataInfo pageTableDataInfo = new PageTableDataInfo();
        pageTableDataInfo.setRows(inventoryListVoIPage.getRecords());
        pageTableDataInfo.setCurr(pageQuery.getPageNum());
        pageTableDataInfo.setTotal(inventoryListVoIPage.getTotal());
        pageTableDataInfo.setSize(pageQuery.getPageSize());
        return pageTableDataInfo;
    }

    @Override
    public List<YangMilkTotal> getTotalStatistics(CxrSiteInventoryPageBo cxrSiteInventoryBo) {
        List<YangMilkTotal> yangMilkTotals = baseMapper.getTotalStatistics(cxrSiteInventoryBo);
        if (CollUtil.isNotEmpty(yangMilkTotals)) {
            for (YangMilkTotal yangvo : yangMilkTotals) {
                if (yangvo.getQuantity() == null) {
                    yangvo.setQuantity(0L);
                }
            }
        }
        return yangMilkTotals;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean distributionAdd(CxrShaftAuditBo bo) {

        LoginEmployee loginEmployee = StaffLoginHelper.getLoginUser();
        Long employeeUserId = loginEmployee.getUserId();

        Long cxrSiteId = bo.getCxrSiteId();
        if (ObjectUtil.isNull(cxrSiteId)) {
            throw new ServiceException("请选择站点");
        }
        List<GoodsInfo> goodsInfos = bo.getShaftMilkDistributionInfo();
        if (CollUtil.isEmpty(goodsInfos)) {
            throw new ServiceException("请选择鲜奶");
        }

        boolean quantityZero = false;
        for (GoodsInfo goodsInfo : goodsInfos) {
            if (goodsInfo.getQuantity() != 0) {
                quantityZero = true;
                break;
            }
        }
        if (!quantityZero) {
            throw new ServiceException("修改口味不能全为0");
        }

        Long goodsQuantity = goodsInfos.stream().map(GoodsInfo::getQuantity).reduce((a, b) -> a + b).get();
        if (goodsQuantity != 0l) {
            throw new ServiceException("口味调整数量合计非零，请修改口味及数量");
        }

        CxrSite cxrSite = cxrSiteMapper.selectById(cxrSiteId);
        //满足条件自动通过审核
        CxrShaftAudit cxrShaftAudit = BeanUtil.copyProperties(bo, CxrShaftAudit.class);
        cxrShaftAudit.setAuditStatus(ShaftAuditStatus.AUDITED.getValue());
        cxrShaftAudit.setAuditBy(loginEmployee.getUserId());
        cxrShaftAudit.setAuditByName(loginEmployee.getUserName());
        cxrShaftAudit.setAuditTime(new Date());
        cxrShaftAudit.setCxrEmployeeId(employeeUserId);
        cxrShaftAudit.setCxrEmployeeName(loginEmployee.getUserName());
        cxrShaftAudit.setCxrEmployeeJobNumber(loginEmployee.getJobNumber());
        cxrShaftAudit.setCxrSiteId(cxrSite.getId());
        cxrShaftAudit.setCxrSiteName(cxrSite.getName());
        cxrShaftAudit.setSourceId(bo.getSourceId());
        // 设置统计数据
        Long totalQuantity = goodsInfos.stream().collect(Collectors.summingLong(GoodsInfo::getQuantity));
        cxrShaftAudit.setTotalQuantity(totalQuantity);
        boolean flag = remoteCxrSaleProductService.distributionAdd(cxrShaftAudit);
        bo.setId(cxrShaftAudit.getId());

        if (flag) {
            //更新库存
            log.info(StrUtil.format("更新库存,:{},{},{},{}", cxrSiteId, JSONUtil.toJsonStr(goodsInfos), 0l, cxrShaftAudit.getId()));
            remoteSiteService.updateSiteStock(cxrShaftAudit.getCxrSiteId(), goodsInfos, 0l, cxrShaftAudit.getId()
                , SiteOperationType.FRESH_MILK_ADJUSTMENT);
        }
        return flag;
    }

    @Override
    public CxrShaftAudit distributionDetail(Long sourceId) {
        return remoteCxrSaleProductService.distributionDetail(sourceId);
    }

}
