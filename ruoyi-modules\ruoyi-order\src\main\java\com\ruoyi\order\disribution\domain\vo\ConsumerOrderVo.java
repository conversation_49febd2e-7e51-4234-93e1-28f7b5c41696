package com.ruoyi.order.disribution.domain.vo;

import com.ruoyi.business.base.api.domain.dto.DailyPerformanceDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class ConsumerOrderVo {

    @ApiModelProperty("列表id")
    private Long id;
    /**
     * 单据;订单单据号
     */
    @ApiModelProperty(name = "单据", notes = "订单单据号")
    private String orderNo;
    /**
     * 订单类型;1、新订单2、增订单3、续订单4、转单5、换单6、退订单7、赠送单
     */
    @ApiModelProperty(name = "订单类型", notes = "1、新订单2、增订单3、续订单4、转单5、换单6、退订单7、赠送单")
    private Short orderType;
    /**
     * 订单日期
     */
    @ApiModelProperty(name = "订单日期")
    private Date orderDate;


    /**
     * 支付状态
     */
    @ApiModelProperty(name = "支付状态")
    private Integer payStatus;
    /**
     * 客户名称
     */
    @ApiModelProperty(name = "客户名称")
    private String customerName;
    /**
     * 客户电话;只取第一个录入的数据
     */
    @ApiModelProperty(name = "客户电话", notes = "只取第一个录入的数据")
    private String customerPhone;
    /**
     * 客户地址; 只取第一个录入的数据
     */
    @ApiModelProperty(name = "客户地址", notes = " 只取第一个录入的数据")
    private String customerAdress;
    /**
     * 单价：鲜奶单价
     */
    @ApiModelProperty(name = "单价：鲜奶单价")
    private BigDecimal unitPrice;
    /**
     * 总数量;= 鲜奶订购数量 - 鲜奶已送数量
     */
    @ApiModelProperty(name = "总数量", notes = "= 鲜奶订购数量 - 鲜奶已送数量")
    private Integer orderQuantity;
    /**
     * 鲜奶赠送数量
     */
    @ApiModelProperty(name = "鲜奶赠送数量")
    private Integer freshMilkGiveQuantity;
    /**
     * 常温奶赠送数量
     */
    @ApiModelProperty(name = "常温奶赠送数量")
    private Integer longMilkGiveQuantity;

    @ApiModelProperty(name = " 鲜奶退订数量,<<退订总数量>>")
    private Integer freshMilkReturnQuantity;
    /**
     * 金额：该笔订单的总金额
     */
    @ApiModelProperty(name = "金额：该笔订单的总金额   ")
    private BigDecimal amount;

    @ApiModelProperty(name = "退订单总金额", notes = "退订单总金额")
    private BigDecimal returnAmount;

    // 完善订单入口
    @ApiModelProperty(name = "订单完善状态", notes = "订单完善状态")
    private Integer perfectStatus;

    @ApiModelProperty(name = "省", notes = "订单完善状态")
    private String province;

    @ApiModelProperty(name = "市", notes = "订单完善状态")
    private String city;

    @ApiModelProperty(name = "区", notes = "订单完善状态")
    private String area;

    @ApiModelProperty("源  1 后端 2 配送端")
    private Short terminalType;

    @ApiModelProperty("审核时间")
    private Date auditTime;

    @ApiModelProperty("支付时间")
    private Date payTime;

    //////////////////////////////////////////////////////////////////////

    //    /** 编号 */
    //    @TableId(type= IdType.ASSIGN_ID)
    //    @ApiModelProperty(name = "编号")
    //    private Long id ;
    //    /** 部门id(公司id) */
    //    @ApiModelProperty(name = "部门id(公司id)")
    //    private Integer sysDeptId ;
    //    /** 乐观锁 */
    //    @ApiModelProperty(name = "乐观锁")
    //    private Integer revision ;
    //    /** 创建人id */
    //    @ApiModelProperty(name = "创建人id")
    //    private Integer createBy ;
    //    /** 创建人名称 */
    //    @ApiModelProperty(name = "创建人名称")
    //    private String createByName ;
    //    /** 创建人类型(详情见字典) */
    //    @ApiModelProperty(name = "创建人类型(详情见字典)")
    //    private String createByType ;
    //    /** 创建时间 */
    //    @ApiModelProperty(name = "创建时间")
    //    private Date createTime ;
    //    /** 更新人id */
    //    @ApiModelProperty(name = "更新人id")
    //    private Integer updateBy ;
    //    /** 更新人名称 */
    //    @ApiModelProperty(name = "更新人名称")
    //    private String updateByName ;
    //    /** 更新人类型(详情见字典) */
    //    @ApiModelProperty(name = "更新人类型(详情见字典)")
    //    private String updateByType ;
    //    /** 更新时间 */
    //    @ApiModelProperty(name = "更新时间")
    //    private Date updateTime ;
    //    /** 删除人id */
    //    @ApiModelProperty(name = "删除人id")
    //    private Integer deleteBy ;
    //    /** 删除人名称 */
    //    @ApiModelProperty(name = "删除人名称")
    //    private String deleteByName ;
    //    /** 删除人类型(详情见字典) */
    //    @ApiModelProperty(name = "删除人类型(详情见字典)")
    //    private String deleteByType ;
    //    /** 删除时间 */
    //    @ApiModelProperty(name = "删除时间")
    //    private Date deleteTime ;
    //    /** 删除状态(详情见字典) */
    //    @ApiModelProperty(name = "删除状态(详情见字典)")
    //    private String deleteStatus ;
    //    /** 排序 */
    //    @ApiModelProperty(name = "排序")
    //    private Integer sortNum ;
    //    /** 大区名称 */
    //    @ApiModelProperty(name = "大区名称")
    //    private String bigAreaName ;

    //    @ApiModelProperty(name = "小区")
    //    private String quarters ;
    //    /** 站点名称 */
    //    @ApiModelProperty(name = "站点名称")
    //    private String siteName ;
    //    /** 站点id */
    //    @ApiModelProperty(name = "站点id")
    //    private Integer siteId ;
    //    /** 业务代理字符串; 以逗号分割 */
    //    @ApiModelProperty(name = "业务代理字符串",notes = " 以逗号分割")
    //    private String  businessAgent;
    //    /** 业务代理字等级; 以逗号分割 */
    //    @ApiModelProperty(name = "业务代理字等级",notes = " 以逗号分割")
    //    private String businessAgentLevel ;
    //
    //
    //
    //    /** 1微信;2 支付宝 */
    //    @ApiModelProperty(name = "1微信",notes = "2 支付宝")
    //    private Integer paymentSouce ;
    //
    //    /** 0数量续单：用于客户账户没有鲜奶了;并且停奶超过了15天。15天后续单的，显示“超过15天”。 */
    //    @ApiModelProperty(name = "0数量续单：用于客户账户没有鲜奶了",notes = "并且停奶超过了15天。15天后续单的，显示“超过15天”。")
    //    private Integer zeroQuantityRenewal ;
    //    /** 用于订单类型为“换单”的订单，显示客户兑换的商品的类目;，剔除重复的  多个以逗号分割 */
    //    @ApiModelProperty(name = "用于订单类型为“换单”的订单，显示客户兑换的商品的类目",notes = "，剔除重复的  多个以逗号分割")
    //    private String conversionType ;
    //
    //    /** 单据;订单单据号 */
    //    @ApiModelProperty(name = "单据",notes = "上传单据，图片的保存地址用 , 分隔")
    //    private String orderImages ;
    //    /** 商户单号 */
    //    @ApiModelProperty(name = "商户单号")
    //    private String merchantOrderNo ;
    //
    //    /** 订购数量：鲜奶订购数量 */
    //    @ApiModelProperty(name = "订购数量：鲜奶订购数量")
    //    private Integer orderQuantity ;
    //
    //    /** 超送数量：根据订单套餐超送规则计算出来的超送数量;祥看原型 */
    //    @ApiModelProperty(name = "超送数量：根据订单套餐超送规则计算出来的超送数量",notes = "祥看原型")
    //    private Integer excessQuantity ;
    //    /** 鲜奶已送数量 */
    //    @ApiModelProperty(name = "鲜奶已送数量")
    //    private Integer freshMilkSentQuantity ;
    //    /** 常温奶已送数量 */
    //    @ApiModelProperty(name = "常温奶已送数量")
    //    private Integer longMilkSentQuantity ;
    //    /** 订单类型为“转单”的时候才会有值;即转换的鲜奶数量 */
    //    @ApiModelProperty(name = "订单类型为“转单”的时候才会有值",notes = "即转换的鲜奶数量")
    //    private Integer conversionQuantity ;
    //
    //
    //    /** 刷卡金额 */
    //    @ApiModelProperty(name = "刷卡金额")
    //    private Double creditCardAmount ;
    //    /** 备注 */
    //    @ApiModelProperty(name = "备注")
    //    private String remark ;
    /**
     * 1;待审核 、 2 已审核 、 3.已拒绝
     */
    @ApiModelProperty(name = "1", notes = "待审核 、 2 已审核 、 3.已拒绝")
    private Integer auditStatus;
    //    /** 是否促销单;true 是 false 否 */
    @ApiModelProperty(name = "是否促销单", notes = "true 是 false 否")
    private Integer promotionalOrderFlag;
    //    /** 是否师徒单;true是 false 否 */
    @ApiModelProperty(name = "是否师徒单", notes = "true是 false 否")
    private Integer apprenticeOrderFlag;
    /**
     * 是否师徒单;true是 false 否
     */
    //    @ApiModelProperty(name = "来源",notes = "1后端 ， 2配送端")
    //    private Integer sourceType;

    @ApiModelProperty(name = "1", notes = "续单是否可以编辑")
    private boolean continueEditFlag = Boolean.FALSE;

    @ApiModelProperty("是否新客户")
    private String newCustomerFlag;

    @ApiModelProperty("销售代理")
    private String proxyName;

    @ApiModelProperty("业绩")
    private String performanceMoney;

    @ApiModelProperty(notes = "合订单促销单是否可以编辑")
    private boolean promotionalEditFlag = Boolean.FALSE;

    @ApiModelProperty("订单业绩")
    private List<DailyPerformanceDto> dailyPerformanceDto;

    @ApiModelProperty("订单编辑标识：0 不可以编辑，1 可以编辑")
    private Boolean editFlag = Boolean.FALSE;

    @ApiModelProperty("0数量续单")
    private Boolean zeroQuantityRenewal;

    /**
     * 库存为0时间大于15天
     */
    private Boolean zero15DayAfter;

    /**
     * 之前已经创建了地址的客户
     */
    private Boolean oldCustomer;

    @ApiModelProperty("配送站点")
    private String deliverySites;

    @ApiModelProperty("退款审核不通过原因")
    private String refundNoAuditReasons;

}
