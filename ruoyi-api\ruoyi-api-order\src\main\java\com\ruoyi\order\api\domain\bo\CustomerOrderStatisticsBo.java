package com.ruoyi.order.api.domain.bo;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.utils.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.*;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("客户订单统计Bo")
public class CustomerOrderStatisticsBo extends PageQuery implements Serializable {

    @ApiModelProperty("开始订单日期  带上时分秒")
    private LocalDate startOrderDate;

    @ApiModelProperty("结束订单日期  带上时分秒")
    @DateTimeFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date endOrderDate;

    @ApiModelProperty(value = "销售代理id集合")
    private List<Long> proxyIds;
    // 大区

    @ApiModelProperty("大区")
    private String bigAreaName;

    @ApiModelProperty("销售代理")
    private String employeeName;

    @ApiModelProperty("销售代理号")
    private String employeeJobNumber;

    @ApiModelProperty("活动Id")
    private Long activityId;

    @ApiModelProperty(value = "订单类型")
    private Integer orderType = null;

    /**
     * 审核状态
     */
    @ApiModelProperty("审核状态")
    private String auditStatus;

    /**
     * 客户电话
     */
    @ApiModelProperty(name = "客户电话", required = true)
    private String customerPhone;

    @ApiModelProperty("站点名称")
    private String siteName;

    @ApiModelProperty(name = "客户地址", notes = " 只取第一个录入的数据")
    private String customerAddress;

    private String proxyIdsJson;

    private Long deptId;

    private Long companyId;

    private boolean employeeFlag;

    private String ifImport;
    private List<Long> siteIds;

    private List<Long> regionIds;

    private String regionIdsJson;

    private String htmlName;

    @ApiModelProperty(value = " 终端类型")
    private Integer terminalType;
    @ApiModelProperty(value = "商品名称")
    private String productName;
    @ApiModelProperty(value = "商品分类名称")
    private String productTypeName;
    @ApiModelProperty(value = "核算类型")
    private Integer accountingType;
    @ApiModelProperty(value = "渠道推广提成：1.有，0.无")
    private Integer promotionCommission;
    @ApiModelProperty(value = "镜像时间")
    private LocalDateTime mirrorImageTime;
    @ApiModelProperty(value = "永久保存：1.有，0.无")
    private Integer retainStatus;

    public String getProxyIdsJson() {
        String s = JSONUtil.toJsonStr(proxyIds);
        return s;
    }

    public Date getEndDateTime() {
        if (ObjectUtil.isNotNull(this.endOrderDate) && ObjectUtil.isNotNull(this.endOrderDate)) {
            LocalDateTime localDateTime =
                LocalDateTime.ofInstant(
                    Instant.ofEpochMilli(this.endOrderDate.getTime()), ZoneId.systemDefault());
            ;
            LocalDateTime endOfDay = localDateTime.with(LocalTime.MAX);
            Date date = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
            return date;
        }
        Date tomorrowDate = DateUtils.getTomorrowDate();
        return tomorrowDate;
    }
}
