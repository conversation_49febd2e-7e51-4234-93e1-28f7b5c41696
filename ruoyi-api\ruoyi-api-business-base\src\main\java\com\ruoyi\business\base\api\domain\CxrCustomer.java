package com.ruoyi.business.base.api.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cxrry.logger.api.annotation.LogModel;
import com.cxrry.logger.api.annotation.LogTag;
import com.ruoyi.business.base.api.domain.vo.CxrLabeleDTO;
import com.ruoyi.business.base.api.typeHandler.TypeHandlerConstant;
import com.ruoyi.common.core.web.domain.CxrBaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * 客户对象 cxr_customer
 *
 * <AUTHOR>
 * @date 2022-07-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@LogModel(value = "客户资料", tableName = "cxr_customer")
@TableName(value = "cxr_customer", autoResultMap = true)
public class CxrCustomer extends CxrBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 客户姓名
     */
    @LogTag(alias = "客户姓名")
    private String name;

    /**
     * 客户性别(详情见字典)
     */
    @LogTag(alias = "客户性别")
    private String genderType;

    /**
     * 客户手机号码
     */
    @LogTag(alias = "客户手机号码")
    private String phone;

    /**
     * 客户生日
     */
    @LogTag(alias = "客户生日")
    private String birthday;

    /**
     * 客户审核状态(详情见字典)
     */
    @LogTag(alias = "客户审核状态")
    private String auditStatus;

    /**
     * 客户来源(详情见字典)
     */
    @LogTag(alias = "客户来源")
    private String sourceType;

    /**
     * 微信openID
     */
    @LogTag(alias = "微信openID")
    private String wxOpenId;

    /**
     * 微信unionID
     */
    private String wxUnionId;

    /**
     * 微信头像
     */
    private String wxHeadPortrait;

    /**
     * 微信昵称
     */
    @LogTag(alias = "微信昵称")
    private String wxNickname;

    /**
     * 省
     */
    @LogTag(alias = "省")
    private String provice;

    /**
     * 市
     */
    @LogTag(alias = "市")
    private String city;

    /**
     * 区
     */
    @LogTag(alias = "区")
    private String area;


    /**
     * 用户库存
     */
    @LogTag(alias = "客户库存")
    private Integer customerStock;

    /**
     * 客户库存奶数量为零的时间
     */
    @LogTag(alias = "客户库存奶数量为零的时间")
    private Date customerStockZeroTime;

    /**
     * 鲜奶订购总数
     */
    @LogTag(alias = "鲜奶订购总数")
    @ApiModelProperty(name = "鲜奶订购总数", notes = "")
    private Integer freshMilkOrderTotal;
    /**
     * 鲜奶赠送总数
     */
    @LogTag(alias = "鲜奶赠送总数")
    @ApiModelProperty(name = "鲜奶赠送总数", notes = "")
    private Integer freshMilkGiveTotal;
    /**
     * 鲜奶已送总数
     */
    @LogTag(alias = "鲜奶已送总数")
    @ApiModelProperty(name = "鲜奶已送总数", notes = "")
    private Integer freshMilkSentTotal;


    /**
     * 鲜奶路条已送总数量
     */
    @LogTag(alias = "鲜奶路条已送总数量")
    @ApiModelProperty(name = "鲜奶路条已送总数量")
    private Integer freshMilkRoadWaySentTotal;

    /**
     * 客户兑换商品所使用的鲜奶总数
     */
    @LogTag(alias = "客户兑换商品所使用的鲜奶总数")
    @ApiModelProperty(name = "客户兑换商品所使用的鲜奶总数", notes = "")
    private Integer exchangeUseFreshMilkTotal;
    /**
     * 配送鲜奶总数
     */
    @LogTag(alias = "配送鲜奶总数")
    @ApiModelProperty(name = "配送鲜奶总数", notes = "")
    private Integer distributionFreshMilkTotal;
    /**
     * 临时加奶总数
     */
    @LogTag(alias = "临时加奶总数")
    @ApiModelProperty(name = "临时加奶总数", notes = "")
    private Integer temporaryAddMilkTotal;
    /**
     * 常温奶赠送总数
     */
    @LogTag(alias = "常温奶赠送总数")
    @ApiModelProperty(name = "常温奶赠送总数", notes = "")
    private Integer longMilkGiveTotal;
    /**
     * 常温奶已送总数
     */
    @LogTag(alias = "常温奶已送总数")
    @ApiModelProperty(name = "常温奶已送总数", notes = "")
    private Integer longMilkSentTotal;


    /**
     * 详细配送地址信息
     */
    @TableField(exist = false)
    private String detailDistributionAddress;

    /**
     * 小区id
     */
    @TableField(exist = false)
    private Long cxrResidentialQuartersId;

    /**
     * 地区id(省市区地区表id)
     */
    @TableField(exist = false)
    private Long sysAreaId;

    /**
     * 站点id(配送站点的id)
     */
    @TableField(exist = false)
    private Long cxrSiteId;

    /**
     * 原因
     */
    @TableField(exist = false)
    private String season;

    @TableField(exist = false)
    @ApiModelProperty("站点名称")
    private String siteName;

    @TableField(exist = false)
    private String CxrResidentialQuartersName;


    @LogTag(alias = "客户库存到期时间")
    @ApiModelProperty("客户库存到期时间")
    private Date expirationTime;

    @LogTag(alias = "应送")
    @ApiModelProperty("应送")
    private Integer shouldSend;

    @ApiModelProperty("标签")
    @TableField(typeHandler = TypeHandlerConstant.labeledJson.class)
    private List<CxrLabeleDTO> labeled;

    /**
     * 客户初始库存
     */
    @LogTag(alias = "客户初始库存")
    @ApiModelProperty("客户初始库存")
    private Long originStock;

    @ApiModelProperty("是否导入客户")
    private Boolean isImport;

    /**
     * 用户锁定库存
     */
    @LogTag(alias = "用户锁定库存")
    @ApiModelProperty("用户锁定库存")
    private Long lockStock;

    private String qwRemark;
    @TableField(exist = false)
    private CxrCustomerAddress cxrCustomerAddress;
    @TableField(exist = false)
    private List<DeliverySiteDTO> deliverySiteDTOS;

}
