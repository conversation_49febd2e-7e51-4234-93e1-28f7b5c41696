package com.ruoyi.calculate.customerOrderStatistics.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.calculate.customerOrderStatistics.service.CustomerOrderStatisticsService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.order.api.domain.bo.CustomerOrderStatisticsBo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.List;

@Api(value = "客户订单统计", tags = {"客户订单统计"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/CustomerOrderStatistics")
public class CustomerOrderStatisticsController {

    private final CustomerOrderStatisticsService customerOrderStatisticsService;


    @ApiOperation("客户订单统计")
    @SaCheckPermission("calculate:CustomerOrderStatistics:page")
    @PostMapping("/page")
    public R<Object> orderStatistics(@Validated @RequestBody CustomerOrderStatisticsBo bo) {
        PageQuery pageQuery = BeanUtil.copyProperties(bo, PageQuery.class);
        return R.ok(customerOrderStatisticsService.orderStatistics(bo, pageQuery));
    }

    @Log(title = "客户订单统计导出", businessType = BusinessType.EXPORT)
    @PostMapping("/orderStatisticsExport")
    public void export(@Validated @RequestBody CustomerOrderStatisticsBo bo,
                       HttpServletResponse response) {
        customerOrderStatisticsService.orderStatisticsExport(bo, response);
    }


    @ApiOperation("客户订单统计 固定数据")
    @SaCheckPermission("calculate:CustomerOrderStatistics:orderFixationStatistics")
    @PostMapping("/orderFixationStatistics")
    public R<Object> orderFixationStatistics(@Validated @RequestBody CustomerOrderStatisticsBo bo) {
        PageQuery pageQuery = BeanUtil.copyProperties(bo, PageQuery.class);
        return R.ok(customerOrderStatisticsService.orderFixationStatistics(bo, pageQuery));
    }

    @ApiOperation("客户订单统计导出 固定数据")
    @Log(title = "客户订单统计导出 固定数据", businessType = BusinessType.EXPORT)
    @PostMapping("/orderFixationStatisticsExport")
    public void orderFixationStatisticsExport(@Validated @RequestBody CustomerOrderStatisticsBo bo,
                                              HttpServletResponse response) {
        customerOrderStatisticsService.orderFixationStatisticsExport(bo, response);
    }

    @ApiOperation("获取镜像时间")
    @GetMapping("/getMirrorImageTime")
    public R<List<LocalDateTime>> getMirrorImageTime() {
        return R.ok(customerOrderStatisticsService.getMirrorImageTime());
    }


}
