package com.ruoyi.business.base.dubbo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.business.base.api.domain.CxrEmployee;
import com.ruoyi.business.base.api.domain.CxrEmployeePost;
import com.ruoyi.business.base.api.domain.CxrRoadStatistis;
import com.ruoyi.business.base.api.domain.CxrSite;
import com.ruoyi.business.base.api.domain.bo.CxrRoadStatistisBo;
import com.ruoyi.business.base.api.domain.dto.SiteStatisticsDto;
import com.ruoyi.business.base.api.domain.json.GoodsInfo;
import com.ruoyi.business.base.api.domain.vo.CxrRoadStatistisVO;
import com.ruoyi.business.base.api.dubbo.RemoteCxrRoadStatistisService;
import com.ruoyi.business.base.cxrRoadStatistis.mapper.CxrRoadStatistisMapper;
import com.ruoyi.business.base.cxrRoadStatistis.service.ICxrRoadStatistisService;
import com.ruoyi.business.base.employee.service.ICxrEmployeeService;
import com.ruoyi.business.base.employeePost.service.ICxrEmployeePostService;
import com.ruoyi.business.base.site.mapper.CxrSiteMapper;
import com.ruoyi.business.base.site.service.ICxrSiteService;
import com.ruoyi.common.core.enums.DeleteStatus;
import com.ruoyi.common.core.enums.SiteOperationType;
import com.ruoyi.common.core.enums.SysYesNo;
import com.ruoyi.common.core.enums.cxrRoadStatistisStatus;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.rocketmq.utils.MqUtil;
import com.ruoyi.system.api.RemoteDeptService;
import io.seata.core.context.RootContext;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@Slf4j
@DubboService
public class RemoteCxrRoadStatistisServiceImpl implements RemoteCxrRoadStatistisService {

    private final CxrSiteMapper cxrSiteMapper;
    private final ICxrRoadStatistisService iCxrRoadStatistisService;
    private final CxrRoadStatistisMapper cxrRoadStatistisMapper;
    private final ICxrSiteService iCxrSiteService;
    private final ICxrEmployeePostService iCxrEmployeePostService;
    private final ICxrEmployeeService iCxrEmployeeService;

    @DubboReference
    private RemoteDeptService remoteDeptService;


    @Autowired
    private MqUtil mqUtil;


    @Override
    public CxrRoadStatistisVO listRoadWay(CxrRoadStatistisBo cxrRoadStatistisBo) {
        return iCxrRoadStatistisService.listRoadWay(cxrRoadStatistisBo);
    }


    @Override
    public List<SiteStatisticsDto> statisticsDistributionStatusTotalMilk(LocalDate now) {
        return cxrRoadStatistisMapper.selectByDistributionStatusAndDistributionDate(SysYesNo.YES.getValue(), now);
    }

    @Override
    public List<SiteStatisticsDto> statisticsDistributionStatusTotalMilk(String month) {
        return cxrRoadStatistisMapper.selectByDistributionStatusAndDistributionMonth(SysYesNo.YES.getValue(), month);
    }

    @Override
    public SiteStatisticsDto statisticsDistributionStatusTotalMilk(Long siteId, LocalDate start, LocalDate end) {
        return cxrRoadStatistisMapper.selectByDistributionStatusAndDistribution(SysYesNo.YES.getValue(), siteId, start,
            end);
    }

    @Override
    public void showFinanceEnd(LocalDate localDate) {
        log.info("财务查询接管结束 执行时间 {} , 执行事务xid {}", localDate, RootContext.getXID());
        List<CxrSite> getSiteId = cxrSiteMapper.selectList(new LambdaQueryWrapper<CxrSite>()
            .eq(CxrSite::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));
        List<Long> SiteId = getSiteId.stream().map(CxrSite::getId).collect(Collectors.toList());
        // 职位的接管结束时间为 23:59:59
        LocalDate now = localDate.plusDays(-1);
        //查询财务的接管结束时间为昨天的
        List<CxrEmployeePost> getEndTime = iCxrEmployeePostService.getIsNotNull(SiteId, now);
        if (CollUtil.isNotEmpty(getEndTime)) {
            for (CxrEmployeePost post : getEndTime) {
                int count = cxrRoadStatistisMapper.update(null, new LambdaUpdateWrapper<CxrRoadStatistis>()
                    .eq(CxrRoadStatistis::getSiteId, post.getCxrSiteId())
                    .le(CxrRoadStatistis::getDistributionDate,
                        DateUtils.getLocalDateFromDate(post.getInChargeEndTime()))
                    .eq(CxrRoadStatistis::getFinanceId, post.getCxrEmployeeId())
                    .ge(CxrRoadStatistis::getFinanceStart, post.getInChargeStartTime())
                    .isNull(CxrRoadStatistis::getFinanceEnd)
                    .set(CxrRoadStatistis::getFinanceEnd, post.getInChargeEndTime())
                );
                if (count < 0) {
                    throw new ServiceException("该站点下的财务信息更新失败");
                }
            }
        }
    }


    @Override
    public List<SiteStatisticsDto> statisticsDistributionStatusTotalMilkBySiteIds(LocalDate now, List<Long> siteIds) {
        return cxrRoadStatistisMapper.selectByDistributionStatusAndDistributionDateBySiteIds(SysYesNo.YES.getValue(),
            now, siteIds);
    }

    @Override
    public void roadStatistis(LocalDate localDate) {
        log.info("自动生成财务发奶 执行时间 {} , 执行事务xid {}", localDate, RootContext.getXID());
        List<CxrSite> getSiteId = cxrSiteMapper.selectList(new LambdaQueryWrapper<CxrSite>()
            .eq(CxrSite::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));

        if (CollUtil.isEmpty(getSiteId)) {
            log.info("自动生成财务发奶");
            return;
        }
        //财务发奶自动确认时间改为当天晚上的11:30
        //先依据N减库存,在去修改状态
        List<CxrRoadStatistis> getRoadStatistis = cxrRoadStatistisMapper.selectList(
            new LambdaQueryWrapper<CxrRoadStatistis>()
                .eq(CxrRoadStatistis::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .eq(CxrRoadStatistis::getDistributionDate, localDate)
                .eq(CxrRoadStatistis::getStatus, cxrRoadStatistisStatus.N.getValue())
        );
        //扣减库存
        for (CxrRoadStatistis road : getRoadStatistis) {
            List<GoodsInfo> goodsInfoList = road.getDistributionInfo().stream().map(x -> {
                GoodsInfo goodsInfo = new GoodsInfo();
                goodsInfo.setProductId(x.getProductId());
                goodsInfo.setProductName(x.getProductName());
                goodsInfo.setProductAlias(x.getProductAlias());
                goodsInfo.setQuantity(-x.getQuantity());

                return goodsInfo;
            }).collect(Collectors.toList());

            for (int i = 0; i < 5; i++) {
                try {
                    List<GoodsInfo> infos = JSONArray.parseArray(JSONObject.toJSONString(goodsInfoList), GoodsInfo.class);
                    iCxrSiteService.updateSiteStock(road.getSiteId(), infos, 0L, road.getId(), SiteOperationType.FINANCIAL_MILK);
                    break;
                }catch (Exception e){
                    e.printStackTrace();
                    log.error("扣减库存失败,正在重试 {}",i);
                }
            }
        }
        List<Long> SiteId = getSiteId.stream().map(CxrSite::getId).collect(Collectors.toList());
        List<CxrEmployeePost> getEndIsNull = iCxrEmployeePostService.getEndIsNull(SiteId, localDate);
        if (ObjectUtil.isNotEmpty(getEndIsNull)) {
            List<Long> employeeId = getEndIsNull.stream().map(CxrEmployeePost::getCxrEmployeeId)
                .collect(Collectors.toList());
            List<CxrEmployee> getEmployeeId = iCxrEmployeeService.getEmployeeIds(employeeId);
            Map<Long, CxrEmployeePost> map = getEndIsNull.stream()
                .collect(Collectors.toMap(CxrEmployeePost::getCxrEmployeeId, Function.identity(), (v1, v2) -> v2));
            if (ObjectUtil.isNotEmpty(getEmployeeId)) {
                getEmployeeId.forEach(employee -> {
                    CxrEmployeePost cxrEmployeePost = map.get(employee.getId());
                    if (ObjectUtil.isNotEmpty(cxrEmployeePost)) {
                        int count = cxrRoadStatistisMapper.update(null, new LambdaUpdateWrapper<CxrRoadStatistis>()
                            .eq(CxrRoadStatistis::getSiteId, cxrEmployeePost.getCxrSiteId())
                            .eq(CxrRoadStatistis::getStatus, cxrRoadStatistisStatus.N.getValue())
                            .eq(CxrRoadStatistis::getDistributionDate, localDate)
                            .isNull(CxrRoadStatistis::getFinanceId)
                            .set(CxrRoadStatistis::getFinanceNum, employee.getJobNumber())
                            .set(CxrRoadStatistis::getFinanceId, employee.getId())
                            .set(CxrRoadStatistis::getFinanceName, employee.getName())
                            .set(CxrRoadStatistis::getDeptId, employee.getSysDeptId())
                            .set(CxrRoadStatistis::getFinanceStart, cxrEmployeePost.getInChargeStartTime())
                        );
                        if (count < 0) {
                            throw new ServiceException("该站点下的财务信息更新失败");
                        }
                    }
                });
            }
            if (CollUtil.isNotEmpty(getSiteId)) {
                getSiteId.forEach(site -> {
                    cxrRoadStatistisMapper.update(null, new LambdaUpdateWrapper<CxrRoadStatistis>()
                        .eq(CxrRoadStatistis::getSiteId, site.getId())
                        .eq(CxrRoadStatistis::getStatus, cxrRoadStatistisStatus.N.getValue())
                        .eq(CxrRoadStatistis::getDistributionDate, localDate)
                        .set(CxrRoadStatistis::getCxrRegionId, site.getCxrRegionId())
                        .set(CxrRoadStatistis::getCxrRootRegionName, site.getCxrRootRegionName())
                    );
                });
            }
        }

        //依据站点id去确定
        int num = cxrRoadStatistisMapper.update(null, new LambdaUpdateWrapper<CxrRoadStatistis>()
            .in(CxrRoadStatistis::getSiteId, SiteId)
            .eq(CxrRoadStatistis::getStatus, cxrRoadStatistisStatus.N.getValue())
            .eq(CxrRoadStatistis::getDistributionDate, localDate)
            .set(CxrRoadStatistis::getConfirmTime, localDate)
            .set(CxrRoadStatistis::getStatus, cxrRoadStatistisStatus.Y.getValue())
        );
        if (num < 0) {
            throw new ServiceException("状态更新失败!");
        }
    }
}
