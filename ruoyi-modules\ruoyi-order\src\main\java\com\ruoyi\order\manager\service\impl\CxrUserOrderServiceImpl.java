package com.ruoyi.order.manager.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.business.api.dubbo.RemoteTransferPostApplyRemoteService;
import com.ruoyi.business.base.api.config.AchievementDetailDate;
import com.ruoyi.business.base.api.domain.*;
import com.ruoyi.business.base.api.domain.dto.*;
import com.ruoyi.business.base.api.domain.json.BusinessAgent;
import com.ruoyi.business.base.api.domain.json.CustomerInfo;
import com.ruoyi.business.base.api.domain.vo.CustomerSentNumberVo;
import com.ruoyi.business.base.api.dubbo.*;
import com.ruoyi.business.base.api.enums.OrderTypeEnums;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.domain.ExcelReportDTO;
import com.ruoyi.common.core.domain.LoginInfo;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.service.DictService;
import com.ruoyi.common.core.utils.SQLException;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.enums.AuditStatusEnums;
import com.ruoyi.common.enums.SourceEnums;
import com.ruoyi.common.enums.TerminalTypeEnums;
import com.ruoyi.common.excel.utils.ExcelExportObj;
import com.ruoyi.common.excel.utils.ExcelMqUtil;
import com.ruoyi.common.excel.utils.ExcelUtil;
import com.ruoyi.common.order.wap.entity.PayNotifyUrlResponse;
import com.ruoyi.common.redis.utils.RedisLockUtils;
import com.ruoyi.common.rocketmq.calculate.CalculateConstant;
import com.ruoyi.common.rocketmq.calculate.CustomerAddressConstant;
import com.ruoyi.common.rocketmq.constant.biz.MqBizConst;
import com.ruoyi.common.rocketmq.constant.customer.CxrCustomerConstant;
import com.ruoyi.common.rocketmq.constant.employee.MqDayPerformanceConst;
import com.ruoyi.common.rocketmq.constant.huibo.HuiboConstant;
import com.ruoyi.common.rocketmq.constant.message.SendMessageReturnOrderConstant;
import com.ruoyi.common.rocketmq.constant.order.OrderConstant;
import com.ruoyi.common.rocketmq.utils.MqUtil;
import com.ruoyi.common.satoken.utils.LoginUtil;
import com.ruoyi.common.satoken.utils.helper.LoginHelper;
import com.ruoyi.core.base.domain.CxrOrderAfterSale;
import com.ruoyi.core.base.domain.CxrPayConfig;
import com.ruoyi.core.base.mapper.CxrOrderAfterSaleMapper;
import com.ruoyi.message.api.domain.dto.PaySucessMessageDTO;
import com.ruoyi.order.api.RemoteOrderService;
import com.ruoyi.order.api.domain.vo.CustomerOrderTotal;
import com.ruoyi.order.api.domain.vo.UserOrderListVo;
import com.ruoyi.order.api.enums.OrderAuditStatusEnums;
import com.ruoyi.order.api.enums.PayStatusEnums;
import com.ruoyi.order.api.enums.PerfectStatusEnums;
import com.ruoyi.order.common.domain.CxrEmployeePostTransferApply;
import com.ruoyi.order.common.domain.bo.CxrUserOrderQueryBo;
import com.ruoyi.order.common.domain.dto.CompleteInfoDTO;
import com.ruoyi.order.common.domain.param.FuyouPayParam;
import com.ruoyi.order.common.domain.response.FuyouResponse;
import com.ruoyi.order.common.domain.vo.CxrUserOrderListVo;
import com.ruoyi.order.common.domain.vo.CxrUserReturnOrderVO;
import com.ruoyi.order.common.domain.vo.LastUserOrderDayInfo;
import com.ruoyi.order.common.domain.vo.LastUserOrderVo;
import com.ruoyi.order.common.entity.CxrUserOrder;
import com.ruoyi.order.common.entity.CxrUserReturnOrder;
import com.ruoyi.order.common.event.ContractOrderSplitEvent;
import com.ruoyi.order.common.event.CustomerInfoWriteback;
import com.ruoyi.order.common.event.OrderCustomerInfoHanderEvent;
import com.ruoyi.order.common.mapper.*;
import com.ruoyi.order.cxrTransferMilk.mapper.CxrServiceWorkOrderMapper;
import com.ruoyi.order.disribution.common.FuyouPayUtils;
import com.ruoyi.order.disribution.domain.CxrPaymentDetails;
import com.ruoyi.order.disribution.enums.*;
import com.ruoyi.order.disribution.mapper.CxrPaymentDetailsMapper;
import com.ruoyi.order.disribution.service.ActivityOrderService;
import com.ruoyi.order.disribution.service.PayService;
import com.ruoyi.order.disribution.service.ReturnOrderService;
import com.ruoyi.order.employee.domain.CxrEmployeeOrder;
import com.ruoyi.order.employee.mapper.CxrEmployeeOrderMapper;
import com.ruoyi.order.extend.mapper.CxrEmployeeMapper;
import com.ruoyi.order.extend.mapper.CxrSiteMapper;
import com.ruoyi.order.manager.service.CxrPayConfigService;
import com.ruoyi.order.manager.service.CxrUserOrderService;
import com.ruoyi.order.manager.strategy.behavior.AbstractCxrUserOrderBehavior;
import com.ruoyi.system.api.model.LoginUser;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.client.producer.TransactionSendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.apache.rocketmq.spring.support.RocketMQHeaders;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.*;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【cxr_user_order(用户订单)】的数据库操作Service实现
 * @createDate 2022-08-04 19:53:00
 */
@Slf4j
@RefreshScope
@RequiredArgsConstructor
@Service
@GlobalTransactional(rollbackFor = Exception.class)
public class CxrUserOrderServiceImpl extends ServiceImpl<CxrUserOrderMapper, CxrUserOrder>
    implements CxrUserOrderService {


    @Autowired
    private AchievementDetailDate achievementDetailDate;


    private final AbstractCxrUserOrderBehavior abstractCxrUserOrderBehavior;
    @DubboReference
    private final RemoteCustomerStockDetailService remoteCustomerStockDetailService;

    @DubboReference
    private RemoteCustomerAddressService remoteCustomerAddressService;

    private final ApplicationEventPublisher applicationEventPublisher;

    private final ReturnOrderService returnOrderService;
    @DubboReference
    private final RemoteEmployeeService remoteEmployeeService;
    private final MqUtil mqUtil;
    @DubboReference
    private RemoteCustomerService remoteCustomerService;

    @DubboReference
    private RemoteTikTokService remoteTikTokService;

    @DubboReference
    private RemoteSiteService remoteSiteService;

    @DubboReference
    private RemoteEmployeeAchievementDetailService remoteEmployeeAchievementDetailService;

    @DubboReference
    private RemoteLongMilkStockService remoteLongMilkStockService;

    @DubboReference
    private RemoteCxrSaleProductService remoteCxrSaleProductService;

    @DubboReference
    private RemoteEmployeeChangeRecordService remoteEmployeeChangeRecordService;

    private final CxrEmployeeChangeRecordMapper cxrEmployeeChangeRecordMapper;
    private final CxrEmployeeCooperateRecordMapper cxrEmployeeCooperateRecordMapper;
    private final CxrEmployeePostTransferApplyMapper cxrEmployeePostTransferApplyMapper;
    private final CxrEmployeeOrderMapper cxrEmployeeOrderMapper;

    private final CxrOrderDetermineTemplateMapper determineTemplateMapper;

    private final CxrSiteMapper cxrSiteMapper;
    private final CxrEmployeeMapper employeeMapper;

    @Autowired
    private PayService payService;
    @DubboReference
    private RemoteTransferPostApplyRemoteService remoteTransferPostApplyRemoteService;

    private final RemoteOrderService remoteOrderService;
    @Autowired
    @Lazy
    private CxrUserOrderService cxrUserOrderService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    private final FuyouPayUtils fyPayUtils;

    private final CxrPaymentDetailsMapper paymentDetailsMapper;

    private final DictService dictService;

    private final CxrPayConfigService cxrPayConfigService;

    private final CxrOrderAfterSaleMapper cxrOrderAfterSaleMapper;

    @Autowired
    private CxrServiceWorkOrderMapper cxrServiceWorkOrderMapper;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Lazy
    @Autowired
    private ActivityOrderService activityOrderService;

    @Autowired
    @Qualifier("futureThreadPool")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Value("${reComputeAchievementOrderFmQty.isContinue: true}")
    private Boolean isContinue;

    /**
     * 分页查询《用户订单》
     *
     * @param cxrUserOrderBo 实例化Bo对象封装的查询参数 +分页参数
     * @return
     */
    @Override
    public Object pageQuery(CxrUserOrderQueryBo cxrUserOrderBo) {

        log.info(DateUtils.format(cxrUserOrderBo.getEndDateTime(), DateUtils.DATE_FORMAT_19));

        LoginInfo loginUser = LoginUtil.getLoginUser();
        Long deptId = loginUser.getDeptId();
        if (deptId == null) {
            deptId = -1L;
        }

        // 有传公司查询条件，判断是否有权限查看
        Long companyId = cxrUserOrderBo.getDeptId();
        if (companyId != null && !"100".equals(deptId.toString()) && !deptId.equals(companyId)) {
            throw new ServiceException("您没有查看该公司数据的权限!");
        }

        // 总部可以查询所有部门的数据，其他部门只能看自己部门的数据
        cxrUserOrderBo.setCompanyId("100".equals(deptId.toString()) ? companyId : deptId);

        StopWatch stopWatch = new StopWatch();
        stopWatch.start("阶段1");

        Page<CxrUserOrder> page = this.getBaseMapper().page(cxrUserOrderBo, cxrUserOrderBo.build());
        stopWatch.stop();

        // 由于退订单的复合字段有点坑 , 需要进行处理 如果是退订单类型需要把 退奶以外的奶数清零才可展示
        List<CxrUserOrder> tempPage = page.getRecords();
        try {
            //匹配销售代理信息--开始
            List<BusinessAgent> businessAgents = tempPage.stream()
                .filter(order -> order.getBusinessAgent() != null)
                .flatMap(order -> order.getBusinessAgent().stream()) // 将多个BusinessAgent合并成一个流
                .collect(Collectors.toList());
            List<Long> uniqueProxyIds = businessAgents.stream() // 将多个BusinessAgent合并成一个流
                .map(agent -> agent.getProxyId()) // 提取proxyId
                .distinct() // 去重
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(uniqueProxyIds)) {
                Map<Long, CxrEmployeeInfoDTO> cxrEmployeeInfoDTOMap = remoteEmployeeService.getEmployeeByIds(
                        uniqueProxyIds).stream()
                    .collect(Collectors.toMap(CxrEmployeeInfoDTO::getEmployeeId, Function.identity(), (v1, v2) -> v2));
                businessAgents.forEach(businessAgent -> {
                    CxrEmployeeInfoDTO cxrEmployeeInfoDTO = cxrEmployeeInfoDTOMap.get(businessAgent.getProxyId());
                    if (cxrEmployeeInfoDTO != null) {
                        businessAgent.setOccupationStatus(
                            cxrEmployeeInfoDTO.getOccupationStatus().equals(OccupationStatus.INDUCTION.getValue()));
                    }
                });
            }
        } catch (Exception e) {
        }
        //匹配销售代理--结束
        List<String> fromProductTypes =
            tempPage.stream().map(CxrUserOrder::getExchangeProductName).collect(Collectors.toList());
        Map<String, String> map = null;
        if (CollectionUtils.isNotEmpty(fromProductTypes)) {
            stopWatch.start("阶段2");
            map = remoteCxrSaleProductService.queryByName(fromProductTypes);
            stopWatch.stop();
        }

        List<Long> ids = tempPage.stream().filter(o -> o.getOrderType() == OrderTypeEnums.RETURN_ORDER.getValue())
            .map(CxrUserOrder::getId).collect(Collectors.toList());
        Map<Long, CxrUserReturnOrder> orderRefundMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(ids)) {
            orderRefundMap = returnOrderService.getRefundSuccessByOrderIds(ids);
        }

        List<Long> orderIds = tempPage.stream().map(CxrUserOrder::getId).collect(Collectors.toList());
        Map<Long, Long> determineTemplateMap = null;
        if (CollUtil.isNotEmpty(orderIds)) {
            List<CxrOrderDetermineTemplate> determineTemplates = determineTemplateMapper
                .selectList(new LambdaQueryWrapper<CxrOrderDetermineTemplate>()
                    .select(CxrOrderDetermineTemplate::getId, CxrOrderDetermineTemplate::getSourceId)
                    .in(CxrOrderDetermineTemplate::getSourceId, orderIds)
                    .eq(CxrOrderDetermineTemplate::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                );
            if (CollUtil.isNotEmpty(determineTemplates)) {
                determineTemplateMap = determineTemplates.stream().collect(
                    Collectors.toMap(CxrOrderDetermineTemplate::getSourceId, CxrOrderDetermineTemplate::getSourceId));
            }
        }

        List<Short> payOrderTypeList = new ArrayList<>();
        payOrderTypeList.add(OrderTypeEnums.NEW_ORDER.getValue());
        payOrderTypeList.add(OrderTypeEnums.CONTINUE_ORDER.getValue());
        payOrderTypeList.add(OrderTypeEnums.INCREASE_ORDER.getValue());
        payOrderTypeList.add(OrderTypeEnums.CONTRACT_ORDER.getValue());
        stopWatch.start("阶段3");

        List<Long> tikTokOrderIds = new ArrayList<>();

        for (CxrUserOrder cxrUserOrder : tempPage) {

            List<CustomerInfo> customerInfoList = cxrUserOrder.getCustomerInfoList();
            if (CollectionUtil.isNotEmpty(customerInfoList)) {
                cxrUserOrder.setAddressCreateByType(customerInfoList.get(0).getAddressCreateByType());
            }

            if (CollectionUtil.isNotEmpty(map)) {
                String s = map.get(cxrUserOrder.getExchangeProductName());
                cxrUserOrder.setFromProductType(Convert.toStr(s, ""));
            }

            if (cxrUserOrder.getOrderType() != OrderTypeEnums.CONTRACT_ORDER.getValue()) {
                cxrUserOrder.setContractTypeTag(null);
            }
            if (CollUtil.isNotEmpty(determineTemplateMap) &&
                ObjectUtil.isNotEmpty(determineTemplateMap.get(cxrUserOrder.getId()))) {
                cxrUserOrder.setDetermineTemplateTag(SysYesNo.YES.getValue());
            }

            if (cxrUserOrder.getOrderType() == OrderTypeEnums.RETURN_ORDER.getValue()) {
                cxrUserOrder.setOrderQuantity(
                    ObjectUtil.isNotEmpty(cxrUserOrder.getFreshMilkReturnQuantity())
                        ? -cxrUserOrder.getFreshMilkReturnQuantity()
                        : cxrUserOrder.getOrderQuantity());
                // cxrUserOrder.setLongMilkGiveQuantity(0);
                // cxrUserOrder.setFreshMilkGiveQuantity(0);
                // cxrUserOrder.setLongMilkSentQuantity(0);
                // cxrUserOrder.setFreshMilkSentQuantity(0);
                // amount 字段这是沿用的
                CxrUserReturnOrder returnOrder = orderRefundMap.get(cxrUserOrder.getId());
                if (returnOrder != null) {
                    cxrUserOrder.setOrderMoney(returnOrder.getOrderMoney());
                    cxrUserOrder.setRrCode(returnOrder.getRrCode());
                    Long afterSalesId = returnOrder.getAfterSalesId();
                    cxrUserOrder.setAfterSalesId(afterSalesId);
                    if (cxrUserOrder.getTerminalType() == TerminalTypeEnums.tiktok.getValue()
                        && returnOrder.getOrderId() != null) {
                        CxrUserOrder order = this.cxrUserOrderService.getOne(new LambdaQueryWrapper<CxrUserOrder>()
                            .eq(CxrUserOrder::getId, returnOrder.getOrderId())
                            .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                        );
                        if (ObjectUtil.isNotEmpty(order)) {
                            cxrUserOrder.setLinkedDouyinNumber(order.getOrderNo());
                        }
                    }
                    if (afterSalesId != null) {
                        cxrUserOrder.setTerminalType(TerminalTypeEnums.xcx.getValue());
                        CxrOrderAfterSale cxrOrderAfterSale = cxrOrderAfterSaleMapper.selectById(afterSalesId);
                        if (cxrOrderAfterSale != null) {
                            cxrUserOrder.setAfterSalesNo(cxrOrderAfterSale.getAfterSaleNo());
                        }
                    }
                }
            } else {
                cxrUserOrder.setRefundSuccessFlag(null);
            }
            Short orderType = cxrUserOrder.getOrderType();
            Short terminalType = cxrUserOrder.getTerminalType();
            Integer auditStatus = Convert.toInt(cxrUserOrder.getAuditStatus(), -1);
            Integer payStatus = cxrUserOrder.getPayStatus();
            if (payOrderTypeList.contains(orderType)) {

                if (NumberUtil.equals(TerminalTypeEnums.manager.getValue(), terminalType)) {
                    cxrUserOrder.setShowAuditMark(OrderAuditStatusEnums.getdesc(auditStatus));
                }

                if (NumberUtil.equals(TerminalTypeEnums.disribution.getValue(), terminalType)) {
                    cxrUserOrder.setShowAuditMark(PayStatusEnums.getdesc(payStatus));
                }

                if (NumberUtil.equals(TerminalTypeEnums.xcx.getValue(), terminalType) || NumberUtil.equals(
                    TerminalTypeEnums.tiktok.getValue(), terminalType)) {
                    cxrUserOrder.setShowAuditMark(PayStatusEnums.getdesc(payStatus));

                    if (NumberUtil.equals(TerminalTypeEnums.tiktok.getValue(), terminalType)) {
                        tikTokOrderIds.add(cxrUserOrder.getId());
                        String tiktokOrderRefundNo = cxrUserOrder.getTiktokOrderRefundNo();
                        if (tiktokOrderRefundNo == null) {
                        } else if ("".equals(tiktokOrderRefundNo)) {
                            cxrUserOrder.setTiktokOrderRefundException(1);
                            cxrUserOrder.setTiktokOrderRefundNo(null);
                        } else {
                            cxrUserOrder.setTiktokOrderRefundException(0);
                        }
                        List<CxrUserReturnOrder> returnOrderList =
                            returnOrderService.queryOrderIdList(cxrUserOrder.getId());
                        if (ObjectUtil.isNotEmpty(returnOrderList)) {
                            List<Long> userOrderIds = returnOrderList.stream().map(CxrUserReturnOrder::getUserOrderId)
                                .collect(
                                    Collectors.toList());
                            CxrUserOrder order = this.cxrUserOrderService.getBaseMapper()
                                .selectOne(new LambdaQueryWrapper<CxrUserOrder>()
                                    .in(CxrUserOrder::getId, userOrderIds)
                                    .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                                );
                            if (ObjectUtil.isNotEmpty(order)) {
                                cxrUserOrder.setLinkedDouyinNumber(order.getOrderNo());
                            }
                        }
                    }
                }
            } else {
                if (cxrUserOrder.getRefundSuccessFlag() != null && cxrUserOrder.getRefundSuccessFlag()) {
                    cxrUserOrder.setShowAuditMark("已打款");
                } else {
                    cxrUserOrder.setShowAuditMark(OrderAuditStatusEnums.getdesc(auditStatus));
                }
            }

            String deliverySites = cxrUserOrder.getDeliverySites();
            if (StrUtil.isNotEmpty(deliverySites)) {
                List<DeliverySiteDTO> deliverySitesDTO = JSONUtil.toList(deliverySites, DeliverySiteDTO.class);
                if (CollUtil.isNotEmpty(deliverySitesDTO)) {
                    cxrUserOrder.setDeliverySite(deliverySitesDTO.stream().map(DeliverySiteDTO::getSiteName)
                        .collect(Collectors.joining(",")));
                }
            }

        }

        stopWatch.stop();
        stopWatch.start("阶段4");
        CustomerOrderTotal customerOrderTotal =
            this.getBaseMapper().pageOrderTotalStatistics(cxrUserOrderBo);
        stopWatch.stop();
        //        if
        // (ObjectUtil.isNotEmpty(cxrUserOrderBo.getOrderType())&&cxrUserOrderBo.getOrderType()==OrderTypeEnums.RETURN_ORDER.getValue()){
        customerOrderTotal.setTotalAmount(
            customerOrderTotal
                .getTotalAmount()
                .subtract(customerOrderTotal.getReturnTotalAmount().negate()));
        //        }

        Map<String, Object> data = new HashMap<>();
        data.put("data", page.getRecords());
        data.put("statistics", customerOrderTotal);
        data.put("size", page.getSize());
        data.put("curr", cxrUserOrderBo.getPageNum());
        data.put("page", page.getPages());
        data.put("total", page.getTotal());

        log.info("pageQuery  {}", stopWatch.prettyPrint(TimeUnit.SECONDS));
        return data;
    }

/*    private void setTiktokOrderRefundException(List<Long> tikTokOrderIds,List<CxrUserOrder> tempPage){
        List<CxrUserReturnOrder> cxrUserReturnOrders = returnOrderService.queryOrderIds(tikTokOrderIds);
        if (CollectionUtil.isNotEmpty(cxrUserReturnOrders)){
            Map<Long, CxrUserReturnOrder> collect = cxrUserReturnOrders.stream().collect(Collectors.toMap(CxrUserReturnOrder::getUserOrderId, Function.identity(), (v1, v2) -> v2));
            for (CxrUserOrder cxrUserOrder : tempPage) {
                if (collect.get(cxrUserOrder.getId()) != null){
                    cxrUserOrder.setTiktokOrderRefundException(1);
                }
            }
        }
    }*/


    /**
     * 审核接口
     *
     * @param id
     * @return
     */
    @Override
    public boolean audit(Long id, boolean systemFlag, Integer auditType, String refundNoAuditReasons) {
        //        putLongMilkStock(id);
        boolean flag = abstractCxrUserOrderBehavior.executeCxrUserOrderAudit(id, systemFlag, auditType, refundNoAuditReasons);

        if (flag) {
            CxrUserOrder order = this.getById(id);

            // 新、增、续、合、赠、退
            if (NumberUtil.equals(order.getOrderType(), OrderTypeEnums.NEW_ORDER.getValue())
                || NumberUtil.equals(order.getOrderType(), OrderTypeEnums.CONTINUE_ORDER.getValue())
                || NumberUtil.equals(order.getOrderType(), OrderTypeEnums.CONTRACT_ORDER.getValue())
                || NumberUtil.equals(order.getOrderType(), OrderTypeEnums.GIVE_ORDER.getValue())
                || NumberUtil.equals(order.getOrderType(), OrderTypeEnums.INCREASE_ORDER.getValue())) {

                if (NumberUtil.equals(order.getOrderType(), OrderTypeEnums.GIVE_ORDER.getValue())) {
                    mqUtil.sendSyncMessage(
                        OrderConstant.ORDER_TOPIC, OrderConstant.LONG_MILK_STOCK_TAG, StrUtil.toString(id));
                    mqUtil.sendSyncMessage(
                        OrderConstant.ORDER_TOPIC, OrderConstant.ORDER_EMPLOYEE_TAG, StrUtil.toString(id));
                }

                // 新增续
                if (NumberUtil.equals(order.getOrderType(), OrderTypeEnums.NEW_ORDER.getValue())
                    || NumberUtil.equals(order.getOrderType(), OrderTypeEnums.CONTINUE_ORDER.getValue())
                    || NumberUtil.equals(order.getOrderType(), OrderTypeEnums.INCREASE_ORDER.getValue())) {
                    // 销售代理业绩处理
                    mqUtil.sendSyncMessage(
                        OrderConstant.ORDER_TOPIC, OrderConstant.ORDER_EMPLOYEE_TAG, StrUtil.toString(id));
                    mqUtil.sendSyncMessage(
                        OrderConstant.ORDER_TOPIC, OrderConstant.SEND_ORDER_MESSAGE_TAG, Convert.toStr(id));
                    // 常温奶库存
                    mqUtil.sendSyncMessage(
                        OrderConstant.ORDER_TOPIC, OrderConstant.LONG_MILK_STOCK_TAG, StrUtil.toString(id));

                    mqUtil.sendSyncMessageHuiBo(HuiboConstant.HUI_BO_ORDER_TOPIC, HuiboConstant.HUI_BO_ORDER_RT_SYN_TAG,
                        id.toString());
                    mqUtil.sendSyncMessage(CxrCustomerConstant.CUSTOMER_TOPIC, CxrCustomerConstant.YDS_CUSTOMER_PAY_TAG,
                        id.toString());
                }
                // 合订单
                if (NumberUtil.equals(order.getOrderType(), OrderTypeEnums.CONTRACT_ORDER.getValue())) {
                    applicationEventPublisher.publishEvent(new ContractOrderSplitEvent(id, this));
                }

                if (ObjectUtil.isNotEmpty(order.getFreshMilkSentQuantity()) && order.getFreshMilkSentQuantity() != 0
                    && !NumberUtil.equals(order.getOrderType(),
                    OrderTypeEnums.CONTRACT_ORDER.getValue())) {

                    CustomerSentNumberVo customerSentNumberVo = new CustomerSentNumberVo();
                    customerSentNumberVo.setMilkDistributionTotal(Long.valueOf(order.getFreshMilkSentQuantity()));
                    customerSentNumberVo.setCxrCustomerId(order.getCustomerId());
                    customerSentNumberVo.setDistributionDate(order.getOrderDate());
                    customerSentNumberVo.setReceiverPhone(order.getCustomerPhone());
                    customerSentNumberVo.setCustomerAddress(order.getCustomerAdress());
                    mqUtil.sendSyncMessage(CustomerAddressConstant.CUSTOMER_ADDRESS_TOPIC,
                        CustomerAddressConstant.CUSTOMER_ORDER_SENT_SUM_CHANGE_TAG,
                        JSONUtil.toJsonStr(customerSentNumberVo));
                }
                return flag;
            } else if (NumberUtil.equals(order.getOrderType(), OrderTypeEnums.RETURN_ORDER.getValue())) {
                // 退订单常温奶库存
                if (ObjectUtil.isNotEmpty(auditType) && auditType == 2) {
                    mqUtil.sendSyncMessage(SendMessageReturnOrderConstant.MESSAGE_CENTER_CXR_CUSTOMER_RETURN_ORDER_TOPIC,
                        SendMessageReturnOrderConstant.CUSTOMER_AUDIT_NO_PASS_RETURN_ORDER_MSG_TAG, id + "");
                } else {
                    mqUtil.sendSyncMessage(
                        OrderConstant.ORDER_TOPIC, OrderConstant.ORDER_EMPLOYEE_TAG, StrUtil.toString(id));
                }
                return flag;
            }
        }
        return true;
    }

    //    public void putLongMilkStock(Long orderId){
    //
    //        /**
    //         * 审核完之后添加一个常温奶库存 常温奶库存  后台的所有订单 和 前端免费的订单都需要审核 ,后台的全部类型的订单都需要审核
    //         */
    //        CxrUserOrderVO order = abstractCxrUserOrderBehavior.executeOrderDetail(orderId);
    //        if(NumberUtil.equals(order.getOrderType(),OrderTypeEnums.NEW_ORDER.getValue())||
    //            NumberUtil.equals(order.getOrderType(),OrderTypeEnums.CONTINUE_ORDER.getValue())||
    //            NumberUtil.equals(order.getOrderType(),OrderTypeEnums.CONTRACT_ORDER.getValue())||
    //            NumberUtil.equals(order.getOrderType(),OrderTypeEnums.GIVE_ORDER.getValue())||
    //            NumberUtil.equals(order.getOrderType(),OrderTypeEnums.INCREASE_ORDER.getValue())
    //        ){
    //
    //            Long id = order.getBusinessAgent().get(0).getProxyId();
    //
    //            CxrEmployee cxrEmployee = remoteEmployeeService.getById(id);
    //
    //            LoginEmployee customer =
    // remoteCustomerService.getCustomerInfoByPhoneNumber(order.getCustomerPhone());
    //
    //
    //            //添加一下东西道库存表
    //            CxrLongMilkStockVO vo = new CxrLongMilkStockVO();
    //            vo.setCxrEmployeeId(LoginHelper.getUserId());
    //            vo.setCxrCustomerPhone(order.getCustomerPhone());// 客户电话
    //            vo.setCxrCustomerId(customer.getUserId());
    //
    //
    //            vo.setCxrSiteId(order.getSiteId());
    //            vo.setCxrSiteName(order.getSiteName());
    //            vo.setOrderTime(order.getOrderDate());
    //            vo.setOrderType(order.getOrderType().toString());
    //
    //            if(NumberUtil.equals(OrderTypeEnums.GIVE_ORDER.getValue(),order.getOrderType())){
    //                //赠送数量
    //                vo.setSentNum(order.getLongMilkGiveQuantity());
    //                //剩余数量 剩余数量相当于一开始的总数
    //                vo.setSurplusNum(order.getLongMilkGiveQuantity());
    //            }else {
    //                //赠送数量
    //                vo.setSentNum(order.getLongMilkGiveQuantity());
    //                //剩余数量 剩余数量相当于一开始的总数
    //                vo.setSurplusNum(order.getLongMilkGiveQuantity());
    //            }
    //
    //            List<BusinessAgent> agents= order.getBusinessAgent();
    //
    //            if(CollectionUtil.isNotEmpty(agents)){
    //
    //                vo.setCxrEmployeeJobNumber(cxrEmployee.getJobNumber()); //没有先不拿
    //                vo.setCxrEmployeeLevelType(String.valueOf(cxrEmployee.getEmployeeLevelType()));
    //                vo.setCxrEmployeeName(cxrEmployee.getName());
    //            }
    //
    //
    //
    // vo.setExpireTime(org.apache.commons.lang3.time.DateUtils.addDays(order.getOrderDate(),30)); //
    // 30天过期
    //            vo.setLongMilkStatus(LongMilkStatus.AVAILABLE.getValue());
    //            vo.setApplyStatus(LongMilkApplyStatus.INITIAL.getValue());
    //            vo.setCxrUserOrderId(order.getId());
    //            //基本只看这个quantity
    //            vo.setCxrUserOrderQuantity(order.getOrderQuantity().longValue());
    //
    //            vo.setApplyNum(0);
    //            remoteLongMilkStockService.addOne(vo);
    //        }
    //    }

    /**
     * 删除订单
     *
     * @param id
     * @return
     */
    @Override
    public boolean deleteOrder(Long id) {
        return abstractCxrUserOrderBehavior.deleteOrder(id);
    }

    /**
     * 订单支持成功之后 客户处理
     *
     * @param cxrUserOrderId
     */
    @Override
    public void orderAfterCustomerHandler(Long cxrUserOrderId) {

        log.info(">>>>>>>>>>>>>>>orderAfterCustomerHandler cxrUserOrderId={}", cxrUserOrderId);
        // region 创建客户账户
        CxrUserOrder cxrUserOrder = this.getById(cxrUserOrderId);

        log.info(">>>>>>>>>>>>>>>orderAfterCustomerHandler cxrUserOrder={}", cxrUserOrder);

        if (NumberUtil.equals(
            PayStatusEnums.PAY_SUCCEEDED.getValue(), cxrUserOrder.getPayStatus().intValue())
            || NumberUtil.equals(
            OrderAuditStatusEnums.AUDITED.getValue(), cxrUserOrder.getAuditStatus())) {
            boolean flag = remoteCustomerService.existsCustomerAccount(cxrUserOrder.getCustomerPhone());
            log.info(">>>>>>>>>>>>>>>orderAfterCustomerHandler flag={}", flag);
            if (!flag) {

                // 判断终端类型 配送端需要完善 故此
                if (NumberUtil.equals(
                    TerminalTypeEnums.disribution.getValue(), cxrUserOrder.getTerminalType())) {
                    // 如果账户已经有,不需要创建 没有创建用户账户
                    Long customerAccountId =
                        remoteCustomerService.addMainAccount(cxrUserOrder.getCustomerPhone());

                    SQLException.isTrueFlag(
                        cxrUserOrderService.updateCustomerIdByOrderId(cxrUserOrderId, customerAccountId),
                        () -> {
                            throw new ServiceException("订单回写用户失败!");
                        });
                    cxrUserOrder.setCustomerId(customerAccountId);
                }

                // 判断终端类型 配送端需要完善 故此
                if (NumberUtil.equals(
                    TerminalTypeEnums.manager.getValue(), cxrUserOrder.getTerminalType())) {
                    // 如果账户已经有,不需要创建 没有创建用户账户
                    OrderCustomerInfoHanderEvent orderCustomerInfoHanderEvent =
                        new OrderCustomerInfoHanderEvent(
                            cxrUserOrder.getCustomerInfoList(), cxrUserOrder.getId(), this);
                    applicationEventPublisher.publishEvent(orderCustomerInfoHanderEvent);
                }

            } else {

                CxrCustomer cxrCustomer =
                    remoteCustomerService.queryByPhone(cxrUserOrder.getCustomerPhone());
                SQLException.isTrueFlag(
                    cxrUserOrderService.updateCustomerIdByOrderId(cxrUserOrderId, cxrCustomer.getId()),
                    () -> {
                        throw new ServiceException("订单回写用户失败!");
                    });
                cxrUserOrder.setCustomerId(cxrCustomer.getId());
            }

            // 用户库存 + 流水明细

            // 查询业绩是否已经处理

            if (!remoteCustomerStockDetailService.exists(cxrUserOrder.getId())) {
                CxrCustomer customer = remoteCustomerService.queryByPhone(cxrUserOrder.getCustomerPhone());

                log.info(">>>>>>>>>>>>customer={}", customer);
                // 库存为0时修改客户起送时间
                OrderTypeEnums type = OrderTypeEnums.getType(cxrUserOrder.getOrderType());
                Date customerStockZeroTime = customer.getCustomerStockZeroTime();

                boolean zeroGe1 = false;
                if (customerStockZeroTime != null) {
                    LocalDate stockZeroTime = DateUtil.toLocalDateTime(customerStockZeroTime).toLocalDate();
                    zeroGe1 = LocalDate.now().isAfter(stockZeroTime) || (LocalDate.now().isEqual(stockZeroTime)
                        && LocalTime.now().isAfter(LocalTime.of(20, 0)));

                    boolean zero15DayAfter = !LocalDate.now().isBefore(com.ruoyi.common.core.utils.DateUtils.getLocalDateFromDate(customerStockZeroTime).plusDays(16));
                    lambdaUpdate().set(CxrUserOrder::getZero15DayAfter, zero15DayAfter).eq(CxrUserOrder::getId, cxrUserOrderId).update();
                }
                if (customer.getCustomerStock().intValue() == 0 && zeroGe1) {
                    remoteCustomerAddressService.updateByCustomerId(customer.getId());
                }

                applicationEventPublisher.publishEvent(
                    new CustomerInfoWriteback(cxrUserOrder.getId(), this));

                List<CxrCustomerStockDetail> cxrCustomerStockDetailList = new ArrayList<>();
                CxrCustomerStockDetail customerStockDetail = new CxrCustomerStockDetail();
                customerStockDetail.setRemark(
                    StrUtil.format("订单id={},订单类型 ={}", cxrUserOrder.getId(), type.getDesc()));
                customerStockDetail.setSourceId(cxrUserOrder.getId());
                customerStockDetail.setSourceType(
                    SourceEnums.getType(cxrUserOrder.getOrderType()).getValue());
                customerStockDetail.setTerminalType(cxrUserOrder.getTerminalType());

                customerStockDetail.setFreshMilkQuantity(
                    Convert.toInt(cxrUserOrder.getOrderQuantity(), 0)
                        + Convert.toInt(cxrUserOrder.getFreshMilkGiveQuantity(), 0)
                        - Convert.toInt(cxrUserOrder.getFreshMilkSentQuantity(), 0));
                customerStockDetail.setOrderQuantity(Convert.toInt(cxrUserOrder.getOrderQuantity(), 0));
                customerStockDetail.setFreshMilkGiveQuantity(
                    Convert.toInt(cxrUserOrder.getFreshMilkGiveQuantity(), 0));
                customerStockDetail.setFreshMilkSentQuantity(
                    Convert.toInt(cxrUserOrder.getFreshMilkSentQuantity(), 0));
                customerStockDetail.setExchangeUseFreshMilkQuantity(0);
                customerStockDetail.setDisributionFreshMilkQuantity(0);
                customerStockDetail.setTemporaryAddMilkQuantity(0);
                customerStockDetail.setLongMilkQuantity(
                    Convert.toInt(cxrUserOrder.getLongMilkGiveQuantity(), 0)
                        - Convert.toInt(cxrUserOrder.getLongMilkSentQuantity(), 0));
                customerStockDetail.setLongMilkGiveQuantity(
                    Convert.toInt(cxrUserOrder.getLongMilkGiveQuantity(), 0));
                customerStockDetail.setLongMilkSentQuantity(
                    Convert.toInt(cxrUserOrder.getLongMilkSentQuantity(), 0));
                cxrCustomerStockDetailList.add(customerStockDetail);
                remoteCustomerStockDetailService.add(customer.getId(), cxrCustomerStockDetailList);

                if (ObjectUtil.isNotEmpty(cxrUserOrder.getFreshMilkSentQuantity())
                ) {
                    CustomerSentNumberVo customerSentNumberVo = new CustomerSentNumberVo();
                    customerSentNumberVo.setLaoMilkDistributionTotal(
                        Long.valueOf(cxrUserOrder.getFreshMilkSentQuantity()));
                    customerSentNumberVo.setMilkDistributionTotal(
                        Long.valueOf(cxrUserOrder.getFreshMilkSentQuantity()));
                    customerSentNumberVo.setCxrCustomerId(customer.getId());
                    customerSentNumberVo.setDistributionDate(cxrUserOrder.getOrderDate());
                    customerSentNumberVo.setReceiverPhone(cxrUserOrder.getCustomerPhone());
                    customerSentNumberVo.setCustomerAddress(cxrUserOrder.getCustomerAdress());
                    mqUtil.sendSyncMessage(CustomerAddressConstant.CUSTOMER_ADDRESS_TOPIC,
                        CustomerAddressConstant.CUSTOMER_ORDER_SENT_SUM_CHANGE_TAG,
                        JSONUtil.toJsonStr(customerSentNumberVo));
                }
            }

            mqUtil.sendSyncMessage(
                OrderConstant.ORDER_TOPIC,
                OrderConstant.LONG_MILK_STOCK_TAG,
                StrUtil.toString(cxrUserOrderId));

            if (cxrUserOrder.getActivityId() > 0) {
                Long customerId = cxrUserOrder.getCustomerId();
                if (customerId == null || customerId.longValue() == 0l) {
                    cxrUserOrder = this.getById(cxrUserOrderId);
                }
                activityOrderService.updateOrderDateil(cxrUserOrder);
            }
        }

        // endregion
    }

    /**
     * 订单支持成功之后 员工处理
     *
     * @param cxrUserOrderId
     */
    @Override
    public void orderAfterEmployeeHandler(Long cxrUserOrderId) {
        // region 销售代理业绩处理
        // 业绩记录
        CxrUserOrder cxrUserOrder = this.lambdaQuery().apply("id={0}", cxrUserOrderId).last("FOR UPDATE").one();

        if (OrderTypeEnums.CONTRACT_ORDER.getValue()
            == Convert.toShort(cxrUserOrder.getOrderType(), (short) -1)) {
            // 所以的子单
            String orderNo = cxrUserOrder.getOrderNo();
            String[] split = orderNo.split("-");
            String parentOrderNo = split[0];
            List<CxrUserOrder> cxrUserOrderList =
                cxrUserOrderService
                    .getBaseMapper()
                    .selectList(
                        new LambdaUpdateWrapper<CxrUserOrder>()
                            .likeRight(CxrUserOrder::getOrderNo, StrUtil.format("{}-", parentOrderNo)));

            CxrUserOrder order = lambdaQuery().eq(CxrUserOrder::getOrderNo, parentOrderNo).one();
            this.lambdaQuery().apply("id={0}", order.getId()).last("FOR UPDATE").one();
            RedisLockUtils.tryLockException(StrUtil.format("cxr:order:contract_order_achievement:{}", parentOrderNo), () -> {
                // 废弃之前的业绩
                List<Long> ids = cxrUserOrderList.stream().map(CxrUserOrder::getId).collect(Collectors.toList());
                remoteEmployeeAchievementDetailService.achievemenDiscard(ids, "订单修改废弃审核的订单业绩");

                for (CxrUserOrder userOrder : cxrUserOrderList) {
                    calculatePerformance(userOrder);
                }
                mqUtil.sendSyncMessage(MqBizConst.DIVIDE_COMPUTE_TOPIC, MqBizConst.DIVIDE_COMPUTE_ORDERS_TAG,
                    JSONObject.toJSONString(ids));
                mqUtil.sendDelayMessage(CalculateConstant.CALCULATE_TOPIC,
                    CalculateConstant.CALCULATE_DAILY_SITE_ACHIEVEMENT_TAG, cxrUserOrderId.toString(), 20);
            });
            return;
        }

        calculatePerformance(cxrUserOrder);

        mqUtil.sendSyncMessage(MqBizConst.DIVIDE_COMPUTE_TOPIC, MqBizConst.DIVIDE_COMPUTE_ORDER_TAG,
            cxrUserOrderId.toString());
        mqUtil.sendDelayMessage(CalculateConstant.CALCULATE_TOPIC,
            CalculateConstant.CALCULATE_DAILY_SITE_ACHIEVEMENT_TAG, cxrUserOrderId.toString(), 20);
        // endregion
    }

    private void calculatePerformance(CxrUserOrder cxrUserOrder) {

        String orderNo = cxrUserOrder.getOrderNo();
        String[] orderNos = orderNo.split("-");

        log.info("===========[method=calculatePerformance]: id={}, orderNo={}, orderNos[]={}===========",
            cxrUserOrder.getId(), orderNo, orderNos);
        RedisLockUtils.tryLockException(
            StrUtil.format("cxr:order:performance:{}", orderNos[0]),
            () -> {
                Long cxrUserOrderId = cxrUserOrder.getId();
                // 处理订单时间
                Date orderDate = cxrUserOrder.getOrderDate();
                Instant instant = orderDate.toInstant();
                ZoneId zoneId = ZoneId.systemDefault();
                LocalDate detailOrderDate = instant.atZone(zoneId).toLocalDate();

                BigDecimal rebates = BigDecimal.ZERO;
                boolean returnOrderExists = true;
                CxrUserReturnOrder returnOrder = null;
                if (NumberUtil.equals(
                    cxrUserOrder.getOrderType().shortValue(), OrderTypeEnums.RETURN_ORDER.getValue())) {
                    returnOrder = returnOrderService.getByOrderId(cxrUserOrderId);
                    returnOrderExists = false;
                    rebates = returnOrder.getRebates();
                }

                // 支付或者审核
                if (NumberUtil.equals(
                    PayStatusEnums.PAY_SUCCEEDED.getValue(), cxrUserOrder.getPayStatus().intValue())
                    || NumberUtil.equals(
                    OrderAuditStatusEnums.AUDITED.getValue(), cxrUserOrder.getAuditStatus())) {

                    // 过滤 已经有业绩的
                    Boolean flag = remoteEmployeeAchievementDetailService.existsDetail(cxrUserOrderId);
                    Boolean isImport = cxrUserOrder.getIsImport();
                    if (ObjectUtil.isEmpty(isImport)) {
                        isImport = false;
                    }
                    if (!flag && !isImport) {
                        // 只有新增续合订单 才会处理超送
                        // 代理
                        List<BusinessAgent> businessAgent = cxrUserOrder.getBusinessAgent();

                        // 要查询转播
                        List<Long> collect =//销售代理 ID集合
                            businessAgent.stream()
                                .map(BusinessAgent::getProxyId)
                                .collect(Collectors.toList());
                        log.info("开始匹配小组：订单{},销售代理ID{}", cxrUserOrder.getOrderNo(),
                            JSONUtil.toJsonStr(collect));

                        //退订单按创建时间计算站点和小组
                        Map<Long, CxrGroupQueryDto> longCxrGroupQueryDtoMap = checkSiteAndGroupId(!returnOrderExists ? cxrUserOrder.getCreateTime() : orderDate, collect);
                        log.info("结束匹配小组：{}", JSONUtil.toJsonStr(longCxrGroupQueryDtoMap));
                        log.info("businessAgent>>>>>>>>>>>>{}", businessAgent);

                        BigDecimal amount = cxrUserOrder.getAmount();
                        BigDecimal excessQuantity =
                            Convert.toBigDecimal(cxrUserOrder.getExcessQuantity(), BigDecimal.ZERO);
                        BigDecimal orderQuantity =
                            new BigDecimal(Convert.toInt(cxrUserOrder.getOrderQuantity(), 0));
                        //                    int freshMilkSentQuantity =
                        // Convert.toInt(cxrUserOrder.getFreshMilkSentQuantity(), 0);
                        BigDecimal freshMilkSentQuantity =
                            Convert.toBigDecimal(cxrUserOrder.getFreshMilkSentQuantity(), BigDecimal.ZERO);
                        BigDecimal LongMilkGiveQuantity =
                            Convert.toBigDecimal(
                                cxrUserOrder.getLongMilkGiveQuantity(), BigDecimal.ZERO); // 常温奶赠送
                        BigDecimal freshMilkGiveQuantity =
                            Convert.toBigDecimal(
                                cxrUserOrder.getFreshMilkGiveQuantity(), BigDecimal.ZERO); // 鲜奶赠送
                        BigDecimal orderFmQty = NumberUtil.add(orderQuantity, freshMilkGiveQuantity); // 鲜奶盒数（订购数+赠送数）

                        BigDecimal returnOrderQuantity = BigDecimal.ZERO;
                        if (!returnOrderExists) {
                            returnOrderQuantity =
                                new BigDecimal(Convert.toInt(returnOrder.getFreshMilkRefundQuantity(), 0));
                            LongMilkGiveQuantity =
                                BigDecimal.ZERO.subtract(
                                    Convert.toBigDecimal(
                                        returnOrder.getLongMilkCancelQuantity(), BigDecimal.ZERO));
                            freshMilkGiveQuantity =
                                BigDecimal.ZERO.subtract(
                                    Convert.toBigDecimal(
                                            returnOrder.getFreshMilkCancelQuantity(), BigDecimal.ZERO)
                                        .subtract(
                                            Convert.toBigDecimal(
                                                returnOrder.getFreshMilkRefundQuantity(), BigDecimal.ZERO)));
                            orderFmQty = BigDecimal.ZERO.subtract(
                                Convert.toBigDecimal(returnOrder.getFreshMilkCancelQuantity(), BigDecimal.ZERO));
                        }

                        int size = businessAgent.size();
                        BigDecimal agentSize = Convert.toBigDecimal(size);

                        BigDecimal avg = NumberUtil.div(amount, size, 2);
                        BigDecimal avgExcessQuantity = excessQuantity.divide(agentSize, 2, BigDecimal.ROUND_DOWN);
                        BigDecimal avgQuantity = orderQuantity.divide(agentSize, 2, BigDecimal.ROUND_DOWN);

                        BigDecimal avgFreshMilkSentQuantity = freshMilkSentQuantity.divide(agentSize, 2,
                            BigDecimal.ROUND_DOWN);
                        BigDecimal avgReturnOrderQuantity = returnOrderQuantity.divide(agentSize, 2,
                            BigDecimal.ROUND_DOWN);
                        BigDecimal avgLongMilkGiveQuantity = LongMilkGiveQuantity.divide(agentSize, 2,
                            BigDecimal.ROUND_DOWN);
                        BigDecimal avgFreshMilkGiveQuantity = freshMilkGiveQuantity.divide(agentSize, 2,
                            BigDecimal.ROUND_DOWN);

                        BigDecimal avgRebates = rebates.divide(agentSize, 2, BigDecimal.ROUND_DOWN);

                        BigDecimal orderCount = BigDecimal.ONE;
                        BigDecimal avgOrderCount = orderCount.divide(agentSize, 2, BigDecimal.ROUND_DOWN);

                        BigDecimal avgOrderFmQty = orderFmQty.divide(agentSize, 2, BigDecimal.ROUND_DOWN);

                        List<CxrEmployeeAchievementDetail> list = new ArrayList<>();
                        for (int i = 0; i < size; i++) {
                            BusinessAgent tempBussinessAgent = businessAgent.get(i);
                            if (i == size - 1) {
                                avg = amount;
                                avgExcessQuantity = excessQuantity;
                                avgQuantity = orderQuantity;
                                avgOrderFmQty = orderFmQty;
                                avgFreshMilkSentQuantity = freshMilkSentQuantity;
                                avgReturnOrderQuantity = returnOrderQuantity;
                                avgFreshMilkGiveQuantity = freshMilkGiveQuantity;
                                avgLongMilkGiveQuantity = LongMilkGiveQuantity;
                                avgOrderCount = orderCount;
                                avgRebates = rebates;
                            } else {
                                amount = amount.subtract(avg);
                                excessQuantity = excessQuantity.subtract(avgExcessQuantity);
                                orderQuantity = orderQuantity.subtract(avgQuantity);
                                orderFmQty = orderFmQty.subtract(avgOrderFmQty);
                                //                            freshMilkSentQuantity = freshMilkSentQuantity -
                                // avgFreshMilkSentQuantity;
                                freshMilkSentQuantity =
                                    NumberUtil.sub(freshMilkSentQuantity, avgFreshMilkSentQuantity);
                                returnOrderQuantity = returnOrderQuantity.subtract(avgReturnOrderQuantity);
                                freshMilkGiveQuantity = freshMilkGiveQuantity.subtract(avgFreshMilkGiveQuantity);
                                LongMilkGiveQuantity = LongMilkGiveQuantity.subtract(avgLongMilkGiveQuantity);
                                orderCount = orderCount.subtract(avgOrderCount);
                                rebates = rebates.subtract(avgRebates);
                            }

                            CxrEmployeeAchievementDetail cxrEmployeeAchievementDetail =
                                new CxrEmployeeAchievementDetail();

                            // 查询销售代理
                            Long siteId = 0L;
                            Long groupId = 0L;
                            if (CollectionUtil.isNotEmpty(longCxrGroupQueryDtoMap)) {
                                CxrGroupQueryDto cxrGroupQueryDto = longCxrGroupQueryDtoMap.get(
                                    tempBussinessAgent.getProxyId());
                                if (ObjectUtil.isNotEmpty(cxrGroupQueryDto)) {
                                    siteId = cxrGroupQueryDto.getOpionSiteId();
                                    groupId = cxrGroupQueryDto.getOptionGroupId();
                                }
                            }
                            cxrEmployeeAchievementDetail.setGroupId(Convert.toLong(groupId, 0L));
                            cxrEmployeeAchievementDetail.setSiteId(siteId);
                            cxrEmployeeAchievementDetail.setEmployeeName(
                                Convert.toStr(tempBussinessAgent.getProxyName(), null));
                            log.info("cxrEmployeeAchievementDetail.getEmployeeName->:{}",
                                cxrEmployeeAchievementDetail.getEmployeeName());
                            cxrEmployeeAchievementDetail.setEmployeeId(tempBussinessAgent.getProxyId());

                            if (!returnOrderExists) {
                                cxrEmployeeAchievementDetail.setAchievementValue(avg.negate());
                                cxrEmployeeAchievementDetail.setBoxs(
                                    BigDecimal.ZERO.subtract(avgReturnOrderQuantity));
                            } else {
                                cxrEmployeeAchievementDetail.setAchievementValue(avg);
                                cxrEmployeeAchievementDetail.setBoxs(avgQuantity);
                            }
                            cxrEmployeeAchievementDetail.setOrderFmQty(avgOrderFmQty);

                            cxrEmployeeAchievementDetail.setSouceId(cxrUserOrder.getId());
                            cxrEmployeeAchievementDetail.setSource(SourceEnums.NEW_ORDER.getValue());
                            cxrEmployeeAchievementDetail.setTerminalType(cxrUserOrder.getTerminalType().intValue());
                            cxrEmployeeAchievementDetail.setExcessQuantity(avgExcessQuantity);
                            cxrEmployeeAchievementDetail.setFreshMilkGiveQuantity(avgFreshMilkGiveQuantity);
                            cxrEmployeeAchievementDetail.setLongMilkGiveQuantity(avgLongMilkGiveQuantity);

                            cxrEmployeeAchievementDetail.setFreshMilkSentQuantity(avgFreshMilkSentQuantity);

                            cxrEmployeeAchievementDetail.setOrderCount(avgOrderCount);
                            cxrEmployeeAchievementDetail.setRebates(avgRebates);

                            cxrEmployeeAchievementDetail.setChannel(cxrUserOrder.getChannel());
                            cxrEmployeeAchievementDetail.setProductType(cxrUserOrder.getProductType());
                            cxrEmployeeAchievementDetail.setProductTypeName(cxrUserOrder.getProductTypeName());
                            cxrEmployeeAchievementDetail.setAccountingType(cxrUserOrder.getAccountingType());
                            cxrEmployeeAchievementDetail.setPromotionCommission(cxrUserOrder.getPromotionCommission());
                            cxrEmployeeAchievementDetail.setCommissionConfigId(cxrUserOrder.getCommissionConfigId());
                            cxrEmployeeAchievementDetail.setProductName(cxrUserOrder.getProductName());

                            Short orderType = cxrUserOrder.getOrderType();
                            if (orderType != null && orderType == OrderTypeEnums.CONTRACT_ORDER.getValue()) {
                                Short contractTypeTag = cxrUserOrder.getContractTypeTag();
                                cxrEmployeeAchievementDetail.setOrderType(contractTypeTag);
                                if (contractTypeTag == null || contractTypeTag == -2) {

                                }
                            } else {
                                cxrEmployeeAchievementDetail.setOrderType(orderType);
                            }

                            // 增加  订单时间
                            cxrEmployeeAchievementDetail.setOrderDate(detailOrderDate);
                            list.add(cxrEmployeeAchievementDetail);
                        }
                        log.info("cxrEmployeeAchievementDetail list >:{}", JSONUtil.toJsonStr(list));
                        remoteEmployeeAchievementDetailService.add(list);
                        for (CxrEmployeeAchievementDetail detail : list) {
                            PerformanceDayDTO dto = new PerformanceDayDTO();
                            dto.setPerformance(detail.getAchievementValue());
                            dto.setEmployeeId(detail.getEmployeeId());
                            dto.setSiteId(detail.getSiteId());
                            dto.setCountDay(detail.getOrderDate());
                            dto.setCurrentDate(new Date());
                            log.info("SALE_DAY_PERFORMANCE_TOPIC JSONUtil.toJsonStr(dto)>:{}", JSONUtil.toJsonStr(dto));

                            mqUtil.sendDelayMessage(MqDayPerformanceConst.SALE_DAY_PERFORMANCE_TOPIC,
                                MqDayPerformanceConst.SALE_DAY_INCREMENT_MODIFICATION_PERFORMANCE_TAG,
                                JSONUtil.toJsonStr(dto), Duration.ofSeconds(1));
                        }
                    }
                }
            });
    }

    @Nullable
    private Map<Long, CxrGroupQueryDto> checkSiteAndGroupId(Date orderDate, List<Long> collect) {

        LocalDate localDateFromDate = com.ruoyi.common.core.utils.DateUtils.getLocalDateFromDate(orderDate);//订单时间
        Map<Long, CxrGroupQueryDto> longCxrGroupQueryDtoMap = remoteEmployeeService.querySiteAndGroupId(
            collect);
        log.info("longCxrGroupQueryDtoMap:{}", JSONUtil.toJsonStr(longCxrGroupQueryDtoMap));
        if (localDateFromDate.compareTo(LocalDate.now()) == 0) {
            log.info("日期相等，orderDate:{},localDateFromDate:{}", orderDate, localDateFromDate);
            return longCxrGroupQueryDtoMap;
        }

        LocalDate dayOfMouth = com.ruoyi.common.core.utils.DateUtils.getDayOfMouth(localDateFromDate,
            achievementDetailDate.getCalculateDates());//规定小组结束时间
        log.info("日期不相等，orderDate:{},localDateFromDate:{},dayOfMouth:{}", orderDate, localDateFromDate,
            dayOfMouth);
        List<CxrGroupQueryDto> dtos = new ArrayList<>();
        for (Long aLong : collect) {
            log.info("aLong:{}", aLong);
            //调岗
            CxrEmployeePostTransferApply PostTransferApply =
                new LambdaQueryChainWrapper<>(cxrEmployeePostTransferApplyMapper)
                    .select(CxrEmployeePostTransferApply::getEmployeeId, CxrEmployeePostTransferApply::getAuditTime,
                        CxrEmployeePostTransferApply::getLeaveCxrSiteId)
                    .eq(CxrEmployeePostTransferApply::getEmployeeId, aLong)
                    .apply("date_format(audit_time, '%Y-%m-%d') >= {0}", localDateFromDate.toString())
                    .in(CxrEmployeePostTransferApply::getApplyAuditStatus, 124, 224, 122)
                    .orderByAsc(CxrEmployeePostTransferApply::getAuditTime)
                    .last("limit 1")
                    .one();
            boolean postTransfer = ObjectUtil.isEmpty(PostTransferApply);
            //离职
            CxrEmployeeCooperateRecord cooperateRecord = new LambdaQueryChainWrapper<>(cxrEmployeeCooperateRecordMapper)
                .select(CxrEmployeeCooperateRecord::getCxrEmployeeId, CxrEmployeeCooperateRecord::getCxrSiteId,
                    CxrEmployeeCooperateRecord::getBsDate)
                .eq(CxrEmployeeCooperateRecord::getCxrEmployeeId, aLong)
                .eq(CxrEmployeeCooperateRecord::getBsType, 2)
                .ge(CxrEmployeeCooperateRecord::getBsDate, localDateFromDate)
                .orderByAsc(CxrEmployeeCooperateRecord::getBsDate)
                .last("limit 1")
                .one();
            boolean cooperate = ObjectUtil.isEmpty(cooperateRecord);
            log.info("postTransfer:{},cooperate:{}", postTransfer, cooperate);
            CxrGroupQueryDto dto = new CxrGroupQueryDto();
            dto.setCxrEmployeeId(aLong);

            if (!postTransfer && !cooperate) {
                LocalDate tranferAuditDate = com.ruoyi.common.core.utils.DateUtils.getLocalDateFromDate(
                    PostTransferApply.getAuditTime()).plusDays(1);
                LocalDate bsDate = cooperateRecord.getBsDate();
                int i = bsDate.compareTo(tranferAuditDate);
                if (i == 0) {
                    dto.setOpionSiteId(cooperateRecord.getCxrSiteId());
                    dto.setLeaveOrQuitTime(bsDate);
                }
                if (i < 0) {
                    dto.setOpionSiteId(cooperateRecord.getCxrSiteId());
                    dto.setLeaveOrQuitTime(bsDate);
                }

                if (i > 0) {
                    dto.setOpionSiteId(PostTransferApply.getLeaveCxrSiteId());
                    dto.setLeaveOrQuitTime(tranferAuditDate);
                }
            }

            if (!postTransfer && cooperate) {
                LocalDate tranferAuditDate = com.ruoyi.common.core.utils.DateUtils.getLocalDateFromDate(
                    PostTransferApply.getAuditTime()).plusDays(1);
                dto.setOpionSiteId(PostTransferApply.getLeaveCxrSiteId());
                dto.setLeaveOrQuitTime(tranferAuditDate);
            }

            if (postTransfer && !cooperate) {
                dto.setOpionSiteId(cooperateRecord.getCxrSiteId());
                dto.setLeaveOrQuitTime(cooperateRecord.getBsDate());
            }

            boolean b = nowGreaterThanFiveDayOfMonth(dayOfMouth);//当天是否大于五号
            log.info("nowGreaterThanFiveDayOfMonth(dayOfMouth):{}", b);
            //小组
            if (b) { //大于
                boolean orderLessSixDay = localDateFromDate.compareTo(dayOfMouth.plusDays(1)) < 0; //订单日期是否小于当月六号
                log.info("orderLessSixDay:{}", orderLessSixDay);
                if (orderLessSixDay) {
                    //订单日期小于 六日
                    LocalDate leaveOrQuitTime = dto.getLeaveOrQuitTime();
                    log.info("leaveOrQuitTime:{}", leaveOrQuitTime);
                    if (ObjectUtil.isNotEmpty(leaveOrQuitTime)) {
                        log.info("ObjectUtil.isNotEmpty(leaveOrQuitTime):{}", leaveOrQuitTime);
                        log.info("  localDateFromDate.compareTo(dayOfMouth) > 0 ? dayOfMouth : localDateFromDate) >= "
                            + "-1:{}", (localDateFromDate.compareTo(dayOfMouth) > 0));
                        log.info(
                            "leaveOrQuitTime.compareTo(localDateFromDate.compareTo(dayOfMouth) > 0 ? dayOfMouth : localDateFromDate) >= -1:{}",
                            leaveOrQuitTime.compareTo(
                                localDateFromDate.compareTo(dayOfMouth) > 0 ? dayOfMouth : localDateFromDate) >= -1);
                        log.info("leaveOrQuitTime.compareTo(localDateFromDate.compareTo(dayOfMouth) < 0 ? dayOfMouth "
                                + ": localDateFromDate) <= 1:{}",
                            leaveOrQuitTime.compareTo(localDateFromDate.compareTo(dayOfMouth) < 0 ? dayOfMouth :
                                localDateFromDate) <= 1);
                        if (leaveOrQuitTime.compareTo(
                            localDateFromDate.compareTo(dayOfMouth) > 0 ? dayOfMouth : localDateFromDate) >= -1
                            && leaveOrQuitTime.compareTo(
                            localDateFromDate.compareTo(dayOfMouth) < 0 ? dayOfMouth : localDateFromDate) <= 1) {
                            log.info("if进来");
                            //离职或调岗在  限定日期 与订单日趋区间中   直接使用订单日期的小组
                            orderDateSet(aLong, dto, localDateFromDate);
                        } else {
                            log.info("if-else进来");
                            //有离职调岗 但没在时间内 也用指定日期
                            dayOfMonthSet(dayOfMouth, dto, aLong);
                        }
                    } else {
                        log.info("ObjectUtil.isNotEmpty(leaveOrQuitTime)-else:{}", leaveOrQuitTime);
                        //没有离职或调岗 用 指定日期
                        dayOfMonthSet(dayOfMouth, dto, aLong);
                    }
                } else {
                    //大于指定日期 用订单日期的
                    log.info("大于指定日期-用订单日期的");
                    orderDateSet(aLong, dto, localDateFromDate);
                }
            }
            log.info("小组匹配结果dto:{}", JSONUtil.toJsonStr(dto));
            //没有小组没又调岗 再拿实时得
            if (ObjectUtil.isEmpty(dto.getOpionSiteId())) {
                if (CollectionUtil.isNotEmpty(longCxrGroupQueryDtoMap)) {
                    CxrGroupQueryDto queryDto = longCxrGroupQueryDtoMap.get(aLong);
                    dto.setOpionSiteId(queryDto.getOpionSiteId());
                }
            }
            if (ObjectUtil.isEmpty(dto.getOptionGroupId())) {
                if (CollectionUtil.isNotEmpty(longCxrGroupQueryDtoMap)) {
                    CxrGroupQueryDto queryDto = longCxrGroupQueryDtoMap.get(aLong);
                    if (queryDto != null) {
                        dto.setOptionGroupId(queryDto.getOptionGroupId());
                    }
                }
            }
            dtos.add(dto);

        }

        return CollectionUtil.isNotEmpty(dtos) ?
            dtos.stream().collect(Collectors.toMap(CxrGroupQueryDto::getCxrEmployeeId,
                Function.identity(),
                (v1, v2) -> v2)) : null;
    }


    public static void main(String[] args) {
        LocalDate localDateFromDate = com.ruoyi.common.core.utils.DateUtils.getLocalDateFromDate(DateUtil.parseDate(
            "2024-06-04"));
        LocalDate dayOfMouth = com.ruoyi.common.core.utils.DateUtils.getDayOfMouth(localDateFromDate,
            5);
        LocalDate leaveOrQuitTime = com.ruoyi.common.core.utils.DateUtils.getLocalDateFromDate(
            DateUtil.parseDate("2024-06-06"));
        boolean b = (leaveOrQuitTime.compareTo(
            localDateFromDate.compareTo(dayOfMouth) > 0 ? dayOfMouth : localDateFromDate) >= -1)
            && (leaveOrQuitTime.compareTo(
            localDateFromDate.compareTo(dayOfMouth) < 0 ? dayOfMouth : localDateFromDate) <= 1);
        System.out.println(dayOfMouth);
    }

    private void orderDateSet(Long aLong, CxrGroupQueryDto dto, LocalDate s) {
        log.info("orderDateSet-进来{},{},{}", aLong, JSONUtil.toJsonStr(dto), s);
        //订单日期
        CxrEmployeeChangeRecord changeRecord = new LambdaQueryChainWrapper<>(
            cxrEmployeeChangeRecordMapper)
            .select(CxrEmployeeChangeRecord::getGroupId, CxrEmployeeChangeRecord::getSiteId)
            .eq(CxrEmployeeChangeRecord::getCxrEmployeeId, aLong)
            .apply(
                "((date_format({0},'%Y-%m-%d') between date_format(start_time,'%Y-%m-%d') and  date_format(end_time,'%Y-%m-%d') and "
                    + "transfer_end_flag =0 ) "
                    + "or  (date_format({0},'%Y-%m-%d') >= date_format(start_time,'%Y-%m-%d') and  date_format({0},'%Y-%m-%d') < date_format(end_time,'%Y-%m-%d') and transfer_end_flag = 1) "
                    + "or  (date_format({0},'%Y-%m-%d') >= date_format(start_time,'%Y-%m-%d') and  end_time"
                    + " is null))",
                s)
            .eq(CxrEmployeeChangeRecord::getType, EmployeeChangeType.GROUP)
            .orderByDesc(CxrEmployeeChangeRecord::getStartTime)
            .last("limit 1")
            .one();
        log.info("orderDateSet-changeRecord:{}", JSONUtil.toJsonStr(changeRecord));
        if (ObjectUtil.isNotEmpty(changeRecord)) {
            dto.setOptionGroupId(changeRecord.getGroupId());
            dto.setOpionSiteId(changeRecord.getSiteId());
        }
    }

    private void dayOfMonthSet(LocalDate dayOfMouth, CxrGroupQueryDto dto, Long along) {
        orderDateSet(along, dto, dayOfMouth);
    }

    private boolean nowGreaterThanFiveDayOfMonth(LocalDate dayOfMouth) {

        return dayOfMouth.compareTo(LocalDate.now()) < 0;
    }

    /**
     * 查询最后1条订单
     *
     * @param phone
     * @return
     */
    @Override
    public CxrUserOrder queryLastOrderInfo(String phone) {

        CxrCustomer cxrCustomer = remoteCustomerService.queryByPhone(phone);
        if (ObjectUtil.isNull(cxrCustomer)) {
            return null;
        }

        CxrUserOrder cxrUserOrder = this.getBaseMapper().queryLastOrderInfo(cxrCustomer.getId());
        if (ObjectUtil.isNull(cxrUserOrder)) {
            return null;
        }

        if (NumberUtil.equals(TerminalTypeEnums.manager.getValue(), cxrUserOrder.getTerminalType())) {

            cxrUserOrder.setCreateTime(cxrUserOrder.getAuditTime());
        }
        if (NumberUtil.equals(
            TerminalTypeEnums.disribution.getValue(), cxrUserOrder.getTerminalType())) {
            cxrUserOrder.setCreateTime(cxrUserOrder.getPayTime());
        }

        //
        // CxrUserOrder cxrUserOrder = this.getBaseMapper().selectOne(new
        // LambdaUpdateWrapper<CxrUserOrder>()
        //    .eq(CxrUserOrder::getCustomerPhone, phone)
        //    .in(CxrUserOrder::getOrderType, OrderTypeEnums.NEW_ORDER.getValue(),
        //        OrderTypeEnums.CONTINUE_ORDER.getValue(),
        //        OrderTypeEnums.INCREASE_ORDER.getValue(),
        //        OrderTypeEnums.CONTRACT_ORDER.getValue())
        //    .and((wrapper) -> {
        //        wrapper.eq(CxrUserOrder::getPayStatus, PayStatusEnums.PAY_SUCCEEDED.getValue())
        //            .or()
        //            .eq(CxrUserOrder::getAuditStatus, OrderAuditStatusEnums.AUDITED.getValue());
        //    })
        //    .orderByDesc(CxrBaseEntity::getCreateTime)
        //    .last(" limit 1"));

        return cxrUserOrder;
    }

    @Override
    public CxrUserOrder queryFirstOrderInfo(String phone, Long customerId) {
        Long cxrCustomerId = null;
        if (StrUtil.isNotBlank(phone)) {
            CxrCustomer cxrCustomer = remoteCustomerService.queryByPhone(phone);
            if (ObjectUtil.isNotNull(cxrCustomer)) {
                cxrCustomerId = cxrCustomer.getId();
            }
        }

        if (null == cxrCustomerId && null != customerId) {
            cxrCustomerId = customerId;
        }

        if (null != cxrCustomerId) {
            return this.getBaseMapper().queryFirstOrderInfo(cxrCustomerId);
        }
        return null;
    }

    @Override
    public CxrUserOrder queryFirstOrderInfoV2(String phone, Long customerId) {
        Long cxrCustomerId = null;
        if (StrUtil.isNotBlank(phone)) {
            CxrCustomer cxrCustomer = remoteCustomerService.queryByPhone(phone);
            if (ObjectUtil.isNotNull(cxrCustomer)) {
                cxrCustomerId = cxrCustomer.getId();
            }
        }

        if (null == cxrCustomerId && null != customerId) {
            cxrCustomerId = customerId;
        }

        if (null != cxrCustomerId) {
            return this.getBaseMapper().queryFirstOrderInfoV2(cxrCustomerId);
        }
        return null;
    }

    @Override
    public CxrUserOrder queryLastOrder(String phone, Long orderId) {

        CxrCustomer cxrCustomer = remoteCustomerService.queryByPhone(phone);
        if (ObjectUtil.isNull(cxrCustomer)) {
            return null;
        }

        CxrUserOrder cxrUserOrder = this.getBaseMapper().queryLastOrder(cxrCustomer.getId(), orderId);
        if (ObjectUtil.isNull(cxrUserOrder)) {
            return null;
        }
        return cxrUserOrder;
    }

    @Override
    public CxrUserOrder queryFirstDayOrder(String phone, Long orderId) {

        CxrCustomer cxrCustomer = remoteCustomerService.queryByPhone(phone);
        if (ObjectUtil.isNull(cxrCustomer)) {
            return null;
        }

        LocalDate date = LocalDate.now();
        CxrUserOrder cxrUserOrder = this.getBaseMapper().queryFirstDayOrder(cxrCustomer.getId(), orderId, date);
        if (ObjectUtil.isNull(cxrUserOrder)) {
            return null;
        }
        return cxrUserOrder;
    }


    @Override
    public CxrUserOrder queryLastPromotionalOrderOrder(String phone, Long orderId) {

        CxrCustomer cxrCustomer = remoteCustomerService.queryByPhone(phone);
        if (ObjectUtil.isNull(cxrCustomer)) {
            return null;
        }

        LocalDateTime localDateTime = LocalDateTime.now();
        CxrUserOrder cxrUserOrder = this.getBaseMapper().queryLastPromotionalOrderOrder(cxrCustomer.getId(), orderId, localDateTime.toLocalDate().plusDays(-9), localDateTime);
        if (ObjectUtil.isNull(cxrUserOrder)) {
            return null;
        }
        return cxrUserOrder;
    }

    @Override
    public Long queryZeroQuantityRenewal(Long customerId, Date orderDate) {
        LocalDate localDate = orderDate.toInstant()
            .atZone(ZoneId.systemDefault())
            .toLocalDate();
        return this.getBaseMapper().queryZeroQuantityRenewal(customerId, localDate);
    }

    @Override
    public CxrUserOrder queryLastOrderInfoNotContinue(String phone) {

        CxrCustomer cxrCustomer = remoteCustomerService.queryByPhone(phone);
        if (ObjectUtil.isNull(cxrCustomer)) {
            return null;
        }

        CxrUserOrder cxrUserOrder =
            this.getBaseMapper().queryLastOrderInfoNotContinue(cxrCustomer.getId());
        if (ObjectUtil.isNull(cxrUserOrder)) {
            return null;
        }
        return cxrUserOrder;
    }

    @Override
    public Integer statisticUserOrder(Long siteId, LocalDateTime startTime, LocalDateTime endTime) {
        return this.baseMapper.statisticUserOrder(siteId, startTime, endTime);
    }

    /**
     * 零续单判断
     *
     * @param phone
     * @return
     */
    @Override
    public boolean zeroContinueFlag(String phone, OrderTypeEnums orderTypeEnums) {
        // 0数量续单  计算
        Boolean zeroQuantityRenewal = Boolean.FALSE;
        CxrCustomer customer = remoteCustomerService.queryByPhone(phone);
        if (ObjectUtil.isNotNull(customer) && orderTypeEnums == OrderTypeEnums.NEW_ORDER) {
            Integer customerStock = customer.getCustomerStock();
            if (customerStock <= 0) {
                // 账户中没有鲜奶数量
                if (!LocalDate.now()
                    .isBefore(
                        com.ruoyi.common.core.utils.DateUtils.getLocalDateFromDate(
                                customer.getCustomerStockZeroTime())
                            .plusDays(16))) {
                    CxrCustomerStockDetail cxrCustomerStockDetail = remoteCustomerStockDetailService.lastRecordByCustomerId(
                        customer.getId());
                    if (cxrCustomerStockDetail != null) {
                        zeroQuantityRenewal = Boolean.TRUE;
                    }
                }
            }
        }

        return zeroQuantityRenewal;
    }

    @Override
    public boolean updateCustomerIdByOrderId(Long cxrUserOrderId, Long customerAccountId) {
        CxrUserOrder cxrUserOrder = new CxrUserOrder();
        cxrUserOrder.setId(cxrUserOrderId);
        cxrUserOrder.setCustomerId(customerAccountId);
        return updateById(cxrUserOrder);
    }

    @Override
    public List<CxrUserOrder> getPhone(String phone) {
        List<CxrUserOrder> cxrUserOrder =
            this.getBaseMapper()
                .selectList(
                    new LambdaUpdateWrapper<CxrUserOrder>().eq(CxrUserOrder::getCustomerPhone, phone)
                        .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                        .notIn(CxrUserOrder::getOrderType, OrderTypeEnums.GIVE_ORDER.getValue())
                );
        return cxrUserOrder;
    }

    @Override
    public void paySucessMessage(
        PaymentPlatform paymentPlatform, String sn, String payWay, String finishTime,
        PayNotifyUrlResponse payNotifyUrlResponse, CxrUserOrder cxrUserOrder) {
        this.paySucessMessage(paymentPlatform, sn, payWay, finishTime, cxrUserOrder.getId(), null,
            payNotifyUrlResponse);
    }

    @Override
    @Deprecated
    public void paySucessMessage(
        PaymentPlatform paymentPlatform, String sn, String payWay, String finishTime, CxrUserOrder cxrUserOrder) {
        this.paySucessMessage(paymentPlatform, sn, payWay, finishTime, cxrUserOrder.getId(), null, null);
    }

    @Override
    public void paySucessMessage(
        PaymentPlatform paymentPlatform, String sn, String payWay, String finishTime, Long orderId, String thirdOrderNo,
        Object responseMsg) {
        PaySucessMessageDTO paySucessMessageDTO = new PaySucessMessageDTO();
        paySucessMessageDTO.setSn(sn);
        paySucessMessageDTO.setPayWay(payWay);
        paySucessMessageDTO.setFinishTime(finishTime);
        paySucessMessageDTO.setOrderId(orderId);
        paySucessMessageDTO.setPaymentPlatform(paymentPlatform.getType());
        paySucessMessageDTO.setResponseMsg(responseMsg);
        paySucessMessageDTO.setThirdOrderNo(thirdOrderNo);
        String messageStr = JSONUtil.toJsonStr(paySucessMessageDTO);
        String orderIdStr = Convert.toStr(orderId);
        // 发送半事务消息
        TransactionSendResult sendResult =
            rocketMQTemplate.sendMessageInTransaction(
                OrderConstant.ORDER_TOPIC_TRANS + ":" + OrderConstant.ORDERR_PAY_HALF_MESSAGE,
                MessageBuilder.withPayload(messageStr)
                    .setHeader(RocketMQHeaders.TRANSACTION_ID, orderIdStr)
                    .setHeader(RocketMQHeaders.KEYS, IdUtil.simpleUUID()) // 相比于使用"KEYS"，使用封装常量更不易出错
                    .build(),
                messageStr);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void paySucess(String sn, String payWay, String finishTime, CxrUserOrder cxrUserOrder, String thirdOrderNo) {
        RedisLockUtils.tryLockException(
            StrUtil.format("cxr:order:update:{}", cxrUserOrder.getId()),
            () -> {
                // String sn = payNotifyUrlResponse.getSn();
                log.info(">>>>>>>>>>>>>>>>>>>>>>paySucess>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
                log.info(">>>>>>>>>>>>>>>>>>>>>>sn={}", sn);
                log.info(">>>>>>>>>>>>>>>>>>>>>>payWay={}", payWay);
                log.info(">>>>>>>>>>>>>>>>>>>>>>finishTime={}", finishTime);
                log.info(">>>>>>>>>>>>>>>>>>>>>>cxrUserOrder={}", cxrUserOrder);
                if (Objects.isNull(cxrUserOrder)) {
                    throw new ServiceException("订单信息为空");
                }
                CxrUserOrder temp = cxrUserOrderService.getById(cxrUserOrder.getId());
                Integer tempPayStatus = Convert.toInt(temp.getPayStatus(), -1);
                if (!NumberUtil.equals(PayStatusEnums.PAY_SUCCEEDED.getValue(), tempPayStatus)) {

                    Date payDate =
                        StringUtils.isBlank(finishTime) ? new Date() : new Date(Long.valueOf(finishTime));

                    // 把订单状态改成已支付
                    LambdaUpdateWrapper<CxrUserOrder> lambdaUpdateWrapper =
                        new LambdaUpdateWrapper<CxrUserOrder>()
                            .set(CxrUserOrder::getPayStatus, PayStatusEnums.PAY_SUCCEEDED.getValue())
                            .set(CxrUserOrder::getPaymentSouce, payWay)
                            .set(CxrUserOrder::getPayTime, payDate)
                            .set(CxrUserOrder::getOrderDate, payDate) // 订单日期修改成支付时间
                            .set(CxrUserOrder::getSqbSn, sn)
                            .set(CxrUserOrder::getPayPlaformType, PaymentPlatform.SHOU_QIAN_BA.getType())
                            .set(CxrUserOrder::getThirdOrderNo, thirdOrderNo)
                            .set(CxrUserOrder::getThirdOrderNoTime, payDate)
                            .eq(CxrUserOrder::getId, cxrUserOrder.getId())
                            .ne(CxrUserOrder::getPayStatus, PayStatusEnums.PAY_SUCCEEDED.getValue());

                    // 支付成功扣减库存
                    if (temp.getActivityId() > 0) {
                        activityOrderService.reduceUsableOrdersAddUsedOrders(cxrUserOrder.getId());
                    }

                    if (sn.length() >= 7) {
                        String substring = sn.substring(sn.length() - 7);
                        lambdaUpdateWrapper.set(CxrUserOrder::getMerchantOrderNo, substring);
                    } else {
                        lambdaUpdateWrapper.set(CxrUserOrder::getMerchantOrderNo, sn);
                    }

                    boolean exists = remoteCustomerService.existsCustomerAccountAddress(
                        cxrUserOrder.getCustomerPhone());
                    if (exists) {
                        lambdaUpdateWrapper.set(
                            CxrUserOrder::getPerfectStatus, PerfectStatusEnums.PERFECT.getValue());
                    } else {
                        lambdaUpdateWrapper.set(
                            CxrUserOrder::getPerfectStatus, PerfectStatusEnums.NO_PERFECT.getValue());
                    }

                    boolean flag = cxrUserOrderService.update(lambdaUpdateWrapper);
                    if (!flag) {
                        throw new ServiceException("更改订单状态失败");
                    }
                    this.closeOtherOrders(cxrUserOrder.getId());
                }
            });
    }


    /**
     * @param fuyouResponse
     * @param reconfirm     是否再次请求富友确认状态
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void fuyouPaySuccess(FuyouResponse fuyouResponse, Boolean reconfirm) {
        log.info("富友-订单状态修改-开始：{}", fuyouResponse);
        if (fuyouResponse.getOrderId() == null) {
            throw new IllegalArgumentException("fuyouPaySuccess order id 为空");
        }
        if (fuyouResponse.getSrcOrderType() == 0) {
            RedisLockUtils.tryLockException(CacheConstants.ORDER_UPDATE + fuyouResponse.getOrderId(),
                () -> {
                    try {
                        Long orderId = fuyouResponse.getOrderId();
                        CxrUserOrder cxrUserOrder = cxrUserOrderService.getById(orderId);
                        //订单已经不存在或被删除，直接返回
                        if (ObjectUtil.isNull(cxrUserOrder) || cxrUserOrder.getDeleteStatus()
                            .equals(DeleteStatus.DELETED.getValue())) {
                            return;
                        }
                        Integer payStatus = cxrUserOrder.getPayStatus();
                        //非待支付状态不处理
                        if (!payStatus.equals(PayStatusEnums.WAIT_PAY.getValue())) {
                            return;
                        }
                        String mchntOrderNo = fuyouResponse.getMchntOrderNo();
                        CxrPaymentDetails cxrPaymentDetails = paymentDetailsMapper.selectOne(
                            new LambdaQueryWrapper<CxrPaymentDetails>().eq(CxrPaymentDetails::getOutOrderNo,
                                mchntOrderNo));
                        if (cxrPaymentDetails == null) {
                            return;
                        }
                        if (cxrPaymentDetails.getPayConfigId() == null) {
                            throw new IllegalArgumentException("支付配置为空");
                        }
//                        CxrPayConfig payConfig = payService.getPayConfig(cxrUserOrder.getSiteId());
                        CxrPayConfig payConfig = cxrPayConfigService.getById(cxrPaymentDetails.getPayConfigId());
                        //查询订单状态
                        FuyouPayParam fuYouPayParam = new FuyouPayParam();
                        fuYouPayParam.setMchntCd(fuyouResponse.getMchntCd());
                        fuYouPayParam.setRandomStr(fuyouResponse.getRandomStr());
                        fuYouPayParam.setOrderType(fuyouResponse.getOrderType());
                        fuYouPayParam.setMchntOrderNo(fuyouResponse.getMchntOrderNo());
                        fuYouPayParam.setTermId(payConfig.getTerminalSn());
                        fuYouPayParam.setMchntKey(payConfig.getPublicKey());
                        //再次查询富友支付状态
                        if (reconfirm) {
                            BeanUtil.copyProperties(fyPayUtils.queryOrder(fuYouPayParam), fuyouResponse);
                        }
                        String reservedFyTraceNo = fuyouResponse.getReservedFyTraceNo();
                        if (!fuyouResponse.getTransStat().equals(FuyouPayResultStatus.SUCCESS.getCode())) {
                            return;
                        }
                        log.info("富友支付成功");
                        Date payDate = DateUtil.parse(ObjectUtil.defaultIfBlank(fuyouResponse.getTxnFinTs(),
                                    fuyouResponse.getReservedTxnFinTs()),
                                "yyyyMMddHHmmss")
                            .toJdkDate();
                        // 把订单状态改成已支付
                        LambdaUpdateWrapper<CxrUserOrder> lambdaUpdateWrapper =
                            new LambdaUpdateWrapper<CxrUserOrder>()
                                .set(CxrUserOrder::getPayStatus, PayStatusEnums.PAY_SUCCEEDED.getValue())
                                .set(CxrUserOrder::getPaymentSouce,
                                    FuyouResponseDeviceType.getCode(fuyouResponse.getOrderType()).getCode())
                                .set(CxrUserOrder::getPayTime, payDate)
                                .set(CxrUserOrder::getOrderDate, payDate) // 订单日期修改成支付时间
                                .set(CxrUserOrder::getSqbSn, reservedFyTraceNo)
                                .set(CxrUserOrder::getMerchantOrderNo, reservedFyTraceNo)
                                .set(CxrUserOrder::getPayPlaformType, PaymentPlatform.FU_YOU.getType())
                                .set(CxrUserOrder::getThirdOrderNo, fuyouResponse.getMchntOrderNo())
                                .eq(CxrUserOrder::getId, cxrUserOrder.getId())
                                .ne(CxrUserOrder::getPayStatus, PayStatusEnums.PAY_SUCCEEDED.getValue());
                        //修改交易记录
                        paymentDetailsMapper.update(null, new LambdaUpdateWrapper<CxrPaymentDetails>()
                            .set(CxrPaymentDetails::getTradeNo, reservedFyTraceNo)
                            .set(CxrPaymentDetails::getPayStatus, PayStatusEnums.PAY_SUCCEEDED.getValue())
                            .set(CxrPaymentDetails::getPayTime, payDate)
                            .set(CxrPaymentDetails::getPayPlatformSn, fuyouResponse.getTransactionId())
                            .set(CxrPaymentDetails::getPayChannelNotes, fuyouResponse.getReservedFundBillList())
                            .set(CxrPaymentDetails::getPayAmount, cxrUserOrder.getAmount())
                            .eq(CxrPaymentDetails::getOutOrderNo, fuyouResponse.getMchntOrderNo())
                            .eq(CxrPaymentDetails::getPayStatus, PayStatusEnums.WAIT_PAY.getValue())
                            .eq(CxrPaymentDetails::getPayPlatform, PaymentPlatform.FU_YOU.getType())
                            .eq(CxrPaymentDetails::getOrderType, PaymentOrderTypeEnums.USER_ORDER.getType())
                        );

                        // 支付成功扣减库存
                        if (cxrUserOrder.getActivityId() > 0) {
                            activityOrderService.reduceUsableOrdersAddUsedOrders(cxrUserOrder.getId());
                        }

                        boolean exists = remoteCustomerService.existsCustomerAccountAddress(
                            cxrUserOrder.getCustomerPhone());
                        if (exists) {
                            lambdaUpdateWrapper.set(
                                CxrUserOrder::getPerfectStatus, PerfectStatusEnums.PERFECT.getValue());
                        } else {
                            lambdaUpdateWrapper.set(
                                CxrUserOrder::getPerfectStatus, PerfectStatusEnums.NO_PERFECT.getValue());
                        }

                        boolean flag = cxrUserOrderService.update(lambdaUpdateWrapper);
                        if (!flag) {
                            throw new ServiceException("更改订单状态失败");
                        }
                        this.closeOtherOrders(orderId);

                    } catch (Exception e) {
                        log.error("富友支付处理异常：{}", fuyouResponse, e);
                        throw e;
                    }
                }
            );
        } else {
            RedisLockUtils.tryLockException(CacheConstants.ORDER_UPDATE + fuyouResponse.getOrderId(),
                () -> {
                    try {
                        Long orderId = fuyouResponse.getOrderId();
                        CxrEmployeeOrder cxrEmployeeOrder = cxrEmployeeOrderMapper.selectById(orderId);
                        //订单已经不存在或被删除，直接返回
                        if (ObjectUtil.isNull(cxrEmployeeOrder) || cxrEmployeeOrder.getDeleteStatus()
                            .equals(DeleteStatus.DELETED.getValue())) {
                            return;
                        }
                        Integer payStatus = cxrEmployeeOrder.getPayStatus();
                        //非待支付状态不处理
                        if (!payStatus.toString().equals(EmployeeOrderEnums.WAIT_PAY.getValue())) {
                            return;
                        }
                        CxrPayConfig payConfig = payService.getPayConfig(cxrEmployeeOrder.getCxrSiteId());
                        //查询订单状态
                        FuyouPayParam fuYouPayParam = new FuyouPayParam();
                        fuYouPayParam.setMchntCd(fuyouResponse.getMchntCd());
                        fuYouPayParam.setRandomStr(fuyouResponse.getRandomStr());
                        fuYouPayParam.setOrderType(fuyouResponse.getOrderType());
                        fuYouPayParam.setMchntOrderNo(fuyouResponse.getMchntOrderNo());
                        fuYouPayParam.setTermId(payConfig.getTerminalSn());
                        fuYouPayParam.setMchntKey(payConfig.getPublicKey());
                        //再次查询富友支付状态
                        if (reconfirm) {
                            BeanUtil.copyProperties(fyPayUtils.queryOrder(fuYouPayParam), fuyouResponse);
                        }
                        String reservedFyTraceNo = fuyouResponse.getReservedFyTraceNo();
                        if (!fuyouResponse.getTransStat().equals(FuyouPayResultStatus.SUCCESS.getCode())) {
                            return;
                        }
                        log.info("富友支付成功");
                        Date payDate = DateUtil.parse(ObjectUtil.defaultIfBlank(fuyouResponse.getTxnFinTs(),
                                    fuyouResponse.getReservedTxnFinTs()),
                                "yyyyMMddHHmmss")
                            .toJdkDate();
                        // 把订单状态改成已支付
                        LambdaUpdateWrapper<CxrEmployeeOrder> lambdaUpdateOrder =
                            new LambdaUpdateWrapper<>(CxrEmployeeOrder.class)
                                .set(CxrEmployeeOrder::getMerchantNo, fuyouResponse.getMchntOrderNo())
                                .set(CxrEmployeeOrder::getPayDate, payDate)
                                .set(
                                    CxrEmployeeOrder::getPayStatus, EmployeeOrderEnums.PayStatus.SUCCESS_PAY.getValue())
                                .set(CxrEmployeeOrder::getStatus, EmployeeOrderEnums.WAIT_DELIVER.getValue())
                                .set(CxrEmployeeOrder::getRevision, cxrEmployeeOrder.getRevision() + 1)
                                .eq(CxrEmployeeOrder::getId, cxrEmployeeOrder.getId())
                                .eq(CxrEmployeeOrder::getPayStatus, EmployeeOrderEnums.PayStatus.NO_PAY.getValue())
                                .eq(CxrEmployeeOrder::getRevision, cxrEmployeeOrder.getRevision());
                        boolean count = cxrEmployeeOrderMapper.update(null, lambdaUpdateOrder) > 0;
                        log.info("支付回调更新 订单表状态：" + count);
                        if (!count) {
                            log.info(">>>>>>>>>>>>>>>>>>>>>支付回调 订单状态更新失败>>>>>>>>>>>>>>>>");
                            throw new ServiceException("富友订单状态更新失败");
                        }
                        //修改交易记录
                        paymentDetailsMapper.update(null, new LambdaUpdateWrapper<CxrPaymentDetails>()
                            .set(CxrPaymentDetails::getTradeNo, reservedFyTraceNo)
                            .set(CxrPaymentDetails::getPayStatus, PayStatusEnums.PAY_SUCCEEDED.getValue())
                            .set(CxrPaymentDetails::getPayTime, payDate)
                            .set(CxrPaymentDetails::getPayAmount, cxrEmployeeOrder.getPayAmount())
                            .set(CxrPaymentDetails::getPayPlatformSn, fuyouResponse.getTransactionId())
                            .set(CxrPaymentDetails::getPayChannelNotes, fuyouResponse.getReservedFundBillList())
                            .eq(CxrPaymentDetails::getOutOrderNo, fuyouResponse.getMchntOrderNo())
                            .eq(CxrPaymentDetails::getPayPlatform, PaymentPlatform.FU_YOU.getType())
                            .eq(CxrPaymentDetails::getOrderType, PaymentOrderTypeEnums.EMP_ORDER.getType())
                        );

                        // mq 异步拆单
                        PayNotifyUrlResponse payNotifyUrlResponse = new PayNotifyUrlResponse();
                        payNotifyUrlResponse.setClient_sn(cxrEmployeeOrder.getOrderNo());
                        mqUtil.sendSyncMessage(
                            OrderConstant.ORDER_TOPIC, OrderConstant.SQB_PRECREATE_NOTIFY_SUCCESS_TAG,
                            JSONUtil.toJsonStr(payNotifyUrlResponse));

                    } catch (Exception e) {
                        log.error("富友支付处理异常：{}", fuyouResponse, e);
                        throw e;
                    }
                }
            );
        }
        log.info("富友-订单状态修改-结束");

    }


    /**
     * 支付成功后关闭其他订单，针对配送端的
     */
    private void closeOtherOrders(Long orderId) {
        //先判断是不是配送端的订单
        CxrUserOrder cxrUserOrder = cxrUserOrderService.getById(orderId);
        if (cxrUserOrder == null) {
            return;
        }
        if (!cxrUserOrder.getCreateByType().equals(DeviceType.CXR_STAFF.getDevice())) {
            return;
        }
        // 关闭订单
        cxrUserOrderService.update(new LambdaUpdateWrapper<CxrUserOrder>()
            .set(CxrUserOrder::getPayStatus, PayStatusEnums.PAY_CLOSE.getValue())
            .eq(CxrUserOrder::getPayStatus, PayStatusEnums.WAIT_PAY.getValue())
            .eq(CxrUserOrder::getCreateByType, DeviceType.CXR_STAFF.getDevice())
            .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            .ne(CxrUserOrder::getOrderType, OrderTypeEnums.CONTRACT_ORDER.getValue())
            .eq(CxrUserOrder::getCustomerPhone, cxrUserOrder.getCustomerPhone())
        );
    }


    @Override
    public void export(CxrUserOrderQueryBo cxrUserOrderBo, HttpServletResponse response) {
        log.info(DateUtils.format(cxrUserOrderBo.getEndDateTime(), DateUtils.DATE_FORMAT_19));
        if (cxrUserOrderBo.getStartDateTime() == null || cxrUserOrderBo.getEndDateTime() == null) {
            throw new ServiceException("请选择完整的时间范围");
        }
        LoginInfo loginUser = LoginUtil.getLoginUser();
        Long deptId = loginUser.getDeptId();
        if (deptId == null) {
            deptId = -1L;
        }

        // 有传公司查询条件，判断是否有权限查看
        Long companyId = cxrUserOrderBo.getDeptId();
        if (companyId != null && !"100".equals(deptId.toString()) && !deptId.equals(companyId)) {
            throw new ServiceException("您没有查看该公司数据的权限!");
        }

        // 总部可以查询所有部门的数据，其他部门只能看自己部门的数据
        cxrUserOrderBo.setCompanyId("100".equals(deptId.toString()) ? companyId : deptId);

        List<Short> payOrderTypeList = new ArrayList<>();
        payOrderTypeList.add(OrderTypeEnums.NEW_ORDER.getValue());
        payOrderTypeList.add(OrderTypeEnums.CONTINUE_ORDER.getValue());
        payOrderTypeList.add(OrderTypeEnums.INCREASE_ORDER.getValue());
        payOrderTypeList.add(OrderTypeEnums.CONTRACT_ORDER.getValue());

        if (cxrUserOrderBo.getExportReturnFlag()) {
            Long count = this.getBaseMapper().userOrderAndReturnCount(cxrUserOrderBo);
            ExcelReportDTO excelReportDTO = ExcelMqUtil.excelSendRecord(cxrUserOrderBo.getHtmlName(), count,
                loginUser.getUserId(), loginUser.getUserType(), loginUser.getUserName());
            threadPoolTaskExecutor.execute(() -> {
                ExcelExportObj excelExportObj = new ExcelExportObj(excelReportDTO.getFileDir(),
                    IdUtil.fastSimpleUUID() + "客户订单.xlsx", CxrUserReturnOrderVO.class);
                while (true) {
                    List<CxrUserReturnOrderVO> records = this.getBaseMapper().userOrderAndReturn(cxrUserOrderBo);
                    if (CollectionUtils.isEmpty(records)) {
                        break;
                    }
                    for (CxrUserReturnOrderVO record : records) {
                        if (record.getAmount() != null) {
                            record.setAmount(record.getAmount().negate());
                        }
                    }
                    fill(records, payOrderTypeList, excelExportObj);
                    ExcelMqUtil.excelSendIncrementData(excelReportDTO.getUniqueMark(), records.size(), null);
                    cxrUserOrderBo.setId(records.get(records.size() - 1).getId());
                }
                excelExportObj.pathClose();
            });
        } else {
            Long count = this.getBaseMapper().count(cxrUserOrderBo);
            ExcelReportDTO excelReportDTO = ExcelMqUtil.excelSendRecord(cxrUserOrderBo.getHtmlName(), count,
                loginUser.getUserId(), loginUser.getUserType(), loginUser.getUserName());
            threadPoolTaskExecutor.execute(() -> {
                try {
                    ExcelExportObj excelExportObj = new ExcelExportObj(excelReportDTO.getFileDir(),
                        IdUtil.fastSimpleUUID() + "客户订单.xlsx", CxrUserOrderListVo.class);
                    while (true) {
                        List<CxrUserOrderListVo> records = this.getBaseMapper().list(cxrUserOrderBo);
                        if (CollectionUtils.isEmpty(records)) {
                            break;
                        }
                        //提取配送站点ID
                        List<Long> deliverySiteIds = records.stream()
                            .map(CxrUserOrderListVo::getDeliverySiteId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                        // 查询配送站点信息
                        if (CollectionUtils.isNotEmpty(deliverySiteIds)) {
                            List<CxrSite> cxrSites = remoteSiteService.queryIds(deliverySiteIds);
                            Map<Long, CxrSite> deliverySiteMap = cxrSites.stream()
                                .collect(Collectors.toMap(CxrSite::getId, Function.identity()));
                            records.forEach(record -> {
                                CxrSite cxrSite = deliverySiteMap.get(record.getDeliverySiteId());
                                if (cxrSite != null) {
                                    record.setDeliverySiteNo(cxrSite.getSiteMark());
                                }
                            });
                        }
                        fill(records, payOrderTypeList, excelExportObj);
                        ExcelMqUtil.excelSendIncrementData(excelReportDTO.getUniqueMark(), records.size(), null);
                        cxrUserOrderBo.setId(records.get(records.size() - 1).getId());
                    }
                    excelExportObj.pathClose();
                } catch (Exception e) {
                    log.error("导出失败", e);
                }

            });
        }
    }

    public void fill(List<? extends CxrUserOrderListVo> records, List<Short> payOrderTypeList,
                     ExcelExportObj excelExportObj) {
        for (CxrUserOrderListVo cxrUserOrder : records) {
            if (cxrUserOrder.getOrderType() == OrderTypeEnums.RETURN_ORDER.getValue()) {
                cxrUserOrder.setFreshMilkHistoryTotal(cxrUserOrder.getReturnOrderQuantity());
                cxrUserOrder.setTotalQuantity(0 - cxrUserOrder.getFreshMilkReturnQuantity());
                cxrUserOrder.setOrderQuantity(0 - cxrUserOrder.getFreshMilkReturnQuantity());

                cxrUserOrder.setLongMilkGiveQuantity(0);
                cxrUserOrder.setFreshMilkGiveQuantity(0);
                cxrUserOrder.setLongMilkSentQuantity(0);
                cxrUserOrder.setFreshMilkSentQuantity(0);
                // amount 字段这是沿用的
                if (cxrUserOrder instanceof CxrUserReturnOrderVO) {
                    CxrUserReturnOrderVO cxrUserReturnOrderVO = (CxrUserReturnOrderVO) cxrUserOrder;
                    String rrCode = cxrUserReturnOrderVO.getRrCode();
                    if (StringUtils.isNotBlank(rrCode)) {
                        cxrUserReturnOrderVO.setRrName(dictService.getDictLabel("order_refund_reason", rrCode, ","));
                    }
                }
                if (cxrUserOrder.getAmount() != null) {
                    cxrUserOrder.setAmount(cxrUserOrder.getAmount().negate());
                }
            } else {
                cxrUserOrder.setTotalQuantity(cxrUserOrder.getOrderQuantity() + cxrUserOrder.getFreshMilkGiveQuantity()
                    - cxrUserOrder.getFreshMilkSentQuantity());
            }
            Short orderType = cxrUserOrder.getOrderType();
            Short terminalType = cxrUserOrder.getTerminalType();
            Integer auditStatus = Convert.toInt(cxrUserOrder.getAuditStatus(), -1);
            Integer payStatus = cxrUserOrder.getPayStatus();
            if (payOrderTypeList.contains(orderType)) {

                if (NumberUtil.equals(TerminalTypeEnums.manager.getValue(), terminalType)) {
                    cxrUserOrder.setShowAuditMark(OrderAuditStatusEnums.getdesc(auditStatus));
                }

                if (NumberUtil.equals(TerminalTypeEnums.disribution.getValue(), terminalType)) {
                    cxrUserOrder.setShowAuditMark(PayStatusEnums.getdesc(payStatus));
                }

            } else {
                cxrUserOrder.setShowAuditMark(OrderAuditStatusEnums.getdesc(auditStatus));
            }

            String deliverySites = cxrUserOrder.getDeliverySites();
            if (StrUtil.isNotEmpty(deliverySites)) {
                List<DeliverySiteDTO> deliverySitesDTO = JSONUtil.toList(deliverySites, DeliverySiteDTO.class);
                if (CollUtil.isNotEmpty(deliverySitesDTO)) {
                    cxrUserOrder.setDeliverySite(deliverySitesDTO.stream().map(DeliverySiteDTO::getSiteName)
                        .collect(Collectors.joining(",")));
                }
            }

            cxrUserOrder.setZeroQuantityRenewalFlag(cxrUserOrder.getZeroQuantityRenewal() ? "是" : "否");
            cxrUserOrder.setPaymentFailureReasons(cxrUserOrder.getPaymentFailureReasons());
            cxrUserOrder.setPaymentStatus(cxrUserOrder.getPaymentStatus());
            cxrUserOrder.setProductName(cxrUserOrder.getProductName());
            cxrUserOrder.setProductTypeName(cxrUserOrder.getProductTypeName());

            List<BusinessAgent> businessAgent = cxrUserOrder.getBusinessAgent();

            StringBuilder builderName = new StringBuilder();
            StringBuilder builderJobNumber = new StringBuilder();
            StringBuilder builderLevel = new StringBuilder();

            if (CollectionUtils.isNotEmpty(businessAgent)) {
                for (int i = 0; i < businessAgent.size(); i++) {
                    BusinessAgent agent = businessAgent.get(i);
                    if (i > 0) {
                        builderName.append("\n");
                        builderLevel.append("\n");
                        builderJobNumber.append("\n");
                    }
                    builderName.append(agent.getProxyName());
                    if (agent.getOccupationStatus() != null
                        && cxrUserOrder.getOrderType() == OrderTypeEnums.RETURN_ORDER.getValue()) {
                        builderName.append("(").append(agent.getOccupationStatus() ? "合作中" : "终止合作").append(")");
                    }
                    builderLevel.append("L").append(agent.getLevel());
                    builderJobNumber.append(agent.getProxyNo());
                }
            }

            if (StringUtils.isNotBlank(cxrUserOrder.getGiveGiftList()) && !cxrUserOrder.getGiveGiftList()
                .equals("null")) {
                log.info("GiveGiftList=>{}", cxrUserOrder.getGiveGiftList());
                log.info("OrderNo=>{}", cxrUserOrder.getOrderNo());
                List<GiveGiftDTO> dtos = JSONUtil.toList(cxrUserOrder.getGiveGiftList(), GiveGiftDTO.class);
                if (dtos.size() > 0) {
                    dtos.forEach(s -> {
                        cxrUserOrder.setGiveGiftNames(StrUtil.isNotBlank(cxrUserOrder.getGiveGiftNames()) ?
                            StrUtil.format("{},{}x{}", cxrUserOrder.getGiveGiftNames(), s.getGoodsName(),
                                s.getGiftSum()) :
                            StrUtil.format("{}x{}", s.getGoodsName(), s.getGiftSum())
                        );
                    });
                }
            }

            cxrUserOrder.setBusinessAgentName(builderName.toString());
            cxrUserOrder.setBusinessAgentJobNumber(builderJobNumber.toString());
            cxrUserOrder.setBusinessAgentLevel(builderLevel.toString());

            Long siteId = cxrUserOrder.getSiteId();
            if (siteId != null) {
                CxrSite cxrSite = remoteSiteService.queryId(siteId);
                if (cxrSite != null) {
                    cxrUserOrder.setSiteMark(cxrSite.getSiteMark());
                }
            }

            if (cxrUserOrder.getOrderType() != OrderTypeEnums.CONTRACT_ORDER.getValue()) {
                cxrUserOrder.setContractTypeTag(null);
            }

            String readConverterExp = "0=新订单,1=续订单,2=退订单,3=增订单,4=转单,5=换单,6=赠送单,7=合订单,8=转单-常温奶";
            String s = ExcelUtil.convertByExp(orderType.toString(), readConverterExp, ",");

            Short contractTypeTag = cxrUserOrder.getContractTypeTag();
            if (contractTypeTag != null) {
                if (contractTypeTag == OrderTypeEnums.NEW_ORDER.getValue()) {
                    s = s + ",新订单";
                } else if (contractTypeTag == OrderTypeEnums.CONTINUE_ORDER.getValue()) {
                    s = s + ",续订单";
                }
            }

            if (cxrUserOrder.getApprenticeOrderFlag()) {
                s = s + ",师徒单";
            }
            if (cxrUserOrder.getPromotionalOrderFlag()) {
                s = s + ",促销单";
            }

            if (ObjectUtil.isNotNull(cxrUserOrder.getAbnormalTag()) && ObjectUtil.equals(cxrUserOrder.getAbnormalTag(), 1)) {
                s = s + ",异常订单";
            }


            cxrUserOrder.setOrderTypeValue(s);
        }
        excelExportObj.writeManySheet(records);
        excelExportObj.setCount(records.size());
    }

    /**
     * 订单的处理
     *
     * @param id
     */
    @Override
    public void orderHandler(Long id) {
        CxrUserOrder cxrUserOrder = cxrUserOrderService.getById(id);

        // 新订单 、续订单 、增订单创建用户
        if (NumberUtil.equals(
            OrderTypeEnums.NEW_ORDER.getValue(), cxrUserOrder.getOrderType().shortValue())
            || NumberUtil.equals(
            OrderTypeEnums.CONTINUE_ORDER.getValue(), cxrUserOrder.getOrderType().shortValue())
            || NumberUtil.equals(
            OrderTypeEnums.INCREASE_ORDER.getValue(), cxrUserOrder.getOrderType().shortValue())) {
            // 客户信息处理
            mqUtil.sendSyncMessage(
                OrderConstant.ORDER_TOPIC, OrderConstant.ORDER_CUSTOMER_TAG, StrUtil.toString(id));
            // 销售代理业绩处理
            mqUtil.sendSyncMessage(
                OrderConstant.ORDER_TOPIC, OrderConstant.ORDER_EMPLOYEE_TAG, StrUtil.toString(id));
            mqUtil.sendSyncMessage(
                OrderConstant.ORDER_TOPIC,
                OrderConstant.SEND_ORDER_MESSAGE_TAG,
                Convert.toStr(cxrUserOrder.getId()));

            mqUtil.sendSyncMessageHuiBo(HuiboConstant.HUI_BO_ORDER_TOPIC, HuiboConstant.HUI_BO_ORDER_RT_SYN_TAG,
                Convert.toStr(id));
        }

        // 合订单拆单
        if (NumberUtil.equals(
            OrderTypeEnums.CONTRACT_ORDER.getValue(), cxrUserOrder.getOrderType().shortValue())) {
            mqUtil.sendSyncMessage(
                OrderConstant.ORDER_TOPIC,
                OrderConstant.CONTRACT_ORDER_HANDLER_TAG,
                StrUtil.toString(id));
        }
    }

    @Override
    public LastUserOrderVo queryLastTenDayOrder(Long customerId, Long orderId) {
        LocalDateTime localDateTime = LocalDateTime.now();
        if (orderId != null) {
            CxrUserOrder userOrder = getBaseMapper().selectById(orderId);
            if (userOrder != null) {

                Date payTime = userOrder.getPayTime();
                Date auditTime = userOrder.getAuditTime();
                if (payTime != null) {
                    localDateTime = com.ruoyi.common.core.utils.DateUtils.getLocalDateTimeFormDate(payTime);
                } else if (auditTime != null) {
                    localDateTime = com.ruoyi.common.core.utils.DateUtils.getLocalDateTimeFormDate(auditTime);
                } else {
                    localDateTime =
                        com.ruoyi.common.core.utils.DateUtils.getLocalDateTimeFormDate(
                            userOrder.getCreateTime());
                }
            }
        }
        return getBaseMapper()
            .queryNotContractLastTenDayOrder(
                customerId, localDateTime.toLocalDate().plusDays(-9), localDateTime, orderId);
    }

    @Override
    public void updateCustomerStock(
        CxrUserOrder cxrUserOrder, CompleteInfoDTO completeInfoDTO, Short orderType) {

        // 如果鲜奶赠送 ，鲜奶已送，常温奶赠送数量没有变 则不对库存做加减
        if (cxrUserOrder.getFreshMilkGiveQuantity().intValue()
            == completeInfoDTO.getFreshMilkGiveCount().intValue()
            && cxrUserOrder.getFreshMilkSentQuantity().intValue()
            == completeInfoDTO.getAlreadyGiveCount().intValue()
            && cxrUserOrder.getLongMilkGiveQuantity().intValue()
            == completeInfoDTO.getMilkGiveCount().intValue()) {
            return;
        }

        // 修改赠送数量
        List<CxrCustomerStockDetail> cxrCustomerStockDetails =
            remoteCustomerStockDetailService.queryStockBySource(
                cxrUserOrder.getId(), SourceEnums.NEW_ORDER_GIVE, true);
        if (CollectionUtils.isNotEmpty(cxrCustomerStockDetails)) {
            List<CxrCustomerStockDetail> cxrCustomerStockDetailList =
                cxrCustomerStockDetails.stream()
                    .map(
                        a -> {
                            a.setFreshMilkQuantity(
                                0 - a.getFreshMilkGiveQuantity() + a.getFreshMilkSentQuantity());
                            a.setFreshMilkGiveQuantity(0 - a.getFreshMilkGiveQuantity());
                            a.setLongMilkQuantity(0 - a.getLongMilkQuantity());
                            a.setLongMilkGiveQuantity(0 - a.getLongMilkGiveQuantity());
                            a.setFreshMilkSentQuantity(0 - a.getFreshMilkSentQuantity());
                            a.setSourceType(SourceEnums.NEW_ORDER_GIVE_SUB.getValue());
                            return a;
                        })
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(cxrCustomerStockDetailList)) {
                boolean add =
                    remoteCustomerStockDetailService.add(
                        cxrUserOrder.getCustomerId(), cxrCustomerStockDetailList);
                if (!add) {
                    throw new ServiceException("修改失败");
                }
            }
        } else {
            List<CxrCustomerStockDetail> customerStockDetails =
                remoteCustomerStockDetailService.queryStockBySource(
                    cxrUserOrder.getId(), SourceEnums.getType(orderType.shortValue()), false);
            List<CxrCustomerStockDetail> cxrCustomerStockDetailList =
                customerStockDetails.stream()
                    .map(
                        a -> {
                            a.setFreshMilkQuantity(
                                0 - a.getFreshMilkGiveQuantity() + a.getFreshMilkSentQuantity());
                            a.setOrderQuantity(0);
                            a.setFreshMilkGiveQuantity(0 - a.getFreshMilkGiveQuantity());
                            a.setLongMilkQuantity(0 - a.getLongMilkQuantity());
                            a.setLongMilkGiveQuantity(0 - a.getLongMilkGiveQuantity());
                            a.setFreshMilkSentQuantity(0 - a.getFreshMilkSentQuantity());
                            a.setSourceType(SourceEnums.NEW_ORDER_GIVE_SUB.getValue());
                            return a;
                        })
                    .collect(Collectors.toList());
            boolean add =
                remoteCustomerStockDetailService.add(
                    cxrUserOrder.getCustomerId(), cxrCustomerStockDetailList);
            if (!add) {
                throw new ServiceException("修改失败");
            }
        }

        List<CxrCustomerStockDetail> cxrCustomerStockDetailList = new ArrayList<>();
        CxrCustomerStockDetail customerStockDetail = new CxrCustomerStockDetail();

        customerStockDetail.setSourceType(SourceEnums.NEW_ORDER_GIVE.getValue());
        customerStockDetail.setSourceId(cxrUserOrder.getId());

        customerStockDetail.setOrderQuantity(0);
        customerStockDetail.setFreshMilkQuantity(
            Convert.toInt(
                completeInfoDTO.getFreshMilkGiveCount() - completeInfoDTO.getAlreadyGiveCount(), 0));
        customerStockDetail.setFreshMilkGiveQuantity(
            Convert.toInt(completeInfoDTO.getFreshMilkGiveCount(), 0));
        customerStockDetail.setFreshMilkSentQuantity(
            Convert.toInt(completeInfoDTO.getAlreadyGiveCount(), 0));
        customerStockDetail.setExchangeUseFreshMilkQuantity(0);
        customerStockDetail.setDisributionFreshMilkQuantity(0);
        customerStockDetail.setTemporaryAddMilkQuantity(0);
        customerStockDetail.setLongMilkQuantity(Convert.toInt(completeInfoDTO.getMilkGiveCount(), 0));
        customerStockDetail.setLongMilkGiveQuantity(
            Convert.toInt(completeInfoDTO.getMilkGiveCount(), 0));
        customerStockDetail.setLongMilkSentQuantity(0);

        customerStockDetail.setFreshMilkSentQuantity(
            Convert.toInt(completeInfoDTO.getAlreadyGiveCount(), 0));

        cxrCustomerStockDetailList.add(customerStockDetail);
        remoteCustomerStockDetailService.add(cxrUserOrder.getCustomerId(), cxrCustomerStockDetailList);

        if (!ObjectUtil.equals(
            completeInfoDTO.getMilkGiveCount(), cxrUserOrder.getLongMilkGiveQuantity())) {
            remoteLongMilkStockService.delLongMilk(cxrUserOrder.getId());
            remoteOrderService.saveLongMilkStock(cxrUserOrder.getId());
        }
    }

    public void updateCustomerStockDiff(CxrUserOrder cxrUserOrder, CompleteInfoDTO completeInfoDTO) {

        log.info(
            "订单id={}，old鲜奶赠送={}，old鲜奶已送={}，old常温奶赠送={} ------- new鲜奶赠送={}，new鲜奶已送={}，new常温奶赠送={}",
            cxrUserOrder.getId(),
            cxrUserOrder.getFreshMilkGiveQuantity(),
            cxrUserOrder.getFreshMilkSentQuantity(),
            cxrUserOrder.getLongMilkGiveQuantity(),
            completeInfoDTO.getFreshMilkGiveCount(),
            completeInfoDTO.getAlreadyGiveCount(),
            completeInfoDTO.getMilkGiveCount());

        // 如果鲜奶赠送 ，鲜奶已送，常温奶赠送数量没有变 则不对库存做加减
        if (cxrUserOrder.getFreshMilkGiveQuantity().intValue()
            == completeInfoDTO.getFreshMilkGiveCount().intValue()
            && cxrUserOrder.getFreshMilkSentQuantity().intValue()
            == completeInfoDTO.getAlreadyGiveCount().intValue()
            && cxrUserOrder.getLongMilkGiveQuantity().intValue()
            == completeInfoDTO.getMilkGiveCount().intValue()) {
            return;
        }

        // 鲜奶赠送差异
        int diffFreshMilkGiveCount =
            completeInfoDTO.getFreshMilkGiveCount().intValue()
                - cxrUserOrder.getFreshMilkGiveQuantity().intValue();
        // 已送数差异
        int diffFreshMilkSentQuantity =
            completeInfoDTO.getAlreadyGiveCount().intValue()
                - cxrUserOrder.getFreshMilkSentQuantity().intValue();
        // 常温奶赠送差异
        int diffLongMilkGiveQuantity =
            completeInfoDTO.getMilkGiveCount().intValue()
                - cxrUserOrder.getLongMilkGiveQuantity().intValue();

        int diffFreshMilkQuantity =
            (completeInfoDTO.getFreshMilkGiveCount() - completeInfoDTO.getAlreadyGiveCount())
                - (cxrUserOrder.getFreshMilkGiveQuantity() - cxrUserOrder.getFreshMilkSentQuantity());

        log.info(
            "订单id={}，鲜奶赠送={}，鲜奶已送={}，常温奶赠送={}",
            cxrUserOrder.getId(),
            diffFreshMilkGiveCount,
            diffFreshMilkSentQuantity,
            diffLongMilkGiveQuantity);

        List<CxrCustomerStockDetail> cxrCustomerStockDetailList = new ArrayList<>();
        CxrCustomerStockDetail customerStockDetail = new CxrCustomerStockDetail();

        customerStockDetail.setSourceType(SourceEnums.UPDATE_ORDER_DIFF.getValue());
        customerStockDetail.setSourceId(cxrUserOrder.getId());

        customerStockDetail.setOrderQuantity(0);
        customerStockDetail.setFreshMilkQuantity(Convert.toInt(diffFreshMilkQuantity, 0));
        customerStockDetail.setFreshMilkGiveQuantity(Convert.toInt(diffFreshMilkGiveCount, 0));
        customerStockDetail.setFreshMilkSentQuantity(Convert.toInt(diffFreshMilkSentQuantity, 0));
        customerStockDetail.setExchangeUseFreshMilkQuantity(0);
        customerStockDetail.setDisributionFreshMilkQuantity(0);
        customerStockDetail.setTemporaryAddMilkQuantity(0);
        customerStockDetail.setLongMilkQuantity(Convert.toInt(diffLongMilkGiveQuantity, 0));
        customerStockDetail.setLongMilkGiveQuantity(Convert.toInt(diffLongMilkGiveQuantity, 0));
        customerStockDetail.setLongMilkSentQuantity(0);

        customerStockDetail.setFreshMilkSentQuantity(Convert.toInt(diffFreshMilkSentQuantity, 0));

        cxrCustomerStockDetailList.add(customerStockDetail);
        remoteCustomerStockDetailService.add(cxrUserOrder.getCustomerId(), cxrCustomerStockDetailList);

        if (!ObjectUtil.equals(
            completeInfoDTO.getMilkGiveCount(), cxrUserOrder.getLongMilkGiveQuantity())) {
            remoteLongMilkStockService.delLongMilk(cxrUserOrder.getId());
            remoteOrderService.saveLongMilkStock(cxrUserOrder.getId());
        }
    }

    @Override
    public List<CxrUserOrder> getByCustomerId(Long customerId) {
        List<CxrUserOrder> cxrUserOrder =
            this.getBaseMapper()
                .selectList(
                    new LambdaUpdateWrapper<CxrUserOrder>()
                        .eq(CxrUserOrder::getCustomerId, customerId)
                        .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));
        return cxrUserOrder;
    }

    @Override
    public Integer getDaySumQty(CxrUserOrder cxrUserOrder) {
        Long customerId = cxrUserOrder.getCustomerId();
        Date payTime = cxrUserOrder.getPayTime();
        Date auditTime = cxrUserOrder.getAuditTime();

        if (payTime == null && auditTime == null) {
            return 0;
        }

        LocalDate start =
            com.ruoyi.common.core.utils.DateUtils.getLocalDateFromDate(
                payTime != null ? payTime : auditTime);
        Integer daySumQty = baseMapper.getDaySumQty(customerId, start.plusDays(-9), start.plusDays(1));
        return daySumQty == null ? 0 : daySumQty;
    }

    @Override
    public CxrUserOrder queryById(Long outLongMilkOrderId) {

        return baseMapper.queryById(outLongMilkOrderId);
    }

    @Override
    public Long queryOrderIdByOrderNo(String outLongMilkOrderNo) {

        return baseMapper.queryOrderIdByOrderNo(outLongMilkOrderNo);
    }

    @Override
    public Date queryOrderDateByOrderNo(String outLongMilkOrderNo) {

        return baseMapper.queryOrderDateByOrderNo(outLongMilkOrderNo);
    }

    @Override
    public List<CxrUserOrder> getByCustomerPhone(String folmerPhone) {
        List<CxrUserOrder> cxrUserOrder =
            this.getBaseMapper()
                .selectList(
                    new LambdaUpdateWrapper<CxrUserOrder>()
                        .eq(CxrUserOrder::getCustomerPhoneSwitch, folmerPhone)
                        .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));
        return cxrUserOrder;
    }


    public boolean reverseAudit(Long id) {
        CxrUserOrder data = cxrUserOrderService.getById(id);
        if (OrderAuditStatusEnums.AUDITED.getValue() != data.getAuditStatus().intValue()) {
            throw new ServiceException("订单不是已审核状态,不能反审核");
        }
        boolean flag = abstractCxrUserOrderBehavior.executeCxrUserOrderReverseAudit(data);

        /** 设置信息 */
        LoginUser loginUser = LoginHelper.getLoginUser();
        CxrUserOrder cxrUserOrder = new CxrUserOrder();
        cxrUserOrder.setId(id);
        cxrUserOrder.setAuditStatus(OrderAuditStatusEnums.WAIT_AUDIT.getValue());
        log.info("订单反审核： id={},userId={},name={},time={}", id, loginUser.getUserId(), loginUser.getUserName(),
            LocalDateTime.now());
        return baseMapper.reverseAuditUpdateById(cxrUserOrder);
    }

    @Override
    public UserOrderListVo queryByCustomerId(Long id) {
        CxrUserOrder cxrUserOrder = baseMapper.selectOne(new LambdaQueryWrapper<CxrUserOrder>()
            .eq(CxrUserOrder::getCustomerId, id)
            .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            .and(wapper -> wapper
                .eq(CxrUserOrder::getAuditStatus, AuditStatusEnums.Audit.code())
                .or()
                .eq(CxrUserOrder::getPayStatus, PayStatusEnums.PAY_SUCCEEDED.getValue())
            )
            .and(wapper -> wapper
                .eq(CxrUserOrder::getOrderType, OrderTypeEnums.NEW_ORDER.getValue())
                .or()
                .eq(CxrUserOrder::getOrderType, OrderTypeEnums.CONTINUE_ORDER.getValue())
                .or()
                .eq(CxrUserOrder::getOrderType, OrderTypeEnums.INCREASE_ORDER.getValue())
                .or()
                .eq(CxrUserOrder::getOrderType, OrderTypeEnums.CONTRACT_ORDER.getValue())
            )
            .orderByDesc(CxrUserOrder::getPayTime)
            .last("limit 1")
        );
        UserOrderListVo userOrderListVo = BeanUtil.copyProperties(cxrUserOrder, UserOrderListVo.class);
        return userOrderListVo;
    }

    @Override
    public boolean del(Long id, LoginInfo loginUser) {
        return cxrUserOrderService.lambdaUpdate()
            .set(CxrUserOrder::getDeleteTime, new Date())
            .set(CxrUserOrder::getDeleteBy, loginUser == null ? 1L : loginUser.getUserId())
            .set(CxrUserOrder::getDeleteByName, loginUser == null ? "admin" : loginUser.getUserName())
            .set(CxrUserOrder::getDeleteByType, loginUser == null ? "1" : loginUser.getUserType())
            .set(CxrUserOrder::getDeleteStatus, DeleteStatus.DELETED.getValue())
            .eq(CxrUserOrder::getId, id)
            .update();
    }

    @Override
    public Page<CxrUserOrder> queryUserOrderByCustomerId(Long customerId, long size, long current) {
        Page<CxrUserOrder> page = new Page<>(current, size);
        if (null != customerId) {
            return baseMapper.queryUserOrderByCustomerId(customerId, page);
        }
        return page;
    }

    @Override
    public CxrUserOrder getByPayOrderId(Long payOrderId) {
        CxrUserOrder cxrUserOrder = baseMapper.selectOne(new LambdaQueryWrapper<CxrUserOrder>()
            .eq(CxrUserOrder::getId, payOrderId)
            .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
        );
        return cxrUserOrder;
    }

    @Override
    public boolean updateByAfterSaleOrderStatus(Long orderId) {
        CxrOrderAfterSale cxrOrderAfterSale = cxrOrderAfterSaleMapper.selectOne(
            new LambdaQueryWrapper<CxrOrderAfterSale>()
                .eq(CxrOrderAfterSale::getRefundOrderId, orderId)
                .eq(CxrOrderAfterSale::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
        );
        if (ObjectUtil.isNotEmpty(cxrOrderAfterSale)) {
            boolean b = cxrOrderAfterSaleMapper.update(null, new LambdaUpdateWrapper<CxrOrderAfterSale>()
                .eq(CxrOrderAfterSale::getId, cxrOrderAfterSale.getId())
                .set(CxrOrderAfterSale::getDeleteStatus, DeleteStatus.DELETED.getValue())
            ) > 0;
            if (!b) {
                throw new ServiceException("售后单删除失败!!!");
            }
            return true;
        }
        return false;
    }

    @Override
    public void reComputeAchievementOrderFmQty() {
        Executors.newSingleThreadExecutor().execute(() -> {
            try {
                while (true) {
                    log.info("重新计算业绩的订单鲜奶盒数 -- {}, {}", LocalDateTime.now(), isContinue);
                    Map<Long, List<Long>> userOrderIdEmployeeIdsMap = remoteEmployeeAchievementDetailService.queryNotDeletedAchievementOrderId();
                    if (CollUtil.isNotEmpty(userOrderIdEmployeeIdsMap) && isContinue) {
                        List<CompletableFuture<Void>> futureTaskList = new ArrayList<>();

                        List<AchievementDetailUpdateDto> updateDatas = new ArrayList<>();
                        for (Map.Entry<Long, List<Long>> entry : userOrderIdEmployeeIdsMap.entrySet()) {
                            Long userOrderId = entry.getKey();
                            List<Long> employeeIds = entry.getValue();
                            if (userOrderId != null && employeeIds != null) {
                                List<AchievementDetailUpdateDto> dtos = generateUpdateData(userOrderId, employeeIds);
                                if (CollUtil.isNotEmpty(dtos)) {
                                    updateDatas.addAll(dtos);
                                }
                            }
                        }
                        if (CollUtil.isNotEmpty(updateDatas)) {

                            int size = updateDatas.size();
                            int updateNum = 20;
                            while (size > 0) {
                                int end = Math.min(updateNum, size);
                                List<AchievementDetailUpdateDto> subList = updateDatas.subList(0, end);
                                remoteEmployeeAchievementDetailService.batchUpdateOrderFmQty(subList);
                                subList.clear();
                                size = updateDatas.size();
                            }
                        }

                    } else {
                        log.info("重新计算业绩的订单鲜奶盒数 -- 正常退出");
                        return;
                    }
                }
            } catch (Exception e) {
                log.error("重新计算业绩的订单鲜奶盒数 -- 异常退出 ", e);
            }
        });
    }

    @Override
    public NewOrderDetermineTemplateDto queryOrderDetermineTemplate(Long cxrOrderId) {
        CxrOrderDetermineTemplate determineTemplate = determineTemplateMapper.selectOne(
            new LambdaQueryWrapper<CxrOrderDetermineTemplate>()
                .eq(CxrOrderDetermineTemplate::getSourceId, cxrOrderId)
                .eq(CxrOrderDetermineTemplate::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .last("limit 1")
        );
        if (determineTemplate != null) {
            return JSONUtil.toBean(determineTemplate.getTemplateJson(), NewOrderDetermineTemplateDto.class);
        }
        return null;
    }

    @Override
    public void updateTiktokNo(Long id) {
        CxrUserReturnOrder returnOrder = returnOrderService.queryUserOrderId(id);
        if (ObjectUtil.isNotEmpty(returnOrder)) {
            CxrUserOrder order = cxrUserOrderService.getById(returnOrder.getOrderId());
            if (ObjectUtil.isNotEmpty(order)) {
                cxrUserOrderService.update(new LambdaUpdateWrapper<CxrUserOrder>()
                    .eq(CxrUserOrder::getId, order.getId())
                    .set(CxrUserOrder::getTiktokOrderRefundNo, null)
                );
            }
        }
    }

    @Override
    public void recalculate(String ids) {
        for (String s : ids.split(StrUtil.COMMA)) {
            long id = Long.parseLong(s);
            remoteEmployeeAchievementDetailService.achievemenDiscard(id, "订单修改废弃审核的订单业绩");
            cxrUserOrderService.orderAfterEmployeeHandler(id);
        }
    }

    @Override
    public void cleaningRegionHistory(LocalDate startDate, LocalDate endDate) {
        List<CxrUserOrder> userOrders = this.list(new LambdaQueryWrapper<CxrUserOrder>()
            .select(CxrUserOrder::getId, CxrUserOrder::getSiteId, CxrUserOrder::getBusinessAgent)
            .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            .between(CxrUserOrder::getOrderDate, startDate, endDate)
        );

        List<List<CxrUserOrder>> listList = CollectionUtil.split(userOrders, 500);
        if (CollUtil.isNotEmpty(listList)) {
            for (List<CxrUserOrder> cxrUserOrders : listList) {
                setThreadPoolTaskExecutor(cxrUserOrders);
            }
        }
    }

    @Override
    public List<LastUserOrderDayInfo> queryLastTenDayOrderInfo(CustomerInfo bo, Long customerId) {
        Long orderId = bo.getOrderId();
        LocalDateTime localDateTime = LocalDateTime.now();
        if (orderId != null) {
            CxrUserOrder userOrder = getBaseMapper().selectById(orderId);
            if (userOrder != null) {

                Date payTime = userOrder.getPayTime();
                Date auditTime = userOrder.getAuditTime();
                if (payTime != null) {
                    localDateTime = com.ruoyi.common.core.utils.DateUtils.getLocalDateTimeFormDate(payTime);
                } else if (auditTime != null) {
                    localDateTime = com.ruoyi.common.core.utils.DateUtils.getLocalDateTimeFormDate(auditTime);
                } else {
                    localDateTime =
                        com.ruoyi.common.core.utils.DateUtils.getLocalDateTimeFormDate(
                            userOrder.getCreateTime());
                }
            }
        }
        return getBaseMapper()
            .queryNotContractLastTenDayOrderInfo(
                customerId, localDateTime.toLocalDate().plusDays(-9), localDateTime, orderId);
    }

    private List<AchievementDetailUpdateDto> generateUpdateData(Long userOrderId, List<Long> employeeIds) {
        List<AchievementDetailUpdateDto> list = new ArrayList<>();
        CxrUserOrder cxrUserOrder = this.getById(userOrderId);
        if (cxrUserOrder != null) {

            boolean returnOrderExists = true;
            CxrUserReturnOrder returnOrder = null;
            if (NumberUtil.equals(
                cxrUserOrder.getOrderType().shortValue(), OrderTypeEnums.RETURN_ORDER.getValue())) {
                returnOrder = returnOrderService.getByOrderId(userOrderId);
                returnOrderExists = false;
            }

            // 代理
            List<BusinessAgent> businessAgent = cxrUserOrder.getBusinessAgent();

            BigDecimal orderQuantity = new BigDecimal(Convert.toInt(cxrUserOrder.getOrderQuantity(), 0));
            BigDecimal freshMilkGiveQuantity =
                Convert.toBigDecimal(
                    cxrUserOrder.getFreshMilkGiveQuantity(), BigDecimal.ZERO); // 鲜奶赠送
            BigDecimal orderFmQty = NumberUtil.add(orderQuantity, freshMilkGiveQuantity); // 鲜奶盒数（订购数+赠送数）

            if (!returnOrderExists) {
                orderFmQty =
                    returnOrder == null ? BigDecimal.ZERO :
                        BigDecimal.ZERO.subtract(
                            Convert.toBigDecimal(returnOrder.getFreshMilkCancelQuantity(), BigDecimal.ZERO));
            }

            int size = businessAgent.size();
            BigDecimal agentSize = Convert.toBigDecimal(size);

            BigDecimal avgOrderFmQty = orderFmQty.divide(agentSize, 2, BigDecimal.ROUND_DOWN);

            for (int i = 0; i < size; i++) {
                BusinessAgent tempBussinessAgent = businessAgent.get(i);
                if (i == size - 1) {
                    avgOrderFmQty = orderFmQty;
                } else {
                    orderFmQty = orderFmQty.subtract(avgOrderFmQty);
                }

                AchievementDetailUpdateDto dto = new AchievementDetailUpdateDto();
                dto.setEmployeeId(tempBussinessAgent.getProxyId());
                dto.setOrderFmQty(avgOrderFmQty);
                dto.setSouceId(userOrderId);

                list.add(dto);
            }
        } else {
            AchievementDetailUpdateDto dto = new AchievementDetailUpdateDto();
            dto.setOrderFmQty(BigDecimal.ZERO);
            dto.setSouceId(userOrderId);

            list.add(dto);
        }
        log.info("generateUpdateData list >:{}", JSONUtil.toJsonStr(list));
        return list;
    }

    private void setThreadPoolTaskExecutor(List<CxrUserOrder> cxrUserOrders) {
//        threadPoolTaskExecutor.execute(() -> {

        List<List<BusinessAgent>> collect = cxrUserOrders.stream().map(x -> x.getBusinessAgent())
            .collect(Collectors.toList());

        List<BusinessAgent> businessAgents = collect.stream()
            .flatMap(List::stream)
            .collect(Collectors.toList());

        Set<Long> employeeIds = businessAgents.stream()
            .filter(x -> ObjectUtil.isNotEmpty(x.getProxyId()))
            .map(x -> x.getProxyId()).collect(Collectors.toSet());
        Set<String> siteNameIds = businessAgents.stream()
            .filter(x -> ObjectUtil.isNotEmpty(x.getSiteName()))
            .map(x -> x.getSiteName()).collect(Collectors.toSet());

        List<CxrSite> cxrSites = cxrSiteMapper.selectList(new LambdaQueryWrapper<CxrSite>().in(CxrSite::getName, siteNameIds));
        List<CxrEmployee> employees = employeeMapper.selectBatchIds(employeeIds);
        Map<Long, CxrSite> cxrSiteMap = cxrSites.stream().collect(Collectors.toMap(x -> x.getId(), x -> x));
        Map<Long, CxrEmployee> employeesMap = employees.stream().collect(Collectors.toMap(x -> x.getId(), x -> x));

        for (CxrUserOrder userOrder : cxrUserOrders) {
            List<BusinessAgent> businessAgent = userOrder.getBusinessAgent();
            businessAgent.stream().map(x -> {
//                if (ObjectUtil.isEmpty(x.getRegionId())) {
                String regionName = null;
                Long regionId = null;
                if (ObjectUtil.isNotEmpty(x.getSiteId())) {
                    CxrSite cxrSite = cxrSiteMap.get(x.getSiteId());
                    if (ObjectUtil.isNotEmpty(cxrSite)) {
                        regionName = cxrSite.getCxrRootRegionName();
                        regionId = cxrSite.getCxrRootRegionId();
                    }
                } else if (ObjectUtil.isNotEmpty(x.getProxyId())) {
                    CxrEmployee cxrEmployee = employeesMap.get(x.getProxyId());
                    if (ObjectUtil.isNotEmpty(cxrEmployee)) {
                        CxrSite cxrSite = cxrSiteMapper.selectById(cxrEmployee.getCxrSiteId());
                        if (ObjectUtil.isNotEmpty(cxrSite)) {
                            regionName = cxrSite.getCxrRootRegionName();
                            regionId = cxrSite.getCxrRootRegionId();
                        }
                    }
                }
                x.setRegionName(regionName);
                x.setRegionId(regionId);
//                }
                return x;
            }).collect(Collectors.toList());
            userOrder.setBusinessAgent(businessAgent);
        }

        this.updateBatchById(cxrUserOrders);
//        });
    }

}
