package com.ruoyi.business.base.excelReportFormsExport.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.business.api.domain.bo.CxrReportFormsExportBO;
import com.ruoyi.business.api.domain.vo.CxrReportFormsExportVo;
import com.ruoyi.business.base.api.domain.CxrReportFormsExport;
import com.ruoyi.business.base.excelReportFormsExport.mapper.CxrReportFormsExportMapper;
import com.ruoyi.business.base.excelReportFormsExport.service.ICxrReportFormsExportService;
import com.ruoyi.common.core.enums.RetainStatusEnums;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.mybatis.core.page.PageTableDataInfo;
import org.apache.commons.io.filefilter.TrueFileFilter;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * 文件下载(CxrReportFormsExport)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-27 16:54:37
 */
@Service("cxrReportFormsExportService")
public class CxrReportFormsExportService extends
    ServiceImpl<CxrReportFormsExportMapper, CxrReportFormsExport> implements ICxrReportFormsExportService {

    @Override
    public PageTableDataInfo<CxrReportFormsExportVo> queryPage(CxrReportFormsExportBO cxrReportFormsExportBO) {
        Page<CxrReportFormsExport> page = this.lambdaQuery()
            .like(StrUtil.isNotBlank(cxrReportFormsExportBO.getHtmlName()), CxrReportFormsExport::getHtmlName,
                cxrReportFormsExportBO.getHtmlName())
            .eq(ObjectUtil.isNotEmpty(cxrReportFormsExportBO.getCreateById()), CxrReportFormsExport::getCreateBy,
                cxrReportFormsExportBO.getCreateById())
            .like(StrUtil.isNotBlank(cxrReportFormsExportBO.getCreateByName()), CxrReportFormsExport::getCreateByName,
                cxrReportFormsExportBO.getCreateByName())
            .ge(ObjectUtil.isNotEmpty(cxrReportFormsExportBO.getStartTime()), CxrReportFormsExport::getCreateTime,
                cxrReportFormsExportBO.getStartTime())
            .le(ObjectUtil.isNotEmpty(cxrReportFormsExportBO.getEndTime()), CxrReportFormsExport::getCreateTime,
                cxrReportFormsExportBO.getEndTime())
            .eq(CxrReportFormsExport::getRetainStatus, RetainStatusEnums.overdue_del.getValue())
            .orderByDesc(CxrReportFormsExport::getCreateTime)
            .page(cxrReportFormsExportBO.build());

        IPage<CxrReportFormsExportVo> convertPage = page.convert(x ->
            BeanUtil.copyProperties(x, CxrReportFormsExportVo.class)
        );
        List<CxrReportFormsExportVo> cxrReportFormsExportVoList = convertPage.getRecords();
        handlerReportFormsExportData(cxrReportFormsExportVoList);

        PageTableDataInfo<CxrReportFormsExportVo> cxrPageVo = PageTableDataInfo.build(convertPage);
        return cxrPageVo;
    }

    @Override
    public void downLoad(Long id, HttpServletResponse response) throws Exception {
        CxrReportFormsExport cxrReportFormsExport = this.getById(id);
        String fileDir = cxrReportFormsExport.getFilePath();
        List<File> list = FileUtil.loopFiles(new File(fileDir), 1, TrueFileFilter.TRUE);
        if (list.size() > 0) {
            String fileName = StrUtil.format("{}{}", cxrReportFormsExport.getHtmlName(), cxrReportFormsExport.getFileNameSuffix());
            if (list.size() == 1) {
                singleFilehandler(response, fileName, list.get(0));
            } else {
                multipleFilehandler(response, fileName, list);
            }

        } else {
            throw new ServiceException("下载失败,目录没有文件！");
        }

    }

    @Override
    public PageTableDataInfo<CxrReportFormsExportVo> reportFormsExporPage(CxrReportFormsExportBO cxrReportFormsExportBO) {

        Page<CxrReportFormsExport> page = this.lambdaQuery()
            .eq(CxrReportFormsExport::getRetainStatus, RetainStatusEnums.retain.getValue())
            .like(StrUtil.isNotBlank(cxrReportFormsExportBO.getHtmlName()), CxrReportFormsExport::getHtmlName,
                cxrReportFormsExportBO.getHtmlName())
            .like(StrUtil.isNotBlank(cxrReportFormsExportBO.getCreateByName()), CxrReportFormsExport::getCreateByName,
                cxrReportFormsExportBO.getCreateByName())
            .ge(ObjectUtil.isNotEmpty(cxrReportFormsExportBO.getStartTime()), CxrReportFormsExport::getCreateTime,
                cxrReportFormsExportBO.getStartTime())
            .le(ObjectUtil.isNotEmpty(cxrReportFormsExportBO.getEndTime()), CxrReportFormsExport::getCreateTime,
                cxrReportFormsExportBO.getEndTime())
            .orderByDesc(CxrReportFormsExport::getCreateTime)
            .page(cxrReportFormsExportBO.build());

        IPage<CxrReportFormsExportVo> convertPage = page.convert(x ->
            BeanUtil.copyProperties(x, CxrReportFormsExportVo.class)
        );
        List<CxrReportFormsExportVo> cxrReportFormsExportVoList = convertPage.getRecords();
        handlerReportFormsExportData(cxrReportFormsExportVoList);

        PageTableDataInfo<CxrReportFormsExportVo> cxrPageVo = PageTableDataInfo.build(convertPage);
        return cxrPageVo;
    }

    private void singleFilehandler(HttpServletResponse response, String filename, File filePath) throws Exception {
        byte[] bytes = FileUtil.readBytes(filePath);
        String suffix = FileUtil.getSuffix(filePath);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml,sheet;charset=utf-8");
        filename = URLEncoder.encode(filename, "UTF-8"); // 文件名
        response.setHeader("Content-Disposition", "attachment;filename=" + StrUtil.format("{}.{}", filename, suffix));
        ServletOutputStream out = response.getOutputStream();
        IoUtil.write(out, false, bytes);
        IoUtil.flush(out);
        IoUtil.close(out);
    }

    private void multipleFilehandler(HttpServletResponse response, String filename, List<File> filePath) throws Exception {

        response.setContentType("application/zip");
        filename = URLEncoder.encode(filename, "UTF-8"); // 文件名
        response.setHeader("Content-Disposition", "attachment;filename=" + StrUtil.format("{}.{}", filename, "zip"));
        ServletOutputStream out = response.getOutputStream();

        List<InputStream> inputStreamList = new ArrayList<>();
        List<String> fileNameList = new ArrayList<>();
        for (File file : filePath) {
            String name = file.getName();
            BufferedInputStream inputStream = FileUtil.getInputStream(file);
            inputStreamList.add(inputStream);
            fileNameList.add(name);
        }

        ZipUtil.zip(out, fileNameList.toArray(new String[fileNameList.size()]), inputStreamList.toArray(new InputStream[inputStreamList.size()]));
        for (InputStream inputStream : inputStreamList) {
            IoUtil.close(inputStream);
        }
        IoUtil.flush(out);
        IoUtil.close(out);
    }

    private void handlerReportFormsExportData(List<CxrReportFormsExportVo> cxrReportFormsExportVoList) {
        if (CollUtil.isNotEmpty(cxrReportFormsExportVoList)) {
            for (CxrReportFormsExportVo cxrReportFormsExportVo : cxrReportFormsExportVoList) {
                Long dataTotal = Convert.toLong(cxrReportFormsExportVo.getDataTotal(), 0L);
                Long exportedTotal = Convert.toLong(cxrReportFormsExportVo.getExportedTotal(), 0L);
                if (NumberUtil.equals(dataTotal, 0L)) {
                    cxrReportFormsExportVo.setDownloadProgressStr("0%");
                    cxrReportFormsExportVo.setDownloadProgress(0d);
                } else {

                    BigDecimal bigDecimal1 = new BigDecimal(exportedTotal);
                    BigDecimal bigDecimal2 = new BigDecimal(dataTotal);

                    BigDecimal result = bigDecimal1.divide(bigDecimal2, 4, BigDecimal.ROUND_HALF_UP);
                    double process = result.multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                    if (process >= 100) {
                        process = 100;
                    }
                    cxrReportFormsExportVo.setDownloadProgressStr(process + "%");
                    cxrReportFormsExportVo.setDownloadProgress(process);
                }
//                boolean flag = LocalDateTime.now().compareTo(cxrReportFormsExportVo.getCreateTime().plusDays(1)) > 0;
//                if (flag) {
//                    cxrReportFormsExportVo.setFileStatus(FileStatusEnums.EXPIRED.name());
//                } else {
//                    cxrReportFormsExportVo.setFileStatus(FileStatusEnums.NO_EXPIRED.name());
//                }
            }
        }
    }

}
