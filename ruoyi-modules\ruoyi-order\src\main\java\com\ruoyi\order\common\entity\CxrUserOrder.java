package com.ruoyi.order.common.entity;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.cxrry.logger.api.annotation.LogModel;
import com.cxrry.logger.api.annotation.LogTag;
import com.ruoyi.business.base.api.domain.json.BusinessAgent;
import com.ruoyi.business.base.api.domain.json.ContractOrderCustomerInfo;
import com.ruoyi.business.base.api.domain.json.CustomerInfo;
import com.ruoyi.common.core.web.domain.CxrBaseEntity;
import com.ruoyi.common.excel.annotation.ExcelDictFormat;
import com.ruoyi.common.excel.convert.ExcelDictConvert;
import com.ruoyi.order.common.typeHandler.TypeHandlerConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 用户订单对象 cxr_user_order
 *
 * <AUTHOR>
 * @date 2022-08-04
 */
@ApiModel
@ExcelIgnoreUnannotated
@Data
@EqualsAndHashCode(callSuper = false)
@LogModel(value = "用户订单", tableName = "cxr_user_order")
@TableName(value = "cxr_user_order", autoResultMap = true)
public class CxrUserOrder extends CxrBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @ApiModelProperty("主键 id")
    @TableId(value = "id")
    private Long id;

    /**
     * 公司id
     */
    @LogTag(alias = "公司id")
    @ApiModelProperty("公司id")
    private Long companyId;

    /**
     * 公司名称
     */
    @LogTag(alias = "公司名称")
    @ApiModelProperty("公司名称")
    @ExcelProperty("所属公司")
    private String companyName;

    /**
     * 大区id
     */
    @LogTag(alias = "大区id")
    @ApiModelProperty("大区id")
    private Long bigAreaId;

    /**
     * 大区名称
     */
    @LogTag(alias = "大区名称")
    @ApiModelProperty("大区名称")
    @ExcelProperty("所属大区")
    private String bigAreaName;

    /**
     * 省
     */
    @LogTag(alias = "省")
    @ApiModelProperty("省")
    @ExcelProperty("省")
    private String province;

    /**
     * 市
     */
    @LogTag(alias = "市")
    @ApiModelProperty("市")
    @ExcelProperty("市")
    private String city;

    @TableField(exist = false)
    private String fromProductType;
    /**
     * 区
     */
    @LogTag(alias = "区")
    @ApiModelProperty("区")
    @ExcelProperty("区")
    private String area;

    /**
     * 站点名称
     */
    @LogTag(alias = "站点名称")
    @ApiModelProperty("站点名称")
    @ExcelProperty("站点")
    private String siteName;

    /**
     * 站点地址
     */
    @LogTag(alias = "站点地址")
    @ApiModelProperty("站点地址")
    private String siteAdress;

    /**
     * 站点id
     */
    @LogTag(alias = "站点id")
    @ApiModelProperty("站点id")
    private Long siteId;

    /**
     * 业务代理字符串; 以逗号分割
     */
    @LogTag(alias = "业务代理详细信息")
    @ApiModelProperty("业务代理详细信息")
    @TableField(typeHandler = TypeHandlerConstant.BusinessAgentTypeHandler.class)
    private List<BusinessAgent> businessAgent;

    @TableField(exist = false)
    @ExcelProperty("销售代理")
    private String businessAgentName;

    @TableField(exist = false)
    @ExcelProperty("等级")
    private String businessAgentLevel;

    /**
     * 客户名称
     */
    @LogTag(alias = "客户名称")
    @ApiModelProperty("客户名称;只取第一个录入的数据")
    @ExcelProperty("客户名称")
    private String customerName;

    /**
     * 客户电话;只取第一个录入的数据
     */
    @LogTag(alias = "客户电话")
    @ApiModelProperty("客户电话;只取第一个录入的数据")
    @ExcelProperty("客户电话")
    private String customerPhone;

    /**
     * 客户地址; 只取第一个录入的数据
     */
    @LogTag(alias = "客户地址")
    @ApiModelProperty("客户地址; 只取第一个录入的数据")
    @ExcelProperty("客户地址")
    private String customerAdress;

    /**
     * 之前已经创建了地址的客户
     */
    private Boolean oldCustomer;

    /**
     * 转入客户姓名
     */
    @LogTag(alias = "转入客户姓名")
    @ApiModelProperty("转入客户姓名")
    @ExcelProperty("转入客户名称")
    private String customerNameSwitch;


    /**
     * 转入客户手机号
     */
    @LogTag(alias = "转入客户手机号")
    @ApiModelProperty("转入客户手机号")
    @ExcelProperty("转入客户电话")
    private String customerPhoneSwitch;

    /**
     * 转入客户地址
     */
    @LogTag(alias = "转入客户地址")
    @ApiModelProperty("转入客户地址")
    @ExcelProperty("转入客户地址")
    private String customerAdressSwitch;


    @LogTag(alias = "转入客户id")
    @ApiModelProperty("转入客户id")
    @ExcelProperty("转入客户id")
    private Long customerIdSwitch;

    /**
     * 1微信;2 支付宝
     */
    @LogTag(alias = "支付来源")
    @ApiModelProperty("支付来源")
    @ExcelProperty("付款来源")
    private String paymentSouce;

    /**
     * 订单类型;1、新订单2、增订单3、续订单4、转单5、换单6、退订单7、赠送单
     */
    @ApiModelProperty("订单类型")
    @ExcelProperty(value = "订单类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=新订单,1=续订单,2=退订单,3=增订单,4=转单,5=换单,6=赠送单,7=合订单,8=转单-常温奶")
    private Short orderType;

    /**
     * 0数量续单：用于客户账户没有鲜奶了;并且停奶超过了15天。15天后续单的，显示“超过15天”。
     */
    @LogTag(alias = "0数量续单")
    @ApiModelProperty("0数量续单")
    private Boolean zeroQuantityRenewal;

    /**
     * 库存为0时间大于15天
     */
    private Boolean zero15DayAfter;


    /**
     * 转换类型，用于订单类型为“换单”的订单，显示客户兑换的商品的类目;，剔除重复的 多个以逗号分割
     */
    @LogTag(alias = "转换类型")
    @ApiModelProperty("转换类型")
    @ExcelProperty("转换类型")
    private String conversionType;

    /**
     * 单据;订单单据号
     */
    @LogTag(alias = "单据")
    @ApiModelProperty("单据")
    @ExcelProperty("单据")
    private String orderNo;

    /**
     * 商户单号
     */
    @LogTag(alias = "商户单号")
    @ApiModelProperty("商户单号")
    @ExcelProperty("商户单号")
    private String merchantOrderNo;

    /**
     * 商户单号
     */
    @LogTag(alias = "收钱吧单号")
    @ApiModelProperty("收钱吧单号")
    private String sqbSn;

    /**
     * 订购数量：鲜奶订购数量
     */
    @LogTag(alias = "订购数量")
    @ApiModelProperty("订购数量")
    @ExcelProperty("订购数量")
    private Integer orderQuantity;

    /**
     * 鲜奶赠送数量
     */
    @LogTag(alias = "鲜奶赠送数量")
    @ApiModelProperty("鲜奶赠送数量")
    @ExcelProperty("鲜奶赠送数量")
    private Integer freshMilkGiveQuantity;

    /**
     * 常温奶赠送数量
     */
    @LogTag(alias = "常温奶赠送数量")
    @ApiModelProperty("常温奶赠送数量")
    @ExcelProperty("常温奶赠送数量")
    private Integer longMilkGiveQuantity;

    /**
     * 超送数量：根据订单套餐超送规则计算出来的超送数量;祥看原型
     */
    @LogTag(alias = "超送数量")
    @ApiModelProperty("超送数量")
    @ExcelProperty("超送数量")
    private Integer excessQuantity;

    /**
     * 鲜奶已送数量
     */
    @LogTag(alias = "鲜奶已送数量")
    @ApiModelProperty("鲜奶已送数量")
    @ExcelProperty("鲜奶已送数量")
    private Integer freshMilkSentQuantity;

    /**
     * 转换数量，订单类型为“转单”的时候才会有值;即转换的鲜奶数量 也可以是常温奶数量
     */
    @LogTag(alias = "转换数量")
    @ApiModelProperty("转换数量")
    @ExcelProperty("转换数量")
    private Integer conversionQuantity;

    /**
     * 常温奶已送数量
     */
    @LogTag(alias = "常温奶已送数量")
    @ApiModelProperty("常温奶已送数量")
    private Integer longMilkSentQuantity;

    @LogTag(alias = "剩余数量")
    @ApiModelProperty(value = "剩余数量")
    private Integer surplusQuantity;

    /**
     * 单价：鲜奶单价
     */
    @LogTag(alias = "单价")
    @ApiModelProperty("单价：鲜奶单价")
    @ExcelProperty("单价")
    private BigDecimal unitPrice;

    /**
     * 金额：该笔订单的总金额
     */
    @LogTag(alias = "金额:该笔订单的总金额")
    @ApiModelProperty("金额：该笔订单的总金额")
    @ExcelProperty("金额")
    private BigDecimal amount;

    @ApiModelProperty("支付金额")
    @ExcelProperty("支付金额")
    private BigDecimal payAmount;

    /**
     * 平台优惠金额
     */
    @ApiModelProperty("平台优惠金额")
    @ExcelProperty("平台优惠金额")
    private BigDecimal platformDiscount;

    /**
     * 刷卡金额
     */
    @LogTag(alias = "刷卡金额")
    @ApiModelProperty("刷卡金额")
    private BigDecimal creditCardAmount;

    /**
     * 订单日期
     */
    @LogTag(alias = "订单日期")
    @ApiModelProperty("订单日期")
    @ExcelProperty("订单日期")
    private Date orderDate;

    /**
     * 1 待审核 、 2 已审核 、 3.已拒绝
     */
    @LogTag(alias = "审核状态1 待审核 、 2 已审核 、 3.已拒绝")
    @ApiModelProperty("1 待审核 、 2 已审核 、 3.已拒绝")
    @ExcelProperty("审核状态")
    private Integer auditStatus;

    /**
     * 审核人
     */
    @ApiModelProperty("审核人")
    @ExcelProperty("审核人")
    private Long auditBy;

    /**
     * 审核人
     */
    @ApiModelProperty("审核人名字")
    @ExcelProperty("审核人名字")
    private String auditByName;

    /**
     * 审核人
     */
    @ApiModelProperty("审核时间")
    @ExcelProperty("审核时间")
    private Date auditTime;

    /**
     * 是否促销单;true 是 false 否
     */
    @LogTag(alias = "是否促销单true 是 false 否")
    @ApiModelProperty("是否促销单;true 是 false 否")
    @ExcelProperty("是否促销单")
    private Boolean promotionalOrderFlag;

    /**
     * 是否师徒单;true是 false 否
     */
    @LogTag(alias = "是否师徒单true是 false 否")
    @ApiModelProperty("是否师徒单;true是 false 否")
    @ExcelProperty("是否师徒单")
    private Boolean apprenticeOrderFlag;

    /**
     * 来源 1 后端 2 配送端
     */
    @LogTag(alias = "来源 1 后端 2 配送端")
    @ApiModelProperty("源  1 后端 2 配送端")
    @ExcelProperty(value = "终端来源", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=后台录入,2=配送端录入,3=客户端录入,4=小程序")
    @TableField(fill = FieldFill.INSERT)
    private Short terminalType;

    /**
     * 单据图片
     */
    @ApiModelProperty("单据图片")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> orderImages;

    /**
     * 支付截图
     */
    @ApiModelProperty("支付截图")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> playImages;

    /**
     * 客户信息
     */
    @ApiModelProperty(" 客户信息")
    @TableField(typeHandler = TypeHandlerConstant.CustomerInfoTypeHandler.class)
    private List<CustomerInfo> customerInfoList;

    /**
     * 客户信息
     */
    @TableField(exist = false)
    private String customerInfos;

    @ApiModelProperty("销售代理")
    @TableField(exist = false)
    private String proxyName;

    @ApiModelProperty("业绩")
    @TableField(exist = false)
    private String performanceMoney;

    @ApiModelProperty(notes = "合订单促销单是否可以编辑")
    @TableField(exist = false)
    private boolean promotionalEditFlag = Boolean.FALSE;

    /**
     * 鲜奶退订数量
     */
    @LogTag(alias = " 鲜奶退订数量")
    @ApiModelProperty(" 鲜奶退订信息")
    @ExcelProperty(" 鲜奶退订数量")
    private Integer freshMilkReturnQuantity;

    @ApiModelProperty(" 支付状态")
    private Integer payStatus;

    // 完善订单入口
    @ApiModelProperty(name = "订单完善状态")
    private Integer perfectStatus;

    /**
     * 合订单客户信息
     */
    @ApiModelProperty(" 合订单客户信息")
    @TableField(typeHandler = TypeHandlerConstant.ContractOrderCustomerInfoTypeHandler.class)
    private List<ContractOrderCustomerInfo> contractOrderExt;

    /**
     * 支付时间
     */
    @LogTag(alias = "支付时间")
    @ApiModelProperty("支付时间")
    private Date payTime;

    @LogTag(alias = "合订单总金额")
    @ApiModelProperty("合订单总金额")
    private BigDecimal contractTotalAmount;

    @LogTag(alias = "合订单总订购数量")
    @ApiModelProperty("合订单总订购数量")
    private Integer contractTotalOrderQuantity;

    @LogTag(alias = "备注")
    @ApiModelProperty("remark")
    @ExcelProperty("备注")
    private String remark;

    @TableField(exist = false)
    private String showAuditMark;


    @ApiModelProperty("换单商品兑换id")
    private Long exchangeProductId;

    @ApiModelProperty("换单兑换商品规格")
    private String spec;

    @LogTag(alias = "兑换数量")
    @ApiModelProperty("换单兑换数量")
    @ExcelProperty("兑换数量")
    private Integer exchangeSum;

    @ApiModelProperty("换单鲜奶兑换商品数量")
    @ExcelProperty("鲜奶兑换数量")
    private Integer milkExchangeSum;

    @ApiModelProperty("换单商品名称")
    private String exchangeProductName;

    @ApiModelProperty("是否导入订单")
    private Boolean isImport;

    @ApiModelProperty("活动赠品数量")
    @ExcelProperty("活动礼品数量")
    private Integer activityGiveQuantity;

    @ApiModelProperty("活动赠品已送数量")
    private Integer activitySentQuantity;

    @ApiModelProperty("客户id")
    private Long customerId;

    @ApiModelProperty("活动类型")
    private String activityType;

    @ApiModelProperty("第三方订单号")
    private String thirdOrderNo;

    @ApiModelProperty("第三方订单号生成时间")
    private LocalDateTime thirdOrderNoTime;

    @ApiModelProperty("是否新客户")
    private String newCustomerFlag;

    @ApiModelProperty("常温奶转出订单id")
    private String outLongMilkOrderNo;

    @ApiModelProperty("活动id")
    private Long activityId;

    @ApiModelProperty("活动名称")
    @ExcelProperty("活动名称")
    private String activityName;

    @ApiModelProperty("活动对象")
    private String giveGiftList;

    @ApiModelProperty("套餐价格")
    @ExcelProperty("套餐金额")
    private BigDecimal setMealPrice;

    @ApiModelProperty("套餐数量")
    private Integer setMealQty;

    @ApiModelProperty("分摊费用")
    @ExcelProperty("活动分摊费用")
    private BigDecimal apportionMoney;

    /**
     * 订单赠送数是否参与增订单核算
     */
    private Boolean pigcFlag;

    /**
     * audit_count
     */
    private Integer auditCount;

    /**
     * 合订单的订单类型标签
     */
    private Short contractTypeTag;

    /**
     * 合订单的订单类型标签
     */
    private Short coType;

    /**
     * 支付的第三方平台 0收钱吧 1富友
     */
    private Integer payPlaformType;

    /**
     * 扫付款二维码次数
     */
    private Integer scanQrTimes;


    /**
     * 打款标识
     */
    @ApiModelProperty("打款标识")
    private Boolean refundSuccessFlag;

    /**
     * 录单人编号
     */
    private String createByCode;

    /**
     * 退订单订购金额
     */
    @ApiModelProperty("退订单订购金额")
    @TableField(exist = false)
    private BigDecimal orderMoney;

    /**
     * 退订原因编号
     */
    @ApiModelProperty("退订原因编号")
    @TableField(exist = false)
    private String rrCode;


    /**
     * 售后单id
     */
    @ApiModelProperty(value = "售后单id")
    @TableField(exist = false)
    private Long afterSalesId;


    /**
     * 售后单编号
     */
    @ApiModelProperty(value = "售后单编号")
    @TableField(exist = false)
    private String afterSalesNo;

    @ApiModelProperty(value = "打款状态:0.未打款、1.退款中,2.已打款、3.打款失败")
    private Integer paymentStatus;

    @ApiModelProperty(value = "打款失败原因")
    private String paymentFailureReasons;

    @ApiModelProperty(value = "支付订单id")
    private Long payOrderId;

    private Integer channel;

    private String productType;

    private String productName;

    private Integer accountingType;

    private Integer promotionCommission;

    private Long commissionConfigId;

    /**
     * 退款异常
     */
    @TableField(exist = false)
    private Integer tiktokOrderRefundException;

    private String tiktokOrderRefundNo;

    /**
     * 地址来源
     */
    @TableField(exist = false)
    private String addressCreateByType;


    /**
     * 订单分配标识
     */
    @TableField(exist = false)
    private String determineTemplateTag;


    @ApiModelProperty(value = "关联抖音单号")
    @TableField(exist = false)
    private String linkedDouyinNumber;

    @ApiModelProperty(value = "商品分类名称")
    private String productTypeName;

    @ApiModelProperty(value = "异常订单状态  1：异常")
    private Integer abnormalTag;

    @ApiModelProperty("配送站点")
    private String deliverySites;

    @ApiModelProperty("退款审核不通过原因")
    private String refundNoAuditReasons;

    @ApiModelProperty(value = "配送站点")
    @TableField(exist = false)
    private String deliverySite;

    @ApiModelProperty(value = "配送站点ID")
    @TableField(exist = false)
    private Long deliverySiteId;


}
