package com.ruoyi.business.base.customer.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.business.base.api.domain.CxrCustomer;
import com.ruoyi.business.base.api.domain.CxrCustomerChangeRecord;
import com.ruoyi.business.base.api.domain.bo.CxrAddressBo;
import com.ruoyi.business.base.api.domain.dto.MallUserInfoDTO;
import com.ruoyi.business.base.api.domain.vo.CxrAddressHistoryVo;
import com.ruoyi.business.base.api.domain.vo.CxrLabeleDTO;
import com.ruoyi.business.base.api.model.CustomerDistributioninfoDTO;
import com.ruoyi.business.base.customer.domain.bo.CustomerLabeledBo;
import com.ruoyi.business.base.customer.domain.bo.CxrCustomerAddressABo;
import com.ruoyi.business.base.customer.domain.bo.CxrCustomerBo;
import com.ruoyi.business.base.customer.domain.bo.MilkDistributionDetailedBo;
import com.ruoyi.business.base.customer.domain.vo.CxrCustomerCountVo;
import com.ruoyi.business.base.customer.domain.vo.CxrCustomerListVo;
import com.ruoyi.business.base.customer.domain.vo.CxrCustomerTransferMilkDetail;
import com.ruoyi.business.base.customer.domain.vo.CxrCustomerVo;
import com.ruoyi.business.base.customerAddress.domain.bo.CxrCustomerAddrBo;
import com.ruoyi.business.base.customerAddress.domain.vo.CxrCustomerAddrVo;
import com.ruoyi.business.base.customerDistributionListRecord.domain.CxrCustomerDistributionListRecord;
import com.ruoyi.business.base.cxrAddressHistory.domain.CxrAddressHistoryBo;
import com.ruoyi.business.base.cxrCustomerAddressHistory.domain.CxrCustomerAddressHistory;
import com.ruoyi.business.base.cxrLabeled.domain.bo.CxrLabeledBo;
import com.ruoyi.business.base.cxrLabeled.domain.vo.CxrLabeledVo;
import com.ruoyi.business.base.roadWay.domain.bo.RoadWayTotalBo;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.mybatis.core.page.PageQuery;
import com.ruoyi.common.mybatis.core.page.PageTableDataInfo;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.ibatis.annotations.Param;

/**
 * 客户Service接口
 *
 * <AUTHOR>
 * @date 2022-07-02
 */
public interface ICxrCustomerService extends IService<CxrCustomer> {


    /**
     * 分页查询《客户》
     *
     * @param cxrCustomerBo 实例化Bo对象封装的查询参数
     * @param pageQuery     分页参数
     * @return
     */
    PageTableDataInfo<CxrCustomerListVo> myPage(CxrCustomerBo cxrCustomerBo, PageQuery pageQuery);


    /**
     * 详细查询《客户》
     *
     * @param id 主键
     * @return
     */
    CxrCustomerCountVo detail(Long id);


    /**
     * 添加数据《客户》
     *
     * @param cxrCustomerBo 实例化Bo对象封装的添加参数
     * @return
     */
    Boolean add(CxrCustomerBo cxrCustomerBo);

    /**
     * 添加地址
     *
     * @param bo
     * @return
     */
    Boolean addressAdd(CxrCustomerAddrBo bo);

    /**
     * 校验客户名称是否唯一
     *
     * @param cxrCustomerBo 客户信息
     * @return 结果
     */
    String checkCustomerPhoneUnique(CxrCustomerBo cxrCustomerBo);

//
//    /**
//     * 修改客户配送地址
//     *
//     * @param cxrCustomerBo 实例化Bo对象封装的修改参数
//     * @return
//     */
//    Boolean edit(CxrCustomerBo cxrCustomerBo);


    boolean editAdress(CxrCustomerAddrBo cxrCustomerAddrBo);


    Boolean editCompleteInformation(CxrCustomerBo cxrCustomerBo);

    /**
     * 删除客户地址信息
     *
     * @param idSet 主键集合
     * @return
     */
    Boolean remove(Set<Long> idSet);

    /**
     * 客户手机端登录
     *
     * @param phone
     * @return
     */
    CxrCustomerVo customerLogin(String phone);
    CxrCustomerVo customerLogin(Long id);

    /**
     * 通过openid查询客户信息
     *
     * @param wxOpenid
     * @return
     */
    CxrCustomer selectByWxOpenid(@Param("wxOpenid") String wxOpenid);

    /**
     * 通过openid更新客户微信头像和昵称
     *
     * @param wxOpenId
     * @param wxNickname
     * @param wxHeadPortrait
     * @return
     */
    boolean updateByWxOpenid(@Param("wxOpenId") String wxOpenId, @Param("wxNickname") String wxNickname,
        @Param("wxHeadPortrait") String wxHeadPortrait);


    /**
     * 通过openid更新客户微信头像和昵称  UnionId
     *
     * @param wxOpenId
     * @param wxNickname
     * @param wxHeadPortrait
     * @return
     */
    boolean updateUnionIDByWxOpenid(@Param("wxOpenId") String wxOpenId, @Param("wxNickname") String wxNickname,
        @Param("wxHeadPortrait") String wxHeadPortrait, @Param("wxUnionid") String wxUnionid);


    /**
     * 通过手机号更新客户微信头像、昵称、openid
     *
     * @param phone
     * @param wxOpenId
     * @param wxNickname
     * @param wxHeadPortrait
     * @return
     */
    boolean updateOpenidByPhone(@Param("phone") String phone, @Param("wxOpenId") String wxOpenId,
        @Param("wxNickname") String wxNickname,
        @Param("wxHeadPortrait") String wxHeadPortrait);


    boolean updateUnionOpenidByPhone(@Param("phone") String phone, @Param("wxOpenId") String wxOpenId,
        @Param("wxNickname") String wxNickname, @Param("wxHeadPortrait") String wxHeadPortrait,
        @Param("wxUnionid") String wxUnionid);

    /**
     * 重载    根据手机号更新 客户openId
     *
     * @param phone
     * @param wxOpenId
     * @return
     */
    boolean updateOpenidByPhone(String phone, String wxOpenId);


    /**
     * 通过手机号 更新客户名称
     *
     * @param phone
     * @param name
     * @return
     */
    boolean updateName(@Param("phone") String phone, @Param("name") String name);

    /**
     * 通过手机号 更新客户库存
     *
     * @param phone
     * @param stock
     * @return
     */
    boolean updateStock(@Param("phone") String phone, @Param("stock") Integer stock);

    /**
     * 检查客户手机号是否存在
     *
     * @param customerPhone
     * @return
     */
    boolean checkExistCustomerPhone(String customerPhone);

    /**
     * 根据手机号查询客户信息
     *
     * @return
     */
    CxrCustomer queryByPhone(String phone);

    /**
     * 更新主账户信息
     *
     * @param distributioninfoDTO
     */
    Boolean updateMainAccount(CustomerDistributioninfoDTO distributioninfoDTO);


    /**
     * 客户管理分页    YHJ  22-09-23
     *
     * @param bo
     * @return
     */
    PageTableDataInfo<CxrCustomerListVo> customerPage(CxrCustomerAddressABo bo);


    /**
     * 地址详细信息
     *
     * @param id
     * @return
     */
    CxrCustomerAddrVo adressDetail(Long id);

    /**
     * 解绑
     *
     * @param id
     * @return
     */
    boolean removeBanding(Long id,Integer flag);

    Object milkDistributionDetailed(MilkDistributionDetailedBo bo);
    Object customerStockInfo();


    /***
     * 排奶
     * @param bos
     * @return
     */
    boolean milkDistribution(List<CxrCustomerAddrBo> bos);

    /**
     * 地址排奶信息  和地址的站点售卖商品
     *
     * @return
     */
    List<CxrCustomerAddrVo> milkDistributionDetail(Set<Long> id);

    /**
     * 根据客户id查询  地址  和站点售卖商品
     *
     * @param id
     * @return
     */
    List<CxrCustomerAddrVo> allMilkDistributionDetail(Long id);


    /**
     * 统计每天送奶量
     *
     * @param bo
     * @return
     */
    List<CxrCustomerDistributionListRecord> selectEveryDayMilkTotal(RoadWayTotalBo bo);

    /**
     * 统计一年的奶
     *
     * @return
     */
    Map<String, Object> selectYearMilkTotal(Long id, Date year);


    List<CxrAddressHistoryVo> queryHistory(CxrCustomerAddressABo bo);

    /**
     * 新增客户地址
     *
     * @param bo
     * @return
     */
    Long customerAdd(CxrCustomerAddrBo bo);

//
//    /**
//     * 根据客户id  获取客户订购的全部年份
//     *
//     * @param id
//     * @return
//     */
//    List<String> getDate(Long id);


    PageTableDataInfo<CxrAddressHistoryVo> findByStaffUserId(CxrAddressHistoryBo bo);


    List<CxrCustomerAddressHistory> queryListHistoryByIdAndDate(Long id, Date updateTime);


    CxrAddressHistoryVo getHistoryById(Long id);

    Object createLabeled(CustomerLabeledBo bo);

    PageTableDataInfo<CxrLabeledVo> labeledPage(CxrLabeledBo bo, PageQuery pageQuery);

    Boolean updateCustomerRemark(String remark, Long customerId);

    Boolean updateCustomerPhone(String phone, Long customerId,boolean unBind);
    R updateCustomerPhone(String phone, Long customerId, Long existCustomerId);


    Integer updateCustomerName(String name, Long customerId);

    void checkUpdataChangeCustomer(Long id);

    int updateChangeMilkChangeRecord(CxrAddressBo bo);

    Page<CxrCustomer> selectCustomerWarningJobUpdateExpirationTime(IPage page);

    CxrCustomerTransferMilkDetail queryCustomerTransferById(Long customerId);

    boolean del(Collection<Long> ids);

    Integer checkStock();

    void syncUpdateCustomerTag(List<CxrLabeleDTO> newLabeleList, CxrCustomer cxrCustomer);


    Long queryActualStock(Long customerId);

    /**
     * 小程序更新头像时，根据 unionId 同步更新客户端客户头像 昵称
     *
     * @param mallUserInfoDTO
     * @return
     */
    boolean updateCustomerInfoByWxUnionId(MallUserInfoDTO mallUserInfoDTO);

    boolean saveCxrCustomerChangeRecordDemo(CxrCustomerChangeRecord customerChangeRecord);

    void addCustomerLabeled(Long customerId, String labeledName);

    void delCustomerLabeled(Long customerId, String labeledName);

    CxrCustomer selectById(Long id);

    int updateWxById(String openId, String unionId, Long customerId);
}
