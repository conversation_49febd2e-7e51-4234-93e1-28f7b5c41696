package com.ruoyi.business.cxrEmployeeDistributionTransfer.service;

import com.alibaba.fastjson.JSONObject;
import com.github.yulichang.base.MPJBaseService;
import com.ruoyi.business.api.domain.bo.CxrEmployeeBo;
import com.ruoyi.business.base.api.domain.CxrSite;
import com.ruoyi.business.cxrEmployeeDimissionApply.domain.vo.CxrEmployeeListVo;
import com.ruoyi.business.cxrEmployeeDistributionTransfer.domain.CxrEmployeeDistributionTransfer;
import com.ruoyi.business.cxrEmployeeDistributionTransfer.domain.bo.*;
import com.ruoyi.business.cxrEmployeeDistributionTransfer.domain.vo.CxrEmployeeDistributionTransferListVo;
import com.ruoyi.business.cxrEmployeeDistributionTransfer.domain.vo.CxrEmployeeDistributionTransferVo;
import com.ruoyi.business.cxrEmployeeDistributionTransfer.domain.vo.EmployeeDistributionTransferVo;
import com.ruoyi.business.cxrEmployeeDistributionTransfer.domain.vo.MilkDistributionVo;
import com.ruoyi.common.mybatis.core.page.PageQuery;
import com.ruoyi.common.mybatis.core.page.PageTableDataInfo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 路条转拨Service接口
 *
 * <AUTHOR>
 * @date 2022-09-17
 */
public interface IStaffCxrEmployeeDistributionTransferService extends MPJBaseService<CxrEmployeeDistributionTransfer> {


    /**
     * 分页查询《路条转拨》
     *
     * @param cxrEmployeeDistributionTransferBo 实例化Bo对象封装的查询参数
     * @param pageQuery                         分页参数
     * @return
     */
    PageTableDataInfo<CxrEmployeeDistributionTransferListVo> page(
        CxrEmployeeDistributionTransferBo cxrEmployeeDistributionTransferBo, PageQuery pageQuery);


    /**
     * 详细查询《路条转拨》
     *
     * @param id 主键
     * @return
     */
    EmployeeDistributionTransferVo detail(Long id);


    /**
     * 删除数据《路条转拨》
     *
     * @param idSet 主键集合
     * @return
     */
    Boolean remove(Set<Long> idSet);

    PageTableDataInfo<CxrEmployeeDistributionTransferListVo> managePage(
        CxrEmployeeDistributionTransferBo cxrEmployeeDistributionTransferBo, PageQuery pageQuery);

    com.ruoyi.common.core.domain.PageTableDataInfo<CxrEmployeeListVo> employeeLisPage(CxrEmployeeBo cxrEmployeeBo,
                                                                                      com.ruoyi.common.core.domain.PageQuery pageQuery);

    Object employeePathPage(RollOutBo bo, PageQuery pageQuery);


    /**
     * 批量转播
     *
     * @param bo
     * @return
     */
    Map<String, Object> allDistributionTransfer(CxrEmployeeDistributionTransferBo bo);


    Boolean staffAdd(DistributionTransferAddEditBo distributionTransferBo);

    Boolean staffedit(DistributionTransferAddEditBo distributionTransferBo);

    MilkDistributionVo distributionDetail(Long addressId, Long enterSiteId);

    Boolean milkDistributionUpdate(MilkDistributionBo milkDistributionBo);

    Boolean staffOptionPath(JSONObject jsonObject);

    Object selectOptionPath(RollOutBo bo);

    Boolean delOptionPath(List<Long> id);

    EmployeeDistributionTransferVo staffApplyDetail(Long id);

    Boolean applyPass(Long id);

    Boolean applyRefuse(Long id);

    /**
     * 审核数据 分页查询
     *
     * @param bo
     * @return
     */
    PageTableDataInfo<CxrEmployeeDistributionTransferVo> TransferPage(CxrTransferPageBo bo);

    PageTableDataInfo<CxrSite> getUserSite(String siteName, String state, PageQuery pageQuery);

    /**
     * 批量修改
     *
     * @param bo
     * @return
     */
    boolean allEdit(CxrTransferPageBo bo);

    Boolean delBatchMiddleIds();

    void distributionTransferMiddle(TransferMiddleMqBo middleMqBo);
}
