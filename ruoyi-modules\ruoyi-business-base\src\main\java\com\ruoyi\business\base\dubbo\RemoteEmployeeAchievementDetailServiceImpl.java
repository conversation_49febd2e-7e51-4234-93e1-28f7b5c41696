package com.ruoyi.business.base.dubbo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.ruoyi.business.api.domain.dto.ViolationCustomerMatchesRecordsAddDTO;
import com.ruoyi.business.base.api.domain.CxrEmployeeAchievementDetail;
import com.ruoyi.business.base.api.domain.CxrEmployeePost;
import com.ruoyi.business.base.api.domain.bo.CxrChannelCommissionConfigBo;
import com.ruoyi.business.base.api.domain.dto.AchievementDetailUpdateDto;
import com.ruoyi.business.base.api.domain.dto.DailyIntergerDto;
import com.ruoyi.business.base.api.domain.dto.DailyPerformanceDto;
import com.ruoyi.business.base.api.domain.dto.PerformanceDayDTO;
import com.ruoyi.business.base.api.domain.dto.RecordData;
import com.ruoyi.business.base.api.dubbo.RemoteEmployeeAchievementDetailService;
import com.ruoyi.business.base.employeeAchievementDetail.mapper.CxrEmployeeAchievementDetailMapper;
import com.ruoyi.business.base.employeeAchievementDetail.service.ICxrEmployeeAchievementDetailService;
import com.ruoyi.business.base.orderAfterSale.mapper.CxrUserOrderMapper;
import com.ruoyi.common.core.enums.DeleteStatus;
import com.ruoyi.common.core.validator.AbstractAssert;
import com.ruoyi.common.rocketmq.calculate.CalculateConstant;
import com.ruoyi.common.rocketmq.constant.ViolationCustomerMatchesRecordsConstant;
import com.ruoyi.common.rocketmq.constant.employee.MqDayPerformanceConst;
import com.ruoyi.common.rocketmq.utils.MqUtil;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>Description: </p>
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/8/10 18:01
 **/
@Slf4j
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteEmployeeAchievementDetailServiceImpl implements RemoteEmployeeAchievementDetailService {

    private final ICxrEmployeeAchievementDetailService cxrEmployeeAchievementDetailService;

    private final CxrEmployeeAchievementDetailMapper baseMapper;

    private final CxrUserOrderMapper customerMapper;

    private final MqUtil mqUtil;

    /**
     * 添加业绩
     *
     * @param cxrEmployeeAchievementDetail
     * @return
     */
    @Override
    public boolean add(List<CxrEmployeeAchievementDetail> cxrEmployeeAchievementDetail) {
        return cxrEmployeeAchievementDetailService.saveBatch(cxrEmployeeAchievementDetail);
    }

    @Override
    public Boolean batchUpdateOrderFmQty(List<AchievementDetailUpdateDto> cxrEmployeeAchievementDetail) {
        if (CollUtil.isNotEmpty(cxrEmployeeAchievementDetail)) {
            return baseMapper.batchUpdateOrderFmQty(cxrEmployeeAchievementDetail) > 1;
        }
        return false;
    }

    @Override
//    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean achievemenDiscard(Long sourceId, String remark) {
        List<CxrEmployeeAchievementDetail> cxrEmployeeAchievementDetail =
            cxrEmployeeAchievementDetailService.list(new LambdaQueryWrapper<CxrEmployeeAchievementDetail>()
                .eq(CxrEmployeeAchievementDetail::getSouceId, sourceId)
                .eq(CxrEmployeeAchievementDetail::getDeleteStatus,  DeleteStatus.NOT_DELETED.getValue())
            );
        boolean update = cxrEmployeeAchievementDetailService.update(
            new LambdaUpdateWrapper<CxrEmployeeAchievementDetail>()
                .set(CxrEmployeeAchievementDetail::getDeleteStatus, DeleteStatus.DELETED.getValue())
                .set(CxrEmployeeAchievementDetail::getDeleteTime, new Date())
                .set(CxrEmployeeAchievementDetail::getRemark, remark)
                .eq(CxrEmployeeAchievementDetail::getSouceId, sourceId)
                .eq(CxrEmployeeAchievementDetail::getDeleteStatus,  DeleteStatus.NOT_DELETED.getValue()));
        if (update) {
            achievemenDiscardMq(cxrEmployeeAchievementDetail);
        }
        return update;
    }

    @Override
//    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean achievemenDiscard(List<Long> sourceIds, String remark) {
        List<CxrEmployeeAchievementDetail> cxrEmployeeAchievementDetailList =
            cxrEmployeeAchievementDetailService.list(new LambdaQueryWrapper<CxrEmployeeAchievementDetail>()
                .in(CxrEmployeeAchievementDetail::getSouceId, sourceIds)
                .eq(CxrEmployeeAchievementDetail::getDeleteStatus,  DeleteStatus.NOT_DELETED.getValue())
            );
        boolean update = cxrEmployeeAchievementDetailService.update(
            new LambdaUpdateWrapper<CxrEmployeeAchievementDetail>()
                .set(CxrEmployeeAchievementDetail::getDeleteStatus, DeleteStatus.DELETED.getValue())
                .set(CxrEmployeeAchievementDetail::getDeleteTime, new Date())
                .set(CxrEmployeeAchievementDetail::getRemark, remark)
                .eq(CxrEmployeeAchievementDetail::getDeleteStatus,  DeleteStatus.NOT_DELETED.getValue())
                .in(CxrEmployeeAchievementDetail::getSouceId, sourceIds));
        if (update) {
            achievemenDiscardMq(cxrEmployeeAchievementDetailList);
        }
        return update;
    }

    /**
     * 是否 存在订单id
     *
     * @param cxrUserOrderId
     */
    @Override
    public Boolean existsDetail(Long cxrUserOrderId) {
        boolean exists =
            cxrEmployeeAchievementDetailService.getBaseMapper()
                .exists(new LambdaUpdateWrapper<CxrEmployeeAchievementDetail>()
                    .eq(CxrEmployeeAchievementDetail::getSouceId, cxrUserOrderId)
                    .eq(CxrEmployeeAchievementDetail::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                );

        return exists;
    }

    @Override
    public BigDecimal selectSaleAchievement(LocalDate start, LocalDate end, Long employeeId) {
        BigDecimal achievement = baseMapper.sumMonthAchievement(start, end, employeeId);
        return achievement == null ? BigDecimal.ZERO : achievement;
    }

    @Override
    public BigDecimal sumSiteMonthAchievement(LocalDate start, LocalDate end, Long siteId) {
        BigDecimal achievement = baseMapper.sumSiteMonthAchievement(start, end, siteId);
        return achievement == null ? BigDecimal.ZERO : achievement;
    }

    @Override
    public BigDecimal sumSiteMonthAchievement(List<CxrEmployeePost> cxrEmployeePosts) {
        BigDecimal achievement = baseMapper.sumSiteMonthAchievementFore(cxrEmployeePosts);
        return achievement == null ? BigDecimal.ZERO : achievement;
    }


    @Override
    public List<DailyPerformanceDto> selectSaleAchievement(List<Long> orderIds, Long employeeId) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return new ArrayList<>();
        }
        return baseMapper.selectSaleAchievement(orderIds, employeeId);
    }

    @Override
    public DailyPerformanceDto sumPerformanceEveryDay(LocalDate now, Long cxrEmployeeId) {
        return baseMapper.sumPerformanceEveryDay(now, DeleteStatus.NOT_DELETED.getValue(), cxrEmployeeId);
    }

    @Override
    public DailyIntergerDto sumboxsEveryDay(LocalDate now, Long cxrEmployeeId) {
        return baseMapper.sumboxsEveryDay(now, DeleteStatus.NOT_DELETED.getValue(), cxrEmployeeId);
    }

    @Override
    public List<DailyIntergerDto> sumboxsEveryDayGroupSite(LocalDate now) {
        return baseMapper.sumboxsEveryDayGroupSite(now, DeleteStatus.NOT_DELETED.getValue());
    }


    @Override
    public List<DailyIntergerDto> countPerByDateGroupSite(LocalDate now, List<Long> ids) {

        return baseMapper.sumPerformanceByDayGroupSite(now, DeleteStatus.NOT_DELETED.getValue(), ids);
    }


    @Override
    public boolean deleteAchievementByOrderIds(List<Long> s) {

        return new LambdaUpdateChainWrapper<>(baseMapper)
            .in(CxrEmployeeAchievementDetail::getSouceId, s)
            .eq(CxrEmployeeAchievementDetail::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            .set(CxrEmployeeAchievementDetail::getDeleteStatus, DeleteStatus.DELETED.getValue())
            .update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dailySiteAchievement(Long sourceId) {
        List<CxrEmployeeAchievementDetail> cxrEmployeeAchievementDetails = this.baseMapper.selectList(
            new LambdaQueryWrapper<CxrEmployeeAchievementDetail>()
                .select(CxrEmployeeAchievementDetail::getOrderDate,CxrEmployeeAchievementDetail::getSiteId)
                .eq(CxrEmployeeAchievementDetail::getSouceId, sourceId));
        if (CollectionUtils.isNotEmpty(cxrEmployeeAchievementDetails)) {
            Collection<CxrEmployeeAchievementDetail> uniqueList = cxrEmployeeAchievementDetails.stream()
                .collect(Collectors.toMap(
                    d -> StrUtil.format("{}_{}", d.getOrderDate(), d.getSiteId()),
                    d -> d, (existing, replacement) -> existing)).values();
            baseMapper.delByDate(uniqueList);
            baseMapper.saveByDate(JSONObject.toJSONString(uniqueList));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void dailySiteAchievement(Long siteId,LocalDate date) {
        baseMapper.delBySiteDate(siteId, date);
        baseMapper.saveBySiteDate(siteId, date);
    }

    public void dailySiteAchievementAll(LocalDate date) {
        baseMapper.saveOrUpdateAll(date);
    }

    public void saveOrUpdateBySiteId(RecordData recordData) {

        JSONObject before = recordData.getBefore();
        JSONObject after = recordData.getAfter();

        if (after != null && before != null) {
            CxrEmployeeAchievementDetail afterAchievement = BeanUtil.toBean(after, CxrEmployeeAchievementDetail.class);
            CxrEmployeeAchievementDetail beforeAchievement = BeanUtil.toBean(before,
                CxrEmployeeAchievementDetail.class);

            if (afterAchievement.getOrderDate().isEqual(beforeAchievement.getOrderDate())
                && afterAchievement.getSiteId().equals(beforeAchievement.getSiteId())) {
                baseMapper.saveOrUpdateBySiteId(afterAchievement.getOrderDate(), afterAchievement.getSiteId());
            } else {
                baseMapper.saveOrUpdateBySiteId(beforeAchievement.getOrderDate(), beforeAchievement.getSiteId());
                baseMapper.saveOrUpdateBySiteId(afterAchievement.getOrderDate(), afterAchievement.getSiteId());
            }
        } else if (after != null) {
            CxrEmployeeAchievementDetail afterAchievement = BeanUtil.toBean(after, CxrEmployeeAchievementDetail.class);
            baseMapper.saveOrUpdateBySiteId(afterAchievement.getOrderDate(), afterAchievement.getSiteId());
        } else if (before != null) {
            CxrEmployeeAchievementDetail beforeAchievement = BeanUtil.toBean(before,
                CxrEmployeeAchievementDetail.class);
            baseMapper.saveOrUpdateBySiteId(beforeAchievement.getOrderDate(), beforeAchievement.getSiteId());
        }
    }

    @Override
    public Map<Long, List<Long>> queryNotDeletedAchievementOrderId() {
        LambdaQueryWrapper<CxrEmployeeAchievementDetail> queryWrapper =
            new LambdaQueryWrapper<CxrEmployeeAchievementDetail>().eq(CxrEmployeeAchievementDetail::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .isNull(CxrEmployeeAchievementDetail::getOrderFmQty).last(" LIMIT 200 ").select(CxrEmployeeAchievementDetail::getSouceId, CxrEmployeeAchievementDetail::getEmployeeId);
        List<CxrEmployeeAchievementDetail> achievementDetails = baseMapper.selectList(queryWrapper);
        if (CollUtil.isNotEmpty(achievementDetails)) {
            return achievementDetails.stream().collect(Collectors.groupingBy(CxrEmployeeAchievementDetail::getSouceId, Collectors.mapping(CxrEmployeeAchievementDetail::getEmployeeId, Collectors.toList())));
        }
        return null;
    }

    @Override
    public void updateChannelCommission(CxrChannelCommissionConfigBo bo) {
        LocalDate firstDay = bo.getCreateTime().toLocalDate().with(TemporalAdjusters.firstDayOfMonth());
        boolean update = cxrEmployeeAchievementDetailService.lambdaUpdate()
            .set(CxrEmployeeAchievementDetail::getCommissionConfigId,bo.getId())
            .set(CxrEmployeeAchievementDetail::getAccountingType,bo.getAccountingType())
            .set(CxrEmployeeAchievementDetail::getPromotionCommission,bo.getPromotionCommissionFlag())
            .ge(CxrEmployeeAchievementDetail::getOrderDate,firstDay)
            .lt(CxrEmployeeAchievementDetail::getOrderDate,firstDay.plusMonths(1))
            .eq(CxrEmployeeAchievementDetail::getChannel,bo.getChannel())
            .eq(CxrEmployeeAchievementDetail::getProductType,bo.getProductType())
            .update();
        AbstractAssert.isTrue(!update,"修改失败了!!!");
    }

    private void achievemenDiscardMq(List<CxrEmployeeAchievementDetail> cxrEmployeeAchievementDetailList) {
        if (CollectionUtils.isEmpty(cxrEmployeeAchievementDetailList)) {
            return;
        }
        for (CxrEmployeeAchievementDetail cxrEmployeeAchievementDetail : cxrEmployeeAchievementDetailList) {
            PerformanceDayDTO dto = new PerformanceDayDTO();
            // 直接解析日期为LocalDate，避免中间转换
//            LocalDate localDate;
//            try {
//                localDate = DateUtil.parse(DateUtil.format(userOrder.getOrderDate(),
//                    DatePattern.NORM_DATE_PATTERN)).toSqlDate().toLocalDate();
//            } catch (Exception e) {
//                // 处理日期解析异常
//                log.error("Failed to parse date: {}", userOrder.getOrderDate(), e);
//                continue;
//            }
            dto.setCountDay(cxrEmployeeAchievementDetail.getOrderDate());
            // 获取businessAgent JSON字符串
            // 设置siteId
            dto.setSiteId(cxrEmployeeAchievementDetail.getSiteId());
            dto.setCurrentDate(new Date());
            dto.setEmployeeId(cxrEmployeeAchievementDetail.getEmployeeId());
            // 发送延迟消息
            mqUtil.sendDelayMessage(
                MqDayPerformanceConst.SALE_DAY_PERFORMANCE_TOPIC,
                MqDayPerformanceConst.SALE_DAY_INCREMENT_MODIFICATION_PERFORMANCE_TAG,
                JSONUtil.toJsonStr(dto),
                Duration.ofSeconds(5)
            );
            mqUtil.sendDelayMessage(ViolationCustomerMatchesRecordsConstant.VIOLATION_CUSTOMER_MATCHES_RECORDS_TOPIC,
                ViolationCustomerMatchesRecordsConstant.VIOLATION_CUSTOMER_MATCHES_RECORDS_TAG,
                JSONUtil.toJsonStr(ViolationCustomerMatchesRecordsAddDTO.builder().triggerType(1).time(new Date()).orderId(cxrEmployeeAchievementDetail.getSouceId()).build()),Duration.ofSeconds(30));
//            List<BusinessAgent> businessAgent = JSONArray.parseArray(userOrder.getBusinessAgent(), BusinessAgent.class);
//            for (BusinessAgent agent : businessAgent) {
//
//            }

            mqUtil.sendDelayMessage(CalculateConstant.CALCULATE_TOPIC, CalculateConstant.CALCULATE_DAILY_SITE_ACHIEVEMENT_TAG, cxrEmployeeAchievementDetail.getSouceId().toString(), 20);
        }
    }
}
