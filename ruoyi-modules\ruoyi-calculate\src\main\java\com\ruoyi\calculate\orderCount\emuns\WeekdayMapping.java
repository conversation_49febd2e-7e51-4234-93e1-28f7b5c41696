package com.ruoyi.calculate.orderCount.emuns;

import lombok.Getter;

/**
 * 英文星期到中文星期的枚举映射
 */
@Getter
public enum WeekdayMapping {
    MONDAY("MONDAY", "周一"),
    TUESDAY("TUESDAY", "周二"),
    WEDNESDAY("WEDNESDAY", "周三"),
    THURSDAY("THURSDAY", "周四"),
    FRIDAY("FRIDAY", "周五"),
    SATURDAY("SATURDAY", "周六"),
    SUNDAY("SUNDAY", "周日");

    private final String englishWeekday;
    private final String chineseWeekday;

    WeekdayMapping(String englishWeekday, String chineseWeekday) {
        this.englishWeekday = englishWeekday;
        this.chineseWeekday = chineseWeekday;
    }

    /**
     * 根据英文星期获取对应的中文星期
     * @param weekday 英文星期
     * @return 中文星期
     */
    public  String getChineseWeekday(String weekday) {
        for (WeekdayMapping mapping : WeekdayMapping.values()) {
            if (mapping.englishWeekday.equalsIgnoreCase(weekday)) {
                return mapping.getChineseWeekday();
            }
        }
        return "未知";
    }


}