package com.ruoyi.business.base.customer.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.business.base.api.domain.DeliverySiteDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 客户视图对象 cxr_customer
 *
 * <AUTHOR>
 * @date 2022-07-02
 */
@Data
@ApiModel("客户视图对象")
@ExcelIgnoreUnannotated
public class CxrCustomerListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    @ApiModelProperty("客户id")
    private Long id;

    /**
     * 客户姓名
     */
    @ExcelProperty(value = "客户姓名")
    @ApiModelProperty("客户姓名")
    private String name;

    /**
     * 配送员姓名
     */
    @ExcelProperty(value = "配送员姓名")
    @ApiModelProperty("配送员姓名")
    private String employeeName;

    /**
     * 地区名称
     */
    @ExcelProperty(value = "地区名称")
    @ApiModelProperty("地区名称")
    private String mergename;

    /**
     * 客户地址id
     */
    @ApiModelProperty("客户地址id")
    @ExcelIgnore
    private Long cxrCustomerAddressId;

    /**
     * 收货人姓名
     */
    @ApiModelProperty("收货人姓名")
    @ExcelIgnore
    private String receiverName;

    /**
     * 收货人电话
     */
    @ApiModelProperty("收货人电话")
    @ExcelIgnore
    private String receiverPhone;

    /**
     * 站点名称
     */
    @ExcelProperty(value = "站点名称")
    @ApiModelProperty("站点名称")
    private String siteName;

    /**
     * 站点详细地址
     */
    @ExcelProperty(value = "站点详细地址")
    @ApiModelProperty("站点详细地址")
    private String detailAddress;

    /**
     * 客户性别(详情见字典)
     */
    @ExcelProperty(value = "客户性别(详情见字典)")
    @ApiModelProperty("客户性别(详情见字典)")
    private String genderType;

    /**
     * 客户手机号码
     */
    @ExcelProperty(value = "客户手机号码")
    @ApiModelProperty("客户手机号码")
    private String phone;
    @ExcelProperty(value = "站点代号")
    @ApiModelProperty("站点代号")
    private String siteMark;

    /**
     * 客户生日
     */
    @ExcelProperty(value = "客户生日")
    @ApiModelProperty("客户生日")
    private String birthday;

    /**
     * 客户审核状态(详情见字典)
     */
    @ExcelProperty(value = "客户审核状态(详情见字典)")
    @ApiModelProperty("客户审核状态(详情见字典)")
    private String auditStatus;

    /**
     * 客户来源(详情见字典)
     */
    @ExcelProperty(value = "客户来源(详情见字典)")
    @ApiModelProperty("客户来源(详情见字典)")
    private String sourceType;

    /**
     * 微信openID
     */
    @ExcelProperty(value = "微信openID")
    @ApiModelProperty("微信openID")
    private String wxOpenId;

    /**
     * 微信unionID
     */
    @ExcelProperty(value = "微信unionID")
    @ApiModelProperty("微信unionID")
    private String wxUnionId;

    /**
     * 微信头像
     */
    @ExcelProperty(value = "微信头像")
    @ApiModelProperty("微信头像")
    private String wxHeadPortrait;

    /**
     * 用户库存
     */
    private Integer customerStock;

    /**
     * 微信昵称
     */
    @ExcelProperty(value = "微信昵称")
    @ApiModelProperty("微信昵称")
    private String wxNickname;

    /**
     * 部门id(公司id)
     */
    @ExcelProperty(value = "部门id(公司id)")
    @ApiModelProperty("部门id(公司id)")
    private Long sysDeptId;


    /**
     * 员工id
     */
    @ApiModelProperty(value = "员工id", required = true)
    private Long cxrEmployeeId;


    @ApiModelProperty(value = "员工等级")
    private String cxrEmployeeLevelType;


    @ApiModelProperty(value = "员工工号")
    private String cxrEmployeeJobNumber;


    /**
     * 站点id
     */
    @ApiModelProperty(value = "站点id", required = true)
    private Long cxrSiteId;
    /**
     * 乐观锁
     */
    @ExcelProperty(value = "乐观锁")
    @ApiModelProperty("乐观锁")
    private Long revision;

    /**
     * 创建人名称
     */
    @ExcelProperty(value = "创建人名称")
    @ApiModelProperty("创建人名称")
    private String createByName;

    /**
     * 创建人类型(详情见字典)
     */
    @ExcelProperty(value = "创建人类型(详情见字典)")
    @ApiModelProperty("创建人类型(详情见字典)")
    private String createByType;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    @ApiModelProperty("创建时间")
    private Date addressCreateTime;


    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人名称
     */
    @ExcelProperty(value = "更新人名称")
    @ApiModelProperty("更新人名称")
    private String updateByName;

    /**
     * 更新人类型(详情见字典)
     */
    @ExcelProperty(value = "更新人类型(详情见字典)")
    @ApiModelProperty("更新人类型(详情见字典)")
    private String updateByType;

    /**
     * 删除人id
     */
    @ExcelProperty(value = "删除人id")
    @ApiModelProperty("删除人id")
    private Long deleteBy;

    /**
     * 删除人名称
     */
    @ExcelProperty(value = "删除人名称")
    @ApiModelProperty("删除人名称")
    private String deleteByName;

    /**
     * 删除人类型(详情见字典)
     */
    @ExcelProperty(value = "删除人类型(详情见字典)")
    @ApiModelProperty("删除人类型(详情见字典)")
    private String deleteByType;

    /**
     * 删除时间
     */
    @ExcelProperty(value = "删除时间")
    @ApiModelProperty("删除时间")
    private Date deleteTime;

    /**
     * 删除状态(详情见字典)
     */
    @ExcelProperty(value = "删除状态(详情见字典)")
    @ApiModelProperty("删除状态(详情见字典)")
    private String deleteStatus;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序")
    @ApiModelProperty("排序")
    private Long sortNum;

    /**
     * 备用id
     */
    @ExcelProperty(value = "备用id")
    @ApiModelProperty("备用id")
    private Long spareId;

    /**
     * 省
     */
    @ApiModelProperty(value = "省")
    private String provice;

    /**
     * 市
     */
    @ApiModelProperty(value = "市")
    private String city;

    /**
     * 区
     */
    @ApiModelProperty(value = "区")
    private String area;


    /**
     * 大区名称
     */
    @ApiModelProperty("大区名称")
    private String bigAreaName;


    @ApiModelProperty(value = "小区名称")
    private String cxrResidentialQuartersName;


    /**
     * 客户详细地址
     */
    @ApiModelProperty(value = "客户详细地址")
    private String customerAdress;

    /**
     * 默认账户地址 Y 是 N否
     */
    @ApiModelProperty(value = "默认账户地址Y 是 N否")
    private String defalutAccountAddress;
    /**
     * 地区id(省市区地区表id)
     */
    @ApiModelProperty(value = "地区id(省市区地区表id)")
    private Long sysAreaId;

    /**
     * 鲜奶订购总数
     */
    @ApiModelProperty(name = "鲜奶订购总数", notes = "")
    private Integer freshMilkOrderTotal;//fresh_milk_order_total
    /**
     * 鲜奶赠送总数
     */
    @ApiModelProperty(name = "鲜奶赠送总数", notes = "")
    private Integer freshMilkGiveTotal;//fresh_milk_give_total
    /**
     * 鲜奶已送总数
     */
    @ApiModelProperty(name = "鲜奶已送总数", notes = "")
    private Integer freshMilkSentTotal;//fresh_milk_sent_total
    /**
     * 常温奶赠送总数
     */
    @ApiModelProperty(name = "常温奶赠送总数", notes = "")
    private Integer longMilkGiveTotal;//long_milk_give_total

    @ApiModelProperty("标签")
//    @TableField(typeHandler = TypeHandlerConstant.labeledJson.class)
    private String labeled;
    /**
     * 鲜奶路条已送总数量
     */
    @ApiModelProperty(name = "鲜奶路条已送总数量")
    private Integer freshMilkRoadWaySentTotal;


    @ApiModelProperty(name = "备注")
    private String remark;

    @ApiModelProperty("已送数<路条配送数+订单已送数量>")
    private Integer freshMilkRoadWaySendAndOrderSend;


    @ApiModelProperty("客户初始库存")
    private Integer originStock;

    private String addressCreateByType;

    @ApiModelProperty("小程序微信openID")
    private String XcxWxOpenId;

    @ApiModelProperty("小程序微信unionID")
    private String XcxWxUnionId;
    @ApiModelProperty("配送站点")
    private List<DeliverySiteDTO> deliverySiteDTOS;


}
