package com.ruoyi.order.disribution.controller;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.business.base.api.domain.json.CustomerInfo;
import com.ruoyi.business.base.api.enums.OrderTypeEnums;
import com.ruoyi.business.base.api.model.LoginEmployee;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.enums.TerminalTypeEnums;
import com.ruoyi.common.idempotent.annotation.RepeatSubmit;
import com.ruoyi.common.satoken.utils.helper.StaffLoginHelper;
import com.ruoyi.order.api.domain.vo.CxrActivityVo;
import com.ruoyi.order.common.domain.vo.CxrUserOrderVO;
import com.ruoyi.order.common.domain.vo.LastUserOrderVo;
import com.ruoyi.order.disribution.domain.bo.UserFastOrderBo;
import com.ruoyi.order.disribution.service.ActivityOrderService;
import com.ruoyi.order.disribution.service.CustomerService;
import com.ruoyi.order.disribution.service.FastOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Api(tags = "客户订单(新的)")
@RestController
@RequestMapping("/userOrder")
public class FastOrderController extends BaseController {

    @Autowired
    private FastOrderService fastOrderService;
    @Autowired
    private CustomerService customerService;

    @Autowired
    private ActivityOrderService activityOrderService;

    /**
     * 详细查询《用户新增订单》
     *
     * @param id 主键
     * @return
     */
    @ApiOperation("查询 “新订单”、“续订单”、“增订单” 详情")
    @PostMapping("/detail")
    public R<CxrUserOrderVO> detail(
        @ApiParam("主键") @NotNull(message = "主键不能为空") @RequestParam Long id) {
        return R.ok(fastOrderService.detail(id));
    }

    /**
     * 新增快单
     */
    @ApiOperation("查询订单类型")
    @PostMapping("/queryOrderType")
    public R<Short> queryOrderType(@RequestBody UserFastOrderBo bo) throws Exception {

        Short aShort = customerService.checkCustomerrAddressExistForFastOrder(bo);

        if (ObjectUtil.isNotNull(aShort)) {
            return R.ok(aShort);
        }

        OrderTypeEnums orderTypeEnums = fastOrderService.queryOrderType(bo.getCustomerInfoList().get(0).getPhone());
        return R.ok(orderTypeEnums.getValue());
    }

    /**
     * 查询10天内最后一张订单
     */
    @ApiOperation("查询10天内全部订单汇总")
    @PostMapping("/queryLastTenDayOrder")
    public R<LastUserOrderVo> queryLastTenDayOrder(@RequestBody CustomerInfo bo) throws Exception {
        return R.ok(fastOrderService.queryLastTenDayOrder(bo));
    }

    @ApiOperation("查询上一笔订单代理")
    @PostMapping("/queryLastOrderBusinessAgent")
    public R<Object> queryLastOrderBusinessAgent(@RequestBody CustomerInfo bo) throws Exception {
        return R.ok(fastOrderService.queryLastOrderBusinessAgent(bo));
    }


    @ApiOperation("查询10天内全部订单")
    @PostMapping("/queryLastTenDayOrderInfo")
    public R<Object> queryLastTenDayOrderInfo(@RequestBody CustomerInfo bo) throws Exception {
        return R.ok(fastOrderService.queryLastTenDayOrderInfo(bo));
    }


    /**
     * 新增快单
     */
    @ApiOperation("保存配送端 新订单 续订单 增订单")
    @PostMapping("/addFastOrder")
    @RepeatSubmit(interval = 5, timeUnit = TimeUnit.SECONDS, message = "{repeat.submit.message}")
    public R<String> addFastOrder(@Validated(AddGroup.class) @RequestBody UserFastOrderBo bo)
        throws Exception {
        if (bo.getParticipateActivityFlag()) {
            activityOrderService.verifyFastOrderParam(bo, TerminalTypeEnums.disribution);
            return R.ok(R.SUCCESS_MSG, activityOrderService.addFastOrder(bo));
        } else {
            fastOrderService.verifyFastOrderParam(bo, TerminalTypeEnums.disribution);
        }
        return R.ok(R.SUCCESS_MSG, fastOrderService.addFastOrder(bo));
    }

    /**
     * 新增订单更新
     *
     * @param bo
     * @return
     */
    @ApiOperation("更新 “新订单”、“续订单”、“增订单”》")
    @PostMapping("/update")
    public R<Void> updateNewOrder(@RequestBody UserFastOrderBo bo) {
        return toAjax(fastOrderService.updateNewOrder(bo) ? 1 : 0);
    }

    /**
     * 查询可以参与活动
     */
    @ApiOperation("查询可以参与活动")
    @PostMapping("/queryActivityList")
    public R<List<CxrActivityVo>> queryActivityList() {
        //登录信息
        LoginEmployee loginUser = StaffLoginHelper.getLoginUser();
        return R.ok(activityOrderService.queryActivityList(loginUser.getUserId()));
    }


    /**
     * 查询所有活动
     */
    @ApiOperation("查询所有活动")
    @PostMapping("/queryAllActivityList")
    public R<List<CxrActivityVo>> queryAllActivityList() {
        return R.ok(activityOrderService.queryAllActivityList());
    }


}
