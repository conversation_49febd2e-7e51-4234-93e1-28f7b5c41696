package com.ruoyi.business.base.dubbo;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.ruoyi.business.base.api.domain.CxrReportFormsExport;
import com.ruoyi.business.base.api.dubbo.RemoteCxrReportFormsExportService;
import com.ruoyi.business.base.excelReportFormsExport.service.ICxrReportFormsExportService;
import com.ruoyi.common.core.domain.ExcelReportDTO;
import com.ruoyi.common.core.enums.FileStatusEnums;
import com.ruoyi.common.core.enums.RetainStatusEnums;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.redis.utils.RedisLockUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteCxrReportFormsExportServiceImpl implements RemoteCxrReportFormsExportService {

    private final ICxrReportFormsExportService exportService;
    private final IdentifierGenerator identifierGenerator;
    private final TransactionTemplate transactionTemplate;

    @Override
    public void reportFormsHandler(String json) {
        ExcelReportDTO excelReportDTO = JSONUtil.toBean(json, ExcelReportDTO.class);
        if (NumberUtil.equals(excelReportDTO.getType(), 0)) {
            // 保存导出excel数据
            save(excelReportDTO);
        } else if (NumberUtil.equals(excelReportDTO.getType(), 1)) {
            // 保存增量数据
            saveIncremen(excelReportDTO);
        } else {
            saveException(excelReportDTO);
        }
    }

    @Override
    public void deleteExcelReportForms() {
        List<CxrReportFormsExport> list = exportService.lambdaQuery()
            .eq(CxrReportFormsExport::getFileStatus, FileStatusEnums.NO_EXPIRED.name())
            .notIn(CxrReportFormsExport::getRetainStatus, RetainStatusEnums.retain.getValue())
            .lt(CxrReportFormsExport::getCreateTime, LocalDateTime.now().minusDays(1))
            .list();

        for (CxrReportFormsExport cxrReportFormsExport : list) {
            transactionTemplate.execute(status -> {
                try {

                    String filePath = cxrReportFormsExport.getFilePath();
                    FileUtil.del(filePath);
                    exportService.lambdaUpdate().set(CxrReportFormsExport::getFileStatus, FileStatusEnums.EXPIRED.name())
                        .eq(CxrReportFormsExport::getId, cxrReportFormsExport.getId()).update();
                } catch (Exception exception) {
                    status.setRollbackOnly();
                    log.error("删除目录或者更新失败", exception);
                }

                return 0;
            });

        }

    }

    private void saveException(ExcelReportDTO excelReportDTO) {
        CxrReportFormsExport one = exportService.lambdaQuery()
            .eq(CxrReportFormsExport::getUniqueMark, excelReportDTO.getUniqueMark()).one();
        if (ObjectUtil.isNotNull(one)) {
            boolean flag = exportService.lambdaUpdate().eq(CxrReportFormsExport::getId, one.getId())
                .set(CxrReportFormsExport::getMessage, excelReportDTO.getErrMessage()).update();
            if (!flag) {
                throw new ServiceException("保存异常 导出报表失败");
            }
        }
    }

    /**
     * 保存导出excel 数据
     *
     * @param excelReportDTO
     */
    private void save(ExcelReportDTO excelReportDTO) {

        String locakKeyy = StrUtil.format("cxr:excel:report:{}", excelReportDTO.getUniqueMark());
        RedisLockUtils.tryLockException(locakKeyy, () -> {
            CxrReportFormsExport cxrReportFormsExport = new CxrReportFormsExport();
            cxrReportFormsExport.setId(identifierGenerator.nextId(null).longValue());

            cxrReportFormsExport.setHtmlName(excelReportDTO.getHtmlName());
            cxrReportFormsExport.setFilePath(excelReportDTO.getFileDir());
            cxrReportFormsExport.setFileStatus(FileStatusEnums.NO_EXPIRED.name());
            cxrReportFormsExport.setFileNameSuffix(excelReportDTO.getFileNameSuffix());
            cxrReportFormsExport.setDataTotal(excelReportDTO.getDataTotal());
            cxrReportFormsExport.setExportedTotal(0L);
            cxrReportFormsExport.setCreateBy(excelReportDTO.getCreateById());
            cxrReportFormsExport.setCreateByName(excelReportDTO.getCreateByName());
            cxrReportFormsExport.setCreateByType(excelReportDTO.getCreateByType());
            cxrReportFormsExport.setCreateTime(excelReportDTO.getCreateTime());
            cxrReportFormsExport.setUniqueMark(excelReportDTO.getUniqueMark());
            cxrReportFormsExport.setRetainStatus(ObjectUtil.isNotEmpty(excelReportDTO.getRetainStatus()) ? excelReportDTO.getRetainStatus() : null);
            boolean flag = exportService.save(cxrReportFormsExport);
            if (!flag) {
                throw new ServiceException("保存导出报表失败");
            }
        });
    }

    /**
     * 爆出 导出excel增量数据
     *
     * @param excelReportDTO
     */
    private void saveIncremen(ExcelReportDTO excelReportDTO) {
        CxrReportFormsExport one = exportService.lambdaQuery()
            .eq(CxrReportFormsExport::getUniqueMark, excelReportDTO.getUniqueMark()).one();
        if (ObjectUtil.isNotNull(one)) {
            Long exportedTotal = one.getExportedTotal() + Convert.toLong(excelReportDTO.getIncrementData(), 0L);
            boolean flag = exportService.lambdaUpdate().eq(CxrReportFormsExport::getId, one.getId())
                .set(CxrReportFormsExport::getExportedTotal, exportedTotal).update();
            if (!flag) {
                throw new ServiceException("保存增量导出报表失败");
            }
        }
    }


}
