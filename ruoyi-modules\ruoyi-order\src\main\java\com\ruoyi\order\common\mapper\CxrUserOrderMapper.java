package com.ruoyi.order.common.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.business.base.api.domain.dto.DailyIntergerDto;
import com.ruoyi.business.base.api.domain.dto.DailyPerformanceDto;
import com.ruoyi.common.mybatis.annotation.DataColumn;
import com.ruoyi.common.mybatis.annotation.DataPermission;
import com.ruoyi.order.api.domain.bo.CustomerOrderStatisticsBo;
import com.ruoyi.order.api.domain.vo.*;
import com.ruoyi.order.common.domain.bo.CxrUserOrderQueryBo;
import com.ruoyi.order.common.domain.bo.SummaryOrderBo;
import com.ruoyi.order.common.domain.vo.*;
import com.ruoyi.order.common.entity.CxrUserOrder;
import com.ruoyi.order.disribution.domain.vo.SummaryOrderVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【cxr_user_order(用户订单)】的数据库操作Mapper
 * @createDate 2022-08-04 19:53:00 @Entity generator.domain.CxrUserOrder
 */
public interface CxrUserOrderMapper extends BaseMapper<CxrUserOrder> {

    @DataPermission({@DataColumn(key = "siteName", value = "t.site_id")})
    Page<CxrUserOrder> page(
        @Param("cxrUserOrderBo") CxrUserOrderQueryBo cxrUserOrderBo, Page<CxrUserOrder> page);

    @DataPermission({@DataColumn(key = "siteName", value = "t.site_id")})
    List<CxrUserOrderListVo> list(@Param("cxrUserOrderBo") CxrUserOrderQueryBo cxrUserOrderBo);

    @DataPermission({@DataColumn(key = "siteName", value = "f.site_id")})
    Long count(@Param("cxrUserOrderBo") CxrUserOrderQueryBo cxrUserOrderBo);

    List<CxrUserOrder> getByTradeNo(String tradeNo);

    CxrUserOrder getByOrderNo(String orderNo);

    List<CxrUserOrder> searchOrder(@Param("orderSearchVo") OrderSearchReqVo reqVo);

    @InterceptorIgnore(tenantLine = "on")
    Page<CxrUserOrder> searchOrderIds(@Param("orderSearchVo") OrderSearchReqVo reqVo, Page page);

    List<SummaryOrderBo> summaryOrder(@Param("orderSearchVo") OrderSearchReqVo reqVo);

    DailyIntergerDto sumOrderQuantity(
        @Param("day") LocalDate now,
        @Param("list") List<Short> list,
        @Param("man") Short man,
        @Param("dis") Short dism,
        @Param("audit") Integer audit,
        @Param("pay") Integer pay,
        @Param("phone") String customerPhone);

    DailyIntergerDto sumTranInMilk(
        @Param("day") LocalDate now,
        @Param("orderType") short value,
        @Param("audit") Integer code,
        @Param("phone") String customerPhone);

    DailyIntergerDto sumTranOutMilk(
        @Param("day") LocalDate now,
        @Param("orderType") short value,
        @Param("audit") Integer code,
        @Param("phone") String customerPhone);

    DailyIntergerDto sumReturnMilk(
        @Param("day") LocalDate now,
        @Param("orderType") short value,
        @Param("audit") Integer code,
        @Param("phone") String customerPhone);

    Integer statisticUserOrder(
        @Param("siteId") Long siteId,
        @Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime);

    /**
     * 统计订单数 天
     *
     * @param now
     * @param value
     * @param value1
     * @param value2
     * @param value3
     * @param code
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    DailyIntergerDto countNewOrder(
        @Param("now") LocalDate now,
        @Param("new") short value,
        @Param("dis") short value1,
        @Param("man") short value2,
        @Param("pay") int value3,
        @Param("audit") Integer code,
        @Param("id") Long cxrEmployeeId);

    @InterceptorIgnore(tenantLine = "on")
    List<DailyIntergerDto> countReturnOrder(
        @Param("now") LocalDate now, @Param("new") short value, @Param("audit") Integer code);

    @InterceptorIgnore(tenantLine = "on")
    List<String> countValidOrder(
        @Param("now") LocalDate now,
        @Param("dis") short value1,
        @Param("man") short value2,
        @Param("pay") int value3,
        @Param("audit") Integer code,
        @Param("id") Long cxrEmployeeId,
        @Param("types") List<Short> types);

    IPage<CustomerOrderStatisticsVo> orderStatistics(
        @Param("bo") CustomerOrderStatisticsBo bo, IPage page);

    @InterceptorIgnore(tenantLine = "on")
    CustomerOrderStatisticsDTO customerOrderStatistics(
        @Param("customerId") Long customerId, @Param("year") Integer year);

    @InterceptorIgnore(tenantLine = "on")
        // 客户订单统计
    CustomerOrderTotal TotalOrderStatistics(@Param("bo") CustomerOrderStatisticsBo bo);

    List<DailyIntergerDto> sumGiveFreshMilkNumber(
        @Param("now") LocalDate min,
        @Param("types") List<Short> orderTypes,
        @Param("dis") short value,
        @Param("man") short value1,
        @Param("pay") int value2,
        @Param("audit") Integer code);

    DailyIntergerDto sumGiveFreshMilkNumberBySite(
        @Param("now") LocalDate min,
        @Param("types") List<Short> orderTypes,
        @Param("dis") short value,
        @Param("man") short value1,
        @Param("pay") int value2,
        @Param("audit") Integer code,
        @Param("siteId") Long siteId);

    List<Long> queryAuditSuccess(
        @Param("min") LocalDate min, @Param("type") short value, @Param("code") Integer code);

    @DataPermission({@DataColumn(key = "siteName", value = "tt.site_id")})
        // 客户订单
    CustomerOrderTotal pageOrderTotalStatistics(
        @Param("cxrUserOrderBo") CxrUserOrderQueryBo cxrUserOrderBo);

    CxrUserOrder queryLastOrderInfo(@Param("customerId") Long customerId);

    CxrUserOrder queryFirstOrderInfo(@Param("customerId") Long customerId);

    CxrUserOrder queryFirstOrderInfoV2(@Param("customerId") Long customerId);

    LastUserOrderVo queryNotContractLastTenDayOrder(
        @Param("customerId") Long customerId,
        @Param("start") LocalDate start,
        @Param("end") LocalDateTime end,
        @Param("orderId") Long orderId);

    List<LastUserOrderDayInfo> queryNotContractLastTenDayOrderInfo(
        @Param("customerId") Long customerId,
        @Param("start") LocalDate start,
        @Param("end") LocalDateTime end,
        @Param("orderId") Long orderId);

    Page<CxrUserOrderListVo> userOrderAndReturn(
        @Param("cxrUserOrderBo") CxrUserOrderQueryBo cxrUserOrderBo, Page<Object> build);

    List<CxrUserReturnOrderVO> userOrderAndReturn(@Param("cxrUserOrderBo") CxrUserOrderQueryBo cxrUserOrderBo);

    Long userOrderAndReturnCount(@Param("cxrUserOrderBo") CxrUserOrderQueryBo cxrUserOrderBo);

    LastUserOrderVo queryNotContractLastTenDayOrder(
        @Param("customerId") Long customerId,
        @Param("start") LocalDate start,
        @Param("end") LocalDateTime end);

    @InterceptorIgnore(tenantLine = "on")
    List<String> countNewOrderManager(
        @Param("now") LocalDate now,
        @Param("orderType") short orderType,
        @Param("man") short value,
        @Param("audit") Integer code,
        @Param("id") Long id);

    @InterceptorIgnore(tenantLine = "on")
    List<String> countNewOrderDisribution(
        @Param("now") LocalDate now,
        @Param("orderType") short orderType,
        @Param("dis") short value,
        @Param("paYstatus") int value1,
        @Param("id") Long cxrEmployeeId);

    SummaryOrderVo summaryOrders(@Param("orderSearchVo") OrderSearchReqVo reqVo);

    BigDecimal summaryOrdersAchievement(@Param("orderSearchVo") OrderSearchReqVo reqVo);

    CxrUserOrder queryLastOrder(@Param("customerId") Long id, @Param("orderId") Long orderId);

    CxrUserOrder queryLastOrderInfoNotContinue(Long id);

    Page<CxrUserOrder> queryCustomerOrder(@Param("currentDate") LocalDateTime currentDate,
                                          @Param("endTime") LocalDateTime endTime, IPage page);

    List<UserOrderListVo> selectListByTime(
        @Param("start") LocalDate startDate, @Param("end") LocalDate endDate);

    Integer getDaySumQty(
        @Param("customerId") Long customerId,
        @Param("start") LocalDate startDate,
        @Param("end") LocalDate endDate);

    IPage<String> queryShouldSendNumber(
        @Param("now") LocalDate now,
        @Param("audit") Integer audit,
        @Param("pay") int pay,
        @Param("type") short orderType,
        @Param("del") String del,
        Page page);

    /**
     * 转单使用的
     *
     * @param outLongMilkOrderId
     * @return
     */
    CxrUserOrder queryById(@Param("OrderId") Long outLongMilkOrderId);

    Long queryOrderIdByOrderNo(@Param("orderNo") String outLongMilkOrderNo);

    Date queryOrderDateByOrderNo(@Param("orderNo") String outLongMilkOrderNo);

    BigDecimal selectActivitySumAmount(@Param("activityId") Long activityId, @Param("customerId") Long customerId);


    List<CustomerOrderRecentlyDate> selectRecentlyOrderDate(@Param("cxrCustomerIds") List<Long> cxrCustomerIds);

    CustomerOrderRecentlyDate selectRecentlyOrderDateByCustomerId(@Param("customerId") Long customerId);

    boolean reverseAuditUpdateById(@Param("bo") CxrUserOrder cxrUserOrder);

    /**
     * 根据客户id分页查询客户订单
     *
     * @param customerId
     * @param page
     * @return
     */
    Page<CxrUserOrder> queryUserOrderByCustomerId(@Param("customerId") Long customerId, Page<CxrUserOrder> page);

    List<DailyPerformanceDto> returenDailyPerformanceDtos(@Param("returnOrderIds") List<Long> returnOrderIds,
                                                          @Param("userId") Long userId, @Param("returnSiteIds") List<Long> returnSiteIds);

    List<String> countMergePortNewOrder(@Param("now") LocalDate now,
                                        @Param("orderType") short orderType,
                                        @Param("id") Long id);

    BigDecimal countMergePortByContractOrderNewOrder(@Param("now") LocalDate now,
                                                     @Param("orderType") short orderType,
                                                     @Param("id") Long id);

    List<String> countValidNewOrder(@Param("now") LocalDate now,
                                    @Param("id") Long cxrEmployeeId,
                                    @Param("types") List<Short> types);

    @InterceptorIgnore(tenantLine = "on")
    CustomerOrderTotal TotalOrderStatisticsUpgrade(@Param("bo") CustomerOrderStatisticsBo bo);

    CxrUserOrder queryFirstDayOrder(@Param("customerId") Long id, @Param("orderId") Long orderId, @Param("date") LocalDate date);

    CxrUserOrder queryLastPromotionalOrderOrder(@Param("customerId") Long id, @Param("orderId") Long orderId,
                                                @Param("start") LocalDate start,
                                                @Param("end") LocalDateTime end);


    Long queryZeroQuantityRenewal(@Param("customerId") Long customerId, @Param("orderDate") LocalDate orderDate);
    Integer queryCustomerSwitch(@Param("customerId") Long customerId);

    IPage<CustomerOrderStatisticsVo> orderFixationStatistics(@Param("bo") CustomerOrderStatisticsBo bo, Page<Object> build);

    @InterceptorIgnore(tenantLine = "on")
    CustomerOrderTotal TotalOrderFixationStatisticsUpgrade(@Param("bo") CustomerOrderStatisticsBo bo);
}
