package com.ruoyi.business.base.employee.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.ruoyi.business.api.domain.EmployeeHistoryDate;
import com.ruoyi.business.api.domain.bo.CxrEmployeeBo;
import com.ruoyi.business.api.dubbo.RemoteCxrEmployeeBorrowingService;
import com.ruoyi.business.base.api.domain.*;
import com.ruoyi.business.base.api.domain.bo.CxrEmployeeDimissionBo;
import com.ruoyi.business.base.api.domain.bo.CxrPostId;
import com.ruoyi.business.base.api.domain.dto.CxrEmployeeAuditDTO;
import com.ruoyi.business.base.api.domain.dto.CxrEmployeeInfoDTO;
import com.ruoyi.business.base.api.domain.dto.CxrEmployeePageBo;
import com.ruoyi.business.base.api.domain.external.train.Organize;
import com.ruoyi.business.base.api.domain.external.train.Position;
import com.ruoyi.business.base.api.domain.external.train.TrainResponse;
import com.ruoyi.business.base.api.domain.external.train.User;
import com.ruoyi.business.base.api.domain.vo.CxrGroupListVo;
import com.ruoyi.business.base.api.domain.vo.EmployeesVo;
import com.ruoyi.business.base.api.dubbo.RemoteCustomerDistributionDetailService;
import com.ruoyi.business.base.api.dubbo.RemoteWorkOrderService;
import com.ruoyi.business.base.api.model.LoginEmployee;
import com.ruoyi.business.base.api.utils.MyLambdaQueryWrapper;
import com.ruoyi.business.base.common.domain.CxrEmployeeBorrowing;
import com.ruoyi.business.base.common.mapper.CxrEmployeeBorrowingMapper;
import com.ruoyi.business.base.common.mapper.SysDeptMapper;
import com.ruoyi.business.base.customerAddress.mapper.CxrCustomerAddressMapper;
import com.ruoyi.business.base.customerChangeRecord.mapper.CxrCustomerChangeRecordMapper;
import com.ruoyi.business.base.cxrAdjustMilkApply.service.CxrAdjustMilkApplyService;
import com.ruoyi.business.base.cxrEmployeeChangeRecord.service.ICxrEmployeeChangeRecordService;
import com.ruoyi.business.base.cxrEmployeeCooperateRecord.mapper.CxrEmployeeCooperateHistoryMapper;
import com.ruoyi.business.base.cxrEmployeeCooperateRecord.mapper.CxrEmployeeCooperateRecordMapper;
import com.ruoyi.business.base.cxrTwiceJoinEmployee.domain.CxrTwiceJoinEmployee;
import com.ruoyi.business.base.cxrTwiceJoinEmployee.mapper.CxrTwiceJoinEmployeeMapper;
import com.ruoyi.business.base.cxrTwiceJoinEmployee.service.CxrTwiceJoinEmployeeService;
import com.ruoyi.business.base.employee.config.TrainpropertiesConfig;
import com.ruoyi.business.base.employee.domain.bo.CxrEmployeeWithSiteNameBo;
import com.ruoyi.business.base.employee.domain.bo.StaffCxrEmployeeBo;
import com.ruoyi.business.base.employee.domain.bo.StaffCxrEmployeeGroupBo;
import com.ruoyi.business.base.employee.domain.bo.StaffCxrPeopleBo;
import com.ruoyi.business.base.employee.domain.config.roleKeyAuthority;
import com.ruoyi.business.base.employee.domain.vo.CxrEmployeeListVo;
import com.ruoyi.business.base.employee.domain.vo.CxrEmployeeVo;
import com.ruoyi.business.base.employee.domain.vo.LeaveEmployeeVo;
import com.ruoyi.business.base.employee.domain.vo.StaffCxrEmployeeListVo;
import com.ruoyi.business.base.employee.mapper.CxrEarnestMoneyRecordMapper;
import com.ruoyi.business.base.employee.mapper.CxrEmployeeLevelAdjustMapper;
import com.ruoyi.business.base.employee.mapper.CxrEmployeeMapper;
import com.ruoyi.business.base.employee.mapper.CxrEmployeeRankTmpMapper;
import com.ruoyi.business.base.employee.service.ICxrEmployeeService;
import com.ruoyi.business.base.employeePost.domain.vo.CxrEmployeePostVo;
import com.ruoyi.business.base.employeePost.domain.vo.PostEmployeeCountVo;
import com.ruoyi.business.base.employeePost.mapper.CxrEmployeePostMapper;
import com.ruoyi.business.base.employeePost.service.ICxrEmployeePostService;
import com.ruoyi.business.base.group.domain.vo.CxrGroupVo;
import com.ruoyi.business.base.group.mapper.CxrGroupMapper;
import com.ruoyi.business.base.group.service.ICxrGroupService;
import com.ruoyi.business.base.groupHistroy.service.ICxrGroupHistoryService;
import com.ruoyi.business.base.logger.OptLoggerManager;
import com.ruoyi.business.base.region.mapper.CxrRegionMapper;
import com.ruoyi.business.base.site.domain.vo.CxrSiteVo;
import com.ruoyi.business.base.site.mapper.CxrSiteMapper;
import com.ruoyi.business.base.site.service.ICxrSiteService;
import com.ruoyi.common.core.config.CxrSecurityConfig;
import com.ruoyi.common.core.constant.SysConfigKeyConstants;
import com.ruoyi.common.core.domain.ExcelReportDTO;
import com.ruoyi.common.core.domain.LoginInfo;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.*;
import com.ruoyi.common.core.validator.AbstractAssert;
import com.ruoyi.common.core.web.domain.CxrBaseEntity;
import com.ruoyi.common.enums.TerminalTypeEnums;
import com.ruoyi.common.excel.utils.ExcelExportObj;
import com.ruoyi.common.excel.utils.ExcelMqUtil;
import com.ruoyi.common.image.config.properties.ImageProperties;
import com.ruoyi.common.mybatis.core.page.PageQuery;
import com.ruoyi.common.mybatis.core.page.PageTableDataInfo;
import com.ruoyi.common.ocr.utils.OcrUtil;
import com.ruoyi.common.redis.annotation.RedisDistributedLock;
import com.ruoyi.common.rocketmq.constant.employee.EmployeeConstant;
import com.ruoyi.common.rocketmq.constant.employee.EmployeeDelayConstant;
import com.ruoyi.common.rocketmq.utils.MqUtil;
import com.ruoyi.common.satoken.utils.LoginTypeUtil;
import com.ruoyi.common.satoken.utils.LoginUtil;
import com.ruoyi.common.satoken.utils.helper.LoginHelper;
import com.ruoyi.common.satoken.utils.helper.StaffLoginHelper;
import com.ruoyi.core.base.config.AppletConfig;
import com.ruoyi.core.base.domain.bo.SynEmployeeBo;
import com.ruoyi.core.base.sale.SaleLevelMqSendUtil;
import com.ruoyi.system.api.*;
import com.ruoyi.system.api.domain.SysDept;
import com.ruoyi.system.api.domain.SysRole;
import com.ruoyi.system.api.model.LoginUser;
import com.ruoyi.system.api.vo.SysDeptvo;
import com.ruoyi.system.enums.DictTypeEnums;
import io.seata.spring.annotation.GlobalTransactional;
import io.seata.tm.api.transaction.Propagation;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.sql.SQLIntegrityConstraintViolationException;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 员工Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-07-02
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class CxrEmployeeServiceImpl extends MPJBaseServiceImpl<CxrEmployeeMapper, CxrEmployee>
    implements ICxrEmployeeService {

    private final CxrEmployeeBorrowingMapper cxrEmployeeBorrowingMapper;
    private final CxrEmployeeMapper baseMapper;

    private final CxrEarnestMoneyRecordMapper cxrEarnestMoneyRecordMapper;

    private final CxrEmployeeRankTmpMapper cxrEmployeeRankTmpMapper;
    private final CxrSiteMapper cxrSiteMapper;
    private final CxrGroupMapper cxrGroupMapper;
    @Autowired
    private CxrSecurityConfig cxrSecurityConfig;
    private final OcrUtil ocrUtil;

    private final CxrAdjustMilkApplyService adjustMilkApplyService;

    private final CxrCustomerChangeRecordMapper cxrCustomerChangeRecordMapper;

    private final CxrCustomerAddressMapper cxrCustomerAddressMapper;


    private final ImageProperties imageProperties;
    private final MqUtil mqUtil;
    private final ICxrEmployeePostService iCxrEmployeePostService;
    private final CxrEmployeePostMapper cxrEmployeePostMapper;
    private final TrainpropertiesConfig trainpropertiesConfig;
    private final CxrRegionMapper cxrRegionMapper;
    private final ICxrGroupService iCxrGroupService;
    private final ICxrSiteService iCxrSiteService;
    private final CxrEmployeeCooperateRecordMapper cxrEmployeeCooperateRecordMapper;
    private final ICxrEmployeeChangeRecordService iCxrEmployeeChangeRecordService;
    private final ICxrGroupHistoryService iCxrGroupHistoryService;

    private final CxrEmployeeLevelAdjustMapper employeeLevelAdjustMapper;

    private final CxrEmployeeCooperateHistoryMapper cxrEmployeeCooperateHistoryMapper;

    private final roleKeyAuthority roleKeyAuthority;
    @Lazy
    @Autowired
    private ICxrEmployeeService iCxrEmployeeService;

    private final OptLoggerManager optLoggerManager;

    @DubboReference
    private RemoteDictService remoteDictService;

    @DubboReference
    private RemoteCustomerDistributionDetailService remoteCustomerDistributionDetailService;

    @DubboReference
    private RemoteDeptService remoteDeptService;
    @DubboReference
    private RemoteRoleService remoteRoleService;
    @DubboReference
    private RemoteAreaService remoteAreaService;
    @DubboReference
    private RemoteSysConfigService remoteSysConfigService;
    @DubboReference
    private RemoteCxrEmployeeBorrowingService remoteCxrEmployeeBorrowingService;
    @DubboReference
    private RemoteWorkOrderService remoteWorkOrderService;
    @Autowired
    private AppletConfig appletConfig;

    @Autowired
    private CxrTwiceJoinEmployeeMapper cxrTwiceJoinEmployeeMapper;

    @Lazy
    @Autowired
    private CxrTwiceJoinEmployeeService cxrTwiceJoinEmployeeService;

    @Autowired
    @Qualifier("futureThreadPool")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private SysDeptMapper sysDeptMapper;

    //    private final ImageUtils imageUtils;

    /**
     * 特殊字符
     */
    private static final String[] symbol = {
        "!", "@", "#", "$", "%", "^", "&", "*", "_", "`", "-", ";", ":", "'", "|", "~"
    };

    @Override
    public CxrEmployeeVo detail(Long id) {
        Long deptId = LoginUtil.getLoginUserException().getDeptId();
        // 连表查询信息
        CxrEmployeeVo cxrEmployeeVo =
            baseMapper.selectOneAndOther(id, DeleteStatus.NOT_DELETED.getValue());

        cxrEmployeeVo.setImageAddressPrefix(imageProperties.getImageAddressMap().get(cxrEmployeeVo.getImageLocation()));

        if (null == cxrEmployeeVo) {
            throw new ServiceException("数据不存在，请刷新页面重试");
        }
        if (null != cxrEmployeeVo.getHealthCertificateUrl()) {
            if (!cxrEmployeeVo.getHealthCertificateUrl().contains("[")) {
                cxrEmployeeVo.setHealthCertificateUrl(
                    "[\"" + cxrEmployeeVo.getHealthCertificateUrl() + "\"]");
            }
        }
        if (null != cxrEmployeeVo.getProxyContractFileUrl()) {
            if (!cxrEmployeeVo.getProxyContractFileUrl().contains("[")) {
                cxrEmployeeVo.setProxyContractFileUrl(
                    "[\"" + cxrEmployeeVo.getProxyContractFileUrl() + "\"]");
            }
        }
        if (null != cxrEmployeeVo.getProxyEndApplyUrl()) {
            if (!cxrEmployeeVo.getProxyEndApplyUrl().contains("[")) {
                cxrEmployeeVo.setProxyEndApplyUrl("[\"" + cxrEmployeeVo.getProxyEndApplyUrl() + "\"]");
            }
        }
        if (null != cxrEmployeeVo.getGradeProxyAgreementFileUrl()) {
            if (!cxrEmployeeVo.getGradeProxyAgreementFileUrl().contains("[")) {
                cxrEmployeeVo.setGradeProxyAgreementFileUrl(
                    "[\"" + cxrEmployeeVo.getGradeProxyAgreementFileUrl() + "\"]");
            }
        }
        if (null != cxrEmployeeVo.getIdCardBackPictureUrl()) {
            if (!cxrEmployeeVo.getIdCardBackPictureUrl().contains("[")) {
                cxrEmployeeVo.setIdCardBackPictureUrl(
                    "[\"" + cxrEmployeeVo.getIdCardBackPictureUrl() + "\"]");
            }
        }

        if (null != cxrEmployeeVo.getProxyEndNoticeUrl()) {
            if (!cxrEmployeeVo.getProxyEndNoticeUrl().contains("[")) {
                cxrEmployeeVo.setProxyEndNoticeUrl("[\"" + cxrEmployeeVo.getProxyEndNoticeUrl() + "\"]");
            }
        }
        if (null != cxrEmployeeVo.getIdCardPositivePictureUrl()) {
            if (!cxrEmployeeVo.getIdCardPositivePictureUrl().contains("[")) {
                cxrEmployeeVo.setIdCardPositivePictureUrl(
                    "[\"" + cxrEmployeeVo.getIdCardPositivePictureUrl() + "\"]");
            }
        }
        if (null != cxrEmployeeVo.getSalesRepresentativeRegisterFileUrl()) {
            if (!cxrEmployeeVo.getSalesRepresentativeRegisterFileUrl().contains("[")) {
                cxrEmployeeVo.setSalesRepresentativeRegisterFileUrl(
                    "[\"" + cxrEmployeeVo.getSalesRepresentativeRegisterFileUrl() + "\"]");
            }
        }
        if (null != cxrEmployeeVo.getDepositBankCardPositivePictureUrl()) {
            if (!cxrEmployeeVo.getDepositBankCardPositivePictureUrl().contains("[")) {
                cxrEmployeeVo.setDepositBankCardPositivePictureUrl(
                    "[\"" + cxrEmployeeVo.getDepositBankCardPositivePictureUrl() + "\"]");
            }
        }

        Long cxrMasterId = cxrEmployeeVo.getCxrMasterId();
        if (ObjectUtil.isNotEmpty(cxrMasterId)) {
            cxrEmployeeVo.setCxrMasterName(baseMapper.getId(cxrMasterId).getName());
        }
        int terminalType = LoginTypeUtil.getTerminalType();
        if (NumberUtil.equals(terminalType, TerminalTypeEnums.disribution.getValue())) {
            // 处理配送不隐藏数据
            return cxrEmployeeVo;
        } else {
            // 根据   用户id 查询  角色信息
            List<String> roleKey = getRoleKeys(LoginHelper.getLoginUser());

            // 判断是否是    人事,it,总经办,财务部    是的话 flag =false
            boolean flag = true;
            a:
            for (String s : roleKey) {
                List<String> key = roleKeyAuthority.getRoleKeys();
                if (CollectionUtil.isNotEmpty(key)) {
                    boolean contains = key.contains(s);
                    if (contains) {
                        flag = false;
                        break a;
                    }
                }
            }
            // false  不会进入   设置无权查看
            // 根据角色名称来进行
            if (flag) {
                cxrEmployeeVo.setIdCard("****");
                cxrEmployeeVo.setIdCardBackPictureUrl("****");
                cxrEmployeeVo.setIdCardPositivePictureUrl("****");
                cxrEmployeeVo.setDepositBankCardIdCard("****");
                cxrEmployeeVo.setDepositBankCardName("****");
                cxrEmployeeVo.setDepositBankCardNumber("****");
                cxrEmployeeVo.setDepositBankCardPhone("****");
                cxrEmployeeVo.setFamilyDepositBankCardPhone("****");
                cxrEmployeeVo.setFamilyDepositBankCardIdCard("****");
                cxrEmployeeVo.setFamilyDepositBankCardNumber("****");
                cxrEmployeeVo.setSalesRepresentativeRegisterFileUrl("****");
                cxrEmployeeVo.setGradeProxyAgreementFileUrl("****");
                cxrEmployeeVo.setProxyContractFileUrl("****");
            }
//            if (cxrEmployeeVo.getWorkFrequency().equals(1) && cxrEmployeeVo.getAuthStatus()
//                .equals(AuthStatusEnums.close.getCode()) && !cxrEmployeeVo.getCxrSiteId()
//                .equals(1595957430981169153L)) {
//                cxrEmployeeVo.setInductionTime(null);
//            }
            return cxrEmployeeVo;
        }
    }

    @Override
    public PageTableDataInfo<CxrEmployeeListVo> page(
        CxrEmployeeBo cxrEmployeeBo, PageQuery pageQuery) {
        // 准备空数据
        PageTableDataInfo<CxrEmployeeListVo> info = new PageTableDataInfo<>();
        List<CxrEmployeeListVo> nullList = new ArrayList<>();
        info.setRows(nullList);

        LoginUser loginUser = LoginHelper.getLoginUser();
        // 公司id
        Long dept = loginUser.getDeptId();

        List<Long> qylSiteIds = loginUser.getSiteIds();
        if (CollUtil.isEmpty(qylSiteIds)) {
            qylSiteIds = new ArrayList<>();

        }

        // 根据   用户id 查询  角色信息
        List<String> roleKey = getRoleKeys(loginUser);

        boolean qyjlFlag = true;
        a:
        for (String s : roleKey) {
            if (s.equals("qyjl")) {
                qyjlFlag = false;
                if (CollectionUtil.isEmpty(qylSiteIds)) {
                    throw new ServiceException("区域经理没有管辖站点!");
                }
                break a;
            }
        }

        // 经理    josn条件     ex部分匹配
        List<String> list = new ArrayList<>();
        list.add(PostType.DIRECTOR.getValue());
        list.add(PostType.GENERATION_DIRECTOR.getValue());
        //        list.add(PostType.DEVELOPMENT_DIRECTOR.getValue());
        String value = JSONUtil.toJsonStr(list);

        // not deleted
        String notDeletedValue = DeleteStatus.NOT_DELETED.getValue();

        // 站点id
        List<Long> siteIds = null;
        List<Long> regionSiteIds = null;
        List<Long> superSiteIds = null;

        // 省/市/区/站点名称    模糊查询站点id
        if (StringUtils.isNotBlank(cxrEmployeeBo.getProvince())
            || StringUtils.isNotBlank(cxrEmployeeBo.getCity())
            || StringUtils.isNotBlank(cxrEmployeeBo.getArea())
            || StringUtils.isNotBlank(cxrEmployeeBo.getCxrSiteName())) {
            siteIds =
                cxrSiteMapper.selectLikeProviceCityArea(
                    cxrEmployeeBo.getProvince(),
                    cxrEmployeeBo.getArea(),
                    cxrEmployeeBo.getCity(),
                    cxrEmployeeBo.getCxrSiteName());
            if (CollectionUtil.isEmpty(siteIds)) {
                return info;
            }

            siteIds.add(-1L);
        }

        // 大区名字模糊查询
        if (StringUtils.isNotBlank(cxrEmployeeBo.getCxrRegionName())) {
            // 查询区域ids
            List<Long> regionIds =
                cxrRegionMapper.selectIdsByName(cxrEmployeeBo.getCxrRegionName(), notDeletedValue);
            if (CollectionUtil.isEmpty(regionIds)) {
                return info;
            }
            // 根据区域id查询站点
            regionSiteIds = cxrSiteMapper.selectByRegionId(regionIds, notDeletedValue);
            if (CollectionUtil.isEmpty(regionSiteIds)) {
                return info;
            }

            regionSiteIds.add(-1L);
        }

        // 大区名字模糊查询
        if (StringUtils.isNotBlank(cxrEmployeeBo.getBigRegionName())) {
            // 查询区域ids
            List<Long> bigRegionIds = remoteDeptService.selectByName(cxrEmployeeBo.getBigRegionName());
            if (CollectionUtil.isEmpty(bigRegionIds)) {
                return info;
            }
            // 根据大区ID查区域
            List<SysDept> sysRegionDepts = sysDeptMapper.selectList(new LambdaQueryWrapper<SysDept>()
                .in(SysDept::getParentId, bigRegionIds)
                .eq(SysDept::getDelFlag, DeleteStatus.NOT_DELETED.getValue())
            );
            if (CollectionUtil.isEmpty(sysRegionDepts)) {
                return info;
            }
            //根据区域查站点
            List<Long> bigRegionSiteIds = cxrSiteMapper.selectByRegionId(
                sysRegionDepts.stream().map(SysDept::getDeptId).collect(Collectors.toList()), notDeletedValue);
            if (CollectionUtil.isEmpty(regionSiteIds)) regionSiteIds = new ArrayList<>();
            regionSiteIds.addAll(bigRegionSiteIds);
            regionSiteIds.add(-1L);
        }

        // 根据主管名称 查站点
        if (StringUtils.isNotBlank(cxrEmployeeBo.getSupervisorName())) {
            // 根据名字查id
            List<Long> superIds =
                baseMapper.selectIdsByName(cxrEmployeeBo.getSupervisorName(), notDeletedValue);
            if (CollectionUtil.isEmpty(superIds)) {
                return info;
            }
            // 根据id查站点
            superSiteIds = cxrEmployeePostMapper.selectSiteIdByEmployeeIds(superIds, notDeletedValue);
            if (CollectionUtil.isEmpty(superSiteIds)) {
                return info;
            }
            superSiteIds.add(-1L);
        }

        List<Long> theSiteIds = null;
        if (CollectionUtil.isNotEmpty(siteIds)
            && CollectionUtil.isNotEmpty(superSiteIds)
            && CollectionUtil.isNotEmpty(regionSiteIds)) {
            theSiteIds = (List) CollectionUtil.intersection(siteIds, superSiteIds, regionSiteIds);
        } else if (CollectionUtil.isNotEmpty(siteIds)) {
            theSiteIds = siteIds;
        } else if (CollectionUtil.isNotEmpty(superSiteIds)) {
            theSiteIds = superSiteIds;
        } else if (CollectionUtil.isNotEmpty(regionSiteIds)) {
            theSiteIds = regionSiteIds;
        }

        // 员工id
        List<Long> postEmployeeIds = null;
        List<Long> groupIds = null;
        // 职务   站点不为空查询站点内的 职务员工id
        if (StringUtils.isNotBlank(cxrEmployeeBo.getOneProxyType())) {
            //站点id
            List<Long> ids = new ArrayList<>();
            ids = CollectionUtil.newCopyOnWriteArrayList(theSiteIds);

            //有区域的站点区域id
            List<Long> regionIds = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(siteIds)) {
                if (StringUtils.isBlank(cxrEmployeeBo.getCxrSiteName()) || cxrEmployeeBo.getOneProxyType()
                    .equals(PostType.DEVELOPMENT_MANAGER.getValue()) || cxrEmployeeBo.getOneProxyType()
                    .equals(PostType.REGION_MANAGER.getValue())
                    || PostType.BIZ_BP.getValue().equals(cxrEmployeeBo.getOneProxyType())) {
                    regionIds.addAll(siteIds);
                }
            }
            if (CollectionUtil.isNotEmpty(regionSiteIds)) {
                regionIds.addAll(regionSiteIds);
                regionIds = regionIds.stream().distinct().collect(Collectors.toList());
            }

            if (!qyjlFlag) {
                //区域经理
                //交集
                regionIds =
                    CollectionUtil.isNotEmpty(regionIds) ? CollectionUtil.unionAll(regionIds, qylSiteIds) : qylSiteIds;
                ids = CollectionUtil.isNotEmpty(ids) ? CollectionUtil.unionAll(ids, qylSiteIds) : qylSiteIds;
            }

            // 组长
            if (cxrEmployeeBo.getOneProxyType().equals(PostType.GROUP_LEADER.getValue())) {
                groupIds = cxrGroupMapper.selectEmployeeId(ids);

                if (CollectionUtil.isEmpty(groupIds)) {
                    return info;
                }
                groupIds.add(-1L);
            } else {
                // 有职务但不是组长
                postEmployeeIds =
                    cxrEmployeePostMapper.selectEmployeeByPost(
                        cxrEmployeeBo.getOneProxyType(),
                        notDeletedValue,
                        cxrEmployeeBo.getOneProxyType().equals("80")
                            ? null
                            : ChargeStatus.IN_PROGRESS.getValue()
                        , ids, regionIds
                    );
                // 如果进来了 然后查出来的是空的  直接返回
                if (CollectionUtil.isEmpty(postEmployeeIds)) {
                    return info;
                }
                postEmployeeIds.add(-1L);
            }
        }

        List<Long> employeeIds = null;
        // 两个条件
        // 取交集
        if (CollectionUtil.isNotEmpty(postEmployeeIds) && CollectionUtil.isNotEmpty(groupIds)) {
            employeeIds = (List) CollectionUtil.intersection(postEmployeeIds, groupIds);
        } else if (CollectionUtil.isNotEmpty(postEmployeeIds)) {
            employeeIds = postEmployeeIds;
        } else if (CollectionUtil.isNotEmpty(groupIds)) {
            employeeIds = groupIds;
        }

        if (cxrEmployeeBo.getRefundOrderFlag()) {
//            cxrEmployeeBo.setMinQuitDay(DateUtils.getMinQuitDate(null));
            cxrEmployeeBo.setMinQuitDay(LocalDate.now().minusDays(90));
        }

        log.info("-----员工分页---");
        IPage<CxrEmployeeListVo> cxrEmployeeListVoIPage =
            baseMapper.page(
                cxrEmployeeBo,
                pageQuery.build(),
                OccupationStatus.WAIT_INDUCTION.getValue(),
                OccupationStatus.NO_INDUCTION.getValue(),
                dept,
                notDeletedValue,
                employeeIds,
                theSiteIds,
                OccupationStatus.QUIT_ONESELF.getValue(),
                OccupationStatus.QUIT_PROCEDURE.getValue(),
                OccupationStatus.INDUCTION.getValue(),
                qyjlFlag,
                qylSiteIds);

        if (CollectionUtil.isNotEmpty(cxrEmployeeListVoIPage.getRecords())) {

            assignment(cxrEmployeeListVoIPage.getRecords(), value);
        }

        PageTableDataInfo<CxrEmployeeListVo> cxrPageVo =
            PageTableDataInfo.build(cxrEmployeeListVoIPage);

        // 判断是否是    人事,it,总经办,财务部    是的话 flag =false
        boolean flag = true;
        a:
        for (String s : roleKey) {
            List<String> key = roleKeyAuthority.getRoleKeys();
            if (CollectionUtil.isNotEmpty(key)) {
                boolean contains = key.contains(s);
                if (contains) {
                    flag = false;
                    break a;
                }
            }
//            if (s.equals("admin")
//                || s.equals("rsxzb")
//                || s.equals("it")
//                || s.equals("zjb")
//                || s.equals("cwb")
//                || s.equals("gly")
//                || s.equals("rsb")) {
//            }
        }
        // false  不会进入   设置无权查看
        // 根据角色名称来进行
        if (flag) {
            cxrPageVo.getRows().stream()
                .forEach(
                    vo -> {
                        vo.setIdCard("****");
                        vo.setIdCardBackPictureUrl("****");
                        vo.setIdCardPositivePictureUrl("****");
                        vo.setDepositBankCardIdCard("****");
                        vo.setDepositBankCardName("****");
                        vo.setDepositBankCardNumber("****");
                        vo.setDepositBankCardPhone("****");
                        vo.setFamilyDepositBankCardPhone("****");
                        vo.setFamilyDepositBankCardIdCard("****");
                        vo.setFamilyDepositBankCardNumber("****");
                        vo.setSalesRepresentativeRegisterFileUrl("****");
                        vo.setGradeProxyAgreementFileUrl("****");
                        vo.setProxyContractFileUrl("****");
                    });
        }
        return cxrPageVo;
    }

    public void assignment(List<CxrEmployeeListVo> cxrEmployeeListVos, String value) {

        Set<Long> cxrGroupIds = new HashSet<>();
        Set<Long> cxrSiteIds = new HashSet<>();
        Set<Long> ids = new HashSet<>();
        Set<Long> introducerIds = new HashSet<>();
        Set<Long> masterIds = new HashSet<>();

        cxrEmployeeListVos.forEach(
            a -> {
                cxrGroupIds.add(a.getCxrGroupId());
                cxrSiteIds.add(a.getCxrSiteId());
                ids.add(a.getId());
                introducerIds.add(a.getIntroducerId());
                masterIds.add(a.getMasterId());
                a.setAuthStatusName(AuthStatusEnums.getMsg(a.getAuthStatus()));
//                if (a.getWorkFrequency().equals(1) && a.getAuthStatus()
//                    .equals(AuthStatusEnums.close.getCode()) && !a.getCxrSiteId().equals(1595957430981169153L)) {
//                    a.setInductionTime(null);
//                }
            });
        cxrGroupIds.removeIf(a -> a == null);
        cxrGroupIds.add(-1L);
        cxrSiteIds.removeIf(a -> a == null);
        cxrSiteIds.add(-1L);
        ids.add(-1L);
        masterIds.removeIf(a -> a == null);
        masterIds.add(-1L);
        introducerIds.removeIf(a -> a == null);
        introducerIds.add(-1L);
        // 公司
        CompletableFuture<Map<Long, SysDeptvo>> mapDeptFuture =
            CompletableFuture.supplyAsync(
                () -> {
                    List<SysDeptvo> sysDeptvos = remoteDeptService.selectAll();
                    return sysDeptvos.stream()
                        .collect(
                            Collectors.toMap(SysDeptvo::getDeptId, Function.identity(), (v1, v2) -> v2));
                });

        // 站点
        CompletableFuture<List<CxrSite>> listSiteFuture =
            CompletableFuture.supplyAsync(
                () -> {
                    List<CxrSite> reSites = cxrSiteMapper.getCxrSiteIds(cxrSiteIds);
                    return CollectionUtil.isNotEmpty(reSites) ? reSites : new ArrayList<>();
                });

        // 区域
        CompletableFuture<List<CxrRegion>> listRegionFuture =
            listSiteFuture.thenApplyAsync(
                reSites -> {
                    List<Long> reRegionIds =
                        reSites.stream()
                            .map(s -> Long.valueOf(s.getCxrRegionId()))
                            .collect(Collectors.toList());
                    List<CxrRegion> cxrRegions = null;
                    if (CollectionUtil.isNotEmpty(reRegionIds)) {
                        cxrRegions =
                            new LambdaQueryChainWrapper<>(cxrRegionMapper)
                                .in(CxrRegion::getId, reRegionIds)
                                .eq(CxrRegion::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                                .list();
                    }
                    return CollectionUtil.isNotEmpty(cxrRegions) ? cxrRegions : new ArrayList<>();
                });

        CompletableFuture<Map<Long, CxrGroupListVo>> mapGroupFuture =
            CompletableFuture.supplyAsync(
                () -> {
                    List<CxrGroupListVo> cxrGroups =
                        cxrGroupMapper.selectListByIds(StringUtils.join(cxrGroupIds, ","));
                    return CollectionUtil.isNotEmpty(cxrGroups)
                        ? cxrGroups.stream()
                        .collect(
                            Collectors.toMap(
                                CxrGroupListVo::getId, Function.identity(), (v1, v2) -> v2))
                        : new HashMap<>();
                });

        CompletableFuture<Map<Long, List<CxrEmployeePostVo>>> postFuture =
            CompletableFuture.supplyAsync(
                () -> {
                    List<CxrEmployeePostVo> cxrEmployeePostVos =
                        cxrEmployeePostMapper.selectBySiteIds(
                            cxrSiteIds, value, ChargeStatus.IN_PROGRESS.getValue());
                    return CollectionUtil.isNotEmpty(cxrEmployeePostVos)
                        ? cxrEmployeePostVos.stream()
                        .collect(Collectors.groupingBy(CxrEmployeePostVo::getCxrSiteId))
                        : new HashMap<>();
                });

        CompletableFuture<Map<Long, List<CxrEmployeePost>>> mapEmployeePostFuture =
            CompletableFuture.supplyAsync(
                () -> {
                    // 自己的职位
                    List<CxrEmployeePost> cxrEmployeePosts =
                        cxrEmployeePostMapper.selectList(
                            new LambdaQueryWrapper<CxrEmployeePost>()
                                .in(CxrEmployeePost::getCxrEmployeeId, ids)
                                .eq(CxrEmployeePost::getChargeStatus, ChargeStatus.IN_PROGRESS.getValue())
                                .eq(
                                    CxrEmployeePost::getDeleteStatus,
                                    DeleteStatus.NOT_DELETED.getValue()));
                    return CollectionUtil.isNotEmpty(cxrEmployeePosts)
                        ? cxrEmployeePosts.stream()
                        .collect(Collectors.groupingBy(CxrEmployeePost::getCxrEmployeeId))
                        : new HashMap<>();
                });

        CompletableFuture<Map<Long, CxrEmployee>> masterMapFuture = CompletableFuture.supplyAsync(
            () -> {
                //师傅名称
                List<CxrEmployee> employeeList = lambdaQuery()
                    .in(CxrEmployee::getId, masterIds)
                    .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                    .select(CxrEmployee::getId, CxrEmployee::getName, CxrEmployee::getJobNumber)
                    .list();

                return CollectionUtil.isNotEmpty(employeeList) ?
                    employeeList.stream().collect(Collectors.toMap(CxrEmployee::getId, Function.identity(),
                        (v1, v2) -> v2)) : new HashMap<>();

            });

        CompletableFuture<Map<Long, CxrEmployee>> introducerMapFuture = CompletableFuture.supplyAsync(
            () -> {
                // 介绍人
                List<CxrEmployee> employeeList = lambdaQuery()
                    .in(CxrEmployee::getId, introducerIds)
                    .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                    .select(CxrEmployee::getId, CxrEmployee::getName, CxrEmployee::getJobNumber)
                    .list();

                return CollectionUtil.isNotEmpty(employeeList) ?
                    employeeList.stream().collect(Collectors.toMap(CxrEmployee::getId, Function.identity(),
                        (v1, v2) -> v2)) : new HashMap<>();
            });

        CompletableFuture.allOf(
                mapDeptFuture,
                listSiteFuture,
                listRegionFuture,
                mapGroupFuture,
                postFuture,
                mapEmployeePostFuture,
                masterMapFuture,
                introducerMapFuture
            )
            .join();

        Map<Long, SysDeptvo> deptvoMap = null;
        Map<Long, CxrSite> siteMap = null;
        Map<Long, CxrRegion> finalRegionMap = null;
        Map<Long, CxrGroupListVo> cxrGroupMap = null;
        Map<Long, List<CxrEmployeePostVo>> postMap = null;
        Map<Long, List<CxrEmployeePost>> cxrEmployeePostMap = null;
        Map<Long, CxrEmployee> masterMap = null;
        Map<Long, CxrEmployee> introducerMap = null;

        try {
            deptvoMap = mapDeptFuture.get();
            siteMap =
                listSiteFuture.get().stream()
                    .collect(Collectors.toMap(CxrSite::getId, Function.identity(), (v1, v2) -> v2));
            finalRegionMap =
                listRegionFuture.get().stream()
                    .collect(Collectors.toMap(CxrRegion::getId, Function.identity(), (v1, v2) -> v2));
            cxrGroupMap = mapGroupFuture.get();
            postMap = postFuture.get();
            cxrEmployeePostMap = mapEmployeePostFuture.get();
            masterMap = masterMapFuture.get();
            introducerMap = introducerMapFuture.get();

        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
        if (CollectionUtil.isEmpty(siteMap)) {
            return;
        }
        for (CxrEmployeeListVo s : cxrEmployeeListVos) {
            // 公司名称
            SysDeptvo sysDeptvo = deptvoMap.get(s.getSysDeptId());
            s.setCompanyName(Objects.isNull(sysDeptvo) ? null : sysDeptvo.getDeptName());


            // 站点
            CxrSite cxrSite = siteMap.get(s.getCxrSiteId());
            if (cxrSite == null) {
                continue;
            }
            s.setProvince(cxrSite.getProvice());
            s.setCity(cxrSite.getCity());
            s.setArea(cxrSite.getArea());
            s.setCxrSiteName(cxrSite.getName());
            s.setRegionId(cxrSite.getCxrRootRegionId());
            s.setRegionName(cxrSite.getCxrRootRegionName());
            //大区
            SysDeptvo sysRegionDept = deptvoMap.get(s.getRegionId());
            if (sysRegionDept != null) {
                SysDeptvo sysBigRegionDept = ObjectUtil.defaultIfNull(deptvoMap.get(sysRegionDept.getParentId()),
                    new SysDeptvo());
                s.setBigRegionName(sysBigRegionDept.getDeptName());

            }
            // 区域
            CxrRegion cxrRegion = finalRegionMap.get(Long.valueOf(cxrSite.getCxrRegionId()));
            if (ObjectUtil.isNotEmpty(cxrRegion)) {
                s.setCxrRegionName(Convert.toStr(cxrRegion.getName(), "区域没有名称"));
            } else {
                s.setCxrRegionName("没有查到区域");
            }

            // 原组长名称
            CxrGroupListVo cxrGroup = cxrGroupMap.get(s.getCxrGroupId());
            if (ObjectUtil.isNotEmpty(cxrGroup) && StringUtils.isNotBlank(cxrGroup.getSourceName())) {
                s.setSourceGroupLeaderName(cxrGroup.getSourceName());
            } else {
                s.setSourceGroupLeaderName("--");
            }

            // 站点主管名称
            List<CxrEmployeePostVo> employeePosts = postMap.get(s.getCxrSiteId());
            if (CollectionUtil.isNotEmpty(employeePosts)) {
                s.setSiteDirectorNameAll(
                    employeePosts.stream()
                        .map(CxrEmployeePostVo::getCxrEmployeeName)
                        .collect(Collectors.joining("；")));
            } else {
                s.setSiteDirectorNameAll(null);
            }
            // 自己的职位
            List<CxrEmployeePost> cxrEmployeePosts = cxrEmployeePostMap.get(s.getId());
            if (CollectionUtil.isNotEmpty(cxrEmployeePosts)) {
                s.setPost(
                    cxrEmployeePosts.stream()
                        .map(i -> i.getCxrPostId().getName())
                        .distinct()
                        .collect(Collectors.joining("；")));
            }

            if (CollectionUtil.isNotEmpty(masterMap)) {
                CxrEmployee cxrEmployee = masterMap.get(Convert.toLong(s.getMasterId(), -1L));
                if (ObjectUtil.isNotEmpty(cxrEmployee)) {
                    s.setMasterName(cxrEmployee.getName());
                    s.setMasterJobNumber(cxrEmployee.getJobNumber());
                }
            }
            if (CollectionUtil.isNotEmpty(introducerMap)) {
                CxrEmployee cxrEmployee = introducerMap.get(Convert.toLong(s.getIntroducerId(), -1L));
                if (ObjectUtil.isNotEmpty(cxrEmployee)) {
                    s.setIntroducerName(cxrEmployee.getName());
                    s.setIntroducerJobNumber(cxrEmployee.getJobNumber());
                }
            }


        }
    }

    /**
     * 根据条件获取全部数据 用于导出数据 <>弃用</>
     *
     * @param cxrEmployeeBo
     * @return
     */
    @Override
    public List<CxrEmployeeListVo> allList(CxrEmployeeBo cxrEmployeeBo) {

        Long deptId = LoginHelper.getLoginUser().getDeptId();
        List<String> list = new ArrayList<>();
        list.add(PostType.DIRECTOR.getValue());
        list.add(PostType.GENERATION_DIRECTOR.getValue());
        list.add(PostType.DEVELOPMENT_DIRECTOR.getValue());

        String value = JSONUtil.toJsonStr(list);

        List<CxrEmployeeListVo> cxrEmployeeListVos =
            baseMapper.selectEmployeeByList(
                cxrEmployeeBo,
                value,
                OccupationStatus.WAIT_INDUCTION.getValue(),
                OccupationStatus.NO_INDUCTION.getValue(),
                deptId,
                DeleteStatus.NOT_DELETED.getValue(),
                ChargeStatus.IN_PROGRESS.getValue());

        // 根据   用户id 查询  角色信息
        Long userId = LoginHelper.getLoginUser().getUserId();
        List<SysRole> sysRoles = remoteRoleService.getByUserId(userId);
        List<String> roleNameList =
            sysRoles.stream()
                .map(
                    s -> {
                        return s.getRoleName();
                    })
                .collect(Collectors.toList());
        // 根据角色名称来进行
        // TODO  待优化         人力和培训部门不能查看  身份证银行卡等隐秘信息
        if (roleNameList.contains("人力") || roleNameList.contains("培训")) {
            cxrEmployeeListVos.stream()
                .forEach(
                    vo -> {
                        vo.setIdCard("无权查看");
                        vo.setIdCardBackPictureUrl("无权查看");
                        vo.setIdCardPositivePictureUrl("无权查看");
                        vo.setDepositBankCardIdCard("无权查看");
                        vo.setDepositBankCardName("无权查看");
                        vo.setDepositBankCardNumber("无权查看");
                        vo.setDepositBankCardPhone("无权查看");
                        vo.setFamilyDepositBankCardPhone("无权查看");
                        vo.setFamilyDepositBankCardIdCard("无权查看");
                        vo.setFamilyDepositBankCardNumber("无权查看");
                        vo.setSalesRepresentativeRegisterFileUrl("无权查看");
                        vo.setGradeProxyAgreementFileUrl("无权查看");
                        vo.setProxyContractFileUrl("无权查看");
                    });
        }
        return cxrEmployeeListVos;
    }

    /**
     * 根据分组 更新组员 ，同时设置改组的组长
     *
     * @param employeeId
     * @param
     */
    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public boolean updateGroupMemberByGroupId(Long employeeId) {
        if (ObjectUtil.isNull(employeeId)) {
            return false;
        }

        CxrEmployee dataCxrEmployee = this.getId(employeeId);
        if (ObjectUtil.isNull(dataCxrEmployee.getCxrGroupId())) {
            return false;
        }

        CxrGroup cxrGroup = iCxrGroupService.getById(dataCxrEmployee.getCxrGroupId());

        List<Long> member = cxrGroup.getMember();
        if (CollUtil.isEmpty(cxrGroup.getMember())) {
            member = new ArrayList<>();
        }

        member.add(employeeId);
        int rows =
            iCxrGroupService
                .getBaseMapper()
                .update(
                    null,
                    Wrappers.lambdaUpdate(CxrGroup.class)
                        .set(CxrGroup::getMember, JSONUtil.toJsonStr(member))
                        .eq(CxrGroup::getId, cxrGroup.getId())
                        .eq(CxrGroup::getRevision, cxrGroup.getRevision())
                        .set(CxrGroup::getRevision, cxrGroup.getRevision() + 1));
        log.info("更新分组表 {}", rows);

        iCxrGroupService.addChangeRecord(cxrGroup.getCxrSiteId(), cxrGroup.getId(), member, 0);
        iCxrGroupHistoryService.insert(iCxrGroupService.getById(cxrGroup.getId()));

        // 更新当前员工的组长 和组长名字
        if (ObjectUtil.isNotNull(cxrGroup.getNowCxrEmployeeId())) {
            CxrEmployee cxrEmployee = baseMapper.getId(cxrGroup.getNowCxrEmployeeId());
            if (ObjectUtil.isNotEmpty(cxrEmployee)) {
                int rows2 =
                    this.getBaseMapper()
                        .update(
                            null,
                            new LambdaUpdateWrapper<CxrEmployee>()
                                .set(CxrEmployee::getGroupLeaderId, cxrEmployee.getId())
                                .set(CxrEmployee::getGroupLeaderName, cxrEmployee.getName())
                                .eq(CxrEmployee::getId, employeeId));
                log.info("更新员工表 {}", rows2);
            }
        }
        return true;
    }

    /**
     * 二次入职 待写 创建者?
     *
     * @param id 员工id
     * @return
     */
    @Override
    public Boolean secondInduction(Long id) {
        return null;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    @RedisDistributedLock(value = "'distribution:employee:add:' + #cxrEmployeeBo.phone")
    public long add(CxrEmployeeBo cxrEmployeeBo) {

        if (StrUtil.isBlank(cxrEmployeeBo.getPhone())) {
            throw new ServiceException("入职手机请必须填写");
        }
        // 尝试获取锁 防止重复录入
        LoginInfo loginUser = LoginUtil.getLoginUser();

        // 校验关联站点信息

        LambdaQueryWrapper<CxrSite> query =
            Wrappers.lambdaQuery(CxrSite.class)
                    .
                // 获取该部门的站点
                    eq(CxrSite::getId, cxrEmployeeBo.getCxrSiteId())
                .eq(CxrSite::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue());

        CxrSite cxrSites = cxrSiteMapper.selectOne(query);

        if (ObjectUtil.isNull(cxrSites)) {
            throw new ServiceException("抱歉，站点信息已不存在，请刷新重试");
        }
        // 第一个站点名称
        String siteName = cxrSites.getName();

        // 校验关联站点小组信息
        Long count = 0L;
        if (ObjectUtil.isNotNull(cxrEmployeeBo.getCxrGroupId())) {
            count =
                cxrGroupMapper.selectCount(
                    Wrappers.lambdaQuery(CxrGroup.class)
                        .eq(CxrGroup::getId, cxrEmployeeBo.getCxrGroupId())
                        .eq(CxrGroup::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));

            if (count < 1) {
                throw new ServiceException("抱歉，该站点的该小组信息已不存在，请刷新重试");
            }
        }

        // 校验手机号码，这里不去校验手机号码是否否合号码规范这种基本校验，而是做业务上的逻辑校验
        // 由于目前使用手机号码作为配送端的登录手机号码，所以员工的手机号码在整个系统中的所有员工表中唯一
        cxrEmployeeBo.setPhone(StrUtil.cleanBlank(cxrEmployeeBo.getPhone()));
        count =
            baseMapper.selectCount(
                Wrappers.lambdaQuery(CxrEmployee.class)
                    .eq(CxrEmployee::getPhone, cxrEmployeeBo.getPhone())
                    .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));
        if (count > 0) {
            throw new ServiceException("该手机号码已被使用");
        }

        // 校验员工工号
        cxrEmployeeBo.setJobNumber(StrUtil.cleanBlank(cxrEmployeeBo.getJobNumber()));
        count =
            baseMapper.selectCount(
                Wrappers.lambdaQuery(CxrEmployee.class)
                    .eq(CxrEmployee::getJobNumber, cxrEmployeeBo.getJobNumber())
                    .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));

        if (count > 0) {
            throw new ServiceException("该工号已被使用");
        }

        // 员工realName可以重复(realName由身份证识别得出，不能主动添加或更改)，但是name不能重复，
        // 这里限制这个name在当前站点下不能重复
        // cxrEmployeeBo.setName(StrUtil.cleanBlank(cxrEmployeeBo.getName()));
        // count = baseMapper.selectCount(Wrappers.lambdaQuery(CxrEmployee.class).
        //    eq(CxrEmployee::getName, cxrEmployeeBo.getName()).
        //    eq(CxrEmployee::getCxrSiteId, cxrEmployeeBo.getCxrSiteId()).
        //    eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));
        //
        // if (count > 0) {
        //    throw new ServiceException("该员工名字已被使用");
        // }

        // 校验身份证号码
        if (StringUtils.isNotBlank(cxrEmployeeBo.getIdCard())) {
            cxrEmployeeBo.setIdCard(StrUtil.cleanBlank(cxrEmployeeBo.getIdCard()));
            count =
                baseMapper.selectCount(
                    Wrappers.lambdaQuery(CxrEmployee.class)
                        .eq(CxrEmployee::getIdCard, cxrEmployeeBo.getIdCard())
                        .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));

            if (count > 0) {
                throw new ServiceException("该身份证号码已被录入系统中");
            }
        }

        CxrEmployee cxrEmployee = BeanUtil.toBean(cxrEmployeeBo, CxrEmployee.class);

        String name = getEmployeeName(cxrEmployee.getCxrSiteId(), cxrEmployee.getRealName());

        cxrEmployee.setProxyType(new ArrayList<String>((Set) cxrEmployeeBo.getCxrProxyIdSet()));

        // 设置一些员工的初始信息
        cxrEmployee.setWorkFrequency(1);
        cxrEmployee.setSubmitTime(new Date());
        cxrEmployee.setIsBindWx(SysYesNo.NO.getValue());
        cxrEmployee.setApplicationChannelsType(cxrEmployeeBo.getApplicationChannelsType());
        cxrEmployee.setSiteName(siteName); // qy新增加字段
        cxrEmployee.setEmployeeAddress(cxrEmployeeBo.getAddress());

        // 时间
        EmployeeHistoryDate employeeHistoryDate = new EmployeeHistoryDate();
        employeeHistoryDate.setStartDate(cxrEmployeeBo.getInductionTime());
        List<EmployeeHistoryDate> employeeHistoryDateList = new ArrayList<>();
        employeeHistoryDateList.add(employeeHistoryDate);
        cxrEmployee.setHistoryDate(employeeHistoryDateList);

        if (NumberUtil.equals(LoginTypeUtil.getTerminalType(), TerminalTypeEnums.manager.getValue())) {
            // 入职时间小于等于当前时间，为《在职》
            if (cxrEmployee.getInductionTime().compareTo(new Date()) < 1) {
                // 入职时间小于等于当前时间，为《在职》  2025-3-14 去掉，不然会导致可以提前做业绩，导致业绩和对不上
                cxrEmployee.setOccupationStatus(OccupationStatus.WAIT_INDUCTION.getValue());
            } else {
                // 入职时间大于当前时间，为《待入职》
                cxrEmployee.setOccupationStatus(OccupationStatus.WAIT_INDUCTION.getValue());
            }
            cxrEmployee.setInductionApplyAuditStatus(InductionApplyAuditStatus.SUCCESS.getValue());
            cxrEmployee.setEmployeeLevelType(cxrEmployeeBo.getEmployeeLevelType());
        }

        if (NumberUtil.equals(
            LoginTypeUtil.getTerminalType(), TerminalTypeEnums.disribution.getValue())) {
            cxrEmployee.setInductionApplyAuditStatus(InductionApplyAuditStatus.SUCCESS.getValue());
            // 入职时间大于当前时间，为《待入职》
            cxrEmployee.setOccupationStatus(OccupationStatus.WAIT_INDUCTION.getValue());
            cxrEmployee.setEmployeeLevelType(
                EmployeeLevelType.getNewInductionEmployeeInitialLevelType().getValue());
        }

        cxrEmployee.setIntroducerId(cxrEmployeeBo.getCxrIntroducerId());
        // 添加  介绍人名称
        cxrEmployee.setIntroducerName(cxrEmployeeBo.getIntroducerName());

        cxrEmployee.setMasterId(cxrEmployeeBo.getCxrMasterId());
        // 添加 师傅名称
        if (StringUtils.isNotBlank(cxrEmployeeBo.getCxrMasterName())) {
            cxrEmployee.setMasterName(cxrEmployeeBo.getCxrMasterName());
        } else {
            cxrEmployee.setMasterName(cxrEmployeeBo.getMasterName());
        }

        // 设置小组长
        if (cxrEmployeeBo.getCxrGroupId() != null) {
            CxrGroup cxrGroup = cxrGroupMapper.selectById(cxrEmployeeBo.getCxrGroupId());
            Long nowCxrEmployeeId = cxrGroup.getNowCxrEmployeeId();
            CxrEmployee cxr = this.getById(nowCxrEmployeeId);
            if (ObjectUtil.isNotNull(cxr)) {
                cxrEmployee.setGroupLeaderId(cxr.getId());
                cxrEmployee.setGroupLeaderName(cxr.getName());
            }
        }

        cxrEmployee.setName(name);
        //如果虚拟账号默认已认证
        if (cxrEmployeeBo.getAccountFlag() != null && cxrEmployeeBo.getAccountFlag() == 2) {
            cxrEmployee.setAuthStatus(AuthStatusEnums.open.getCode());
        }
        boolean flag = false;
        try {
            //白名单
            if (cxrSecurityConfig.getDealerRegionId().contains(cxrSites.getCxrRootRegionId().toString())) {
                cxrEmployee.setOccupationStatus(OccupationStatus.INDUCTION.getValue());
            }
            flag = SqlHelper.retBool(baseMapper.insert(cxrEmployee));
            cxrEmployeeBo.setId(cxrEmployee.getId());
        } catch (Exception e) {
            Throwable cause = e.getCause();
            if (cause instanceof SQLIntegrityConstraintViolationException) {
                // 错误信息
                String errMsg = ((java.sql.SQLIntegrityConstraintViolationException) cause).getMessage();
                // 根据约束名称定位是那个字段
                if (StringUtils.isNotBlank(errMsg) && errMsg.indexOf("job_number") != -1) {
                    throw new ServiceException("生成重复员工工号,请重新提交!");
                }
            }
            throw e;
        }
        if (!flag) {
            throw new ServiceException("保存失败,请刷新重试!");
        }

        if (OccupationStatus.WAIT_INDUCTION.getValue().equals(cxrEmployee.getOccupationStatus())
            && NumberUtil.equals(
            LoginTypeUtil.getTerminalType(), TerminalTypeEnums.manager.getValue())) {
            SpringUtils.getBean(ICxrEmployeeService.class)
                .mqProducerToEmployeeInduction(
                    cxrEmployee.getId(), cxrEmployee.getInductionTime().getTime());
        }

        // 添加员工职位 默认是普通职员
        CxrEmployeePost cxrEmployeePostBo = new CxrEmployeePost();
        cxrEmployeePostBo.setCxrEmployeeId(cxrEmployee.getId());

        CxrPostId cxrPostId = new CxrPostId();
        cxrPostId.setValue(PostType.COMMON_EMPLOYEE.getValue());
        cxrPostId.setScope(PostType.COMMON_EMPLOYEE.getScope());
        cxrPostId.setName(PostType.COMMON_EMPLOYEE.getName());

        cxrEmployeePostBo.setCxrPostId(cxrPostId);
        cxrEmployeePostBo.setCxrSiteId(cxrEmployee.getCxrSiteId());
        cxrEmployeePostBo.setInChargeStartTime(new Date());

        iCxrEmployeePostService.save(cxrEmployeePostBo);
        saveOrUpdateCooperateHistory(cxrEmployee.getInductionTime(), cxrEmployee.getId(), null, null, 1);

        // 配送端添加 不需要审核直接同步到培训系统
        if (NumberUtil.equals(LoginTypeUtil.getTerminalType(), TerminalTypeEnums.manager.getValue())) {
            updateGroupMemberByGroupId(cxrEmployee.getId());
            mqUtil.sendSyncMessage(
                EmployeeConstant.EMPLOYEE_TOPIC,
                EmployeeConstant.SYNC_EMPLOYEE_INFO_TAG,
                cxrEmployee.getId() + "");
        } else {
            //白名单
            if (cxrSecurityConfig.getDealerRegionId().contains(cxrSites.getCxrRootRegionId().toString())) {
                updateGroupMemberByGroupId(cxrEmployee.getId());
                mqUtil.sendSyncMessage(
                    EmployeeConstant.EMPLOYEE_TOPIC,
                    EmployeeConstant.SYNC_EMPLOYEE_INFO_TAG,
                    cxrEmployee.getId() + "");
            }
            //招聘更新
            updateEmployeeRecruit(cxrEmployee);
        }

        //后端添加的直接入职
        if (NumberUtil.equals(LoginTypeUtil.getTerminalType(), TerminalTypeEnums.manager.getValue())) {
            saveRecord(cxrEmployee, 1);
            updateEmployeeRecruit(cxrEmployee);
            //新人入职 L1 mq  后台允许自定义等级
            SaleLevelMqSendUtil.sendChangeAll(cxrEmployee.getId(), cxrEmployee.getSubmitTime(),
                EmployeeLevelType.getEnumByValue(cxrEmployeeBo.getEmployeeLevelType()), null, 3);
        } else {
            //新人入职 L1 mq 配送端默认为1级
            SaleLevelMqSendUtil.sendChangeAll(cxrEmployee.getId(), cxrEmployee.getSubmitTime(),
                EmployeeLevelType.getEnumByValue(cxrEmployeeBo.getEmployeeLevelType()), 1, 3);
        }

        if (flag) {
            mqUtil.sendSyncMessage(EmployeeConstant.EMPLOYEE_TOPIC, EmployeeConstant.EMPLOYEE_PHONE_TAG, cxrEmployee.getPhone());
            return cxrEmployee.getId();
        }
        return 0L;
    }

    private void saveRecord(CxrEmployee cxrEmployee, Integer cooperateCount) {

        //入职记录
        CxrEmployeeCooperateRecord employeeCooperateRecord = new CxrEmployeeCooperateRecord();
        employeeCooperateRecord.setEmployeeLevelType(cxrEmployee.getEmployeeLevelType());
        employeeCooperateRecord.setCxrEmployeeId(cxrEmployee.getId());
        employeeCooperateRecord.setBsDate(DateUtils.getLocalDateFromDate(cxrEmployee.getInductionTime()));
        employeeCooperateRecord.setCxrSiteId(cxrEmployee.getCxrSiteId());
        employeeCooperateRecord.setCooperateCount(cooperateCount);
        employeeCooperateRecord.setBsType(CooperateStatus.COOPERATION.getValue());
        mqUtil.sendSyncMessage(EmployeeConstant.EMPLOYEE_TOPIC, EmployeeConstant.EMPLOYEE_COOPERATION_STOP_TAG,
            JSONUtil.toJsonStr(employeeCooperateRecord));
    }

    public void saveOrUpdateCooperateHistory(Date induction, Long id, String occupationStatus, Date quitTime,
                                             int cooperate) {

        try {
            if (cooperate == 1) {
                List<CxrEmployeeCooperateHistory> cooperateHistoryList = cxrEmployeeCooperateHistoryMapper
                    .selectList(new LambdaQueryWrapper<CxrEmployeeCooperateHistory>()
                        .eq(CxrEmployeeCooperateHistory::getEmployeeId, id)
                        .orderByAsc(CxrEmployeeCooperateHistory::getFrequency));
                LocalDate inductionDate = DateUtils.getLocalDateFromDate(induction);
                if (CollectionUtil.isEmpty(cooperateHistoryList)) {
                    CxrEmployeeCooperateHistory cooperateHistory = new CxrEmployeeCooperateHistory();
                    cooperateHistory.setEmployeeId(id);
                    cooperateHistory.setStartDate(inductionDate);
                    cooperateHistory.setOccupation(occupationStatus);
                    cooperateHistory.setFrequency(1);
                    cooperateHistory.setCreateTime(new Date());
                    cxrEmployeeCooperateHistoryMapper.insert(cooperateHistory);
                } else {

                    CxrEmployeeCooperateHistory last = null;
                    for (CxrEmployeeCooperateHistory cooperateHistory : cooperateHistoryList) {
                        LocalDate endDate = cooperateHistory.getEndDate();

                        AbstractAssert.isNull(endDate,
                            "上次加入还没结束，不能重新加入。上次加入时间：" + cooperateHistory.getStartDate());
                        AbstractAssert.isTrue(!inductionDate.isAfter(endDate),
                            "上次加入时间：" + cooperateHistory.getStartDate() + "，结束时间：" + endDate
                                + "，时间不能重复。");

                        last = cooperateHistory;
                    }

                    CxrEmployeeCooperateHistory cooperateHistory = new CxrEmployeeCooperateHistory();
                    cooperateHistory.setEmployeeId(id);
                    cooperateHistory.setStartDate(inductionDate);
                    cooperateHistory.setOccupation(occupationStatus);
                    cooperateHistory.setFrequency(last.getFrequency() + 1);
                    cooperateHistory.setCreateTime(new Date());
                    cxrEmployeeCooperateHistoryMapper.insert(cooperateHistory);
                }
            } else if (cooperate == 2) {
                CxrEmployeeCooperateHistory cooperateHistory = cxrEmployeeCooperateHistoryMapper
                    .selectOne(new LambdaQueryWrapper<CxrEmployeeCooperateHistory>()
                        .eq(CxrEmployeeCooperateHistory::getEmployeeId, id)
                        .isNull(CxrEmployeeCooperateHistory::getEndDate));

                AbstractAssert.isNull(cooperateHistory, "该销售代理没有合作中的记录。");
                AbstractAssert.isNull(quitTime, "终止合作结束时间不能为空。");

                LocalDate quitDate = DateUtils.getLocalDateFromDate(quitTime);
                AbstractAssert.isTrue(!cooperateHistory.getStartDate().isBefore(quitDate),
                    "终止合作时间不能在入职时间之前");

                cxrEmployeeCooperateHistoryMapper.update(null, Wrappers.<CxrEmployeeCooperateHistory>lambdaUpdate()
                    .set(CxrEmployeeCooperateHistory::getEndDate, quitDate)
                    .set(CxrEmployeeCooperateHistory::getOccupation, occupationStatus)
                    .eq(CxrEmployeeCooperateHistory::getId, cooperateHistory.getId()));
            } else if (cooperate == 3) {
                List<CxrEmployeeCooperateHistory> cooperateHistoryList = cxrEmployeeCooperateHistoryMapper
                    .selectList(new LambdaQueryWrapper<CxrEmployeeCooperateHistory>()
                        .eq(CxrEmployeeCooperateHistory::getEmployeeId, id)
                        .orderByAsc(CxrEmployeeCooperateHistory::getFrequency));
                AbstractAssert.isEmpty(cooperateHistoryList, "合作记录数据异常,没有合作记录");

                CxrEmployeeCooperateHistory last = null;
                for (CxrEmployeeCooperateHistory cooperateHistory : cooperateHistoryList) {
                    AbstractAssert.isNull(cooperateHistory.getEndDate(), "合作记录数据异常，有未结束的记录");
                    last = cooperateHistory;
                }
                cxrEmployeeCooperateHistoryMapper.update(null, Wrappers.<CxrEmployeeCooperateHistory>lambdaUpdate()
                    .set(CxrEmployeeCooperateHistory::getEndDate, null)
                    .eq(CxrEmployeeCooperateHistory::getId, last.getId()));
            }
        } catch (Exception e) {
            log.error("saveOrUpdateCooperateHistory:{}", e.getMessage());
        }
    }

    @Override
    public void authSeccuss(CxrEmployee cxrEmployee) {
        //同步到培训系统
        this.updateGroupMemberByGroupId(cxrEmployee.getId());
        if (com.ruoyi.common.core.utils.StringUtils.equals(
            cxrEmployee.getOccupationStatus(), OccupationStatus.WAIT_INDUCTION.getValue())) {
            SpringUtils.getBean(ICxrEmployeeService.class)
                .mqProducerToEmployeeInduction(
                    cxrEmployee.getId(), cxrEmployee.getInductionTime().getTime());
        }
        mqUtil.sendSyncMessage(
            EmployeeConstant.EMPLOYEE_TOPIC,
            EmployeeConstant.UPDATE_SYNC_EMPLOYEE_INFO_TAG,
            cxrEmployee.getId() + "");

        this.saveRecord(cxrEmployee, cxrEmployee.getWorkFrequency() > 1 ? 2 : 1);

        SynEmployeeBo bo = new SynEmployeeBo();
        bo.setEmployeeId(cxrEmployee.getId());
        bo.setJobNumber(cxrEmployee.getJobNumber());
        bo.setPhone(cxrEmployee.getPhone());
        bo.setRealName(cxrEmployee.getRealName());
        this.syncIntoMallUserEmployee(bo);
    }

    @Override
    public List<CxrEmployee> repairData(String id) {
        String[] idArray = id.split(",");
        List<Long> idList = Arrays.stream(idArray)
            .map(Long::parseLong)
            .collect(Collectors.toList());
        List<CxrEmployee> employeeList =
            iCxrEmployeeService.getBaseMapper().selectList(new LambdaQueryWrapper<CxrEmployee>()
                .in(CxrEmployee::getId, idList)
            );
        for (CxrEmployee employee : employeeList) {
            CxrGroup cxrGroup = cxrGroupMapper.selectOne(new LambdaQueryWrapper<CxrGroup>()
                .eq(CxrGroup::getCxrSiteId, employee.getCxrSiteId())
                .eq(CxrGroup::getId, employee.getCxrGroupId())
                .like(CxrGroup::getMember, employee.getId())
                .eq(CxrGroup::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            );
            if (ObjectUtil.isEmpty(cxrGroup)) {
                iCxrEmployeeService.updateGroupMemberByGroupId(employee.getId());
            }
        }
        return employeeList;
    }

    @Override
    public Boolean deleteById(Long id) {
        CxrEmployee cxrEmployee = baseMapper.selectById(id);
        LoginEmployee loginEmployee = StaffLoginHelper.getLoginUser();
        if (ObjectUtil.isEmpty(cxrEmployee)) {
            throw new ServiceException("数据异常,请刷新页面!!!");
        }
        if (cxrEmployee.getOccupationStatus().equals(OccupationStatus.INDUCTION.getValue())
            && cxrEmployee.getAuthStatus().equals(AuthStatusEnums.close.getCode())) {
            throw new ServiceException("已入职待认证,不能进行删除!!!");
        }
        if (cxrEmployee.getWorkFrequency() == 1 && cxrEmployee.getAuthStatus().equals(AuthStatusEnums.close.getCode())
        ) {
            return baseMapper.update(null, new LambdaUpdateWrapper<CxrEmployee>()
                .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .eq(CxrEmployee::getId, id)
                .set(CxrEmployee::getDeleteStatus, DeleteStatus.DELETED.getValue())
                .set(CxrEmployee::getDeleteBy, loginEmployee.getUserId())
                .set(CxrEmployee::getDeleteTime, new Date())
                .set(CxrEmployee::getDeleteByName, loginEmployee.getUserName())
            ) > 0;
        }
        return false;
    }

    @Override
    public Page<CxrEmployee> queryPage(CxrEmployeePageBo employeeQueryDTO) {
        cxrEmployeeRankTmpMapper.saveBatch(employeeQueryDTO.getEmployees());
        Page<CxrEmployee> page = new Page<>(employeeQueryDTO.getPageNum(), employeeQueryDTO.getPageSize());
        Page<CxrEmployee> cxrEmployeePage = baseMapper.queryPage(employeeQueryDTO, page);
        cxrEmployeeRankTmpMapper.delete(
            new LambdaQueryWrapper<CxrEmployeeRankTmp>().eq(CxrEmployeeRankTmp::getUuid, employeeQueryDTO.getUuid()));
        return cxrEmployeePage;
    }

    @Override
    public void test3(Long siteId, String jobNumer) {
//        List<CxrCustomerChangeRecord> customerChangeRecords =
//            cxrCustomerChangeRecordMapper.selectCustomerChangeRecord();
        List<CxrCustomerChangeRecord> customerChangeRecords = cxrCustomerChangeRecordMapper.selectList(
            new LambdaQueryWrapper<>());
        List<CxrCustomerAddress> customerAddressList = new ArrayList<>();
        for (CxrCustomerChangeRecord item : customerChangeRecords) {
            CxrCustomerAddress customerAddress = new CxrCustomerAddress();
            customerAddress.setAmDistributionInfo(item.getAmDistributionInfo());
            customerAddress.setAmDistributionStatus(item.getAmDistributionStatus());
            customerAddress.setAmDistributionStartDeliveryTime(item.getAmDistributionStartDeliveryTime());
            customerAddress.setAmDistributionSuspendStartTime(item.getAmDistributionSuspendStartTime());
            customerAddress.setAmDistributionSuspendEndTime(item.getAmDistributionSuspendEndTime());
            customerAddress.setIsShowAmDistribution(item.getIsShowAmDistribution());

            customerAddress.setPmDistributionInfo(item.getPmDistributionInfo());
            customerAddress.setPmDistributionStatus(item.getPmDistributionStatus());
            customerAddress.setPmDistributionStartDeliveryTime(item.getPmDistributionStartDeliveryTime());
            customerAddress.setPmDistributionSuspendStartTime(item.getPmDistributionSuspendStartTime());
            customerAddress.setPmDistributionSuspendEndTime(item.getPmDistributionSuspendEndTime());
            customerAddress.setIsShowPmDistribution(item.getIsShowPmDistribution());
            customerAddress.setChangeStatus(0);
            customerAddress.setId(item.getCustomerAddressId());
            customerAddressList.add(customerAddress);

        }
        cxrCustomerAddressMapper.updateCustomerAddressMilkInfo(customerAddressList);
    }

    private void saveStopRecord(CxrEmployee cxrEmployee, String occupationStatus, Date quitDate) {

        //入职记录
        CxrEmployeeCooperateRecord employeeCooperateRecord = new CxrEmployeeCooperateRecord();
        employeeCooperateRecord.setEmployeeLevelType(cxrEmployee.getEmployeeLevelType());
        employeeCooperateRecord.setCxrEmployeeId(cxrEmployee.getId());
        employeeCooperateRecord.setBsDate(DateUtils.getLocalDateFromDate(quitDate));
        employeeCooperateRecord.setCxrSiteId(cxrEmployee.getCxrSiteId());
        employeeCooperateRecord.setCooperateCount(cxrEmployee.getWorkFrequency() > 1 ? 2 : 1);
        employeeCooperateRecord.setBsType(CooperateStatus.STOP.getValue());
        mqUtil.sendSyncMessage(EmployeeConstant.EMPLOYEE_TOPIC, EmployeeConstant.EMPLOYEE_COOPERATION_STOP_TAG,
            JSONUtil.toJsonStr(employeeCooperateRecord));
    }


    @Override
    public Boolean edit(CxrEmployeeBo cxrEmployeeBo) {

        LoginInfo loginUser = LoginUtil.getLoginUser();

        CxrEmployee oldCxrEmployee =
            baseMapper.selectOne(
                new LambdaQueryWrapper<CxrEmployee>()
                    .eq(CxrEmployee::getId, cxrEmployeeBo.getId())
                    .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));

        if (null == oldCxrEmployee) {
            throw new ServiceException("员工信息不存在");
        }

        // 校验手机号码，这里不去校验手机号码是否否合号码规范这种基本校验，而是做业务上的逻辑校验
        // 由于目前使用手机号码作为配送端的登录手机号码，所以员工的手机号码在整个系统中的所有员工表中唯一
        cxrEmployeeBo.setPhone(StrUtil.cleanBlank(cxrEmployeeBo.getPhone()));
        Long count =
            baseMapper.selectCount(
                Wrappers.lambdaQuery(CxrEmployee.class)
                    .ne(CxrEmployee::getId, cxrEmployeeBo.getId())
                    .eq(CxrEmployee::getPhone, cxrEmployeeBo.getPhone())
                    .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));

        if (count > 0) {
            throw new ServiceException("该手机号码已被使用");
        }

        // 校验员工工号
        cxrEmployeeBo.setJobNumber(StrUtil.cleanBlank(cxrEmployeeBo.getJobNumber()));
        count =
            baseMapper.selectCount(
                Wrappers.lambdaQuery(CxrEmployee.class)
                    .ne(CxrEmployee::getId, cxrEmployeeBo.getId())
                    .eq(CxrEmployee::getJobNumber, cxrEmployeeBo.getJobNumber())
                    .eq(loginUser.getDeptId() != 100L, CxrEmployee::getSysDeptId, loginUser.getDeptId())
                    .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));

        if (count > 0) {
            throw new ServiceException("该工号已被使用");
        }

        // 员工realName可以重复(realName由身份证识别得出，不能主动添加或更改)，但是name不能重复，
        // 这里限制这个name在当前站点下不能重复
        String employeeName =
            getEmployeeName(oldCxrEmployee.getCxrSiteId(), cxrEmployeeBo.getRealName());
        cxrEmployeeBo.setName(employeeName);

        String name = cxrEmployeeBo.getName();

//        count =
//            baseMapper.selectCount(
//                Wrappers.lambdaQuery(CxrEmployee.class)
//                    .ne(CxrEmployee::getId, cxrEmployeeBo.getId())
//                    .eq(CxrEmployee::getName, name)
//                    .eq(loginUser.getDeptId() != 100L, CxrEmployee::getSysDeptId, loginUser.getDeptId())
//                    .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));
//
//        if (count > 0) {//相同名称
//            if (oldCxrEmployee.getName().equals(name)) {
//                name = name + "-" + count;
//            }
//
//        }

        //
        // CxrEmployee ocrCxrEmployee = new CxrEmployee();

        // 校验身份证号码
        if (StringUtils.isNotBlank(cxrEmployeeBo.getIdCard())) {
            cxrEmployeeBo.setIdCard(StrUtil.cleanBlank(cxrEmployeeBo.getIdCard()));
            count =
                baseMapper.selectCount(
                    Wrappers.lambdaQuery(CxrEmployee.class)
                        .ne(CxrEmployee::getId, cxrEmployeeBo.getId())
                        .eq(CxrEmployee::getIdCard, cxrEmployeeBo.getIdCard())
                        .eq(
                            loginUser.getDeptId() != 100L,
                            CxrEmployee::getSysDeptId,
                            loginUser.getDeptId())
                        .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));

            if (count > 0) {
                throw new ServiceException("该身份证号码已被录入系统中");
            }
        }

        cxrEmployeeBo.setOccupationStatus(null);

        List<EmployeeHistoryDate> historyDate = oldCxrEmployee.getHistoryDate();
        if (CollectionUtil.isEmpty(historyDate)) {
            throw new ServiceException("历史入职时间为空,脏数据请联系it处理");
        }

        checkTime(oldCxrEmployee, cxrEmployeeBo);

        if (cxrEmployeeBo.getInductionTime() != null) {
            EmployeeHistoryDate employeeHistoryDate = historyDate.get(historyDate.size() - 1);
            employeeHistoryDate.setStartDate(cxrEmployeeBo.getInductionTime());
            employeeHistoryDate.setEndDate(cxrEmployeeBo.getQuitTime());
            cxrEmployeeBo.setHistoryDate(historyDate);
        }

        // 只有在使用乐观锁修改时才去修改乐观锁的值
        boolean isRevisionUpdate = null != cxrEmployeeBo.getRevision();

        LambdaUpdateWrapper<CxrEmployee> wrapper =
            Wrappers.lambdaUpdate(CxrEmployee.class)
                .eq(CxrEmployee::getId, cxrEmployeeBo.getId())
                .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .eq(loginUser.getDeptId() != 100L, CxrEmployee::getSysDeptId, loginUser.getDeptId())
                .eq(isRevisionUpdate, CxrEmployee::getRevision, cxrEmployeeBo.getRevision())
                .set(StrUtil.isNotBlank(cxrEmployeeBo.getIdCardBackPictureUrl()), CxrEmployee::getIdCardBackPictureUrl,
                    cxrEmployeeBo.getIdCardBackPictureUrl())
                .set(StrUtil.isNotBlank(cxrEmployeeBo.getIdCardPositivePictureUrl()),
                    CxrEmployee::getIdCardPositivePictureUrl, cxrEmployeeBo.getIdCardPositivePictureUrl())
                .set(StrUtil.isNotBlank(cxrEmployeeBo.getProxyEndApplyUrl()),
                    CxrEmployee::getProxyEndApplyUrl, cxrEmployeeBo.getProxyEndApplyUrl())
                .set(StrUtil.isNotBlank(cxrEmployeeBo.getProxyEndNoticeUrl()),
                    CxrEmployee::getProxyEndNoticeUrl, cxrEmployeeBo.getProxyEndNoticeUrl())
                .set(StrUtil.isNotBlank(cxrEmployeeBo.getHealthCertificateUrl()),
                    CxrEmployee::getHealthCertificateUrl, cxrEmployeeBo.getHealthCertificateUrl())
                .set(
                    isRevisionUpdate,
                    CxrEmployee::getRevision,
                    isRevisionUpdate ? (cxrEmployeeBo.getRevision() + 1) : 1)
                .set(CxrEmployee::getGenderType, cxrEmployeeBo.getGenderType())
                .set(CxrEmployee::getEducationType, cxrEmployeeBo.getEducationType())
                .set(CxrEmployee::getEmergencyContactName, cxrEmployeeBo.getEmergencyContactName())
                .set(CxrEmployee::getEmergencyContactPhone, cxrEmployeeBo.getEmergencyContactPhone())
                .set(
                    CxrEmployee::getEmergencyContactRelationshipType,
                    cxrEmployeeBo.getEmergencyContactRelationshipType())
                .set(
                    CxrEmployee::getApplicationChannelsType, cxrEmployeeBo.getApplicationChannelsType())
                .set(CxrEmployee::getDepositBankName, cxrEmployeeBo.getDepositBankName())
                .set(CxrEmployee::getDepositBankCardName, cxrEmployeeBo.getDepositBankCardName())
                .set(CxrEmployee::getDepositBankCardPhone, cxrEmployeeBo.getDepositBankCardPhone())
                .set(CxrEmployee::getDepositBankCardIdCard, cxrEmployeeBo.getDepositBankCardIdCard())
                .set(CxrEmployee::getDepositBankCardNumber, cxrEmployeeBo.getDepositBankCardNumber())
                .set(CxrEmployee::getFamilyDepositBankName, cxrEmployeeBo.getFamilyDepositBankName())
                .set(CxrEmployee::getRecruitCommName, cxrEmployeeBo.getRecruitCommName())
                .set(
                    CxrEmployee::getFamilyDepositBankCardName,
                    cxrEmployeeBo.getFamilyDepositBankCardName())
                .set(
                    CxrEmployee::getFamilyDepositBankCardPhone,
                    cxrEmployeeBo.getFamilyDepositBankCardPhone())
                .set(
                    CxrEmployee::getFamilyDepositBankCardIdCard,
                    cxrEmployeeBo.getFamilyDepositBankCardIdCard())
                .set(
                    CxrEmployee::getFamilyDepositBankCardNumber,
                    cxrEmployeeBo.getFamilyDepositBankCardNumber())
                .set(CxrEmployee::getFamilyRelationshipType, cxrEmployeeBo.getFamilyRelationshipType())
                .set(
                    CxrEmployee::getSalesRepresentativeRegisterFileUrl,
                    cxrEmployeeBo.getSalesRepresentativeRegisterFileUrl())
                .set(
                    CxrEmployee::getGradeProxyAgreementFileUrl,
                    cxrEmployeeBo.getGradeProxyAgreementFileUrl())
                .set(CxrEmployee::getProxyContractFileUrl, cxrEmployeeBo.getProxyContractFileUrl())
                .set(CxrEmployee::getRealName, cxrEmployeeBo.getRealName())
                .set(CxrEmployee::getName, name)
                .set(
                    StringUtils.isNotBlank(cxrEmployeeBo.getCxrMasterName()),
                    CxrEmployee::getMasterName,
                    cxrEmployeeBo.getCxrMasterName())
                .set(
                    StringUtils.isNotBlank(cxrEmployeeBo.getMasterName()),
                    CxrEmployee::getMasterName,
                    cxrEmployeeBo.getMasterName())
                .set(CxrEmployee::getMasterId, cxrEmployeeBo.getCxrMasterId())
                .set(CxrEmployee::getIntroducerId, cxrEmployeeBo.getCxrIntroducerId())
                .set(CxrEmployee::getIntroducerName, cxrEmployeeBo.getIntroducerName())
                .set(CxrEmployee::getProxyType, JSONUtil.toJsonStr(cxrEmployeeBo.getCxrProxyIdSet()))
                .set(
                    CxrEmployee::getDepositBankCardPositivePictureUrl,
                    cxrEmployeeBo.getDepositBankCardPositivePictureUrl())
                .set(CxrEmployee::getCredit, cxrEmployeeBo.getCredit())
                .set(CxrEmployee::getIsHaveAirConditioner, cxrEmployeeBo.getIsHaveAirConditioner())
                .set(CxrEmployee::getIsHaveInsurance, cxrEmployeeBo.getIsHaveInsurance())
                .set(CxrEmployee::getIsCanEat, cxrEmployeeBo.getIsCanEat())
                .set(CxrEmployee::getIsCanLive, cxrEmployeeBo.getIsCanLive())
                .set(CxrEmployee::getHistoryDate, JSONUtil.toJsonStr(cxrEmployeeBo.getHistoryDate()))
                // .set(CxrEmployee::getInductionTime,cxrEmployeeBo.getInductionTime())
                .set(CxrEmployee::getQuitTime, cxrEmployeeBo.getQuitTime())
                .set(CxrEmployee::getAuthInfo, cxrEmployeeBo.getAuthInfo())
                .set(CxrEmployee::getAccountFlag, cxrEmployeeBo.getAccountFlag())
                .set(cxrEmployeeBo.getAccountFlag() != null && cxrEmployeeBo.getAccountFlag() == 2,
                    CxrEmployee::getAuthStatus, AuthStatusEnums.open.getCode());

        if (ObjectUtil.isNotNull(cxrEmployeeBo.getCxrGroupId())) {
            CxrGroup cxrGroup = iCxrGroupService.getById(cxrEmployeeBo.getCxrGroupId());
            if (ObjectUtil.isNotNull(cxrGroup) && ObjectUtil.isNotNull(cxrGroup.getNowCxrEmployeeId())) {
                CxrEmployee employee = getById(cxrGroup.getNowCxrEmployeeId());
                CxrEmployee dataCxrEmployee = this.getBaseMapper().getId(cxrGroup.getNowCxrEmployeeId());
                wrapper
                    .set(
                        ObjectUtil.isNotNull(employee), CxrEmployee::getGroupLeaderName, employee.getName())
                    .set(ObjectUtil.isNotNull(employee), CxrEmployee::getGroupLeaderId, employee.getId());
            }
        }
        if (NumberUtil.equals(LoginTypeUtil.getTerminalType(), TerminalTypeEnums.manager.getValue())) {
            wrapper.set(CxrEmployee::getInductionTime, cxrEmployeeBo.getInductionTime());
            wrapper.set(CxrEmployee::getPhone, cxrEmployeeBo.getPhone());
            wrapper.set(CxrEmployee::getIdCard, cxrEmployeeBo.getIdCard());
        }

        if (NumberUtil.equals(
            LoginTypeUtil.getTerminalType(), TerminalTypeEnums.disribution.getValue())) {
            wrapper.set(
                StrUtil.isNotBlank(cxrEmployeeBo.getInductionApplyAuditStatus()),
                CxrEmployee::getInductionApplyAuditStatus,
                cxrEmployeeBo.getInductionApplyAuditStatus());
            wrapper.set(CxrEmployee::getInductionTime, cxrEmployeeBo.getInductionTime());
        }
        // 校验级别类型
        if (cxrEmployeeBo.getEmployeeLevelType() != null) {
            EmployeeLevelType employeeLevelType =
                EmployeeLevelType.getEnumByValue(cxrEmployeeBo.getEmployeeLevelType());
            if (employeeLevelType == null) {
                throw new ServiceException("员工级别信息不正确");
            }
            wrapper.set(CxrEmployee::getEmployeeLevelType, employeeLevelType.getValue());
        }
        //等级别发生变化发mq
        if (!oldCxrEmployee.getEmployeeLevelType().equals(cxrEmployeeBo.getEmployeeLevelType())) {
            Date nowDate = new Date();
            SaleLevelMqSendUtil.sendChangeAll(cxrEmployeeBo.getId(), nowDate,
                EmployeeLevelType.getEnumByValue(cxrEmployeeBo.getEmployeeLevelType()), null, 1);
        }

        boolean flag = baseMapper.update(null, wrapper) > 0;
        if (cxrEmployeeBo.getEmployeeLevelType() != null) {
            mqUtil.sendSyncMessage(EmployeeConstant.EMPLOYEE_TOPIC, EmployeeConstant.UPDATE_SYNC_EMPLOYEE_INFO_TAG,
                oldCxrEmployee.getId() + "");
        }
        if (!flag) {
            throw new ServiceException("修改失败，请刷新页面重试");
        } else {
        }
        mqUtil.sendSyncMessage(EmployeeConstant.EMPLOYEE_TOPIC, EmployeeConstant.EMPLOYEE_PHONE_TAG, cxrEmployeeBo.getPhone());
        return true;
    }

    public void checkTime(CxrEmployee oldCxrEmployee, CxrEmployeeBo cxrEmployeeBo) {

        try {
            Date inductionTimeNew = cxrEmployeeBo.getInductionTime();
            Date quitTime = cxrEmployeeBo.getQuitTime();
            LocalDate inductionDateNew = DateUtils.getLocalDateFromDate(inductionTimeNew);

            List<CxrEmployeeCooperateHistory> cxrEmployeeCooperateHistories = cxrEmployeeCooperateHistoryMapper.selectList(
                Wrappers.<CxrEmployeeCooperateHistory>lambdaQuery()
                    .eq(CxrEmployeeCooperateHistory::getEmployeeId, oldCxrEmployee.getId())
                    .orderByAsc(CxrEmployeeCooperateHistory::getFrequency));

            AbstractAssert.isEmpty(cxrEmployeeCooperateHistories, "入职记录数据为空");

            int size = cxrEmployeeCooperateHistories.size();
            if (size == 1) {
                cxrEmployeeCooperateHistoryMapper.update(null, Wrappers.<CxrEmployeeCooperateHistory>lambdaUpdate()
                    .set(CxrEmployeeCooperateHistory::getStartDate, inductionDateNew)
                    .set(CxrEmployeeCooperateHistory::getEndDate,
                        quitTime == null ? null : DateUtils.getLocalDateFromDate(quitTime))
                    .eq(CxrEmployeeCooperateHistory::getId, cxrEmployeeCooperateHistories.get(0).getId()));
            } else {
                CxrEmployeeCooperateHistory cxrEmployeeCooperateHistoryPre = cxrEmployeeCooperateHistories.get(
                    size - 2);
                LocalDate endDate = cxrEmployeeCooperateHistoryPre.getEndDate();
                AbstractAssert.isTrue(inductionDateNew.isBefore(endDate) || inductionDateNew.isEqual(endDate),
                    "入职时间不能小于上一次入职时间");
                cxrEmployeeCooperateHistoryMapper.update(null, Wrappers.<CxrEmployeeCooperateHistory>lambdaUpdate()
                    .set(CxrEmployeeCooperateHistory::getStartDate, inductionDateNew)
                    .set(CxrEmployeeCooperateHistory::getEndDate,
                        quitTime == null ? null : DateUtils.getLocalDateFromDate(quitTime))
                    .eq(CxrEmployeeCooperateHistory::getId, cxrEmployeeCooperateHistories.get(size - 1).getId()));

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //    /**
    //     * ocr识别
    //     *
    //     * @param cxrEmployeeBo
    //     * @param ocrCxrEmployee
    //     */
    //    private void ocrValidate(CxrEmployeeBo cxrEmployeeBo, CxrEmployee ocrCxrEmployee) {
    //        //校验身份证正面照图片
    //        if (StringUtils.isNotBlank(cxrEmployeeBo.getIdCardPositivePictureUrl())) {
    //            IdCardOcrBo idCardOcrBo = new IdCardOcrBo();
    //
    // idCardOcrBo.setCardUrl(imageUtils.getBaseUrl(cxrEmployeeBo.getIdCardPositivePictureUrl()));
    //            idCardOcrBo.setType(true);
    //            IdCardOcrVo idCardOcrVo = ocrUtil.idCardOcr(idCardOcrBo);
    //            //暂时只设置真实姓名
    //            ocrCxrEmployee.setRealName(idCardOcrVo.getName());
    //
    //            //由于ocr识别的结果不能是百分百准确，那么不会自动将ocr的识别结果与用户输入的进行比对，ocr只是保证上传的图片是想要的目标图片
    //        }
    //
    //        //校验身份证反面照图片
    //        if (StringUtils.isNotBlank(cxrEmployeeBo.getIdCardBackPictureUrl())) {
    //            //使用OCR判断是否符合身份证反面照的标准
    //            IdCardOcrBo idCardOcrBo = new IdCardOcrBo();
    //
    // idCardOcrBo.setCardUrl(imageUtils.getBaseUrl(cxrEmployeeBo.getIdCardBackPictureUrl()));
    //            idCardOcrBo.setType(false);
    //            ocrUtil.idCardOcr(idCardOcrBo);
    //        }
    //
    //        //校验银行卡正面照图片
    //        if (StringUtils.isNotBlank(cxrEmployeeBo.getDepositBankCardPositivePictureUrl())) {
    //            BankCardOcrBo bankCardOcrBo = new BankCardOcrBo();
    //            JSONObject jsonObject = JSONObject.parseObject(imageUtils.getBaseUrl(cxrEmployeeBo
    //            .getDepositBankCardPositivePictureUrl()));
    //
    // bankCardOcrBo.setBankCardUrl(jsonObject.get(UserConstants.FILE_UPLOAD_COLUMN_URL).toString());
    //            ocrUtil.bankCardOCR(bankCardOcrBo);
    //        }
    //    }

    @Override
    public String getNewestJobNumber() {
        CxrEmployee cxrEmployee =
            baseMapper.selectOne(
                new QueryWrapper<CxrEmployee>().select("MAX(job_number + 0) AS job_number"));

        if (cxrEmployee == null || cxrEmployee.getJobNumber() == null) {
            // 给一个初始的员工工号
            String initialJobNumber =
                remoteSysConfigService.selectConfigByKey(SysConfigKeyConstants.INITIAL_JOB_NUMBER);

            if (!StringUtils.isNotBlank(initialJobNumber)) {
                throw new ServiceException("系统未配置员工初始工号，请联系管理员");
            }

            return initialJobNumber;
        }

        return (new BigDecimal(cxrEmployee.getJobNumber()).longValue() + 1) + "";
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public void mqConsumerToEmployeeInduction(Long id) {
        // 根据id查询 员工
        CxrEmployee cxrEmployee =
            baseMapper.selectOne(
                new LambdaQueryWrapper<CxrEmployee>()
                    .eq(CxrEmployee::getId, id)
                    .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));
        // 确认 有
        if (null != cxrEmployee) {
            // 对比  现在和入职时间       入职时间  < 返回-1  等于 返回0    大于返回1
            if (cxrEmployee.getInductionTime().compareTo(new Date()) < 1) {
                // 如果状态 是待入职
                if (OccupationStatus.WAIT_INDUCTION.getValue().equals(cxrEmployee.getOccupationStatus())) {
                    CxrEmployee updateCxrEmployee = new CxrEmployee();
                    updateCxrEmployee.setId(id);
                    updateCxrEmployee.setOccupationStatus(OccupationStatus.INDUCTION.getValue());
                    updateCxrEmployee.setRevision(cxrEmployee.getRevision() + 1);

                    // 更新
                    boolean flag =
                        baseMapper.update(
                            updateCxrEmployee,
                            new LambdaQueryWrapper<CxrEmployee>()
                                .eq(CxrEmployee::getId, id)
                                .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                                .eq(CxrEmployee::getRevision, cxrEmployee.getRevision()))
                            > 0;

                    if (!flag) {
                        throw new ServiceException("修改失败");
                    }
                } else {
                    throw new ServiceException("员工职业状态未处于待入职");
                }
            } else {
                throw new ServiceException("员工入职时间在当前时间之前");
            }
        } else {
            throw new ServiceException("员工信息不存在");
        }
    }

    @Override
    @Async
    public void mqProducerToEmployeeInduction(Long id, Long inductionTime) {
        // 发送一个员工入职状态修改的定时消息
        mqUtil.sendDelayMessage(
            EmployeeConstant.EMPLOYEE_TOPIC,
            EmployeeDelayConstant.EMPLOYEE_OCCUPATION_STATUS_DELAY_TAG,
            id.toString(),
            new Date(inductionTime));
    }

    /**
     * 统计 组长 主管 财务 数量
     */
    @Override
    public Map statistics() {
        Long deptId = LoginHelper.getLoginUser().getDeptId();

        String value = DeleteStatus.NOT_DELETED.getValue();
        String occStatus = OccupationStatus.INDUCTION.getValue();
        // 统计   不重复总人数
        int regionManager =
            baseMapper.statistics(
                PostType.REGION_MANAGER.getValue(),
                deptId,
                value,
                occStatus,
                ChargeStatus.IN_PROGRESS.getValue());
        int stationUp =
            baseMapper.statistics(
                PostType.DIRECTOR.getValue(),
                deptId,
                value,
                occStatus,
                ChargeStatus.IN_PROGRESS.getValue());
        int finance =
            baseMapper.statistics(
                PostType.FINANCIAL.getValue(),
                deptId,
                value,
                occStatus,
                ChargeStatus.IN_PROGRESS.getValue());
        int group = baseMapper.statisticsGroup(deptId, value, occStatus);

        Map<String, Integer> map = new HashMap<>();
        map.put("groupUp", group);
        map.put("stationUp", stationUp);
        map.put("finance", finance);
        map.put("regionManager", regionManager);
        return map;
    }

    /**
     * 离职逻辑
     *
     * @param bo
     * @return
     */
    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public Integer employeeDimission(CxrEmployeeDimissionBo bo) {
        // 1.设置员工表
        // 根据id 修改员工表中的 在职状态    离职时间
        CxrEmployee cxrEmployee = this.getById(bo.getId());
        if (!cxrEmployee.getOccupationStatus().equals(OccupationStatus.INDUCTION.getValue())) {
            throw new ServiceException("员工非在职状态不能离职!");
        }
        bo.setSiteId(cxrEmployee.getCxrSiteId());
        // 设置这次离职时间     往例时间中
        List<EmployeeHistoryDate> historyDate = cxrEmployee.getHistoryDate();
        EmployeeHistoryDate employeeHistoryDate = historyDate.get(historyDate.size() - 1);
        employeeHistoryDate.setEndDate(bo.getQuitTime());
        employeeHistoryDate.setEmployeeName(cxrEmployee.getName());
        if (remoteCustomerDistributionDetailService.countEmAddress(bo.getId()) > 0) {
            throw new ServiceException("该销售代理还有路条没有转拨,请转拨后操作!");
        }
        //  离职工单判断
        if (remoteWorkOrderService.checkUserMount(bo.getId())) {
            List<CxrEmployeePost> employeePosts = cxrEmployeePostMapper.selectList(
                new LambdaQueryWrapper<CxrEmployeePost>()
                    .eq(CxrEmployeePost::getCxrEmployeeId, bo.getId())
                    .eq(CxrEmployeePost::getCxrSiteId, bo.getSiteId())
                    .eq(CxrEmployeePost::getChargeStatus, ChargeStatus.IN_PROGRESS.getValue())
                    .eq(CxrEmployeePost::getDeleteStatus, DeleteStatus.not_deleted)
            );
            if (employeePosts.size() > 0) {
                Optional<CxrEmployeePost> employeePost = employeePosts.stream()
                    .max(Comparator.comparingInt(o -> Integer.valueOf(o.getCxrPostId().getValue())));
                String value = employeePost.get().getCxrPostId().getValue();
                PostType enumByValue = PostType.getEnumByValue(value);
                remoteWorkOrderService.workOrderRoam(bo.getId(), cxrEmployee.getCxrSiteId(), enumByValue.getValue());
            } else {
                throw new ServiceException("该员工还有工单未转出，请转出后操作!");
            }
        }

        Long adjustMilkCount = adjustMilkApplyService.countEmployeeAdjustMilkApply(bo.getId());
        if (ObjectUtil.isNull(bo.getAdjustOcclude()) && adjustMilkCount > 0l) {
            return -1;
//            throw new ServiceException("该销售代理存在 待处理 的路条调整奶数据，请尽快联系站点进行处理。");
        }
        saveOrUpdateCooperateHistory(null, cxrEmployee.getId(), bo.getOccupationStatus(), bo.getQuitTime(), 2);

        // 判断时间
        long time = bo.getQuitTime().getTime();
        if (time < new Date().getTime()) {
            // 设置职业状态
            boolean b =
                this.update(
                    Wrappers.lambdaUpdate(CxrEmployee.class)
                        .set(
                            CxrEmployee::getQuitApplyAuditStatus,
                            EmployeeDimissionStatus.APPROV.getValue().toString())
                        .set(CxrEmployee::getQuitTime, bo.getQuitTime())
                        .set(CxrEmployee::getRemark, bo.getReson())
                        .set(CxrEmployee::getHistoryDate, JSONUtil.toJsonStr(historyDate))
                        .set(CxrEmployee::getOccupationStatus, bo.getOccupationStatus())
                        .set(CxrEmployee::getUpdateByName, LoginUtil.getLoginUser().getUserName())
                        .set(CxrEmployee::getUpdateBy, LoginUtil.getLoginUser().getUserId())
                        .set(CxrEmployee::getUpdateByType, LoginUtil.getLoginUser().getUserType())
                        .set(CxrEmployee::getUpdateTime, new Date())
                        .eq(CxrEmployee::getId, cxrEmployee.getId()));

            if (!b) {
                throw new ServiceException("更新失败,请重试!");
            }
            // 删除  这个人在小组位置
            try {
                GroupThreadUtil.set(GroupThreadUtil.QUIT_FLAG, true);
                iCxrGroupService.deleteGroupLeaderOrMembers(bo.getId());
            } finally {
                GroupThreadUtil.clear();
            }
            mqUtil.sendSyncMessage(
                EmployeeConstant.EMPLOYEE_TOPIC,
                EmployeeConstant.UPDATE_SYNC_EMPLOYEE_INFO_TAG,
                cxrEmployee.getId() + "");

            // TODO :离职后同步借支管理
            remoteCxrEmployeeBorrowingService.updateBorrowingByOccupationStatus(
                cxrEmployee.getId(), bo.getQuitTime());

            saveStopRecord(cxrEmployee, bo.getOccupationStatus(), bo.getQuitTime());
            syncMallUserEmployee(cxrEmployee.getId());
            return b ? 1 : 0;
        } else {

            boolean b =
                this.update(
                    Wrappers.lambdaUpdate(CxrEmployee.class)
                        .set(
                            CxrEmployee::getQuitApplyAuditStatus,
                            EmployeeDimissionStatus.APPROV.getValue().toString())
                        .set(CxrEmployee::getQuitTime, bo.getQuitTime())
                        .set(CxrEmployee::getRemark, bo.getReson())
                        .set(CxrEmployee::getHistoryDate, JSONUtil.toJsonStr(historyDate))
                        .set(CxrEmployee::getUpdateByName, LoginUtil.getLoginUser().getUserName())
                        .set(CxrEmployee::getUpdateBy, LoginUtil.getLoginUser().getUserId())
                        .set(CxrEmployee::getUpdateByType, LoginUtil.getLoginUser().getUserType())
                        .set(CxrEmployee::getUpdateTime, new Date())
                        .eq(CxrEmployee::getId, cxrEmployee.getId()));
            if (b) {
                syncMallUserEmployee(cxrEmployee.getId());
                // 发送一个员工入职状态修改的定时消息
                mqUtil.sendDelayMessage(
                    EmployeeConstant.EMPLOYEE_TOPIC,
                    EmployeeDelayConstant.LEAVE_EMPLOYEE_OCCUPATION_STATUS_DELAY_TAG,
                    bo.getId().toString() + "," + bo.getOccupationStatus(),
                    new Date(time));
                // TODO :离职后同步借支管理
                remoteCxrEmployeeBorrowingService.updateBorrowingByOccupationStatus(
                    cxrEmployee.getId(), bo.getQuitTime());

                saveStopRecord(cxrEmployee, bo.getOccupationStatus(), bo.getQuitTime());
            }

            return b ? 1 : 0;
        }
    }

    /**
     * 获取介绍人 根据公司id
     *
     * @param id
     * @return
     */
    @Override
    public List<CxrEmployeeVo> getIntroducerByCompanyId(Long id) {
        Long deptId = LoginHelper.getLoginUser().getDeptId();
        if (deptId == 100) {
            List<CxrEmployee> cxrEmployees =
                baseMapper.selectList(
                    new LambdaQueryWrapper<CxrEmployee>()
                        .select(CxrEmployee::getName, CxrEmployee::getId)
                        .eq(CxrEmployee::getSysDeptId, id)
                        .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));
            List<CxrEmployeeVo> collect =
                cxrEmployees.stream()
                    .map(
                        s -> {
                            CxrEmployeeVo vo = new CxrEmployeeVo();
                            vo.setName(s.getName());
                            vo.setId(s.getId());
                            return vo;
                        })
                    .collect(Collectors.toList());
            return collect;
        } else {
            List<CxrEmployee> cxrEmployees =
                baseMapper.selectList(
                    new LambdaQueryWrapper<CxrEmployee>()
                        .select(CxrEmployee::getName, CxrEmployee::getId)
                        .eq(CxrEmployee::getSysDeptId, deptId)
                        .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));
            List<CxrEmployeeVo> collect =
                cxrEmployees.stream()
                    .map(
                        s -> {
                            CxrEmployeeVo vo = new CxrEmployeeVo();
                            vo.setName(s.getName());
                            vo.setId(s.getId());
                            return vo;
                        })
                    .collect(Collectors.toList());
            return collect;
        }
    }

    /**
     * 获取师傅
     *
     * @param id
     * @return
     */
    @Override
    public List<CxrEmployeeVo> getMasterBySiteId(Long id) {
        List<CxrEmployee> cxrEmployees =
            baseMapper.selectList(
                new LambdaQueryWrapper<CxrEmployee>()
                    .select(CxrEmployee::getId, CxrEmployee::getName)
                    .eq(CxrEmployee::getCxrSiteId, id)
                    .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                    .eq(CxrEmployee::getOccupationStatus, OccupationStatus.INDUCTION.getValue()));

        List<CxrEmployeeVo> collect =
            cxrEmployees.stream()
                .map(
                    s -> {
                        CxrEmployeeVo vo = new CxrEmployeeVo();
                        vo.setName(s.getName());
                        vo.setId(s.getId());
                        return vo;
                    })
                .collect(Collectors.toList());

        return collect;
    }

    /**
     * 根据站点获取小组id和名字
     *
     * @param id
     * @return
     */
    @Override
    public List<CxrGroup> getGroupBySiteId(Long id) {

        List<CxrGroup> cxrGroups =
            cxrGroupMapper.selectList(
                new LambdaQueryWrapper<CxrGroup>()
                    .eq(CxrGroup::getCxrSiteId, id)
                    .eq(CxrGroup::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));

        return cxrGroups;
    }

    /**
     * 根据公司id获取 站点
     *
     * @param id
     * @return
     */
    @Override
    public List<CxrSiteVo> getSiteByCompanyId(Long id) {
        List<CxrSite> cxrSites =
            cxrSiteMapper.selectList(
                new LambdaQueryWrapper<CxrSite>()
                    .eq(CxrSite::getSysDeptId, id)
                    .eq(CxrSite::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));

        List<CxrSiteVo> collect =
            cxrSites.stream()
                .map(
                    s -> {
                        CxrSiteVo cxrSiteVo = new CxrSiteVo();
                        cxrSiteVo.setId(s.getId());
                        cxrSiteVo.setName(s.getName());
                        return cxrSiteVo;
                    })
                .collect(Collectors.toList());

        return collect;
    }

    /**
     * 获取公司名
     *
     * @return
     */
    @Override
    public List<SysDeptvo> getCompany() {
        Long deptId = LoginHelper.getLoginUser().getDeptId();
        if (deptId != 100) {
            SysDept sysDept = remoteDeptService.queryById(deptId);
            if (sysDept == null) {
                throw new ServiceException("所登录用户未知公司");
            }
            SysDeptvo vo = new SysDeptvo();
            vo.setDeptName(sysDept.getDeptName());
            vo.setDeptId(deptId);
            List<SysDeptvo> list = new ArrayList<>();
            list.add(vo);
            return list;
        }

        return remoteDeptService.selectAll();
    }

    /**
     * 后台二次入职
     */
    @Override
    @Transactional
    public Boolean managerTwoInduction(CxrEmployeeBo cxrEmployeeBo) {
        CxrTwiceJoinEmployee twiceJoinEmployee = cxrTwiceJoinEmployeeService.addTwoJoinByEmployeeId(cxrEmployeeBo);
        cxrEmployeeBo.setTwiceJoinEmployeeId(twiceJoinEmployee.getId());
        return this.secondInduction(cxrEmployeeBo);
    }

    /**
     * 二次入职
     *
     * @param cxrEmployeeBo 员工上一次任职的信息
     * @return
     */
    @Override
    public Boolean secondInduction(CxrEmployeeBo cxrEmployeeBo) {
        // 账户用户id  部门id

        CxrEmployee oldCxrEmployee =
            this.getBaseMapper()
                .selectOne(
                    Wrappers.lambdaQuery(CxrEmployee.class)
                        .eq(CxrEmployee::getIdCard, cxrEmployeeBo.getIdCard())
                        .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                );

        if (ObjectUtil.isNull(oldCxrEmployee)) {
            throw new ServiceException("未找到该员工信息,不能办理二次入职");
        }
        //临时限制一个月内不能重复入职
        if (ObjectUtil.isNotNull(oldCxrEmployee.getQuitTime()) &&
            ChronoUnit.DAYS.between(oldCxrEmployee.getQuitTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(), LocalDate.now()) < 30) {
            throw new ServiceException("一个月内不能重复入职");
        }
        saveOrUpdateCooperateHistory(cxrEmployeeBo.getInductionTime(), oldCxrEmployee.getId(), null, null, 1);

        CxrEmployee cxrEmployee = BeanUtil.copyProperties(cxrEmployeeBo, CxrEmployee.class);
        cxrEmployee.setId(oldCxrEmployee.getId());
        cxrEmployee.setAuthStatus(oldCxrEmployee.getAuthStatus());
        // 设置原入职的身份证   不让修改
        cxrEmployee.setIdCard(oldCxrEmployee.getIdCard());
        cxrEmployee.setIdCardBackPictureUrl(cxrEmployeeBo.getIdCardBackPictureUrl());
        cxrEmployee.setIdCardPositivePictureUrl(cxrEmployeeBo.getIdCardPositivePictureUrl());
        // 工作次数+1
        cxrEmployee.setWorkFrequency(Convert.toInt(oldCxrEmployee.getWorkFrequency(), 0) + 1);

        // 离职时间滞空   改为在职     等级默认l1
        // id滞空
        cxrEmployee.setQuitTime(null);
        LocalDate quitTime = oldCxrEmployee.getQuitTime().toInstant().atOffset(ZoneOffset.UTC)
            .toLocalDate().plusDays(5);
        LocalDate inductionTime = cxrEmployeeBo.getInductionTime().toInstant().atOffset(ZoneOffset.UTC)
            .toLocalDate();
        Boolean isInDayLimit = inductionTime.isBefore(quitTime);
        if (NumberUtil.equals(LoginTypeUtil.getTerminalType(), TerminalTypeEnums.manager.getValue())) {
            //覆盖加入日期
            cxrEmployee.setAuthOfInduction(1);
            String employeeLevelType = cxrEmployeeBo.getEmployeeLevelType();

            if (ObjectUtil.isNotEmpty(cxrEmployeeBo.getAuthStatus())) {
                cxrEmployee.setSiteName(cxrEmployeeBo.getCxrSiteName());
                cxrEmployee.setCxrSiteId(cxrEmployeeBo.getCxrSiteId());
                if (isInDayLimit) {
                    cxrEmployee.setEmployeeLevelType(cxrEmployeeBo.getOldEmployeeLevelType());
                    cxrEmployee.setOccupationStatus(OccupationStatus.WAIT_INDUCTION.getValue());
                    cxrEmployee.setAuthOfInduction(null);//5天之内不需要覆盖
                    LocalDateTime now = LocalDateTime.now();
                    if (ObjectUtil.isNotEmpty(oldCxrEmployee.getAuthEndTime()) && oldCxrEmployee.getAuthEndTime()
                        .isAfter(now)) {
                        cxrEmployee.setAuthStatus(AuthStatusEnums.open.getCode());
                        cxrEmployee.setOccupationStatus(OccupationStatus.INDUCTION.getValue());
                    }
                } else {
                    cxrEmployee.setEmployeeLevelType(employeeLevelType);
                    cxrEmployee.setOccupationStatus(OccupationStatus.WAIT_INDUCTION.getValue());
                    cxrEmployee.setAuthStatus(AuthStatusEnums.close.getCode());
                }
                // 入职时间小于等于当前时间，为《在职》
                cxrEmployee.setInductionApplyAuditStatus(InductionApplyAuditStatus.SUCCESS.getValue());
            } else {
                if (StrUtil.isNotBlank(employeeLevelType)) {
                    cxrEmployee.setEmployeeLevelType(employeeLevelType);
                } else {
                    cxrEmployee.setEmployeeLevelType(oldCxrEmployee.getEmployeeLevelType());
                }

                // 入职时间小于等于当前时间，为《在职》
                if (cxrEmployee.getInductionTime().compareTo(new Date()) < 1) {
                    // 入职时间小于等于当前时间，为《在职》
                    cxrEmployee.setOccupationStatus(OccupationStatus.INDUCTION.getValue());
                } else {
                    // 入职时间大于当前时间，为《待入职》
                    cxrEmployee.setOccupationStatus(OccupationStatus.WAIT_INDUCTION.getValue());
                }
                cxrEmployee.setInductionApplyAuditStatus(InductionApplyAuditStatus.SUCCESS.getValue());
            }
        }
        if (NumberUtil.equals(
            LoginTypeUtil.getTerminalType(), TerminalTypeEnums.disribution.getValue())) {
            cxrEmployee.setEmployeeLevelType(EmployeeLevelType.L1.getValue());
            cxrEmployee.setInductionApplyAuditStatus(InductionApplyAuditStatus.WAIT.getValue());

            // 入职时间大于当前时间，为《待入职》
            cxrEmployee.setOccupationStatus(OccupationStatus.WAIT_INDUCTION.getValue());
        }

        // 入职时间    全部
        List<EmployeeHistoryDate> historyDate = oldCxrEmployee.getHistoryDate();
        historyDate.get(historyDate.size() - 1).setOccupation(oldCxrEmployee.getOccupationStatus());
        EmployeeHistoryDate employeeHistoryDate = new EmployeeHistoryDate();
        employeeHistoryDate.setStartDate(cxrEmployeeBo.getInductionTime());
        historyDate.add(employeeHistoryDate);
        cxrEmployee.setHistoryDate(historyDate);
        // 入职时间
        cxrEmployee.setInductionTime(cxrEmployeeBo.getInductionTime());
        cxrEmployee.setSubmitTime(ObjectUtil.defaultIfNull(cxrEmployeeBo.getSubmitTime(), new Date()));
        // 离职 入职审核时间  改为null
        cxrEmployee.setQuitApplyAuditStatus(null);

        // 设置小组长
        if (cxrEmployeeBo.getCxrGroupId() != null) {
            CxrGroup cxrGroup = cxrGroupMapper.selectById(cxrEmployeeBo.getCxrGroupId());
            Long nowCxrEmployeeId = cxrGroup.getNowCxrEmployeeId();
            CxrEmployee cxr = this.getById(nowCxrEmployeeId);
            if (ObjectUtil.isNotNull(cxr)) {
                cxrEmployee.setGroupLeaderId(cxr.getId());
                cxrEmployee.setGroupLeaderName(cxr.getName());
            }
        }

        // 保险吃住
        cxrEmployee.setIsHaveInsurance(cxrEmployeeBo.getIsHaveInsurance());
        cxrEmployee.setIsCanEat(cxrEmployeeBo.getIsCanEat());
        cxrEmployee.setIsCanLive(cxrEmployeeBo.getIsCanLive());
        cxrEmployee.setIsHaveAirConditioner(cxrEmployeeBo.getIsHaveAirConditioner());

        // 设置代理信息

        cxrEmployee.setProxyType(cxrEmployeeBo.getProxyType());
        if (NumberUtil.equals(
            LoginTypeUtil.getTerminalType(), TerminalTypeEnums.disribution.getValue())) {
//            cxrEmployee.setEmployeeLevelType(EmployeeLevelType.L1.getValue());
        }

        cxrEmployee.setIntroducerId(cxrEmployeeBo.getCxrIntroducerId());
        // 添加  介绍人名称
        cxrEmployee.setIntroducerName(cxrEmployeeBo.getIntroducerName());

        cxrEmployee.setMasterId(cxrEmployeeBo.getCxrMasterId());
        // 添加 师傅名称
        if (StringUtils.isNotBlank(cxrEmployeeBo.getCxrMasterName())) {
            cxrEmployee.setMasterName(cxrEmployeeBo.getCxrMasterName());
        } else {
            cxrEmployee.setMasterName(cxrEmployeeBo.getMasterName());
        }
        // 二次入禁止前端修改工号，防止同步到培训系统有异常
        cxrEmployee.setJobNumber(null);
        String name = getEmployeeName(cxrEmployee.getCxrSiteId(), cxrEmployee.getRealName());

        cxrEmployee.setName(name);
        //白名单
        CxrSite cxrSite = cxrSiteMapper.getCxrSite(cxrEmployee.getCxrSiteId());
        if (cxrSite != null && cxrSecurityConfig.getDealerRegionId()
            .contains(cxrSite.getCxrRootRegionId().toString())) {
            cxrEmployee.setOccupationStatus(OccupationStatus.INDUCTION.getValue());
        }
        //超过限制天数则等级初始化
        //2024-12-26 逻辑调整：二次加入通过配置阶段来控制等级
//        if(!isInDayLimit){
//            cxrEmployee.setEmployeeLevelType(EmployeeLevelType.L1.getValue());
//        }

        boolean flag =
            this.update(
                cxrEmployee,
                Wrappers.lambdaUpdate(CxrEmployee.class)
                    .set(CxrEmployee::getQuitTime, null)
                    .set(CxrBaseEntity::getRemark, null)
                    .eq(CxrEmployee::getId, cxrEmployee.getId()));

        //boolean flag = this.saveOrUpdate(cxrEmployee);

        if (!flag) {
            throw new ServiceException("二次入职失败,请刷新重试!");
        }

        if (ObjectUtil.isEmpty(cxrEmployeeBo.getAuthStatus())) {
            // 后端添加 不需要审核直接同步到培训系统
            if (NumberUtil.equals(LoginTypeUtil.getTerminalType(), TerminalTypeEnums.manager.getValue())) {
                updateGroupMemberByGroupId(cxrEmployee.getId());

                if (StringUtils.equals(
                    cxrEmployee.getOccupationStatus(), OccupationStatus.WAIT_INDUCTION.getValue())) {
                    SpringUtils.getBean(ICxrEmployeeService.class)
                        .mqProducerToEmployeeInduction(
                            cxrEmployee.getId(), cxrEmployee.getInductionTime().getTime());
                }
                mqUtil.sendSyncMessage(
                    EmployeeConstant.EMPLOYEE_TOPIC,
                    EmployeeConstant.UPDATE_SYNC_EMPLOYEE_INFO_TAG,
                    cxrEmployee.getId() + "");

            }
        }
        saveRecord(cxrEmployee, 2);
        SynEmployeeBo bo = new SynEmployeeBo();
        bo.setEmployeeId(oldCxrEmployee.getId());
        bo.setJobNumber(oldCxrEmployee.getJobNumber());
        bo.setPhone(cxrEmployeeBo.getPhone());
        bo.setRealName(oldCxrEmployee.getRealName());
        this.syncIntoMallUserEmployee(bo);
        // 处理二次加入没有同步用户状态
        mqUtil.sendSyncMessage(
            EmployeeConstant.EMPLOYEE_TOPIC,
            EmployeeConstant.UPDATE_SYNC_EMPLOYEE_SYS_USER_INFO_TAG,
            cxrEmployee.getId() + "");

        mqUtil.sendSyncMessage(EmployeeConstant.EMPLOYEE_TOPIC,EmployeeConstant.EMPLOYEE_PHONE_TAG,cxrEmployee.getPhone());
        //处理等级的问题 超过五天
        //2024-12-26 去掉5天的逻辑
//        if(!isInDayLimit){
//            //修改二次入职的等级
//            cxrTwiceJoinEmployeeService.updateNewEmployeeLevel(cxrEmployee.getId(),cxrEmployee.getEmployeeLevelType()
//                ,cxrEmployeeBo.getTwiceJoinEmployeeId());
//        }
        CxrEmployee newCxrEmployee = this.getById(cxrEmployee.getId());
        if (cxrEmployee.getEmployeeLevelType() != null && cxrEmployee.getSubmitTime() != null) {
            SaleLevelMqSendUtil.sendChangeAll(cxrEmployee.getId(), cxrEmployee.getSubmitTime(),
                EmployeeLevelType.getEnumByValue(cxrEmployee.getEmployeeLevelType()), null, 3, cxrEmployeeBo.getTwiceJoinEmployeeId());
        } else {
            SaleLevelMqSendUtil.sendChangeAll(newCxrEmployee.getId(), newCxrEmployee.getSubmitTime(),
                EmployeeLevelType.getEnumByValue(newCxrEmployee.getEmployeeLevelType()), null, 3, cxrEmployeeBo.getTwiceJoinEmployeeId());
        }
        return flag;
    }

    @Override
    public CxrEmployeeVo employeeLogin(String phone) {
        CxrEmployeeVo cxrEmployeeVo =
            baseMapper.selectVoOne(
                new LambdaQueryWrapper<CxrEmployee>()
                    .eq(CxrEmployee::getPhone, phone)
                    .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                    .in(CxrEmployee::getOccupationStatus, OccupationStatus.WAIT_INDUCTION.getValue(),
                        OccupationStatus.INDUCTION.getValue())
            );

        if (null == cxrEmployeeVo) {
            throw new ServiceException(StrUtil.format("[{}]员工不存在或已终止合作!", phone));
        }

        /** 填充代理类型 */
        List<String> proxyTypes = cxrEmployeeVo.getProxyType();

        List<Map<String, String>> pairs =
            remoteDictService.getDictLabelPairs(DictTypeEnums.PROXY_TYPE.getValue(), proxyTypes);

        cxrEmployeeVo.setProxyTypeParis(pairs);

        // 代理
        //        List<CxrEmployeeProxy> cxrEmployeeProxyList =
        //            cxrEmployeeProxyMapper.selectList(new LambdaQueryWrapper<CxrEmployeeProxy>().
        //                eq(CxrEmployeeProxy::getCxrEmployeeId, cxrEmployeeVo.getId()).
        //                eq(CxrEmployeeProxy::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));
        //
        //        Set<Long> cxrProxyIdSet =
        //
        // cxrEmployeeProxyList.stream().map(CxrEmployeeProxy::getCxrProxyId).collect(Collectors.toSet());
        //        if (cxrProxyIdSet.size() > 0) {
        //            cxrEmployeeVo.setCxrProxyList(cxrProxyMapper.selectVoList(new
        // LambdaQueryWrapper<CxrProxy>().
        //                in(CxrProxy::getId, cxrProxyIdSet).
        //                orderByAsc(CxrProxy::getSortNum).
        //                orderByDesc(CxrProxy::getCreateTime)));
        //        }

//        if (!OccupationStatus.INDUCTION.getValue().equals(cxrEmployeeVo.getOccupationStatus())) {
//            throw new ServiceException("员工目前处于非“在职”状态");
//        }
        return cxrEmployeeVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean batchUpdateEmployeeLevel(List<CxrEmployee> cxrEmployees) {
        try {
            for (CxrEmployee cxrEmployee : cxrEmployees) {
                LambdaUpdateWrapper<CxrEmployee> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                lambdaUpdateWrapper.eq(CxrEmployee::getJobNumber, cxrEmployee.getJobNumber());
                baseMapper.update(cxrEmployee, lambdaUpdateWrapper);
            }
        } catch (Exception e) {
            throw new ServiceException("批量修改员工等级吃住空调失败");
        }
        return true;
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateEmployeeQuitApplyAuditStatus(Long employeeId, String quitApplyAuditStatus) {
        LambdaUpdateWrapper<CxrEmployee> objectLambdaUpdateWrapper = Wrappers.lambdaUpdate();
        objectLambdaUpdateWrapper.eq(CxrEmployee::getId, employeeId);
        objectLambdaUpdateWrapper.set(CxrEmployee::getQuitApplyAuditStatus, quitApplyAuditStatus);
        return baseMapper.update(null, objectLambdaUpdateWrapper) > 0;
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateDistributionStationIdById(Long id, Long distributionStationId) {
        CxrEmployee cxrEmployee = new CxrEmployee();
        cxrEmployee.setId(id);
        cxrEmployee.setCxrSiteId(distributionStationId);

        if (null == cxrSiteMapper.selectById(distributionStationId)) {
            return false;
        }

        String siteName = cxrSiteMapper.selectById(distributionStationId).getName();

        // 更新这个冗余信息
        cxrEmployee.setSiteName(siteName);

        return baseMapper.updateById(cxrEmployee) > 0;
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateEmployeeGroupNullByEmployeeId(Long id, Long groupId) {
        CxrEmployee cxrEmployee = new CxrEmployee();
        cxrEmployee.setId(id);
        cxrEmployee.setCxrGroupId(groupId);
        return baseMapper.updateById(cxrEmployee) > 0;
    }

    @Override
    @GlobalTransactional(propagation = Propagation.NOT_SUPPORTED)
    public CxrEmployee selectByWxOpenid(String wxOpenid) {

        CxrEmployee cxrEmployee =
            baseMapper.selectOne(
                new LambdaQueryWrapper<CxrEmployee>()
                    .eq(CxrEmployee::getWxOpenid, wxOpenid)
                    .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));

        return cxrEmployee;
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public boolean updateUnionIdByWxOpenid(
        String wxOpenid, String wxNickname, String wxHeadPortrait, String wxUnionid) {

        CxrEmployee cxrEmployee = new CxrEmployee();
        cxrEmployee.setWxOpenid(wxOpenid);
        cxrEmployee.setWxNickname(wxNickname);
        cxrEmployee.setWxHeadPortrait(wxHeadPortrait);
        cxrEmployee.setWxUnionid(wxUnionid);

        return baseMapper.update(
            null,
            new LambdaUpdateWrapper<CxrEmployee>()
                .eq(CxrEmployee::getWxOpenid, cxrEmployee.getWxOpenid())
                .set(ObjectUtil.isNotEmpty(cxrEmployee.getWxUnionid()), CxrEmployee::getWxUnionid,
                    cxrEmployee.getWxUnionid())
                .set(CxrEmployee::getWxNickname, cxrEmployee.getWxNickname())
                .set(CxrEmployee::getWxHeadPortrait, cxrEmployee.getWxHeadPortrait())
                .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()))
            > 0;
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public boolean updateByWxOpenid(String wxOpenid, String wxNickname, String wxHeadPortrait) {

        CxrEmployee cxrEmployee = new CxrEmployee();
        cxrEmployee.setWxOpenid(wxOpenid);
        cxrEmployee.setWxNickname(wxNickname);
        cxrEmployee.setWxHeadPortrait(wxHeadPortrait);

        return baseMapper.update(
            cxrEmployee,
            new LambdaUpdateWrapper<CxrEmployee>()
                .eq(CxrEmployee::getWxOpenid, cxrEmployee.getWxOpenid())
                .set(CxrEmployee::getWxNickname, cxrEmployee.getWxNickname())
                .set(CxrEmployee::getWxHeadPortrait, cxrEmployee.getWxHeadPortrait())
                .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()))
            > 0;
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public boolean updateUnionIdOpenidByPhone(
        String phone, String wxOpenid, String wxNickname, String wxHeadPortrait, String wxUnionid) {

        // CxrEmployee cxrEmployee = new CxrEmployee();
        // cxrEmployee.setPhone(phone);
        // cxrEmployee.setWxOpenid(wxOpenid);
        // cxrEmployee.setWxNickname(wxNickname);
        // cxrEmployee.setWxHeadPortrait(wxHeadPortrait);
        // cxrEmployee.setWxUnionid(wxUnionid);
        // cxrEmployee.setIsBindWx(SysYesNo.YES.getValue());

        return baseMapper.update(
            null,
            new LambdaUpdateWrapper<CxrEmployee>()
                .eq(CxrEmployee::getPhone, phone)
                .set(CxrEmployee::getWxOpenid, wxOpenid)
                .set(CxrEmployee::getWxNickname, wxNickname)
                .set(CxrEmployee::getWxHeadPortrait, wxHeadPortrait)
                .set(CxrEmployee::getWxUnionid, wxUnionid)
                .set(CxrEmployee::getIsBindWx, SysYesNo.YES.getValue())
                .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()))
            > 0;
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public boolean updateUnionIdOpenidById(Long id) {
        return baseMapper.updateUnionIdOpenidById(id) > 0;
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public boolean updateOpenidByPhone(
        String phone, String wxOpenid, String wxNickname, String wxHeadPortrait) {

        //        CxrEmployee employee = baseMapper.selectOne(new
        // LambdaQueryWrapper<CxrEmployee>().eq(CxrEmployee::getPhone,
        //            phone).eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));
        //        if (employee != null && StringUtils.isNotBlank(employee.getWxOpenid()) &&
        // !wxOpenid.equals(employee.getWxOpenid())) {
        //            throw new ServiceException("该账号已经绑定其他微信，请解绑后再登录");
        //        }

        // 查询openid 是否已经绑定其他账号，如果绑定自动解绑
        CxrEmployee employee =
            baseMapper.selectOne(
                new LambdaQueryWrapper<CxrEmployee>()
                    .eq(CxrEmployee::getWxOpenid, wxOpenid)
                    .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));
        if (employee != null && !phone.equals(employee.getPhone())) {
            baseMapper.updateUnionIdOpenidById(employee.getId());
        }

        return baseMapper.update(
            null,
            new LambdaUpdateWrapper<CxrEmployee>()
                .eq(CxrEmployee::getPhone, phone)
                .set(
                    employee == null || !phone.equals(employee.getPhone()),
                    CxrEmployee::getWxOpenid,
                    wxOpenid)
                .set(CxrEmployee::getWxNickname, wxNickname)
                .set(CxrEmployee::getWxHeadPortrait, wxHeadPortrait)
                .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()))
            > 0;
    }

    @Override
    public PageTableDataInfo<StaffCxrEmployeeListVo> selectPageByStatus(
        StaffCxrEmployeeBo staffCxrEmployeeBo) {

        String status = staffCxrEmployeeBo.getStatus();
        String nameOrJobNumber = staffCxrEmployeeBo.getNameOrJobNumber();
        Long cxrSiteId = staffCxrEmployeeBo.getCxrSiteId();

        if (StringUtils.isBlank(status)) {
            throw new ServiceException("员工状态不能为空");
        }

        MPJLambdaWrapper<CxrEmployee> lambdaWrapper =
            new MPJLambdaWrapper<CxrEmployee>()
                .eq(cxrSiteId != null, CxrEmployee::getCxrSiteId, cxrSiteId)
                .like(StringUtils.isNotBlank(nameOrJobNumber), CxrEmployee::getName, nameOrJobNumber)
                .or()
                .like(
                    StringUtils.isNotBlank(nameOrJobNumber), CxrEmployee::getJobNumber, nameOrJobNumber)
                .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue());

        EmployeeStatus enumByValue = EmployeeStatus.getEnumByValue(status);
        if (enumByValue != null) {
            switch (enumByValue) {
                case INDUCTION:
                    lambdaWrapper = lambdaWrapper.eq(CxrEmployee::getOccupationStatus, status);
                    break;
                case WAIT:
                case FAIL:
                    lambdaWrapper = lambdaWrapper.eq(CxrEmployee::getInductionApplyAuditStatus, status);
                    break;
                default:
                    throw new ServiceException("配送端员工状态不正确");
            }
        }

        IPage<StaffCxrEmployeeListVo> cxrEmployeeListVoIPage =
            baseMapper.selectVoPage(
                staffCxrEmployeeBo.build(), lambdaWrapper, StaffCxrEmployeeListVo.class);
        return PageTableDataInfo.build(cxrEmployeeListVoIPage);
    }

    @Override
    public PageTableDataInfo<StaffCxrEmployeeListVo> selectBySiteId(
        Long cxrSiteId, PageQuery pageQuery) {

        MPJLambdaWrapper<CxrEmployee> lambdaWrapper =
            new MPJLambdaWrapper<CxrEmployee>()
                .eq(CxrEmployee::getOccupationStatus, OccupationStatus.INDUCTION.getValue())
                .eq(CxrEmployee::getCxrSiteId, cxrSiteId)
                .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue());

        IPage<StaffCxrEmployeeListVo> cxrEmployeeListVoIPage =
            baseMapper.selectVoPage(pageQuery.build(), lambdaWrapper, StaffCxrEmployeeListVo.class);
        return PageTableDataInfo.build(cxrEmployeeListVoIPage);
    }

    @Override
    public PageTableDataInfo<StaffCxrEmployeeListVo> selectBySiteIdAndGroupId(
        Long cxrSiteId, Long cxrGroupId, PageQuery pageQuery) {

        MPJLambdaWrapper<CxrEmployee> lambdaWrapper =
            new MPJLambdaWrapper<CxrEmployee>()
                .eq(CxrEmployee::getOccupationStatus, OccupationStatus.INDUCTION.getValue())
                .eq(CxrEmployee::getCxrSiteId, cxrSiteId)
                .eq(CxrEmployee::getCxrGroupId, cxrGroupId)
                .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue());

        // 根据站点id和小组id查询组员
        IPage<StaffCxrEmployeeListVo> cxrEmployeeListVoIPage =
            baseMapper.selectVoPage(pageQuery.build(), lambdaWrapper, StaffCxrEmployeeListVo.class);

        if (CollectionUtil.isNotEmpty(cxrEmployeeListVoIPage.getRecords())) {

            // 筛选组长
            for (StaffCxrEmployeeListVo record : cxrEmployeeListVoIPage.getRecords()) {
                // 根据站点id和小组id和员工id查询小组组长
                CxrGroupVo cxrGroupVo =
                    cxrGroupMapper.selectVoOne(
                        new LambdaQueryWrapper<CxrGroup>()
                            .eq(CxrGroup::getNowCxrEmployeeId, record.getId())
                            .eq(CxrGroup::getCxrSiteId, cxrSiteId)
                            .eq(CxrGroup::getId, cxrGroupId)
                            .eq(CxrGroup::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));

                if (cxrGroupVo != null) {
                    record.setGroupLeader(true);
                }
            }
        }

        return PageTableDataInfo.build(cxrEmployeeListVoIPage);
    }

    // 根据小组id和站点id查询小组
    public CxrGroup getGroup(StaffCxrEmployeeGroupBo staffCxrEmployeeGroupBo) {
        CxrGroup cxrGroup =
            cxrGroupMapper.selectOne(
                new LambdaQueryWrapper<CxrGroup>()
                    .eq(CxrGroup::getId, staffCxrEmployeeGroupBo.getCxrGroupId())
                    .eq(CxrGroup::getCxrSiteId, staffCxrEmployeeGroupBo.getCxrSiteId())
                    .eq(CxrGroup::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));

        if (cxrGroup == null) {
            throw new ServiceException("站点下的小组数据不存在");
        }

        return cxrGroup;
    }

    public CxrEmployee getEmployee(StaffCxrEmployeeGroupBo staffCxrEmployeeGroupBo) {
        // 一个小组只能有一个组长
        List<Long> cxrEmployeeIds = staffCxrEmployeeGroupBo.getCxrEmployeeIds();
        if (cxrEmployeeIds.size() > 1) {
            throw new ServiceException("员工传参异常");
        }

        CxrEmployee cxrEmployee =
            baseMapper.selectOne(
                new LambdaQueryWrapper<CxrEmployee>()
                    .eq(CxrEmployee::getOccupationStatus, OccupationStatus.INDUCTION.getValue())
                    .eq(CxrEmployee::getId, cxrEmployeeIds.get(0))
                    .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));

        if (cxrEmployee == null) {
            throw new ServiceException("员工不存在");
        }

        return cxrEmployee;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveGroupLeader(StaffCxrEmployeeGroupBo staffCxrEmployeeGroupBo) {

        CxrGroup cxrGroup = getGroup(staffCxrEmployeeGroupBo);

        CxrEmployee cxrEmployee = getEmployee(staffCxrEmployeeGroupBo);

        int update =
            cxrGroupMapper.update(
                cxrGroup,
                new LambdaUpdateWrapper<CxrGroup>()
                    .eq(CxrGroup::getId, cxrGroup.getId())
                    .eq(CxrGroup::getCxrSiteId, cxrGroup.getCxrSiteId())
                    .set(CxrGroup::getNowCxrEmployeeId, cxrEmployee.getId())
                    .eq(CxrGroup::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));

        int flag = 0;

        if (update > 0) {

            // 员工升为组长，同时绑定小组
            flag =
                baseMapper.update(
                    cxrEmployee,
                    new LambdaUpdateWrapper<CxrEmployee>()
                        .eq(CxrEmployee::getId, cxrEmployee.getId())
                        .eq(CxrEmployee::getCxrSiteId, cxrEmployee.getCxrSiteId())
                        .set(CxrEmployee::getCxrGroupId, cxrGroup.getId())
                        .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                        .eq(CxrEmployee::getOccupationStatus, OccupationStatus.INDUCTION.getValue()));
        }

        return flag > 0 ? true : false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveMembers(StaffCxrEmployeeGroupBo staffCxrEmployeeGroupBo) {

        int update = 0;

        for (Long cxrEmployeeId : staffCxrEmployeeGroupBo.getCxrEmployeeIds()) {

            CxrEmployee cxrEmployee =
                baseMapper.selectOne(
                    new LambdaQueryWrapper<CxrEmployee>()
                        .eq(CxrEmployee::getOccupationStatus, OccupationStatus.INDUCTION.getValue())
                        .eq(CxrEmployee::getId, cxrEmployeeId)
                        .eq(CxrEmployee::getCxrSiteId, staffCxrEmployeeGroupBo.getCxrSiteId())
                        .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));

            if (cxrEmployee == null) {
                throw new ServiceException("站点下的员工不存在");
            }

            update =
                baseMapper.update(
                    cxrEmployee,
                    new LambdaUpdateWrapper<CxrEmployee>()
                        .eq(CxrEmployee::getId, cxrEmployee.getId())
                        .eq(CxrEmployee::getCxrSiteId, cxrEmployee.getCxrSiteId())
                        .set(CxrEmployee::getCxrGroupId, staffCxrEmployeeGroupBo.getCxrGroupId())
                        .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                        .eq(CxrEmployee::getOccupationStatus, OccupationStatus.INDUCTION.getValue()));
        }

        return update > 0 ? true : false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeGroupLeader(StaffCxrEmployeeGroupBo staffCxrEmployeeGroupBo) {

        CxrGroup cxrGroup = getGroup(staffCxrEmployeeGroupBo);

        CxrEmployee cxrEmployee = getEmployee(staffCxrEmployeeGroupBo);

        return cxrGroupMapper.update(
            cxrGroup,
            new LambdaUpdateWrapper<CxrGroup>()
                .eq(CxrGroup::getId, cxrGroup.getId())
                .eq(CxrGroup::getCxrSiteId, cxrGroup.getCxrSiteId())
                .set(CxrGroup::getSourceCxrEmployeeId, cxrEmployee.getId())
                .set(CxrGroup::getNowCxrEmployeeId, null)
                .eq(CxrGroup::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()))
            > 0;
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public Boolean promotedGroupLeader(StaffCxrEmployeeGroupBo staffCxrEmployeeGroupBo) {

        CxrGroup cxrGroup = getGroup(staffCxrEmployeeGroupBo);

        CxrEmployee cxrEmployee = getEmployee(staffCxrEmployeeGroupBo);

        return cxrGroupMapper.update(
            cxrGroup,
            new LambdaUpdateWrapper<CxrGroup>()
                .eq(CxrGroup::getId, cxrGroup.getId())
                .eq(CxrGroup::getCxrSiteId, cxrGroup.getCxrSiteId())
                .set(CxrGroup::getNowCxrEmployeeId, cxrEmployee.getId())
                .eq(CxrGroup::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()))
            > 0;
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public Boolean exchangeGroup(StaffCxrEmployeeGroupBo staffCxrEmployeeGroupBo) {

        CxrGroup cxrGroup = getGroup(staffCxrEmployeeGroupBo);

        CxrEmployee cxrEmployee = getEmployee(staffCxrEmployeeGroupBo);

        return baseMapper.update(
            cxrEmployee,
            new LambdaUpdateWrapper<CxrEmployee>()
                .eq(CxrEmployee::getId, cxrEmployee.getId())
                .eq(CxrEmployee::getCxrSiteId, cxrEmployee.getCxrSiteId())
                .set(CxrEmployee::getCxrGroupId, cxrGroup.getId())
                .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .eq(CxrEmployee::getOccupationStatus, OccupationStatus.INDUCTION.getValue()))
            > 0;
    }

    /**
     * 根据站点查询 销售代理
     *
     * @param cxrEmployeeBo
     * @param pageQuery
     * @return
     */
    @Override
    public PageTableDataInfo<CxrEmployeeListVo> querySaleProxy(
        CxrEmployeeBo cxrEmployeeBo, PageQuery pageQuery) {

        //        if (ObjectUtil.isNull(cxrEmployeeBo.getCxrSiteId())) {
        //            return PageTableDataInfo.build(new Page<>());
        //        }
        List<String> list = new ArrayList<>();
        list.add(ProxyType.SALES_AGENT.getValue());

        Page<CxrEmployeeListVo> cxrEmployeeListVoPage =
            this.baseMapper.queryByProxyType(
                cxrEmployeeBo.getCxrSiteId(), JSONUtil.toJsonStr(list), pageQuery.build());

        if (CollUtil.isNotEmpty(cxrEmployeeListVoPage.getRecords())) {
            List<CxrEmployeeListVo> records = cxrEmployeeListVoPage.getRecords();
            List<Long> siteIds =
                records.stream().map(CxrEmployeeListVo::getCxrSiteId).collect(Collectors.toList());
            List<CxrSite> cxrSites =
                iCxrSiteService
                    .getBaseMapper()
                    .selectList(Wrappers.lambdaQuery(CxrSite.class).in(CxrSite::getId, siteIds));
            Map<Long, CxrSite> map =
                cxrSites.stream()
                    .collect(Collectors.toMap(CxrSite::getId, Function.identity(), (v1, v2) -> v2));
            for (CxrEmployeeListVo record : records) {
                CxrSite cxrSite = map.get(record.getSpareId());
                if (ObjectUtil.isNotNull(cxrSite)) {
                    record.setDepositBankCardIdCard(cxrSite.getName());
                }
            }
        }

        return PageTableDataInfo.build(cxrEmployeeListVoPage);
    }

    @Override
    public PageTableDataInfo<CxrEmployeeListVo> querySaleProxyWithSiteName(
        CxrEmployeeWithSiteNameBo cxrEmployeeBo, PageQuery pageQuery) {

        if (ObjectUtil.isNull(cxrEmployeeBo.getSiteName())
            && ObjectUtil.isNull(cxrEmployeeBo.getConditionStr())) {
            return PageTableDataInfo.build(new Page<>());
        }
        List<String> list = new ArrayList<>();
        list.add(ProxyType.SALES_AGENT.getValue());

        Page<CxrEmployeeListVo> cxrEmployeeListVoPage =
            this.baseMapper.queryByProxyTypeWithSiteName(
                cxrEmployeeBo.getSiteName(),
                cxrEmployeeBo.getConditionStr(),
                JSONUtil.toJsonStr(list),
                pageQuery.build());

        return PageTableDataInfo.build(cxrEmployeeListVoPage);
    }

    @Override
    public List<CxrEmployee> getByWxOpenid(String wxOpenid) {
        return baseMapper.getByWxOpenid(wxOpenid);
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public Integer updateWxOpenidById(Long id, String wxOpenid) {
        return baseMapper.updateWxOpenidById(id, wxOpenid, new Date());
    }

    @Override
    public List<CxrEmployee> getByPhone(String phone) {
        return baseMapper.getByPhone(phone);
    }

    @Override
    public CxrEmployee getNewByWxOpenid(String wxOpenid) {
        return baseMapper.getNewByWxOpenid(wxOpenid);
    }

    @Override
    public CxrEmployee getPhone(String phone) {
        return baseMapper.getPhone(phone);
    }

    @Override
    public CxrEmployee getPhoneOpenid(String phone, String wxOpenid) {
        return baseMapper.getPhoneOpenid(phone, wxOpenid);
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public int saveCxrEmployee(CxrEmployee cxxrEmployeeBo) {
        return baseMapper.insert(cxxrEmployeeBo);
    }

    @Override
    public List<CxrEmployee> getList(Long siteId) {
        return baseMapper.selectList(
            new LambdaQueryWrapper<CxrEmployee>().eq(CxrEmployee::getCxrSiteId, siteId));
    }

    /**
     * 根据站带你查询销售代理
     *
     * @param
     * @param page
     * @return
     */
    @Override
    public Page<CxrEmployee> queryBussinessAgentBySiteId(
        Map<String, Object> map, Page<CxrEmployee> page) {
        Page<CxrEmployee> resultPage = this.baseMapper.queryBussinessAgentBySiteId(map, page);
        return resultPage;
    }

    @Override
    public Page<CxrEmployee> queryBussinessAgentDimissionOrStaffBySiteId(
        Map<String, Object> map, Page<CxrEmployee> page) {
        Page<CxrEmployee> resultPage =
            this.baseMapper.queryBussinessAgentDimissionOrStaffBySiteId(map, page);
        return resultPage;
    }

    // 员工管理的分页查询
    @Override
    public PageTableDataInfo<StaffCxrEmployeeListVo> selectPageByPeople(
        StaffCxrPeopleBo staffCxrPeopleBo) {
//        Collection<Long> authSiteIds = StaffLoginHelper.getLoginUser().getAuthSiteIds();
        List<CxrEmployeePost> posts = cxrEmployeePostMapper.selectList(
            new LambdaQueryWrapper<CxrEmployeePost>()
                .eq(CxrEmployeePost::getCxrEmployeeId, StaffLoginHelper.getLoginUser().getUserId())
                .eq(CxrEmployeePost::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .eq(CxrEmployeePost::getChargeStatus, ChargeStatus.IN_PROGRESS.getValue())
                .apply("cxr_post_id -> '$.value' in( {0}, {1})", PostType.REGION_MANAGER.getValue(),
                    PostType.DIRECTOR.getValue())
        );

        Long cxrSiteId = staffCxrPeopleBo.getCxrSiteId();
        if (CollectionUtil.isNotEmpty(posts)) {
            List<Long> authSiteIds = new ArrayList<>();
            Map<String, List<CxrEmployeePost>> postMap = posts.stream()
                .collect(Collectors.groupingBy(a -> a.getCxrPostId().getValue()));
            List<CxrEmployeePost> employeePosts = postMap.get(PostType.DIRECTOR.getValue());
            if (CollUtil.isNotEmpty(employeePosts)) {
                List<Long> siteIds = employeePosts.stream().map(CxrEmployeePost::getCxrSiteId)
                    .collect(Collectors.toList());
                authSiteIds.addAll(siteIds);
            }
            List<CxrEmployeePost> employeePostList = postMap.get(PostType.REGION_MANAGER.getValue());
            if (CollUtil.isNotEmpty(employeePostList)) {
                List<Long> regionIds = employeePostList.stream().map(CxrEmployeePost::getCxrRegionId)
                    .collect(Collectors.toList());
                List<CxrSite> cxrSites = cxrSiteMapper.selectList(new LambdaQueryWrapper<CxrSite>()
                    .in(CxrSite::getCxrRegionId, regionIds)
                    .eq(CxrSite::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));
                List<Long> siteIds = cxrSites.stream().map(CxrSite::getId)
                    .collect(Collectors.toList());
                authSiteIds.addAll(siteIds);
            }

            String nameOrJobNumber = staffCxrPeopleBo.getNameOrJobNumber();
            MPJLambdaWrapper<CxrEmployee> lambdaWrapper =
                new MPJLambdaWrapper<CxrEmployee>()
                    .selectAll(CxrEmployee.class)
                    .selectAs(CxrSite::getProvice, StaffCxrEmployeeListVo::getProvince)
                    .selectAs(CxrSite::getCity, StaffCxrEmployeeListVo::getCity)
                    .selectAs(CxrSite::getArea, StaffCxrEmployeeListVo::getArea)
                    .selectAs(CxrSite::getName, StaffCxrEmployeeListVo::getCxrSiteName)
                    .leftJoin(CxrSite.class, CxrSite::getId, CxrEmployee::getCxrSiteId)
                    // 省
                    .eq(
                        StringUtils.isNotBlank(staffCxrPeopleBo.getProvince()),
                        CxrSite::getProvice,
                        staffCxrPeopleBo.getProvince())
                    // 城市
                    .eq(
                        StringUtils.isNotBlank(staffCxrPeopleBo.getCity()),
                        CxrSite::getCity,
                        staffCxrPeopleBo.getCity())
                    // 区
                    .eq(
                        StringUtils.isNotBlank(staffCxrPeopleBo.getArea()),
                        CxrSite::getArea,
                        staffCxrPeopleBo.getArea())
                    // 员工等级
                    .eq(
                        StringUtils.isNotBlank(staffCxrPeopleBo.getEmployeeLevelType()),
                        CxrEmployee::getEmployeeLevelType,
                        staffCxrPeopleBo.getEmployeeLevelType())
                    // 站点
                    .eq(cxrSiteId != null, CxrEmployee::getCxrSiteId, cxrSiteId)
                    .in(!CollectionUtil.isEmpty(authSiteIds), CxrEmployee::getCxrSiteId, authSiteIds)
                    .le(
                        staffCxrPeopleBo.getInductionBeginTime() == null
                            && staffCxrPeopleBo.getInductionEndTime() != null,
                        CxrEmployee::getInductionTime,
                        staffCxrPeopleBo.getInductionEndTime())
                    .between(
                        staffCxrPeopleBo.getInductionBeginTime() != null
                            && staffCxrPeopleBo.getInductionEndTime() != null,
                        CxrEmployee::getInductionTime,
                        staffCxrPeopleBo.getInductionBeginTime(),
                        staffCxrPeopleBo.getInductionEndTime())
                    .ge(
                        staffCxrPeopleBo.getInductionBeginTime() != null
                            && staffCxrPeopleBo.getInductionEndTime() == null,
                        CxrEmployee::getInductionTime,
                        staffCxrPeopleBo.getInductionBeginTime())
                    .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                    .and(wrapper -> wrapper
                        .eq(CxrEmployee::getOccupationStatus, OccupationStatus.INDUCTION.getValue())
                        .or()
                        .eq(CxrEmployee::getOccupationStatus, OccupationStatus.WAIT_INDUCTION.getValue())
                    )
                    .and(StringUtils.isNotBlank(nameOrJobNumber), wrapper -> wrapper// 人员和电话号码的模糊查询
                        .like(StringUtils.isNotBlank(nameOrJobNumber), CxrEmployee::getName, nameOrJobNumber)
                        .or()
                        .likeRight(
                            StringUtils.isNotBlank(nameOrJobNumber), CxrEmployee::getPhone, nameOrJobNumber)
                        .or()
                        .likeRight(StrUtil.isNotBlank(nameOrJobNumber), CxrEmployee::getJobNumber, nameOrJobNumber)
                    )
                    .eq(staffCxrPeopleBo.getAuthStatus() != null, CxrEmployee::getAuthStatus,
                        staffCxrPeopleBo.getAuthStatus());
            IPage<StaffCxrEmployeeListVo> staffCxrEmployeeListVoIPage =
                baseMapper.selectJoinPage(
                    staffCxrPeopleBo.build(), StaffCxrEmployeeListVo.class, lambdaWrapper);
            return PageTableDataInfo.build(staffCxrEmployeeListVoIPage);
        }
        return PageTableDataInfo.build();
    }

    // 依据部门id来查询详情
    @Override
    public CxrEmployee getId(Long id) {
        CxrEmployee cxrEmployee = baseMapper.getId(id);
        if (cxrEmployee == null) {
            throw new ServiceException("员工数据已经不存在");
        }
        return cxrEmployee;
    }

    // 依据不同人来查询不同的站点
    @Override
    public PageTableDataInfo<StaffCxrEmployeeListVo> listPeopleSide(
        StaffCxrPeopleBo staffCxrPeopleBo) {
        Collection<CxrSite> aAuthSites = StaffLoginHelper.getLoginUser().getAuthSites();
        Collection<Long> authSiteIds = StaffLoginHelper.getLoginUser().getAuthSiteIds();
        if (CollectionUtil.isNotEmpty(aAuthSites)) {
            MPJLambdaWrapper<CxrEmployee> lambdaWrapper =
                new MPJLambdaWrapper<CxrEmployee>()
                    .selectAll(CxrEmployee.class)
                    .selectAs(CxrSite::getName, StaffCxrEmployeeListVo::getCxrSiteName)
                    .leftJoin(CxrSite.class, CxrSite::getId, CxrEmployee::getCxrSiteId)
                    .in(!CollectionUtil.isEmpty(authSiteIds), CxrEmployee::getCxrSiteId, authSiteIds)
                    .eq(
                        StringUtils.isNotBlank(staffCxrPeopleBo.getCxrSiteName()),
                        CxrSite::getName,
                        staffCxrPeopleBo.getCxrSiteName());
            IPage<StaffCxrEmployeeListVo> staffCxrEmployeeListVoIPage =
                baseMapper.selectJoinPage(
                    staffCxrPeopleBo.build(), StaffCxrEmployeeListVo.class, lambdaWrapper);
            return PageTableDataInfo.build(staffCxrEmployeeListVoIPage);
        }
        return PageTableDataInfo.build();
    }

    /**
     * 同步员工信息到培训系统
     *
     * @param cxrEmployeeId
     */
    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public void sendSyncEmployssToTrain(Long cxrEmployeeId) {

        CxrEmployee cxrEmployee = getId(cxrEmployeeId);
        CxrSite cxrSite = cxrSiteMapper.selectById(cxrEmployee.getCxrSiteId());

        // 同步组织
        syncOrganizeVer(
            cxrSite.getCxrRootRegionName(), cxrSite.getProvice(), cxrSite.getCity(), cxrSite.getName());

        // 同步岗位
        String postName = PostType.COMMON_EMPLOYEE.getName();
        List<CxrEmployeePost> cxrEmployeePosts =
            iCxrEmployeePostService
                .getBaseMapper()
                .selectList(
                    Wrappers.lambdaQuery(CxrEmployeePost.class)
                        .eq(CxrEmployeePost::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                        .and(
                            a ->
                                a.eq(
                                        CxrEmployeePost::getChargeStatus,
                                        ChargeStatus.IN_PROGRESS.getValue())
                                    .or()
                                    .isNull(CxrEmployeePost::getInChargeEndTime))
                        .eq(CxrEmployeePost::getCxrEmployeeId, cxrEmployee.getId()));

        if (CollUtil.isNotEmpty(cxrEmployeePosts)) {
            // 是否组长
            Long count =
                cxrGroupMapper.selectCount(
                    new LambdaQueryWrapper<CxrGroup>()
                        .eq(CxrGroup::getNowCxrEmployeeId, cxrEmployeeId)
                        .eq(CxrGroup::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));

            List<CxrPostId> cxrPostIdList =
                cxrEmployeePosts.stream().map(CxrEmployeePost::getCxrPostId).collect(Collectors.toList());

            log.info("cxrPostIdList{}", cxrPostIdList);
            cxrPostIdList.sort(Comparator.comparing(CxrPostId::getValue).reversed());

            if (cxrPostIdList.size() > 1) {
                postName = cxrPostIdList.get(0).getName();
            } else {
                postName = cxrPostIdList.get(0).getName();
            }

            log.info("postName{}", postName);
            if (postName.equals("普通职员")) {
                postName = "销售代理";
                if (count > 0l) {
                    postName = "组长";
                }
            }

            syncPositionsVer(postName);

        } else {
            syncPositionsVer(postName);
        }
        Integer workFrequency = Convert.toInt(cxrEmployee.getWorkFrequency(), 0);

        if (NumberUtil.equals(workFrequency, 1)) {
            // 同步员工
            syncUsersVer(cxrEmployee, cxrSite.getName(), postName);
        } else {
            sendUpdateSyncEmployssToTrain(cxrEmployeeId);
        }
    }

    /**
     * 更新同步员工状态 ->培训系统
     *
     * @param cxrEmployeeId
     */
    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public void sendUpdateSyncEmployssToTrain(Long cxrEmployeeId) {
        String requestURI = trainpropertiesConfig.getUsersUrl();
        String sigin = sigin(trainpropertiesConfig.getSiginUsersUrl());
        String corpCode = trainpropertiesConfig.getCorpCode();
        String appKey = trainpropertiesConfig.getAppKey();
        // 获取员工
        CxrEmployee cxrEmployee = baseMapper.getId(cxrEmployeeId);
        CxrSite cxrSite = cxrSiteMapper.selectById(cxrEmployee.getCxrSiteId());
        List<User> userList = new ArrayList<>();
        User user = new User();
        // 工号
        user.setEmployeeCode(cxrEmployee.getJobNumber());
        // 企业id     配置中的
        user.setCorpCode(corpCode);
        // 员工姓名
        user.setUserName(cxrEmployee.getName());
        String postName = PostType.COMMON_EMPLOYEE.getName();
        List<CxrEmployeePost> cxrEmployeePosts =
            iCxrEmployeePostService
                .getBaseMapper()
                .selectList(
                    Wrappers.lambdaQuery(CxrEmployeePost.class)
                        .eq(CxrEmployeePost::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                        .and(
                            a ->
                                a.eq(
                                        CxrEmployeePost::getChargeStatus,
                                        ChargeStatus.IN_PROGRESS.getValue())
                                    .or()
                                    .isNull(CxrEmployeePost::getInChargeEndTime))
                        .eq(CxrEmployeePost::getCxrEmployeeId, cxrEmployee.getId()));

        if (CollUtil.isNotEmpty(cxrEmployeePosts)) {

            // 是否组长
            Long count =
                cxrGroupMapper.selectCount(
                    new LambdaQueryWrapper<CxrGroup>()
                        .eq(CxrGroup::getNowCxrEmployeeId, cxrEmployeeId)
                        .eq(CxrGroup::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));

            List<CxrPostId> cxrPostIdList =
                cxrEmployeePosts.stream().map(CxrEmployeePost::getCxrPostId).collect(Collectors.toList());

            log.info("cxrPostIdList{}", cxrPostIdList);
            cxrPostIdList.sort(
                Comparator.comparing((CxrPostId person) -> Integer.parseInt(person.getValue())).reversed());
            if (cxrPostIdList.size() > 1) {
                postName = cxrPostIdList.get(0).getName();
            } else {
                postName = cxrPostIdList.get(0).getName();
            }

            log.info("postName{}", postName);
            if (postName.equals("普通职员")) {
                postName = "销售代理";
                if (count > 0l) {
                    postName = "组长";
                }
            }

            syncPositionsVer(postName);
        }

        user.setPositionCode(postName);
        user.setOnBoarding(cxrEmployee.getInductionTime().toString());
        // 手机号
        // user.setLoginName(cxrEmployee.getPhone());
        user.setLoginName(cxrEmployee.getJobNumber());
        // 如果是离职状态   就填写  冻结
        if (cxrEmployee.getOccupationStatus().equals(OccupationStatus.QUIT_ONESELF.getValue())
            || cxrEmployee.getOccupationStatus().equals(OccupationStatus.QUIT_PROCEDURE.getValue())) {
            // 账号状态  冻结
            user.setAccountStatus("FORBIDDEN");
        } else if (cxrEmployee.getOccupationStatus().equals(OccupationStatus.INDUCTION.getValue())) {
            // 账号状态   激活
            user.setAccountStatus("ENABLE");
        }
        // 部门编号
        user.setOrganizeCode(cxrSite.getName());
        //        user.setOrganizeCode(cxrSite.getArea());
        user.setMobile(cxrEmployee.getPhone());
        user.setRank(convertLevel(cxrEmployee.getEmployeeLevelType()));
        userList.add(user);

        Map<String, String> map = new HashMap<>();
        map.put("users", JSONUtil.toJsonStr(userList));
        map.put("updatePassword", Boolean.FALSE + "");
        map.put("sign_", sigin); // 签名
        map.put("appKey_", appKey); // key
        map.put("timestamp_", System.currentTimeMillis() + ""); // 时间 毫秒值

        // 发送 请求
        OkHttpUtils.postMapSync(
            requestURI,
            map,
            new OkHttpUtils.OnOkHttpCallback() {

                @SneakyThrows
                @Override
                public void onFailure(IOException e) {
//                    sendUpdateSyncExceptionExecutionToTrain(cxrEmployee);
                    throw new ServiceException("同步人员失败");
                }

                @SneakyThrows
                @Override
                public void onFailure(String msg) {
//                    sendUpdateSyncExceptionExecutionToTrain(cxrEmployee);
                    throw new ServiceException("同步人员失败");
                }

                @SneakyThrows
                @Override
                public void onSuccessful(String json) {
                    log.info("更新同步人员放回json:{}", json);
                    TrainResponse trainResponse = null;
                    try {
                        trainResponse = JSONUtil.toBean(json, TrainResponse.class);
                    } catch (Exception e) {
//                        throw new RuntimeException(e);
                        log.error("更新同步人员失败", e);
                    }
                    if (StrUtil.equals(trainResponse.getStatus(), "OK")
                        && ObjectUtil.equals(trainResponse.getSuccess(), Boolean.TRUE)
                        && NumberUtil.equals(trainResponse.getSuccessCount(), 1)) {
                        log.info("同步人员完成");
                    } else {
//                        sendUpdateSyncExceptionExecutionToTrain(cxrEmployee);
                        throw new ServiceException("同步人员失败");
                    }
                }
            });
    }

    @Override
    public void test1() {

        ExecutorService touchWorker =
            Executors.newFixedThreadPool(30, Executors.defaultThreadFactory());
        List<CxrEmployee> employees =
            baseMapper.selectList(
                new LambdaQueryWrapper<CxrEmployee>().isNotNull(CxrEmployee::getQuitTime)
                //            .ge(CxrEmployee::getQuitTime, "2023-02-01")
            );

        List<List<CxrEmployee>> split = CollectionUtil.split(employees, 60);

        for (List<CxrEmployee> importListVos : split) {
            touchWorker.execute(() -> demo111(importListVos)); // 线程开始
        }
        touchWorker.shutdown(); // 关闭线程池
        while (true) {
            if (touchWorker.isTerminated()) {
                break;
            }
        }
    }

    @Override
    public void test2(Long siteId, String jobNumer) {

//        List<CxrEmployeeLevelAdjust> levelAdjusts = employeeLevelAdjustMapper.selectList(
//            new LambdaQueryWrapper<CxrEmployeeLevelAdjust>()
////                .like(CxrEmployeeLevelAdjust::getCreateTime, "%2023-10%")
//        );
//
//        List<Long> listId = levelAdjusts.stream().map(CxrEmployeeLevelAdjust::getCxrEmployeeId)
//            .collect(Collectors.toList());
        List<CxrEmployee> employees =
            baseMapper.selectList(
                new MyLambdaQueryWrapper<CxrEmployee>()
//                    .in(CxrEmployee::getInductionTime, "2023-07-11", "2023-07-10")
//                    .isNotNull(CxrEmployee::getQuitTime)
                    .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
//                    .in(ObjectUtil.isNotEmpty(siteId), CxrEmployee::getCxrSiteId, siteId)
//                    .in(CxrEmployee::getId, listId)
//                    .in(CxrEmployee::getOccupationStatus, OccupationStatus.QUIT_ONESELF.getValue(),
//                        OccupationStatus.QUIT_PROCEDURE.getValue())
                    .eq(CxrEmployee::getJobNumber, jobNumer)
            );

//        List<CxrEmployee> employees = baseMapper.ConverPeiXun();
        List<List<CxrEmployee>> split = CollectionUtil.split(employees, 10);
        ExecutorService touchWorker =
            Executors.newFixedThreadPool(10, Executors.defaultThreadFactory());
        for (List<CxrEmployee> cxrEmployees : split) {
            touchWorker.execute(() -> executorEmployees(cxrEmployees)); // 线程开始
        }
        touchWorker.shutdown(); // 关闭线程池
        while (true) {
            if (touchWorker.isTerminated()) {
                break;
            }
        }
    }

    @Override
    public List<EmployeesVo> getEmployeesVoThreeLeaves(String mouth, Long cxrSiteId) {
        return baseMapper.getEmployeesVoThreeLeaves(mouth, cxrSiteId);
    }

    @Override
    public List<EmployeesVo> getEmployeesVoLeavePeoPles(
        String mouth, List<Long> ids) {
        return baseMapper.getEmployeesVoLeavePeoPles(mouth, ids);
    }

    @Override
    public List<EmployeesVo> getEmployeeOnAccupation(List<Long> ids) {
        return baseMapper.getEmployeeOnAccupation(ids);
    }

    private void executorEmployees(List<CxrEmployee> cxrEmployees) {
        for (CxrEmployee item : cxrEmployees) {
            sendSyncEmployssToTrain(item.getId());
            sendUpdateSyncEmployssToTrain(item.getId());
        }
    }

    private void demo111(List<CxrEmployee> employees) {
        String requestURI = trainpropertiesConfig.getUsersUrl();
        String sigin = sigin(trainpropertiesConfig.getSiginUsersUrl());
        String corpCode = trainpropertiesConfig.getCorpCode();
        String appKey = trainpropertiesConfig.getAppKey();
        for (CxrEmployee cxrEmployee : employees) {

            CxrSite cxrSite = cxrSiteMapper.selectById(cxrEmployee.getCxrSiteId());
            List<User> userList = new ArrayList<>();
            User user = new User();
            // 工号
            user.setEmployeeCode(cxrEmployee.getJobNumber());
            // 企业id     配置中的
            user.setCorpCode(corpCode);
            // 员工姓名
            user.setUserName(cxrEmployee.getName());
            String postName = PostType.COMMON_EMPLOYEE.getName();
            List<CxrEmployeePost> cxrEmployeePosts =
                iCxrEmployeePostService
                    .getBaseMapper()
                    .selectList(
                        Wrappers.lambdaQuery(CxrEmployeePost.class)
                            .eq(CxrEmployeePost::getCxrEmployeeId, cxrEmployee.getId()));

            if (CollUtil.isNotEmpty(cxrEmployeePosts)) {

                List<CxrPostId> cxrPostIdList =
                    cxrEmployeePosts.stream()
                        .map(CxrEmployeePost::getCxrPostId)
                        .collect(Collectors.toList());
                log.info("cxrPostIdList{}", cxrPostIdList);
                cxrPostIdList.sort(Comparator.comparing(CxrPostId::getValue).reversed());
                postName = cxrPostIdList.get(0).getName();
                log.info("postName{}", postName);
                if (postName.equals("普通职员")) {
                    postName = "销售代理";
                }
                //                syncPositionsVer(postName);
            }

            user.setPositionCode(postName);

            // 手机号
            user.setLoginName(cxrEmployee.getJobNumber());

            // 如果是离职状态   就填写  冻结
            if (cxrEmployee.getOccupationStatus().equals(OccupationStatus.QUIT_ONESELF.getValue())
                || cxrEmployee.getOccupationStatus().equals(OccupationStatus.QUIT_PROCEDURE.getValue())) {
                // 账号状态  冻结
                user.setAccountStatus("FORBIDDEN");
            } else if (cxrEmployee.getOccupationStatus().equals(OccupationStatus.INDUCTION.getValue())) {
                // 账号状态   激活
                user.setAccountStatus("ENABLE");
            }

            // 部门编号
            user.setOrganizeCode(cxrSite.getName());
            //            user.setOrganizeCode(cxrSite.getArea());
            user.setMobile(cxrEmployee.getPhone());
            user.setRank(convertLevel(cxrEmployee.getEmployeeLevelType()));
            userList.add(user);

            Map<String, String> map = new HashMap<>();
            map.put("users", JSONUtil.toJsonStr(userList));
            map.put("updatePassword", Boolean.FALSE + "");
            map.put("sign_", sigin); // 签名
            map.put("appKey_", appKey); // key
            map.put("timestamp_", System.currentTimeMillis() + ""); // 时间 毫秒值

            // 发送 请求
            OkHttpUtils.postMapSync(
                requestURI,
                map,
                new OkHttpUtils.OnOkHttpCallback() {

                    @SneakyThrows
                    @Override
                    public void onFailure(IOException e) {
                        //                    sendUpdateSyncExceptionExecutionToTrain(cxrEmployee);
                        //                    throw new ServiceException("同步人员失败");
                    }

                    @SneakyThrows
                    @Override
                    public void onFailure(String msg) {
                        //                    sendUpdateSyncExceptionExecutionToTrain(cxrEmployee);
                        //                    throw new ServiceException("同步人员失败");
                    }

                    @SneakyThrows
                    @Override
                    public void onSuccessful(String json) {
                        TrainResponse trainResponse = JSONUtil.toBean(json, TrainResponse.class);
                        if (StrUtil.equals(trainResponse.getStatus(), "OK")
                            && ObjectUtil.equals(trainResponse.getSuccess(), Boolean.TRUE)
                            && NumberUtil.equals(trainResponse.getSuccessCount(), 1)) {
                            log.info("同步人员完成");
                        } else {
                            //                        sendUpdateSyncExceptionExecutionToTrain(cxrEmployee);
                            //                        throw new ServiceException("同步人员失败");
                        }
                    }
                });
        }
    }

    public void sendUpdateSyncExceptionExecutionToTrain(CxrEmployee cxrEmployee) {
        String requestURI = trainpropertiesConfig.getUsersUrl();
        String sigin = sigin(trainpropertiesConfig.getSiginUsersUrl());
        String corpCode = trainpropertiesConfig.getCorpCode();
        String appKey = trainpropertiesConfig.getAppKey();
        // 获取员工

        CxrSite cxrSite = cxrSiteMapper.selectById(cxrEmployee.getCxrSiteId());
        List<User> userList = new ArrayList<>();
        User user = new User();
        // 工号
        user.setEmployeeCode(cxrEmployee.getJobNumber());
        // 企业id     配置中的
        user.setCorpCode(corpCode);
        // 员工姓名
        user.setUserName(cxrEmployee.getName());
        String postName = PostType.COMMON_EMPLOYEE.getName();
        List<CxrEmployeePost> cxrEmployeePosts =
            iCxrEmployeePostService
                .getBaseMapper()
                .selectList(
                    Wrappers.lambdaQuery(CxrEmployeePost.class)
                        .eq(CxrEmployeePost::getCxrEmployeeId, cxrEmployee.getId()));

        if (CollUtil.isNotEmpty(cxrEmployeePosts)) {

            List<CxrPostId> cxrPostIdList =
                cxrEmployeePosts.stream().map(CxrEmployeePost::getCxrPostId).collect(Collectors.toList());
            log.info("cxrPostIdList{}", cxrPostIdList);
            cxrPostIdList.sort(Comparator.comparing(CxrPostId::getValue).reversed());
            postName = cxrPostIdList.get(0).getName();
            log.info("postName{}", postName);
            if (postName.equals("普通职员")) {
                postName = "销售代理";
            }
            syncPositionsVer(postName);
        }

        user.setPositionCode(postName);

        // 手机号
        // user.setLoginName(cxrEmployee.getPhone());
        user.setLoginName(cxrEmployee.getJobNumber());
        // 如果是离职状态   就填写  冻结
        if (cxrEmployee.getOccupationStatus().equals(OccupationStatus.QUIT_ONESELF.getValue())
            || cxrEmployee.getOccupationStatus().equals(OccupationStatus.QUIT_PROCEDURE.getValue())) {
            // 账号状态  冻结
            user.setAccountStatus("FORBIDDEN");
        } else if (cxrEmployee.getOccupationStatus().equals(OccupationStatus.INDUCTION.getValue())) {
            // 账号状态   激活
            user.setAccountStatus("ENABLE");
        }
        // 部门编号
        //        user.setOrganizeCode(cxrSite.getName());
        user.setOrganizeCode(cxrSite.getArea());
        user.setMobile(cxrEmployee.getPhone());
        user.setRank(convertLevel(cxrEmployee.getEmployeeLevelType()));
        userList.add(user);

        Map<String, String> map = new HashMap<>();
        map.put("users", JSONUtil.toJsonStr(userList));
        map.put("updatePassword", Boolean.FALSE + "");
        map.put("sign_", sigin); // 签名
        map.put("appKey_", appKey); // key
        map.put("timestamp_", System.currentTimeMillis() + ""); // 时间 毫秒值

        // 发送 请求
        OkHttpUtils.postMapSync(
            requestURI,
            map,
            new OkHttpUtils.OnOkHttpCallback() {

                @SneakyThrows
                @Override
                public void onFailure(IOException e) {
                    throw new ServiceException("同步人员失败");
                }

                @SneakyThrows
                @Override
                public void onFailure(String msg) {
                    throw new ServiceException("同步人员失败");
                }

                @SneakyThrows
                @Override
                public void onSuccessful(String json) {
                    TrainResponse trainResponse = JSONUtil.toBean(json, TrainResponse.class);
                    if (StrUtil.equals(trainResponse.getStatus(), "OK")
                        && ObjectUtil.equals(trainResponse.getSuccess(), Boolean.TRUE)
                        && NumberUtil.equals(trainResponse.getSuccessCount(), 1)) {
                        log.info("同步人员完成");
                    } else {
                        throw new ServiceException("同步人员失败");
                    }
                }
            });
    }

    /**
     * 签名
     *
     * @param requestURI
     * @return
     */
    private String sigin(String requestURI) {
        String signText =
            trainpropertiesConfig.getAppSecret()
                + "|"
                + requestURI
                + "|"
                + trainpropertiesConfig.getAppSecret();
        System.out.println(signText);
        String s = MD5.create().digestHex(signText).toUpperCase();
        System.out.println(s);
        return s;
    }

    private String convertLevel(String value) {
        String levelLabel =
            remoteDictService.getDictLabel(DictTypeEnums.EMPLOYEE_LEVEL_TYPE.getValue(), value, "");
        log.info(">>>>>>>>>>convertLevel levelLabel ={}", levelLabel);
        return levelLabel;
    }

    /**
     * @param cxrEmployee
     * @param postName
     */
    private void syncUsersVer(CxrEmployee cxrEmployee, String siteName, String postName) {
        String requestURI = trainpropertiesConfig.getUsersUrl();
        String sigin = sigin(trainpropertiesConfig.getSiginUsersUrl());
        String corpCode = trainpropertiesConfig.getCorpCode();
        String appKey = trainpropertiesConfig.getAppKey();

        List<User> userList = new ArrayList<>();
        User user = new User();
        // 工号
        user.setEmployeeCode(cxrEmployee.getJobNumber());
        // 企业id     配置中的
        user.setCorpCode(corpCode);
        // 员工姓名
        user.setUserName(cxrEmployee.getName());
        // 手机号
        // user.setLoginName(cxrEmployee.getPhone());
        user.setLoginName(cxrEmployee.getJobNumber());
        user.setOnBoarding(cxrEmployee.getInductionTime().toString());
        // 账号状态
        user.setAccountStatus("ENABLE");
        // 部门编号
        user.setOrganizeCode(siteName);
        // 岗位编号
        user.setPositionCode(postName);
        // 默认密码
        user.setPassword(MD5.create().digestHex(cxrEmployee.getJobNumber()));
        user.setMobile(cxrEmployee.getPhone());
        user.setRank(convertLevel(cxrEmployee.getEmployeeLevelType()));
        userList.add(user);

        log.info("{}", JSONUtil.toJsonStr(userList));

        Map<String, String> map = new HashMap<>();
        map.put("users", JSONUtil.toJsonStr(userList));
        map.put("updatePassword", Boolean.TRUE + "");
        map.put("sign_", sigin); // 签名
        map.put("appKey_", appKey); // key
        map.put("timestamp_", System.currentTimeMillis() + ""); // 时间 毫秒值

        // 发送 请求
        OkHttpUtils.postMapSync(
            requestURI,
            map,
            new OkHttpUtils.OnOkHttpCallback() {

                @SneakyThrows
                @Override
                public void onFailure(IOException e) {
                    throw new ServiceException("同步人员失败");
                }

                @SneakyThrows
                @Override
                public void onFailure(String msg) {
                    throw new ServiceException("同步人员失败");
                }

                @SneakyThrows
                @Override
                public void onSuccessful(String json) {
                    TrainResponse trainResponse = JSONUtil.toBean(json, TrainResponse.class);
                    log.info("同步人员放回：{},json:{}", trainResponse, json);
                    if (StrUtil.equals(trainResponse.getStatus(), "OK")
                        && ObjectUtil.equals(trainResponse.getSuccess(), Boolean.TRUE)
                        && NumberUtil.equals(trainResponse.getSuccessCount(), 1)) {
                        log.info("同步人员完成");
                    } else {
                        throw new ServiceException("同步人员失败");
                    }
                }
            });
    }

    /**
     * 同步职位
     *
     * @param postName
     */
    private void syncPositionsVer(String postName) {
        String requestURI = trainpropertiesConfig.getPositionsUrl();
        String sigin = sigin(trainpropertiesConfig.getSiginPositionsUrl());
        String corpCode = trainpropertiesConfig.getCorpCode();
        String appKey = trainpropertiesConfig.getAppKey();

        List<Position> positionList = new ArrayList<>();
        Position position = new Position();
        position.setPositionCode(postName);
        position.setPositionName(postName);
        position.setCategoryCode(postName);
        position.setCategoryName(postName);
        position.setCorpCode(corpCode);

        positionList.add(position);
        Map<String, String> map = new HashMap<>();
        map.put("positions", JSONUtil.toJsonStr(positionList));
        map.put("sign_", sigin);
        map.put("appKey_", appKey);
        map.put("timestamp_", System.currentTimeMillis() + "");

        OkHttpUtils.postMapSync(
            requestURI,
            map,
            new OkHttpUtils.OnOkHttpCallback() {
                @SneakyThrows
                @Override
                public void onFailure(IOException e) {
                    //                throw new ServiceException("同步岗位失败");
                }

                @SneakyThrows
                @Override
                public void onFailure(String msg) {
                    //                throw new ServiceException("同步岗位失败");
                }

                @SneakyThrows
                @Override
                public void onSuccessful(String json) {
                    try {
                        TrainResponse trainResponse = JSONUtil.toBean(json, TrainResponse.class);
                        if (StrUtil.equals(trainResponse.getStatus(), "OK")
                            && ObjectUtil.equals(trainResponse.getSuccess(), Boolean.TRUE)
                            && NumberUtil.equals(trainResponse.getSuccessCount(), 1)) {
                            log.info("同步岗位完成");
                        } else {
                            log.error("同步岗位失败信息:{}", json);
                            //                    throw new ServiceException("同步岗位失败");
                        }
                    } catch (Exception e) {
                        log.error("同步岗位失败信息:", e);
                    }
                }
            });
    }

    /**
     * 同步组织
     *
     * @param bigArea 大区名称
     * @param provice 省名称
     * @param city    城市名称
     * @param
     */
    @SneakyThrows
    private void syncOrganizeVer(String bigArea, String provice, String city, String siteName) {

        String requestURI = trainpropertiesConfig.getOrganizeUrl();
        String sigin = sigin(trainpropertiesConfig.getSiginOrganizeUrl());
        String corpCode = trainpropertiesConfig.getCorpCode();
        String appKey = trainpropertiesConfig.getAppKey();

        List<Organize> organizeList = new ArrayList<>();

        Organize organize = new Organize();
        organize.setOrganizeCode(bigArea);
        organize.setOrganizeName(bigArea);
        organize.setParentCode("*");
        organize.setCorpCode(corpCode);

        Organize organize2 = new Organize();
        organize2.setOrganizeCode(bigArea + provice);
        organize2.setOrganizeName(provice);
        organize2.setParentCode(bigArea);
        organize2.setCorpCode(corpCode);

        Organize organize3 = new Organize();
        organize3.setOrganizeCode(bigArea + provice + city);
        organize3.setOrganizeName(city);
        organize3.setParentCode(bigArea + provice);
        organize3.setCorpCode(corpCode);

        Organize organize4 = new Organize();
        organize4.setOrganizeCode(siteName);
        organize4.setOrganizeName(siteName);
        organize4.setParentCode(bigArea + provice + city);
        organize4.setCorpCode(corpCode);

        organizeList.add(organize);
        organizeList.add(organize2);
        organizeList.add(organize3);
        organizeList.add(organize4);

        Map<String, String> map = new HashMap<>();
        map.put("organizes", JSONUtil.toJsonStr(organizeList));
        map.put("sign_", sigin);
        map.put("appKey_", appKey);
        map.put("timestamp_", System.currentTimeMillis() + "");
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        OkHttpUtils.postMapSync(
            requestURI,
            map,
            new OkHttpUtils.OnOkHttpCallback() {

                @SneakyThrows
                @Override
                public void onFailure(IOException e) {
                    //                throw new ServiceException("同步组织失败");
                }

                @SneakyThrows
                @Override
                public void onFailure(String msg) {
                    //                throw new ServiceException("同步组织失败");
                }

                @SneakyThrows
                @Override
                public void onSuccessful(String json) {
                    try {
                        TrainResponse trainResponse = JSONUtil.toBean(json, TrainResponse.class);
                        if (StrUtil.equals(trainResponse.getStatus(), "OK")
                            && ObjectUtil.equals(trainResponse.getSuccess(), Boolean.TRUE)
                            && NumberUtil.equals(trainResponse.getSuccessCount(), 4)) {
                            log.info("同步组织完成");
                        } else {
                            log.error("同步组织失败信息:{}", json);
                            //                    throw new ServiceException("同步组织失败");
                        }
                    } catch (Exception e) {
//                        throw new RuntimeException(e); 屏蔽不然会影响后台二次加入
                        log.error("同步组织失败信息", e);
                    }

                }
            });
    }

    @Override
    public PageTableDataInfo<LeaveEmployeeVo> getEmployeeByTimeAndSite(CxrEmployeeBo bo) {
        List<CxrEmployeePost> getFinancial = cxrEmployeePostMapper.selectList(
            new LambdaQueryWrapper<CxrEmployeePost>()
                .eq(CxrEmployeePost::getCxrEmployeeId, StaffLoginHelper.getLoginUser().getUserId())
                .eq(CxrEmployeePost::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .eq(CxrEmployeePost::getChargeStatus, ChargeStatus.IN_PROGRESS.getValue())
                .apply("json_contains(cxr_post_id,json_object('value',{0}))", PostType.FINANCIAL.getValue())
        );

        List<Long> siteIds = getFinancial.stream().map(CxrEmployeePost::getCxrSiteId)
            .collect(Collectors.toList());

        YearMonth currentMonth = YearMonth.now();
        YearMonth previousMonth = currentMonth.minusMonths(2);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        String previousMonthFormatted = previousMonth.format(formatter);

        List<LeaveEmployeeVo> getLeavePeoples = baseMapper.getLeavePeoples(siteIds, previousMonthFormatted);
        List<Long> list = new ArrayList<>();
        LocalDate now = LocalDate.now();
        for (LeaveEmployeeVo record : getLeavePeoples) {
            LocalDate currentDate = record.getQuitTime();
            LocalDate lastDayOfMonth = currentDate.withDayOfMonth(currentDate.lengthOfMonth());
            LocalDate newDate = lastDayOfMonth.plusDays(45);
            record.setSeeTime(newDate);
            record.setQuitTimeEnd(lastDayOfMonth);
            if (record.getSeeTime().isAfter(now)) {
                list.add(record.getId());
            }
        }

        IPage<LeaveEmployeeVo> getEmployeeByYang = baseMapper
            .getEmployeeByYang(list, siteIds, bo.build(), previousMonthFormatted, bo);

        return PageTableDataInfo.build(getEmployeeByYang);
    }

    @Override
    public List<CxrEmployee> getEmployeeIds(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return baseMapper.selectList(
            new LambdaQueryWrapper<CxrEmployee>()
                .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .in(CxrEmployee::getId, ids));
    }

    @Override
    public List<CxrEmployee> getCxrSites(List<Long> ids) {
        return baseMapper.getCxrSites(ids);
    }

    @Override
    public PageTableDataInfo<CxrEmployeeListVo> staffPageEmployeePromote(
        CxrEmployeeBo cxrEmployeeBo, PageQuery pageQuery) {
        // 准备空数据
        PageTableDataInfo<CxrEmployeeListVo> info = new PageTableDataInfo<>();
        List<CxrEmployeeListVo> nullList = new ArrayList<>();
        info.setRows(nullList);

        // 公司id
        Long dept = StaffLoginHelper.getLoginUser().getDeptId();

        // 经理    josn条件     ex部分匹配
        List<String> list = new ArrayList<>();
        list.add(PostType.DIRECTOR.getValue());
        list.add(PostType.GENERATION_DIRECTOR.getValue());
        list.add(PostType.DEVELOPMENT_DIRECTOR.getValue());
        String value = JSONUtil.toJsonStr(list);

        // not deleted
        String notDeletedValue = DeleteStatus.NOT_DELETED.getValue();

        // 员工id
        List<Long> postEmployeeIds = null;
        List<Long> groupIds = null;
        // 站点id
        List<Long> siteIds = null;
        List<Long> regionSiteIds = null;
        List<Long> superSiteIds = null;

        // 职务
        if (StringUtils.isNotBlank(cxrEmployeeBo.getOneProxyType())) {
            // 组长
            if (cxrEmployeeBo.getOneProxyType().equals(PostType.GROUP_LEADER.getValue())) {
                groupIds = cxrGroupMapper.selectEmployeeId(null);

                if (CollectionUtil.isEmpty(groupIds)) {
                    return info;
                }
                groupIds.add(-1L);
            } else {
                // 有职务但不是组长

                List<PostEmployeeCountVo> employeeCountVos =
                    cxrEmployeePostMapper.selectPromoteEmployeeByPost(
                        cxrEmployeeBo.getOneProxyType(),
                        notDeletedValue,
                        cxrEmployeeBo.getOneProxyType().equals("80")
                            ? null
                            : ChargeStatus.IN_PROGRESS.getValue());
                if (employeeCountVos.size() > 0) {
                    postEmployeeIds =
                        employeeCountVos.stream()
                            .filter(a -> a.getCounts().equals(1))
                            .map(PostEmployeeCountVo::getCxrEmployeeId)
                            .collect(Collectors.toList());
                }
                // 如果进来了 然后查出来的是空的  直接返回
                if (CollectionUtil.isEmpty(postEmployeeIds)) {
                    return info;
                }
                postEmployeeIds.add(-1L);
            }
        }

        // 省/市/区/站点名称    模糊查询站点id
        if (StringUtils.isNotBlank(cxrEmployeeBo.getProvince())
            || StringUtils.isNotBlank(cxrEmployeeBo.getCity())
            || StringUtils.isNotBlank(cxrEmployeeBo.getArea())
            || StringUtils.isNotBlank(cxrEmployeeBo.getCxrSiteName())) {
            siteIds =
                cxrSiteMapper.selectLikeProviceCityArea(
                    cxrEmployeeBo.getProvince(),
                    cxrEmployeeBo.getArea(),
                    cxrEmployeeBo.getCity(),
                    cxrEmployeeBo.getCxrSiteName());
            if (CollectionUtil.isEmpty(siteIds)) {
                return info;
            }

            siteIds.add(-1L);
        }

        // 大区名字模糊查询
        if (StringUtils.isNotBlank(cxrEmployeeBo.getCxrRegionName())) {
            // 查询区域ids
            List<Long> regionIds =
                cxrRegionMapper.selectIdsByName(cxrEmployeeBo.getCxrRegionName(), notDeletedValue);
            if (CollectionUtil.isEmpty(regionIds)) {
                return info;
            }
            // 根据区域id查询站点
            regionSiteIds = cxrSiteMapper.selectByRegionId(regionIds, notDeletedValue);
            if (CollectionUtil.isEmpty(regionSiteIds)) {
                return info;
            }

            regionSiteIds.add(-1L);
        }

        // 根据主管名称 查站点
        if (StringUtils.isNotBlank(cxrEmployeeBo.getSupervisorName())) {
            // 根据名字查id
            List<Long> superIds =
                baseMapper.selectIdsByName(cxrEmployeeBo.getSupervisorName(), notDeletedValue);
            if (CollectionUtil.isEmpty(superIds)) {
                return info;
            }
            // 根据id查站点
            superSiteIds = cxrEmployeePostMapper.selectSiteIdByEmployeeIds(superIds, notDeletedValue);
            if (CollectionUtil.isEmpty(superSiteIds)) {
                return info;
            }
            superSiteIds.add(-1L);
        }
        List<Long> employeeIds = null;
        List<Long> theSiteIds = null;
        // 两个条件
        // 取交集
        if (CollectionUtil.isNotEmpty(postEmployeeIds) && CollectionUtil.isNotEmpty(groupIds)) {
            employeeIds = (List) CollectionUtil.intersection(postEmployeeIds, groupIds);
        } else if (CollectionUtil.isNotEmpty(postEmployeeIds)) {
            employeeIds = postEmployeeIds;
        } else if (CollectionUtil.isNotEmpty(groupIds)) {
            employeeIds = groupIds;
        }

        if (CollectionUtil.isNotEmpty(siteIds)
            && CollectionUtil.isNotEmpty(superSiteIds)
            && CollectionUtil.isNotEmpty(regionSiteIds)) {
            theSiteIds = (List) CollectionUtil.intersection(siteIds, superSiteIds, regionSiteIds);
        } else if (CollectionUtil.isNotEmpty(siteIds)) {
            theSiteIds = siteIds;
        } else if (CollectionUtil.isNotEmpty(superSiteIds)) {
            theSiteIds = superSiteIds;
        } else if (CollectionUtil.isNotEmpty(regionSiteIds)) {
            theSiteIds = regionSiteIds;
        }

        IPage<CxrEmployeeListVo> cxrEmployeeListVoIPage =
            baseMapper.staffPageEmployeePromote(
                cxrEmployeeBo,
                pageQuery.build(),
                OccupationStatus.WAIT_INDUCTION.getValue(),
                OccupationStatus.NO_INDUCTION.getValue(),
                dept,
                notDeletedValue,
                employeeIds,
                theSiteIds);

        if (CollectionUtil.isNotEmpty(cxrEmployeeListVoIPage.getRecords())) {
            List<SysDeptvo> sysDeptvos = remoteDeptService.selectAll();
            Map<Long, SysDeptvo> deptvoMap =
                sysDeptvos.stream()
                    .collect(Collectors.toMap(SysDeptvo::getDeptId, Function.identity(), (v1, v2) -> v2));

            FutureTaskWorker<CxrEmployeeListVo, Void> cxrEmployeeListVoVoidFutureTaskWorker =
                new FutureTaskWorker<>(
                    cxrEmployeeListVoIPage.getRecords(),
                    (CxrEmployeeListVo s) -> {
                        return CompletableFuture.runAsync(
                            () -> {
                                System.out.println("===============================================");
                                // 公司名称
                                SysDeptvo sysDeptvo = deptvoMap.get(s.getSysDeptId());
                                s.setCompanyName(sysDeptvo.getDeptName());
                                // 站点
                                CxrSite cxrSite = cxrSiteMapper.selectById(s.getCxrSiteId());
                                s.setProvince(cxrSite.getProvice());
                                s.setCity(cxrSite.getCity());
                                s.setArea(cxrSite.getArea());
                                s.setCxrSiteName(cxrSite.getName());
                                // 大区
                                CxrRegion cxrRegion =
                                    cxrRegionMapper.selectById(cxrSite.getCxrRootRegionId());
                                s.setCxrRegionName(
                                    ObjectUtil.isEmpty(cxrRegion)
                                        ? cxrSite.getCxrRootRegionName()
                                        : cxrRegion.getName());
                                // 原组长名称
                                if (null != s.getCxrGroupId()) {
                                    CxrGroup cxrGroup = cxrGroupMapper.selectById(s.getCxrGroupId());
                                    if (null != cxrGroup.getSourceCxrEmployeeId()) {
                                        CxrEmployee cxrEmployee =
                                            baseMapper.selectById(cxrGroup.getSourceCxrEmployeeId());
                                        if (ObjectUtil.isNotEmpty(cxrEmployee)) {
                                            s.setSourceGroupLeaderName(cxrEmployee.getName());
                                        }
                                    }
                                }
                                // 站点主管名称
                                s.setSiteDirectorNameAll(null);
                                List<Long> emIds =
                                    cxrEmployeePostMapper.selectBySiteId(
                                        s.getCxrSiteId(), value, ChargeStatus.IN_PROGRESS.getValue());
                                if (CollectionUtil.isNotEmpty(emIds)) {
                                    List<CxrEmployee> cxrEmployees =
                                        baseMapper.selectList(
                                            new LambdaQueryWrapper<CxrEmployee>()
                                                .in(CxrEmployee::getId, emIds)
                                                .eq(CxrEmployee::getDeleteStatus, notDeletedValue));
                                    if (CollectionUtil.isNotEmpty(cxrEmployees)) {
                                        cxrEmployees.forEach(
                                            i -> {
                                                s.setSiteDirectorNameAll(
                                                    Convert.toStr(s.getSiteDirectorNameAll(), "")
                                                        + i.getName()
                                                        + ";");
                                            });
                                    }
                                }
                                // 自己的职位
                                List<CxrEmployeePost> cxrEmployeePosts =
                                    cxrEmployeePostMapper.selectList(
                                        new LambdaQueryWrapper<CxrEmployeePost>()
                                            .eq(CxrEmployeePost::getCxrEmployeeId, s.getId())
                                            .eq(
                                                CxrEmployeePost::getChargeStatus,
                                                ChargeStatus.IN_PROGRESS.getValue()));
                                s.setPost("");
                                if (CollectionUtil.isNotEmpty(cxrEmployeePosts)) {
                                    cxrEmployeePosts.forEach(
                                        i -> {
                                            s.setPost(s.getPost() + i.getCxrPostId().getName() + ";");
                                        });
                                }
                            });
                    });

            cxrEmployeeListVoVoidFutureTaskWorker.getAllResult();

            if (ObjectUtil.isNotEmpty(cxrEmployeeBo.getOneProxyType())
                && cxrEmployeeBo.getOneProxyType().equals("80")) {
                List<CxrEmployeeListVo> employeeListVos =
                    cxrEmployeeListVoIPage.getRecords().stream()
                        .filter(
                            a ->
                                !a.getName()
                                    .equals(
                                        ObjectUtil.isEmpty(a.getGroupLeaderName())
                                            ? ""
                                            : a.getGroupLeaderName()))
                        .collect(Collectors.toList());
                cxrEmployeeListVoIPage.setRecords(employeeListVos);
                cxrEmployeeListVoIPage.setTotal(employeeListVos.size());
            }
        }

        PageTableDataInfo<CxrEmployeeListVo> cxrPageVo =
            PageTableDataInfo.build(cxrEmployeeListVoIPage);

        // 根据   用户id 查询  角色信息
        Long userId = StaffLoginHelper.getLoginUser().getUserId();
        List<SysRole> sysRoles = remoteRoleService.getByUserId(userId);
        List<String> roleNameList =
            sysRoles.stream()
                .map(
                    s -> {
                        return s.getRoleName();
                    })
                .collect(Collectors.toList());
        // 根据角色名称来进行
        return cxrPageVo;
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public void updateGroup(Long employeeId) {
        LambdaUpdateWrapper<CxrEmployee> updateWrapper = Wrappers.lambdaUpdate(CxrEmployee.class);

        CxrEmployee cxrEmployee = baseMapper.selectById(employeeId);

        if (ObjectUtil.isNotEmpty(cxrEmployee.getCxrGroupId())) { // 有小组
            CxrGroup cxrGroup = iCxrGroupService.getBaseMapper().selectById(cxrEmployee.getCxrGroupId());
            LambdaUpdateWrapper<CxrGroup> updateCxrGroupWrapper = Wrappers.lambdaUpdate(CxrGroup.class);
            //            -- 如果是组长把数据  now_cxr_employee_id 清空  换到 source_cxr_employee_id
            //            -- 如果是组员把数据 去组长哪里 member中找到自己清空
            if (ObjectUtil.isNotEmpty(cxrEmployee.getGroupLeaderId())
                && cxrEmployee.getGroupLeaderId().equals(employeeId)) { // 组长
                updateCxrGroupWrapper
                    .set(CxrGroup::getNowCxrEmployeeId, null)
                    .set(CxrGroup::getSourceCxrEmployeeId, cxrGroup.getNowCxrEmployeeId());
            } else { // 组员
                List<Long> member = cxrGroup.getMember();
                if (CollectionUtil.isNotEmpty(member)) {
                    if (member.size() > 0) {
                        List<Long> members = new ArrayList<>();
                        for (Long id : member) {
                            if (!id.equals(employeeId)) {
                                members.add(id);
                            }
                        }
                        if (members.size() > 0) {
                            updateCxrGroupWrapper.set(CxrGroup::getMember, JSONUtil.toJsonStr(members));
                        }
                    }
                }
            }
            updateCxrGroupWrapper.eq(CxrGroup::getId, cxrEmployee.getCxrGroupId());
            updateCxrGroupWrapper.set(CxrGroup::getUpdateTime, new Date());
            iCxrGroupService.update(null, updateCxrGroupWrapper);
        }
        updateWrapper
            .eq(CxrEmployee::getId, employeeId)
            .set(CxrEmployee::getCxrGroupId, null)
            .set(CxrEmployee::getGroupLeaderId, null)
            .set(CxrEmployee::getGroupLeaderName, null); // 必清理

        baseMapper.update(null, updateWrapper);
    }

    @Override
    public List<CxrEmployee> getByGroup(Long groupId) {
        if (ObjectUtil.isEmpty(groupId)) {
            return new ArrayList<>();
        }

        List<CxrEmployee> cxrEmployeeList =
            this.baseMapper.selectList(
                Wrappers.lambdaQuery(CxrEmployee.class).eq(CxrEmployee::getCxrGroupId, groupId));

        return cxrEmployeeList;
    }

    @Override
    public List<CxrEmployee> getGroupById(List<Long> ids) {
        return baseMapper.getGroupById(ids);
    }

    @Override
    public String getEmployeeName(Long siteId, String realName) {
        CxrSite cxrSite = iCxrSiteService.getById(siteId);
        if (ObjectUtil.isNull(cxrSite)) {
            throw new ServiceException("站点不存在,请重新填写");
        }

        String name = StrUtil.format("{}{}", cxrSite.getSiteAbbreviation(), realName);
        return name;
    }

    @Override
    public void sendSyncEmployssToTrainUpdate(Long cxrEmployeeId) {

        CxrEmployee cxrEmployee = getId(cxrEmployeeId);
        CxrSite cxrSite = cxrSiteMapper.selectById(cxrEmployee.getCxrSiteId());

        // 同步组织
        syncOrganizeVer(
            cxrSite.getCxrRootRegionName(), cxrSite.getProvice(), cxrSite.getCity(), cxrSite.getName());

        // 同步岗位
        String postName = PostType.COMMON_EMPLOYEE.getName();
        List<CxrEmployeePost> cxrEmployeePosts =
            iCxrEmployeePostService
                .getBaseMapper()
                .selectList(
                    Wrappers.lambdaQuery(CxrEmployeePost.class)
                        .eq(CxrEmployeePost::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                        .and(
                            a ->
                                a.eq(
                                        CxrEmployeePost::getChargeStatus,
                                        ChargeStatus.IN_PROGRESS.getValue())
                                    .or()
                                    .isNull(CxrEmployeePost::getInChargeEndTime))
                        .eq(CxrEmployeePost::getCxrEmployeeId, cxrEmployee.getId()));

        if (CollUtil.isNotEmpty(cxrEmployeePosts)) {

            List<CxrPostId> cxrPostIdList =
                cxrEmployeePosts.stream().map(CxrEmployeePost::getCxrPostId).collect(Collectors.toList());

            cxrPostIdList.sort(Comparator.comparing(CxrPostId::getValue).reversed());
            postName = cxrPostIdList.get(0).getName();
            if (postName.equals("普通职员")) {
                postName = "销售代理";
            }
            syncPositionsVer(postName);
        } else {
            syncPositionsVer(postName);
        }
        sendUpdateSyncEmployssToTrain(cxrEmployeeId);
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public boolean aduitEmployee(CxrEmployeeAuditDTO cxrEmployeeAuditDTO) {

        if (ObjectUtil.isNull(cxrEmployeeAuditDTO.getEmployeeId())) {
            throw new ServiceException("员工信息为空");
        }

        if (StrUtil.isBlank(cxrEmployeeAuditDTO.getResult())) {
            throw new ServiceException("审核结果为空");
        }

        LambdaUpdateWrapper<CxrEmployee> wrapper = Wrappers.lambdaUpdate(CxrEmployee.class);

        if (StrUtil.equals(cxrEmployeeAuditDTO.getResult(), "0")
            || StrUtil.equals(cxrEmployeeAuditDTO.getResult(), "1")) {
            if (StrUtil.equals(cxrEmployeeAuditDTO.getResult(), "0")) {
                wrapper.set(
                    CxrEmployee::getInductionApplyAuditStatus, InductionApplyAuditStatus.FAIL.getValue());
            }

            if (StrUtil.equals(cxrEmployeeAuditDTO.getResult(), "1")) {
                wrapper.set(
                    CxrEmployee::getInductionApplyAuditStatus,
                    InductionApplyAuditStatus.SUCCESS.getValue());
                wrapper.set(CxrEmployee::getOccupationStatus, OccupationStatus.INDUCTION.getValue());
            }
            wrapper.set(
                StrUtil.isNotBlank(cxrEmployeeAuditDTO.getRemark()),
                CxrEmployee::getInductionFailAuditReason,
                cxrEmployeeAuditDTO.getRemark());
        } else {
            throw new ServiceException("审核结果为空");
        }
        wrapper.eq(CxrEmployee::getId, cxrEmployeeAuditDTO.getEmployeeId());

        int rows = this.getBaseMapper().update(null, wrapper);
        boolean flag = SqlHelper.retBool(rows);
        CxrEmployee cxrEmployee = baseMapper.getId(cxrEmployeeAuditDTO.getEmployeeId());
        if (flag && StrUtil.equals(cxrEmployeeAuditDTO.getResult(), "1")) {

            saveOrUpdateCooperateHistory(cxrEmployee.getInductionTime(), cxrEmployee.getId(), null, null, 1);

            mqUtil.sendSyncMessage(
                EmployeeConstant.EMPLOYEE_TOPIC,
                EmployeeConstant.SYNC_EMPLOYEE_INFO_TAG,
                cxrEmployeeAuditDTO.getEmployeeId() + "");

            pushAddEmployee(cxrEmployee);
            log.info("查询打印数据{}", cxrEmployee);
            //招聘更新
            updateEmployeeRecruit(cxrEmployee);
        }

        if (StrUtil.equals(cxrEmployeeAuditDTO.getResult(), "1")) {
            this.updateGroupMemberByGroupId(cxrEmployeeAuditDTO.getEmployeeId());
            SynEmployeeBo bo = new SynEmployeeBo();
            bo.setEmployeeId(cxrEmployeeAuditDTO.getEmployeeId());
            bo.setJobNumber(cxrEmployee.getJobNumber());
            bo.setPhone(cxrEmployee.getPhone());
            bo.setRealName(cxrEmployee.getRealName());
            this.syncIntoMallUserEmployee(bo);
            saveRecord(baseMapper.getId(cxrEmployeeAuditDTO.getEmployeeId()), 1);
        }
        return flag;
    }

    @Override
    public void updateEmployeeRecruit(CxrEmployee cxrEmployee) {
        mqUtil.sendSyncMessage(EmployeeConstant.EMPLOYEE_TOPIC, EmployeeConstant.EMPLOYEE_RECRUIT_TAG,
            JSONUtil.toJsonStr(cxrEmployee));
    }

    private void pushAddEmployee(CxrEmployee employee) {
        Map<String, Object> data = new HashMap<>();
        data.put("erpEmployeeId", employee.getId());
        data.put("erpName", employee.getName());
        data.put("erpEmployeeJobNumber", employee.getJobNumber());
        data.put("erpPhone", employee.getPhone());
        data.put("erpWxUnionId", employee.getWxUnionid());
        data.put("erpWxOpenId", employee.getWxOpenid());
        data.put("erpWxHeadPortrait", employee.getWxHeadPortrait());
        data.put("erpWxNickname", employee.getWxNickname());
        data.put("erpGenderType", employee.getGenderType());
        data.put("mobile", employee.getPhone());

        HttpResponse execute = HttpRequest
            .post(appletConfig.getSendInduction())
            .body(JSONUtil.toJsonStr(data))
            .execute();
        log.info("打印发请求的数据{}", JSONUtil.toJsonStr(execute.body()));
    }

    @Override
    public List<CxrEmployee> getListRoomPeopele(Long siteId, String mouth) {
        return baseMapper.getListRoomPeopele(siteId, mouth);
    }

    @Override
    public CxrEmployee getListRoomPeopeleIds(Long employeeId, String timeStr) {
        return baseMapper.getListRoomPeopeleIds(employeeId, timeStr);
    }

    @Override
    public List<EmployeesVo> getEmployeeListId(String mouth, List<Long> ids) {
        return baseMapper.getEmployeeListId(mouth, ids);
    }

    @Override
    public List<EmployeesVo> getEmployeeListIds(
        String mouth, Date startTime, Date endTime, Long cxrSiteId) {
        return baseMapper.getEmployeeListIds(mouth, startTime, endTime, cxrSiteId);
    }

    @Override
    public boolean updateEmployeeNameBySiteSimpleName(
        Long siteId, String souceSiteSimpleName, String siteSimpleName) {
        List<CxrEmployee> employees =
            this.getBaseMapper()
                .selectList(
                    new LambdaQueryWrapper<CxrEmployee>()
                        .eq(CxrEmployee::getCxrSiteId, siteId)
                        .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                        .eq(CxrEmployee::getOccupationStatus, OccupationStatus.INDUCTION.getValue()));

        if (CollectionUtil.isEmpty(employees)) {
            return true;
        }

        employees.stream()
            .forEach(
                s -> {
                    s.setName(siteSimpleName + s.getRealName());
                });

        return baseMapper.updateNameByIdAndRevison(employees);
    }

    /**
     * @param cxrEmployeeName
     * @param siteIds           不能为null
     * @param cxrGroupId
     * @param employeeLevelType
     * @return
     */
    @Override
    public List<Long> queryDisEmployeePerformance(
        String cxrEmployeeName, List<Long> siteIds, Long cxrGroupId, String employeeLevelType) {

        return baseMapper.queryDisEmployeePerformanceEmployeeId(
            cxrEmployeeName, siteIds, cxrGroupId, employeeLevelType);
    }

    @Override
    public String getNameById(Long employeeId) {

        return baseMapper.getNameById(employeeId);
    }

    @Override
    public List<CxrEmployeeInfoDTO> getEmployeeByIds(List<Long> ids) {
        return baseMapper.getEmployeeByIds(ids);
    }

    @Override
    public boolean removeBanding(Long id) {
        boolean update = lambdaUpdate()
            .eq(CxrEmployee::getId, id)
            .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            .set(CxrEmployee::getWxOpenid, null)
            .set(CxrEmployee::getWxHeadPortrait, null)
            .set(CxrEmployee::getWxNickname, null)
            .set(CxrEmployee::getIsBindWx, SysYesNo.NO.getValue()).update();

        return update;
    }

    @Override
    public boolean exportExcel(CxrEmployeeBo cxrEmployeeBo) {

        LoginUser loginUser = LoginHelper.getLoginUser();
        // 公司id
        Long dept = loginUser.getDeptId();

        List<Long> qylSiteIds = loginUser.getSiteIds();
        if (CollUtil.isEmpty(qylSiteIds)) {
            qylSiteIds = new ArrayList<>();
        }

        // 根据   用户id 查询  角色信息
        List<String> roleKey = getRoleKeys(loginUser);

        boolean qyjlFlag = true;
        a:
        for (String s : roleKey) {
            if (s.equals("qyjl")) {
                qyjlFlag = false;
                if (CollectionUtil.isEmpty(qylSiteIds)) {
                    throw new ServiceException("区域经理没有管辖站点!");
                }
                break a;
            }
        }

        // 经理    josn条件     ex部分匹配
        List<String> list = new ArrayList<>();
        list.add(PostType.DIRECTOR.getValue());
        list.add(PostType.GENERATION_DIRECTOR.getValue());
        //        list.add(PostType.DEVELOPMENT_DIRECTOR.getValue());
        String value = JSONUtil.toJsonStr(list);

        // not deleted
        String notDeletedValue = DeleteStatus.NOT_DELETED.getValue();

        // 站点id
        List<Long> siteIds = null;
        List<Long> regionSiteIds = null;
        List<Long> superSiteIds = null;

        // 省/市/区/站点名称    模糊查询站点id
        if (StringUtils.isNotBlank(cxrEmployeeBo.getProvince())
            || StringUtils.isNotBlank(cxrEmployeeBo.getCity())
            || StringUtils.isNotBlank(cxrEmployeeBo.getArea())
            || StringUtils.isNotBlank(cxrEmployeeBo.getCxrSiteName())) {
            siteIds =
                cxrSiteMapper.selectLikeProviceCityArea(
                    cxrEmployeeBo.getProvince(),
                    cxrEmployeeBo.getArea(),
                    cxrEmployeeBo.getCity(),
                    cxrEmployeeBo.getCxrSiteName());
            if (CollectionUtil.isEmpty(siteIds)) {
                throw new ServiceException("没有数据可以导出!");
            }

            siteIds.add(-1L);
        }

        // 大区名字模糊查询
        if (StringUtils.isNotBlank(cxrEmployeeBo.getCxrRegionName())) {
            // 查询区域ids
            List<Long> regionIds =
                cxrRegionMapper.selectIdsByName(cxrEmployeeBo.getCxrRegionName(), notDeletedValue);
            if (CollectionUtil.isEmpty(regionIds)) {
                throw new ServiceException("没有数据可以导出!");
            }
            // 根据区域id查询站点
            regionSiteIds = cxrSiteMapper.selectByRegionId(regionIds, notDeletedValue);
            if (CollectionUtil.isEmpty(regionSiteIds)) {
                throw new ServiceException("没有数据可以导出!");
            }

            regionSiteIds.add(-1L);
        }

        // 大区名字模糊查询
        if (StringUtils.isNotBlank(cxrEmployeeBo.getBigRegionName())) {
            // 查询区域ids
            List<Long> bigRegionIds = remoteDeptService.selectByName(cxrEmployeeBo.getBigRegionName());
            if (CollectionUtil.isEmpty(bigRegionIds)) {
                throw new ServiceException("没有数据可以导出!");
            }
            // 根据大区ID查区域
            List<SysDept> sysRegionDepts = sysDeptMapper.selectList(new LambdaQueryWrapper<SysDept>()
                .in(SysDept::getParentId, bigRegionIds)
                .eq(SysDept::getDelFlag, DeleteStatus.NOT_DELETED.getValue())
            );
            if (CollectionUtil.isEmpty(sysRegionDepts)) {
                throw new ServiceException("没有数据可以导出!");
            }
            //根据区域查站点
            List<Long> bigRegionSiteIds = cxrSiteMapper.selectByRegionId(
                sysRegionDepts.stream().map(SysDept::getDeptId).collect(Collectors.toList()), notDeletedValue);
            if (CollectionUtil.isEmpty(regionSiteIds)) regionSiteIds = new ArrayList<>();
            regionSiteIds.addAll(bigRegionSiteIds);
            regionSiteIds.add(-1L);
        }

        // 根据主管名称 查站点
        if (StringUtils.isNotBlank(cxrEmployeeBo.getSupervisorName())) {
            // 根据名字查id
            List<Long> superIds =
                baseMapper.selectIdsByName(cxrEmployeeBo.getSupervisorName(), notDeletedValue);
            if (CollectionUtil.isEmpty(superIds)) {
                throw new ServiceException("没有数据可以导出!");
            }
            // 根据id查站点
            superSiteIds = cxrEmployeePostMapper.selectSiteIdByEmployeeIds(superIds, notDeletedValue);
            if (CollectionUtil.isEmpty(superSiteIds)) {
                throw new ServiceException("没有数据可以导出!");
            }
            superSiteIds.add(-1L);
        }

        List<Long> theSiteIds = null;
        if (CollectionUtil.isNotEmpty(siteIds)
            && CollectionUtil.isNotEmpty(superSiteIds)
            && CollectionUtil.isNotEmpty(regionSiteIds)) {
            theSiteIds = (List) CollectionUtil.intersection(siteIds, superSiteIds, regionSiteIds);
        } else if (CollectionUtil.isNotEmpty(siteIds)) {
            theSiteIds = siteIds;
        } else if (CollectionUtil.isNotEmpty(superSiteIds)) {
            theSiteIds = superSiteIds;
        } else if (CollectionUtil.isNotEmpty(regionSiteIds)) {
            theSiteIds = regionSiteIds;
        }

        // 员工id
        List<Long> postEmployeeIds = null;
        List<Long> groupIds = null;
        // 职务   站点不为空查询站点内的 职务员工id
        if (StringUtils.isNotBlank(cxrEmployeeBo.getOneProxyType())) {
            //站点id
            List<Long> ids = new ArrayList<>();
            ids = CollectionUtil.newCopyOnWriteArrayList(theSiteIds);

            //有区域的站点区域id
            List<Long> regionIds = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(siteIds)) {
                if (StringUtils.isBlank(cxrEmployeeBo.getCxrSiteName()) || cxrEmployeeBo.getOneProxyType()
                    .equals(PostType.DEVELOPMENT_MANAGER.getValue()) || cxrEmployeeBo.getOneProxyType()
                    .equals(PostType.REGION_MANAGER.getValue())) {
                    regionIds.addAll(siteIds);
                }
            }
            if (CollectionUtil.isNotEmpty(regionSiteIds)) {
                regionIds.addAll(regionSiteIds);
                regionIds = regionIds.stream().distinct().collect(Collectors.toList());
            }

            if (!qyjlFlag) {
                //区域经理
                //交集
                regionIds =
                    CollectionUtil.isNotEmpty(regionIds) ? CollectionUtil.unionAll(regionIds, qylSiteIds) : qylSiteIds;
                ids = CollectionUtil.isNotEmpty(ids) ? CollectionUtil.unionAll(ids, qylSiteIds) : qylSiteIds;
            }

            // 组长
            if (cxrEmployeeBo.getOneProxyType().equals(PostType.GROUP_LEADER.getValue())) {
                groupIds = cxrGroupMapper.selectEmployeeId(ids);

                if (CollectionUtil.isEmpty(groupIds)) {
                    throw new ServiceException("没有数据可以导出!");
                }
                groupIds.add(-1L);
            } else {
                // 有职务但不是组长
                postEmployeeIds =
                    cxrEmployeePostMapper.selectEmployeeByPost(
                        cxrEmployeeBo.getOneProxyType(),
                        notDeletedValue,
                        cxrEmployeeBo.getOneProxyType().equals("80")
                            ? null
                            : ChargeStatus.IN_PROGRESS.getValue()
                        , ids, regionIds
                    );
                // 如果进来了 然后查出来的是空的  直接返回
                if (CollectionUtil.isEmpty(postEmployeeIds)) {
                    throw new ServiceException("没有数据可以导出!");
                }
                postEmployeeIds.add(-1L);
            }
        }

        List<Long> employeeIds = null;
        // 两个条件
        // 取交集
        if (CollectionUtil.isNotEmpty(postEmployeeIds) && CollectionUtil.isNotEmpty(groupIds)) {
            employeeIds = (List) CollectionUtil.intersection(postEmployeeIds, groupIds);
        } else if (CollectionUtil.isNotEmpty(postEmployeeIds)) {
            employeeIds = postEmployeeIds;
        } else if (CollectionUtil.isNotEmpty(groupIds)) {
            employeeIds = groupIds;
        }
        Long total =
            baseMapper.countExcelExport(
                cxrEmployeeBo,
                OccupationStatus.WAIT_INDUCTION.getValue(),
                OccupationStatus.NO_INDUCTION.getValue(),
                dept,
                notDeletedValue,
                employeeIds,
                theSiteIds,
                OccupationStatus.QUIT_ONESELF.getValue(),
                OccupationStatus.QUIT_PROCEDURE.getValue(),
                OccupationStatus.INDUCTION.getValue(),
                qyjlFlag,
                qylSiteIds);
        log.info("exportExcel:total:{}", total.toString());

        if (total < 1) {
            throw new ServiceException("没有数据可以导出!");
        }
        log.info("exportExcel:SendRecord:页面:{},用户:{}", cxrEmployeeBo.getHtmlName(),
            loginUser.getUserId().toString());
        ExcelReportDTO excelReportDTO = ExcelMqUtil.excelSendRecord(cxrEmployeeBo.getHtmlName(), total,
            loginUser.getUserId(), loginUser.getUserType(), loginUser.getUserName());

        log.info("exportExcel开始导出");
        iCxrEmployeeService.exportExcelEmployee(cxrEmployeeBo, dept, qylSiteIds, roleKey, qyjlFlag, notDeletedValue,
            employeeIds,
            theSiteIds, total,
            excelReportDTO);
        return true;
    }

    @Async("scheduledExecutorService")
    public void exportExcelEmployee(CxrEmployeeBo cxrEmployeeBo, Long dept, List<Long> qylSiteIds,
                                    List<String> roleKey,
                                    boolean qyjlFlag, String notDeletedValue, List<Long> employeeIds, List<Long> theSiteIds, Long total,
                                    ExcelReportDTO excelReportDTO) {
        try {
            // 经理    josn条件     ex部分匹配
            List<String> list = new ArrayList<>();
            list.add(PostType.DIRECTOR.getValue());
            list.add(PostType.GENERATION_DIRECTOR.getValue());
            //        list.add(PostType.DEVELOPMENT_DIRECTOR.getValue());
            String value = JSONUtil.toJsonStr(list);

            // 判断是否是    人事,it,总经办,财务部    是的话 flag =false
            boolean flag = true;
            a:
            for (String s : roleKey) {
                List<String> key = roleKeyAuthority.getRoleKeys();
                if (CollectionUtil.isNotEmpty(key)) {
                    boolean contains = key.contains(s);
                    if (contains) {
                        flag = false;
                        break a;
                    }
                }
            }

            Long limit = 1000L;
            Long totalLimit = 0L;
            Long lastId = 0L;

            long fileName = IdWorker.getId();
            ExcelExportObj excelExportObj = new ExcelExportObj(excelReportDTO.getFileDir(),
                fileName + ExcelTypeEnum.XLSX.getValue(), "销售代理", CxrEmployeeListVo.class);

            while (true) {
                List<CxrEmployeeListVo> records = baseMapper.excelExportPage(
                    cxrEmployeeBo,
                    OccupationStatus.WAIT_INDUCTION.getValue(),
                    OccupationStatus.NO_INDUCTION.getValue(),
                    dept,
                    notDeletedValue,
                    employeeIds,
                    theSiteIds,
                    OccupationStatus.QUIT_ONESELF.getValue(),
                    OccupationStatus.QUIT_PROCEDURE.getValue(),
                    OccupationStatus.INDUCTION.getValue(),
                    qyjlFlag,
                    qylSiteIds, lastId, limit);
                assignment(records, value);

                totalLimit += records.size();
                // false  不会进入
                // 根据角色名称来进行  设置数据加密
                if (flag) {
                    records.stream()
                        .forEach(
                            vo -> {
                                vo.setIdCard("****");
                                vo.setIdCardBackPictureUrl("****");
                                vo.setIdCardPositivePictureUrl("****");
                                vo.setDepositBankCardIdCard("****");
                                vo.setDepositBankCardName("****");
                                vo.setDepositBankCardNumber("****");
                                vo.setDepositBankCardPhone("****");
                                vo.setFamilyDepositBankCardPhone("****");
                                vo.setFamilyDepositBankCardIdCard("****");
                                vo.setFamilyDepositBankCardNumber("****");
                                vo.setSalesRepresentativeRegisterFileUrl("****");
                                vo.setGradeProxyAgreementFileUrl("****");
                                vo.setProxyContractFileUrl("****");
                            });
                }

                excelExportObj.writeManySheet(records);

                ExcelMqUtil.excelSendIncrementData(excelReportDTO.getUniqueMark(), Convert.toLong(records.size()),
                    null);

                if (totalLimit >= total) {
                    break;
                }
                lastId = records.get(records.size() - 1).getId();
            }
            excelExportObj.pathClose();
        } catch (Exception e) {
            log.error("message>{},exceptionDetail>{}", e.getMessage(), Arrays.toString(e.getStackTrace()));
            ExcelMqUtil.excelSendIncrementData(excelReportDTO.getUniqueMark(), 0L, e);
        }
    }

    @NotNull
    private List<String> getRoleKeys(LoginUser loginUser) {
        // 根据   用户id 查询  角色信息
        Long userId = loginUser.getUserId();
        List<SysRole> sysRoles = remoteRoleService.getByUserId(userId);
        return sysRoles.stream()
            .map(
                s -> {
                    return s.getRoleKey();
                })
            .collect(Collectors.toList());
    }

    @Override
    public List<EmployeesVo> queryLeaves(String mouth, Long cxrSiteId) {
        return baseMapper.queryLeaves(mouth, cxrSiteId);
    }

    /**
     * 恢复在职
     *
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean employeeRestore(Long id, boolean flag) {
        CxrEmployee employee = iCxrEmployeeService
            .lambdaQuery()
            .eq(CxrEmployee::getId, id)
            .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            .select(CxrEmployee::getOccupationStatus,
                CxrEmployee::getQuitTime,
                CxrEmployee::getHistoryDate,
                CxrEmployee::getCxrSiteId,
                CxrEmployee::getId,
                CxrEmployee::getPhone,
                CxrEmployee::getJobNumber,
                CxrEmployee::getRealName
            )
            .one();

        //离职时间
        Date quitTime = employee.getQuitTime();
        LocalDate quitLocalDate = DateUtils.getLocalDateFromDate(quitTime);
        //校验
        checkEmployeeAboutRestore(employee, flag);

        saveOrUpdateCooperateHistory(null, employee.getId(), null, quitTime, 3);
        //更新数据
        updateChangeData(id, employee, quitLocalDate);

        //5.解冻培训系统
        mqUtil.sendSyncMessage(EmployeeConstant.EMPLOYEE_TOPIC, EmployeeConstant.UPDATE_SYNC_EMPLOYEE_INFO_TAG,
            id.toString());
        SynEmployeeBo bo = new SynEmployeeBo();
        bo.setEmployeeId(employee.getId());
        bo.setJobNumber(employee.getJobNumber());
        bo.setPhone(employee.getPhone());
        bo.setRealName(employee.getRealName());
        this.syncIntoMallUserEmployee(bo);
        return true;
    }

    private void updateChangeData(Long id, CxrEmployee employee, LocalDate quitLocalDate) {
        //1.删除人员离职记录
        boolean updateCooperate = new LambdaUpdateChainWrapper<>(cxrEmployeeCooperateRecordMapper)
            .eq(CxrEmployeeCooperateRecord::getCxrEmployeeId, id)
            .eq(CxrEmployeeCooperateRecord::getBsDate, quitLocalDate)
            .eq(CxrEmployeeCooperateRecord::getBsType, 2)
            .set(CxrEmployeeCooperateRecord::getDeleteStatus, DeleteStatus.DELETED.getValue())
            .update();

        if (!updateCooperate) {
            log.error(StrUtil.format("恢复在职删除离职记录失败    姓名=>:{},离职日期=>:{}", employee.getName(),
                quitLocalDate));
        }

        //2.恢复小组
        //2.1 人员变动记录修复
        CxrEmployeeChangeRecord changeRecord = iCxrEmployeeChangeRecordService
            .lambdaQuery()
            .select(CxrEmployeeChangeRecord::getId
                , CxrEmployeeChangeRecord::getGroupId
                , CxrEmployeeChangeRecord::getSiteId)
            .eq(CxrEmployeeChangeRecord::getCxrEmployeeId, id)
            .eq(CxrEmployeeChangeRecord::getType, EmployeeChangeType.GROUP)
            .apply("date_format(end_time,'%Y-%m-%d') ={0}", quitLocalDate)
            .orderByDesc(CxrEmployeeChangeRecord::getStartTime)
            .last("limit 1")
            .one();

        boolean b = false;
        if (ObjectUtil.isNotEmpty(changeRecord)) {
            if (changeRecord.getSiteId().equals(employee.getCxrSiteId())) {
                b = true;
            }
        }

        //2.2 恢复人员小组
        if (b) {
            CxrGroup cxrGroup = iCxrGroupService.lambdaQuery()
                .eq(CxrGroup::getId, changeRecord.getGroupId())
                .eq(CxrGroup::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .one();
            if (ObjectUtil.isNotEmpty(cxrGroup)) {
                List<Long> member = ObjectUtil.isNotEmpty(cxrGroup.getMember()) ?
                    cxrGroup.getMember() : new ArrayList<Long>();
                member.add(id);
                //人员变动记录修复
                boolean changeRecordUpdate = iCxrEmployeeChangeRecordService
                    .lambdaUpdate()
                    .eq(CxrEmployeeChangeRecord::getId, changeRecord.getId())
                    .set(CxrEmployeeChangeRecord::getEndTime, null)
                    .update();
                //恢复人员小组
                boolean groupUpdate = iCxrGroupService
                    .lambdaUpdate()
                    .eq(CxrGroup::getId, changeRecord.getGroupId())
                    .eq(CxrGroup::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                    .set(CxrGroup::getMember, JSONUtil.toJsonStr(member))
                    .update();

                if (!changeRecordUpdate || !groupUpdate) {
                    throw new ServiceException("网络波动,请刷新重试!");
                }
            } else {
                b = false;
            }
        }

        //2.3 小组历史恢复 TODO 先不管

        //3.修改销售代理状态 并且将历史时间得最后一次离职时间 清空
        List<EmployeeHistoryDate> historyDate = employee.getHistoryDate();
        EmployeeHistoryDate employeeHistoryDate = historyDate.get(historyDate.size() - 1);
        employeeHistoryDate.setEndDate(null);
        employeeHistoryDate.setAuditNmae(null);
        employeeHistoryDate.setAuditId(null);

        boolean update = iCxrEmployeeService
            .lambdaUpdate()
            .eq(CxrEmployee::getId, id)
            .set(CxrEmployee::getOccupationStatus, OccupationStatus.INDUCTION.getValue())
            .set(CxrEmployee::getHistoryDate, JSONUtil.toJsonStr(historyDate))
            .set(CxrEmployee::getQuitTime, null)
            .set(CxrEmployee::getCxrGroupId, !b ? null : changeRecord.getGroupId())
//            .set(b, CxrEmployee::getCxrGroupId, changeRecord.getGroupId())
            .update();

        if (!update) {
            throw new ServiceException("网络波动,请刷新重试!");
        }

        //4.借支状态恢复
        boolean borrowingUpdate = new LambdaUpdateChainWrapper<>(cxrEmployeeBorrowingMapper)
            .eq(CxrEmployeeBorrowing::getEmployeeId, id)
            .ge(CxrEmployeeBorrowing::getApplyDate, quitLocalDate)
            .eq(CxrEmployeeBorrowing::getCooperateStatus, OccupationStatus.INDUCTION.getValue())
            .set(CxrEmployeeBorrowing::getQuitTime, null)
            .set(CxrEmployeeBorrowing::getCooperateStatus, OccupationStatus.WAIT_INDUCTION.getValue())
            .update();
//        if (!borrowingUpdate) {
//            throw new ServiceException("网络波动,请刷新重试!");
//        }
    }

    private void checkEmployeeAboutRestore(CxrEmployee employee, boolean flag) {
        //校验状态
        List<String> occupationStatuss = new ArrayList<>();
        occupationStatuss.add(OccupationStatus.WAIT_INDUCTION.getValue());
        occupationStatuss.add(OccupationStatus.INDUCTION.getValue());
        occupationStatuss.add(OccupationStatus.NO_INDUCTION.getValue());
        if (occupationStatuss.contains(employee.getOccupationStatus())) {
            throw new ServiceException("请检查销售代理是否在职");
        }

        if (flag) {
            //校验离职时间
            Date quitTime = employee.getQuitTime();
            LocalDate quitLocalDate = ObjectUtil.isNotEmpty(quitTime) ?
                DateUtils.getLocalDateFromDate(quitTime) : LocalDate.MAX;
            LocalDate plusDay = quitLocalDate.plusDays(5);
            if (plusDay.compareTo(LocalDate.now()) < 0) {
                throw new ServiceException("离职时间太长不能恢复在职");
            }
        }

    }


    @Override
    public boolean updateOccStatus(Long id, String status) {

        boolean b = checkActiveOrNot(id);

        if (b) {
            throw new ServiceException("销售代理不是离职状态,无法修改其离职类型!");
        }

        return lambdaUpdate()
            .eq(CxrEmployee::getId, id)
            .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            .set(CxrEmployee::getOccupationStatus, status)
            .update();
    }

    @Override
    public void sendUpdateSyncEmployssLevelToTrain(Long employeeId) {
        sendUpdateSyncEmployssToTrain(employeeId);
    }

    @Override
    public void syncMallUserEmployee(Long id) {
        log.info("appletConfig.syncMallUserEmployee 请求数据{}", id);
        String body = HttpUtil.get(appletConfig.getSyncMallUserEmployee() + "employeeId=" + id);
        log.info("appletConfig.syncMallUserEmployee 放回数据{}", body);
    }

    @Override
    public void syncIntoMallUserEmployee(SynEmployeeBo bo) {
        Map<String, Object> data = new HashMap<>();
        data.put("employeeId", bo.getEmployeeId());
        data.put("jobNumber", bo.getJobNumber());
        data.put("phone", bo.getPhone());
        data.put("realName", bo.getRealName());
        HttpResponse execute = HttpRequest
            .post(appletConfig.getSyncIntoMallUserEmployee())
            .body(JSONUtil.toJsonStr(data))
            .execute();
        log.info("打印发请求的数据{}", JSONUtil.toJsonStr(execute.body()));
    }

    /**
     * 校验是否在职
     *
     * @param id 销售代理  id
     * @return 在职  true   离职 false    不在职不离职  serviceException
     */
    private boolean checkActiveOrNot(Long id) {
        CxrEmployee employee = lambdaQuery()
            .eq(CxrEmployee::getId, id)
            .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            .select(CxrEmployee::getOccupationStatus)
            .one();

        if (ObjectUtil.isEmpty(employee)) {
            throw new ServiceException("查不到销售代理!");
        }
        String status = employee.getOccupationStatus();
        if (status.equals(OccupationStatus.NO_INDUCTION.getValue())
            || status.equals(OccupationStatus.WAIT_INDUCTION.getValue())
            || status.equals(OccupationStatus.PEND_ONESELF.getValue())
        ) {
            throw new ServiceException("销售代理还未入职/其他!");
        }

        if (status.equals(OccupationStatus.INDUCTION.getValue())) {
            return true;
        }
        return false;
    }

    /**
     * 修改当前的保证金，入参是修改后的金额
     *
     * @param sourceId
     * @param employeeId
     * @param afterAmount
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BigDecimal detainEarnestMoney(Long sourceId, Long employeeId, BigDecimal afterAmount) {

        AbstractAssert.isTrue(afterAmount.compareTo(BigDecimal.ZERO) <= 0, "保证金金额必须大于0");

        CxrEmployee employee = baseMapper.selectById(employeeId);
        BigDecimal earnestMoney = employee.getEarnestMoney();
        BigDecimal amount = afterAmount.subtract(earnestMoney);

        CxrEarnestMoneyRecord cxrEarnestMoneyRecord = new CxrEarnestMoneyRecord();
        cxrEarnestMoneyRecord.setEmployeeId(employeeId);
        cxrEarnestMoneyRecord.setSourceId(sourceId);
        cxrEarnestMoneyRecord.setType(1);
        cxrEarnestMoneyRecord.setBeforeEarnestMoney(earnestMoney);
        cxrEarnestMoneyRecord.setAmount(amount);
        BigDecimal afterEarnestMoney = NumberUtil.add(earnestMoney, amount);
        cxrEarnestMoneyRecord.setAfterEarnestMoney(afterEarnestMoney);
        cxrEarnestMoneyRecord.setCreateTime(LocalDateTime.now());

        Long revision = employee.getRevision();
        boolean update = this.lambdaUpdate()
            .set(CxrEmployee::getEarnestMoney, afterEarnestMoney)
            .set(CxrEmployee::getRevision, revision + 1)
            .eq(CxrEmployee::getRevision, revision)
            .eq(CxrEmployee::getEarnestMoney, earnestMoney)
            .eq(CxrEmployee::getId, employeeId).update();

        AbstractAssert.isTrue(!update, "更新失败!");
        AbstractAssert.isTrue(cxrEarnestMoneyRecordMapper.insert(cxrEarnestMoneyRecord) <= 0, "保存失败!");
        return afterEarnestMoney;
    }

    public BigDecimal queryEarnestMoney(Long employeeId, LocalDateTime dateTime) {
        CxrEarnestMoneyRecord cxrEarnestMoneyRecord = cxrEarnestMoneyRecordMapper.selectOne(
            new LambdaQueryWrapper<CxrEarnestMoneyRecord>()
                .eq(CxrEarnestMoneyRecord::getEmployeeId, employeeId)
                .lt(CxrEarnestMoneyRecord::getCreateTime, dateTime)
                .orderByDesc(CxrEarnestMoneyRecord::getCreateTime)
                .last("LIMIT 1"));
        if (cxrEarnestMoneyRecord != null) {
            return cxrEarnestMoneyRecord.getAfterEarnestMoney();
        }
        return null;
    }

    public BigDecimal queryEarnestMoney(Long sourceId) {
        List<CxrEarnestMoneyRecord> cxrEarnestMoneyRecords = cxrEarnestMoneyRecordMapper.selectList(
            new LambdaQueryWrapper<CxrEarnestMoneyRecord>()
                .eq(CxrEarnestMoneyRecord::getSourceId, sourceId));
        if (cxrEarnestMoneyRecords != null) {
            return cxrEarnestMoneyRecords.stream().map(CxrEarnestMoneyRecord::getAmount).reduce(BigDecimal::add).get();
        }
        return BigDecimal.ZERO;
    }

    @Override
    public void initCooperateHistory() {


       /* List<CxrEmployee> list = lambdaQuery().select(CxrEmployee::getId).list();
        list.forEach(v -> {

            List<CxrEmployeeCooperateRecord> cxrEmployeeCooperateRecords = cxrEmployeeCooperateRecordMapper.
                selectList(new LambdaQueryWrapper<CxrEmployeeCooperateRecord>().eq(CxrEmployeeCooperateRecord::getCxrEmployeeId,v.getId())
                    .orderByAsc(CxrEmployeeCooperateRecord::getCreateTime));


            if (CollectionUtil.isNotEmpty(cxrEmployeeCooperateRecords)){

                int a = 1;
                CxrEmployeeCooperateHistory cxrEmployeeCooperateHistory = null;
                List<CxrEmployeeCooperateHistory> histories = new ArrayList<>();
                for (CxrEmployeeCooperateRecord cxrEmployeeCooperateRecord : cxrEmployeeCooperateRecords) {
                    if (a == 1) {

                        if (cxrEmployeeCooperateRecord.getBsType() != 1){
                            continue;
                        }

                        cxrEmployeeCooperateHistory = new CxrEmployeeCooperateHistory();
                        cxrEmployeeCooperateHistory.setEmployeeId(cxrEmployeeCooperateRecord.getCxrEmployeeId());
                        cxrEmployeeCooperateHistory.setStartDate(cxrEmployeeCooperateRecord.getBsDate());
                        cxrEmployeeCooperateHistory.setFrequency(1);

                        a = 2;
                    }else if (a == 2){
                        if (cxrEmployeeCooperateRecord.getBsType() != 2){
                            continue;
                        }
                        cxrEmployeeCooperateHistory.setEndDate(cxrEmployeeCooperateRecord.getBsDate());

                        a = 1;
                        histories.add(cxrEmployeeCooperateHistory);
                    }
                }

                cxrEmployeeCooperateHistoryMapper.insertBatch(histories);
            }
        });*/

        List<CxrEmployee> list = lambdaQuery().select(CxrEmployee::getId, CxrEmployee::getOccupationStatus,
            CxrEmployee::getHistoryDate).list();
        CompletableFuture.allOf(CollUtil.split(list, 500).stream().map(v -> CompletableFuture.runAsync(() -> {
            for (CxrEmployee employee : v) {

                try {

                    List<EmployeeHistoryDate> historyDate = employee.getHistoryDate();
                    List<CxrEmployeeCooperateHistory> histories = new ArrayList<>();
                    if (CollectionUtil.isNotEmpty(historyDate)) {

                        for (int i = 0; i < historyDate.size(); i++) {

                            EmployeeHistoryDate employeeHistoryDate = historyDate.get(i);
                            CxrEmployeeCooperateHistory cxrEmployeeCooperateHistory = new CxrEmployeeCooperateHistory();
                            cxrEmployeeCooperateHistory.setEmployeeId(employee.getId());
                            cxrEmployeeCooperateHistory.setOccupation(employeeHistoryDate.getOccupation());
                            cxrEmployeeCooperateHistory.setFrequency(i + 1);
                            cxrEmployeeCooperateHistory.setStartDate(
                                DateUtils.getLocalDateFromDate(employeeHistoryDate.getStartDate()));
                            cxrEmployeeCooperateHistory.setCreateTime(new Date());
                            Date endDate = employeeHistoryDate.getEndDate();
                            if (endDate != null) {
                                cxrEmployeeCooperateHistory.setEndDate(DateUtils.getLocalDateFromDate(endDate));
                            }
                            histories.add(cxrEmployeeCooperateHistory);
                        }
                    } else {
                        CxrEmployeeCooperateHistory cxrEmployeeCooperateHistory = new CxrEmployeeCooperateHistory();
                        cxrEmployeeCooperateHistory.setEmployeeId(employee.getId());
                        cxrEmployeeCooperateHistory.setOccupation(employee.getOccupationStatus());
                        cxrEmployeeCooperateHistory.setFrequency(1);
                        cxrEmployeeCooperateHistory.setStartDate(
                            DateUtils.getLocalDateFromDate(employee.getInductionTime()));
                        cxrEmployeeCooperateHistory.setCreateTime(new Date());
                        Date endDate = employee.getQuitTime();
                        if (endDate != null) {
                            cxrEmployeeCooperateHistory.setEndDate(DateUtils.getLocalDateFromDate(endDate));
                        }
                        histories.add(cxrEmployeeCooperateHistory);
                    }
                    cxrEmployeeCooperateHistoryMapper.insertBatch(histories);
                } catch (Exception e) {
                    log.error("initCooperateHistory save CxrEmployeeCooperateHistory 异常：{}", e.getMessage());
                }
            }
        })).toArray(CompletableFuture[]::new)).join();

    }
}

