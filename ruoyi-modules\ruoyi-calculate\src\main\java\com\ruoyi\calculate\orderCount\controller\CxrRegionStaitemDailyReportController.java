package com.ruoyi.calculate.orderCount.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.calculate.api.domain.enums.RegionStatisticsDimensionType;
import com.ruoyi.calculate.orderCount.domain.excel.*;
import com.ruoyi.calculate.orderCount.service.CxrRegionStaitemDailyReportService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.auto.excel.build.ExportClassBuilder;
import com.ruoyi.common.auto.excel.domain.bo.ExcelAutoQuery;
import com.ruoyi.common.auto.excel.utils.ExcelAutoHeadUtil;
import com.ruoyi.core.base.domain.bo.CxrRegionStaitemDailyReportBo;
import com.ruoyi.core.base.domain.vo.CxrRegionStaitemDailyReportVo;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 区域单数统计明细Controller
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@Api(value = "区域单数统计明细", tags = {"区域单数统计明细"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/cxrRegionStaitemDailyReport")
public class CxrRegionStaitemDailyReportController extends BaseController {

    private final CxrRegionStaitemDailyReportService reportService;
    private final ExcelAutoHeadUtil excelAutoHeadUtil;

    /**
     * 分页查询区域单数统计明细
     *
     * @param bo 查询参数
     * @return 分页结果
     */
    @SaCheckPermission("calculate:cxrRegionStaitemDailyReport:page")
    @ApiOperation("后台-分页查询《区域单数统计明细》")
    @PostMapping("/page")
    public R<Page<CxrRegionStaitemDailyReportVo>> page(@Validated @RequestBody CxrRegionStaitemDailyReportBo bo) {
        return R.ok(reportService.page(bo, bo.build()));
    }

    /**
     * 合计查询区域单数统计明细
     *
     * @param bo 查询参数
     * @return 合计结果
     */
    @SaCheckPermission("calculate:cxrRegionStaitemDailyReport:page")
    @ApiOperation("后台-合计《区域单数统计明细》")
    @PostMapping("/sum")
    public R<CxrRegionStaitemDailyReportVo> sum(@Validated @RequestBody CxrRegionStaitemDailyReportBo bo) {
        return R.ok(reportService.sum(bo));
    }

    /**
     * 导出区域单数统计明细
     *
     * @param bo 查询参数
     * @param query Excel导出参数
     * @return 导出结果
     */
    @SaCheckPermission("calculate:cxrRegionStaitemDailyReport:export")
    @ApiOperation("后台-导出《区域单数统计明细》")
    @PostMapping("/export")
    public R<CxrRegionStaitemDailyReportVo> export(@Validated @RequestBody CxrRegionStaitemDailyReportBo bo, ExcelAutoQuery query) {
        excelAutoHeadUtil.writeWithPageT(query,
            bo.getDimensionType(),
            ExportClassBuilder.create()
                .add(RegionStatisticsDimensionType.COMPANY.getCode(), CxrRegionStaitemDailyReportCompanyExcel.class)
                .add(RegionStatisticsDimensionType.ROOT_REGION.getCode(), CxrRegionStaitemDailyReportRootRegionExcel.class)
                .add(RegionStatisticsDimensionType.REGION.getCode(), CxrRegionStaitemDailyReportRegionExcel.class)
                .add(RegionStatisticsDimensionType.SITE.getCode(), CxrRegionStaitemDailyReportSiteExcel.class)
                .add(RegionStatisticsDimensionType.AGENT.getCode(), CxrRegionStaitemDailyReportAgentExcel.class)
                .build(),
            (page) -> {
                return reportService.page(bo, page);
            }
        );
        return R.ok();
    }
}
