package com.ruoyi.business.base.customerAddress.controller;

import com.ruoyi.business.base.api.domain.DeliverySiteDTO;
import com.ruoyi.business.base.api.domain.bo.CxrCustomerAddressBo;
import com.ruoyi.business.base.customerAddress.domain.bo.AddressBo;
import com.ruoyi.business.base.customerAddress.domain.bo.CxrCustomerAddressQueryBo;
import com.ruoyi.business.base.customerAddress.domain.vo.CustomerAddressDetailVo;
import com.ruoyi.business.base.customerAddress.domain.vo.CxrCustomerAddressQueryVo;
import com.ruoyi.business.base.customerAddress.domain.vo.CxrCustomerAddressVo;
import com.ruoyi.business.base.customerAddress.service.ICxrCustomerAddressService;
import com.ruoyi.business.base.dataImport.service.UpdateDistributionInfoService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.mybatis.core.page.PageQuery;
import com.ruoyi.common.mybatis.core.page.PageTableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@Validated
@Api(value = "后台路条转拨管理", tags = {"后台路条转拨管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/cxrCustomerAddress")
public class CxrCustomerAddressController extends BaseController {

    private final ICxrCustomerAddressService cxrCustomerAddressService;

    private final UpdateDistributionInfoService updateDistributionInfoService;

    @ApiOperation("新增分页查询后台路条转拨管理")
//    @SaCheckPermission("base:cxrCustomerAddress:listAddPeople")
    @PostMapping("/listAddPeople")
    public R<PageTableDataInfo<CxrCustomerAddressVo>> listAddPeople(@RequestBody CxrCustomerAddressBo cxrEmployeeBo,
                                                                    PageQuery pageQuery) {
        return R.ok(cxrCustomerAddressService.listAddPeople(cxrEmployeeBo, pageQuery));
    }


    @ApiOperation("地址列表 显示主账户的手机号进行重新")
    @PostMapping("/customerPageQuery")
    public R<PageTableDataInfo<CxrCustomerAddressQueryVo>> customerPageQuery(@RequestBody CxrCustomerAddressQueryBo bo) {
        return R.ok(cxrCustomerAddressService.customerPageQuery(bo));
    }


    @ApiOperation("跑线上客户地址的经纬度")
    @PostMapping("/getLoglat")
    public R<Void> getCustomerAddressLoglat(
        @RequestBody AddressBo bo) {
        cxrCustomerAddressService.getCustomerAddressLoglat(bo);
        return R.ok();
    }

    @PostMapping("/getByCustomerId")
    public R<List<CustomerAddressDetailVo>> getByCustomerId() {
        return R.ok(cxrCustomerAddressService.getByCustomerId());
    }

    @GetMapping("/updateTaste/1")
    public R<String> updateTaste1(@RequestParam String province, @RequestParam String localDate) {
        return R.ok(updateDistributionInfoService.updateTaste1(province, LocalDate.parse(localDate)));
    }

    @GetMapping("/updateTaste/2")
    public R<String> updateTaste2(@RequestParam String province, @RequestParam String localDate) {
        return R.ok(updateDistributionInfoService.updateTaste2(province, LocalDate.parse(localDate)));
    }

    @GetMapping("/deliverySiteQuery")
    public R<List<DeliverySiteDTO>> deliverySiteQuery(@RequestParam(required = false) Long customerId,
                                                      @RequestParam(required = false) String customerPhone) {
        return R.ok(cxrCustomerAddressService.deliverySiteQuery(customerId, customerPhone));
    }
}
