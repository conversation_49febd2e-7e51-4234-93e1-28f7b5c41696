package com.ruoyi.business.base.api.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.cxrry.logger.api.annotation.LogTag;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.business.api.domain.vo.SaleProductListVo;
import com.ruoyi.business.base.api.domain.CxrSaleProduct;
import com.ruoyi.business.base.api.domain.json.CustomerAddressMilkDistributionInfo;
import com.ruoyi.business.base.api.typeHandler.TypeHandlerConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

@Data
@EqualsAndHashCode
@ApiModel("修改配送变动Bo对象")
public class CxrAddressVo implements Serializable {

    @ApiModelProperty("地址站点售卖商品集合")
    private List<SaleProductListVo> cxrSaleProducts;


    @ApiModelProperty("地址站点售卖商品集合")
    private List<CxrSaleProduct> cxrSaleProductsCustomer;

    /**
     * id
     */
    @ApiModelProperty(value = "id", required = true)
    @NotNull(message = "id不能为空")
    private Long id;
    /**
     * 客户id(客户id)
     */
    @ApiModelProperty(value = "客户id(客户id)", required = true)
    @NotNull(message = "客户id(客户id)不能为空")
    private Long cxrCustomerId;
    /**
     * 收货人姓名
     */
    @ApiModelProperty(value = "收货人姓名", required = true)
    @NotBlank(message = "收货人姓名不能为空")
    private String receiverName;
    /**
     * 收货人电话
     */
    @ApiModelProperty(value = "收货人电话", required = true)
    @NotBlank(message = "收货人电话不能为空")
    private String receiverPhone;
    /**
     * 详细配送地址信息
     */
    @ApiModelProperty(value = "详细配送地址信息", required = true)
    @NotBlank(message = "详细配送地址信息不能为空")
    private String detailDistributionAddress;
    /**
     * 小区id
     */
    @ApiModelProperty(value = "小区id", required = true)
    @NotBlank(message = "小区id")
    private Long cxrResidentialQuartersId;
    /**
     * 小区id
     */
    @ApiModelProperty(value = "小区名字", required = true)
    @NotBlank(message = "小区名字")
    private String cxrResidentialQuartersName;
    /**
     * 地区id(省市区地区表id)
     */
    @ApiModelProperty(value = "地区id(省市区地区表id)", required = true)
    @NotNull(message = "地区id(省市区地区表id)不能为空")
    private Long sysAreaId;
    /**
     * 站点id(配送站点的id)
     */
    @ApiModelProperty(value = "站点id(配送站点的id)", required = true)
    @NotNull(message = "站点id(配送站点的id)不能为空")
    private Long cxrSiteId;
    /**
     * 站点id(配送站点的id)
     */
    @ApiModelProperty(value = "站点名字(配送站点的名字)", required = true)
    @NotNull(message = "站点名字(配送站点的名字)不能为空")
    private String cxrSiteName;
    /**
     * 员工id(配送员工的id)
     */
    @ApiModelProperty(value = "员工id(配送员工的id)", required = true)
    @NotNull(message = "员工id(配送员工的id)不能为空")
    private Long cxrEmployeeId;
    /**
     * 员工id(配送员工的id)
     */
    @ApiModelProperty(value = "员工名称(配送员工的名称)", required = true)
    @NotNull(message = "员工名称(配送员工的名称)不能为空")
    private String cxrEmployeeName;
    /**
     * 省
     */
    @ApiModelProperty(value = "省")
    private String provice;
    /**
     * 市
     */
    @ApiModelProperty(value = "市")
    private String city;
    /**
     * 区
     */
    @ApiModelProperty(value = "区")
    private String area;
    /**
     * 上午配送状态(详情见字典)
     */
    @ApiModelProperty(value = "上午配送状态")
    private String amDistributionStatus;
    /**
     * 上午配送起送时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate amDistributionStartDeliveryTime;
    /**
     * 上午配送暂停开始时间
     */
    @ApiModelProperty(value = "上午配送暂停开始时间")
    private LocalDate amDistributionSuspendStartTime;
    /**
     * 上午配送暂停结束时间
     */
    @ApiModelProperty(value = "上午配送暂停结束时间")
    private LocalDate amDistributionSuspendEndTime;
    /**
     * 是否显示上午配送信息
     */
    @ApiModelProperty(value = "是否显示上午配送信息")
    private String isShowAmDistribution;
    /**
     * 是否显示下午配送信息
     */
    @ApiModelProperty(value = "是否显示下午配送信息")
    private String isShowPmDistribution;
    /**
     * 下午配送状态(详情见字典)
     */
    @ApiModelProperty(value = "下午配送状态")
    private String pmDistributionStatus;
    /**
     * 下午配送起送时间
     */
    @ApiModelProperty(value = "下午配送起送时间")
    private LocalDate pmDistributionStartDeliveryTime;
    /**
     * 下午配送暂停开始时间
     */
    @ApiModelProperty(value = "下午配送暂停开始时间")
    private LocalDate pmDistributionSuspendStartTime;
    /**
     * 下午配送暂停结束时间
     */
    @ApiModelProperty(value = "下午配送暂停结束时间")
    private LocalDate pmDistributionSuspendEndTime;
    /**
     * 默认账户地址 Y 是 N否
     */
    @ApiModelProperty(value = "默认账户地址")
    private String defalutAccountAddress;
    /**
     * 上午配送详细信息
     */
    @ApiModelProperty(value = "上午配送详细信息")
    @TableField(typeHandler = TypeHandlerConstant.CustomerAddressMilkDistributionInfoTypeHandler.class)
    private CustomerAddressMilkDistributionInfo amDistributionInfo;
    /**
     * 下午配送详细信息
     */
    @ApiModelProperty(value = "下午配送详细信息")
    @TableField(typeHandler = TypeHandlerConstant.CustomerAddressMilkDistributionInfoTypeHandler.class)
    private CustomerAddressMilkDistributionInfo pmDistributionInfo;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date updateTime;

    @ApiModelProperty("员工姓名")
    private String name;

    @ApiModelProperty("手机号码")
    private String phone;

    @ApiModelProperty("小区名称")
    private String rname;


    @ApiModelProperty("历史")
    private CxrAddressHistoryVo cxrAddressHistoryVo;

    /**
     * 上午排奶是否展示
     */
    private boolean amWhetherToDisplay;
    /**
     * 下午排奶是否展示
     */
    private boolean pmWhetherToDisplay;

    private Integer changeStatus;

    /**
     * 暂停默认时间
     */
    @ApiModelProperty("暂停默认时间")
    private LocalDate suspendDate;

    private String addressCreateByType;

    /**
     * 异动截止时间
     */
    @LogTag(alias = "异动截止时间")
    private LocalTime changeEndTime;

    /**
     * 异动间隔天数 8点之前
     */
    @LogTag(alias = "异动间隔天数 8点之前")
    private Long changeIntervalDays;

    /**
     * 异动间隔天数 8点之后
     */
    @LogTag(alias = "8点后异动天数")
    @ApiModelProperty(value = "8点后异动天数")
    private Long changeIntervalDaysAfter;
}
