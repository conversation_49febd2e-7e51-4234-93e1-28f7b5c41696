package com.ruoyi.order.manager.strategy.behavior;

import com.ruoyi.business.base.api.enums.OrderTypeEnums;
import com.ruoyi.order.common.domain.vo.CxrUserOrderVO;
import com.ruoyi.order.common.entity.CxrUserOrder;

/**
 * Description:
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/8/6 11:09
 */
public interface CxrUserOrderBehavior {

    /**
     * 订单添加
     *
     * @param obj
     * @return
     */
    public Long executeOrderAddOrUpdate(Object obj, OrderTypeEnums orderTypeEnums);

    /**
     * 订单更新
     *
     * @param obj
     * @return
     */
    public boolean executeOrderUpdate(Object obj, OrderTypeEnums orderTypeEnums);

    /**
     * 订单审核
     *
     * @param obj
     * @return
     */
    public boolean executeOrderAudit(Object obj, OrderTypeEnums orderTypeEnums);

    /**
     * 订单保存
     *
     * @param cxrUserOrder
     * @return
     */
    public boolean orderSaveOrUpdate(CxrUserOrder cxrUserOrder);

    /**
     * 订单更新
     *
     * @param cxrUserOrder
     * @return
     */
    public boolean orderUpdate(Object obj, CxrUserOrder cxrUserOrder);

    public boolean orderUpdates(Object obj, CxrUserOrder cxrUserOrder);

    /**
     * 订单审核处理
     *
     * @param id
     */
    Boolean executeCxrUserOrderAudit(Long id, boolean systemFlag, Integer auditType, String refundNoAuditReasons);

    Boolean executeCxrUserOrderAuditSubLock(Long id, Boolean subLock);

    /**
     * 订单审核处理
     *
     * @param id
     */
    CxrUserOrderVO executeOrderDetail(Long id);

    /**
     * 删除订单
     *
     * @param id
     * @return
     */
    boolean deleteOrder(Long id);

    String disributionContractOrderAdd(Object obj, OrderTypeEnums orderTypeEnums);


    boolean executeCxrUserOrderReverseAudit(CxrUserOrder data);
}
