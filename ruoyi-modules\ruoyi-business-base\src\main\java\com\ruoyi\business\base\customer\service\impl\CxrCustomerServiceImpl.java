package com.ruoyi.business.base.customer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.google.common.collect.Sets;
import com.ruoyi.business.api.domain.vo.SaleProductListVo;
import com.ruoyi.business.api.dubbo.RemoteServiceWorkOrderService;
import com.ruoyi.business.base.api.PlanMilkDataConvertUtil;
import com.ruoyi.business.base.api.domain.*;
import com.ruoyi.business.base.api.domain.bo.CxrAddressBo;
import com.ruoyi.business.base.api.domain.dto.CustomerAddressUpdateDTO;
import com.ruoyi.business.base.api.domain.dto.CustomerBindUpdateDTO;
import com.ruoyi.business.base.api.domain.dto.CustomerPhoneUpdateDTO;
import com.ruoyi.business.base.api.domain.dto.MallUserInfoDTO;
import com.ruoyi.business.base.api.domain.json.CustomerAddressMilkDistributionInfo;
import com.ruoyi.business.base.api.domain.json.MilkDistributionInfo;
import com.ruoyi.business.base.api.domain.vo.CustomersVo;
import com.ruoyi.business.base.api.domain.vo.CxrAddressHistoryVo;
import com.ruoyi.business.base.api.domain.vo.CxrLabeleDTO;
import com.ruoyi.business.base.api.dubbo.RemoteCustomerAddressService;
import com.ruoyi.business.base.api.enums.customer.CustomerAddressUpdateType;
import com.ruoyi.business.base.api.model.CustomerDistributioninfoDTO;
import com.ruoyi.business.base.api.model.LoginEmployee;
import com.ruoyi.business.base.api.model.MilkDistributionDTO;
import com.ruoyi.business.base.api.utils.huibo.HuiBoSignUtil;
import com.ruoyi.business.base.customer.domain.MallUserEntity;
import com.ruoyi.business.base.customer.domain.bo.CustomerLabeledBo;
import com.ruoyi.business.base.customer.domain.bo.CxrCustomerAddressABo;
import com.ruoyi.business.base.customer.domain.bo.CxrCustomerBo;
import com.ruoyi.business.base.customer.domain.bo.MilkDistributionDetailedBo;
import com.ruoyi.business.base.customer.domain.dto.CountAndSumQuantity;
import com.ruoyi.business.base.customer.domain.vo.*;
import com.ruoyi.business.base.customer.mapper.CxrCustomerMapper;
import com.ruoyi.business.base.customer.mapper.MallUserMapper;
import com.ruoyi.business.base.customer.service.ICxrCustomerService;
import com.ruoyi.business.base.customerAddress.domain.bo.CxrCustomerAddrBo;
import com.ruoyi.business.base.customerAddress.domain.vo.CxrCustomerAddrVo;
import com.ruoyi.business.base.customerAddress.mapper.CxrCustomerAddressMapper;
import com.ruoyi.business.base.customerAddress.service.ICxrCustomerAddressService;
import com.ruoyi.business.base.customerChangeRecord.service.CxrCustomerChangeRecordService;
import com.ruoyi.business.base.customerDistributionListRecord.domain.CxrCustomerDistributionListRecord;
import com.ruoyi.business.base.customerDistributionListRecord.domain.vo.CustomerDistributionListRecordVo;
import com.ruoyi.business.base.customerDistributionListRecord.mapper.CxrCustomerDistributionListRecordMapper;
import com.ruoyi.business.base.customerDistributionListRecord.service.CxrCustomerDistributionListRecordService;
import com.ruoyi.business.base.customerRemark.entity.CxrCustomerRemark;
import com.ruoyi.business.base.customerRemark.mapper.CxrCustomerRemarkMapper;
import com.ruoyi.business.base.customerStockDetai.domain.CxrCustomerStockLockDetail;
import com.ruoyi.business.base.customerStockDetai.mapper.CxrCustomerStockDetailMapper;
import com.ruoyi.business.base.customerStockDetai.mapper.CxrCustomerStockLockDetailMapper;
import com.ruoyi.business.base.customerStockDetai.service.CxrCustomerStockLockDetailService;
import com.ruoyi.business.base.cxrAddressHistory.domain.CxrAddressHistoryBo;
import com.ruoyi.business.base.cxrAddressHistory.mapper.CxrAddressHistoryMapper;
import com.ruoyi.business.base.cxrAddressHistory.service.CxrAddressHistoryService;
import com.ruoyi.business.base.cxrCustomerAddressHistory.domain.CxrCustomerAddressHistory;
import com.ruoyi.business.base.cxrCustomerAddressHistory.service.ICxrCustomerAddressHistoryService;
import com.ruoyi.business.base.cxrCustomerExchange.mapper.CxrCustomerExchangeMapper;
import com.ruoyi.business.base.cxrCustomerWayNumber.mapper.CxrCustomerWayNumberMapper;
import com.ruoyi.business.base.cxrLabeled.domain.CxrLabeled;
import com.ruoyi.business.base.cxrLabeled.domain.bo.CxrLabeledBo;
import com.ruoyi.business.base.cxrLabeled.domain.vo.CxrLabeledVo;
import com.ruoyi.business.base.cxrLabeled.mapper.CxrLabeledMapper;
import com.ruoyi.business.base.cxrLongMilkStock.mapper.CxrLongMilkStockMapper;
import com.ruoyi.business.base.employee.mapper.CxrEmployeeMapper;
import com.ruoyi.business.base.employee.service.ICxrEmployeeService;
import com.ruoyi.business.base.residentialQuarters.mapper.CxrResidentialQuartersMapper;
import com.ruoyi.business.base.roadWay.domain.bo.RoadWayTotalBo;
import com.ruoyi.business.base.roadWay.mapper.CxrRoadWayMapper;
import com.ruoyi.business.base.saleProduct.service.ICxrSaleProductService;
import com.ruoyi.business.base.site.service.ICxrSiteService;
import com.ruoyi.common.core.annotation.CustomerAfter;
import com.ruoyi.common.core.config.HuiBoConfig;
import com.ruoyi.common.core.config.TxMapConfig;
import com.ruoyi.common.core.constant.HttpStatus;
import com.ruoyi.common.core.constant.UserConstants;
import com.ruoyi.common.core.domain.LoginInfo;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.*;
import com.ruoyi.common.core.utils.phone.PhoneNumberValidator;
import com.ruoyi.common.core.validator.AbstractAssert;
import com.ruoyi.common.mybatis.core.page.PageQuery;
import com.ruoyi.common.mybatis.core.page.PageTableDataInfo;
import com.ruoyi.common.redis.annotation.RedisDistributedLock;
import com.ruoyi.common.rocketmq.calculate.CustomerAddressConstant;
import com.ruoyi.common.rocketmq.constant.customer.CxrCustomerConstant;
import com.ruoyi.common.rocketmq.constant.huibo.HuiboConstant;
import com.ruoyi.common.rocketmq.constant.lbs.GetLngLatConstant;
import com.ruoyi.common.rocketmq.constant.updatePhone.UpdatePhoneCustomerConstant;
import com.ruoyi.common.rocketmq.utils.MqUtil;
import com.ruoyi.common.satoken.utils.LoginUtil;
import com.ruoyi.common.satoken.utils.helper.CustomerLoginHelper;
import com.ruoyi.common.satoken.utils.helper.LoginHelper;
import com.ruoyi.common.satoken.utils.helper.StaffLoginHelper;
import com.ruoyi.core.base.config.AppletConfig;
import com.ruoyi.core.base.domain.CxrOrderAfterSale;
import com.ruoyi.core.base.mapper.CxrOrderAfterSaleMapper;
import com.ruoyi.message.api.RemoteLabeledService;
import com.ruoyi.order.api.RemoteOrderService;
import com.ruoyi.order.api.domain.vo.CustomerOrderStatisticsDTO;
import com.ruoyi.system.api.RemoteAreaService;
import com.ruoyi.system.api.domain.AreaDTO;
import com.ruoyi.system.api.model.LoginUser;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.ibatis.annotations.Param;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 客户Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-07-02
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class CxrCustomerServiceImpl extends ServiceImpl<CxrCustomerMapper, CxrCustomer> implements ICxrCustomerService {

    private final ICxrSaleProductService iCxrSaleProductService;
    private final IdentifierGenerator identifierGenerator;
    private final CxrCustomerStockDetailMapper detailMapper;
    private final CxrRoadWayMapper cxrRoadWayMapper;
    private final CxrCustomerMapper baseMapper;
    private final CxrCustomerAddressMapper cxrCustomerAddressMapper;

    private final CxrCustomerStockLockDetailMapper stockLockDetailMapper;
    private final CxrCustomerChangeRecordService customerChangeRecordService;
    private final CxrResidentialQuartersMapper cxrResidentialQuartersMapper;
    private final ICxrSiteService iCxrSiteService;
    private final ICxrEmployeeService iCxrEmployeeService;
    private final CxrEmployeeMapper cxrEmployeeMapper;
    private final ICxrCustomerAddressService iCxrCustomerAddressService;
    private final ICxrCustomerAddressHistoryService iCxrCustomerAddressHistoryService;
    private final CxrLongMilkStockMapper cxrLongMilkStockMapper;
    private final CxrCustomerExchangeMapper cxrCustomerExchangeMapper;

    private final CxrLabeledMapper cxrLabeledMapper;
    private final CxrAddressHistoryService cxrAddressHistoryService;
    private final CxrCustomerWayNumberMapper cxrCustomerWayNumberMapper;
    private final CxrAddressHistoryMapper cxrAddressHistoryMapper;

    private final CxrCustomerDistributionListRecordMapper recordMapper;

    private final CxrCustomerRemarkMapper cxrCustomerRemarkMapper;

    private final CxrCustomerDistributionListRecordService cxrCustomerDistributionListRecordService;
    @Autowired
    private RocketMQTemplate rocketMQTemplate;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private TxMapConfig txMapConfig;

    @Autowired
    private MqUtil mqUtil;

    @Autowired
    private AppletConfig appletConfig;

    @Resource(name = "scheduledExecutorService")
    private ScheduledExecutorService scheduledExecutorService;
    @Autowired
    @Qualifier("futureThreadPool")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @DubboReference(timeout = 12000)
    private RemoteOrderService remoteOrderService;

    @DubboReference(timeout = 12000)
    private RemoteCustomerAddressService addressService;
    @DubboReference
    private RemoteServiceWorkOrderService remoteServiceWorkOrderService;
    @DubboReference
    private RemoteAreaService remoteAreaService;
    @Autowired
    private CxrOrderAfterSaleMapper cxrOrderAfterSaleMapper;


    @Autowired
    private final HuiBoConfig huiBoConfig;

    @DubboReference
    private RemoteLabeledService remoteLabeledService;

    @Autowired
    private MallUserMapper mallUserMapper;

    @Autowired
    @Lazy
    private CxrCustomerStockLockDetailService cxrCustomerStockLockDetailService;
    @Autowired
    private CxrCustomerMapper customerMapper;

    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    // 查询客户的信息、配送员和配送地址
    public MPJLambdaWrapper<CxrCustomer> mpjLambdaWrapper() {
        MPJLambdaWrapper<CxrCustomer> lambdaWrapper = new MPJLambdaWrapper<CxrCustomer>()
            .selectAll(CxrCustomer.class)
            .selectAs(CxrEmployee::getName, CxrCustomerListVo::getEmployeeName)
            .selectAs(CxrSite::getName, CxrCustomerListVo::getSiteName)
            .selectAs(CxrCustomerAddress::getId, CxrCustomerListVo::getCxrCustomerAddressId)
            .selectAs(CxrCustomerAddress::getReceiverName, CxrCustomerListVo::getReceiverName)
            .selectAs(CxrCustomerAddress::getReceiverPhone, CxrCustomerListVo::getReceiverPhone)
            .leftJoin(CxrCustomerAddress.class, wrapper ->
                wrapper.eq(CxrCustomerAddress::getCxrCustomerId, CxrCustomer::getId)
                    .eq(CxrCustomerAddress::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                    .eq(CxrCustomerAddress::getSysDeptId, LoginHelper.getLoginUser().getDeptId()))
            .leftJoin(CxrSite.class, wrapper ->
                wrapper.eq(CxrSite::getId, CxrCustomerAddress::getCxrSiteId)
                    .eq(CxrSite::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                    .eq(CxrSite::getSysDeptId, LoginHelper.getLoginUser().getDeptId()))
            .leftJoin(CxrEmployee.class, wrapper ->
                wrapper.eq(CxrEmployee::getId, CxrCustomerAddress::getCxrEmployeeId)
                    .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                    .eq(CxrEmployee::getSysDeptId, LoginHelper.getLoginUser().getDeptId()));

        return lambdaWrapper;
    }

    // 新增客户地址信息
    public Boolean addCustomerAddress(CxrCustomerAddress cxrCustomerAddress, Boolean flag, Boolean saveHistoryAddress) {

        // 如果上午起送状态为null 就当天默认永久停送
        if (StrUtil.isBlank(cxrCustomerAddress.getAmDistributionStatus())) {
            cxrCustomerAddress.setAmDistributionStatus(SysYesNo.NO.getValue());
            cxrCustomerAddress.setAmDistributionSuspendStartTime(LocalDate.now());
        }
        // 如果下午起送状态为null 就当天默认永久停送
        if (StrUtil.isBlank(cxrCustomerAddress.getPmDistributionStatus())) {
            cxrCustomerAddress.setPmDistributionStatus(SysYesNo.NO.getValue());
            cxrCustomerAddress.setPmDistributionSuspendStartTime(LocalDate.now());
        }

        boolean customerAddressStatus = cxrCustomerAddressMapper.insert(cxrCustomerAddress) > 0;

        if (customerAddressStatus) {
            //保存历史
            AddressHistoryMqDoMain addressHistoryMqDoMain = new AddressHistoryMqDoMain();
            addressHistoryMqDoMain.setCxrCustomerAddressId(cxrCustomerAddress.getId());
            addressHistoryMqDoMain.setCxrCustomerId(cxrCustomerAddress.getCxrCustomerId());
            AlterRecord alterRecord = new AlterRecord();
            CustomerAddressUpdateDTO customerAddressUpdateDTO = new CustomerAddressUpdateDTO();
            customerAddressUpdateDTO.setUpdateType(CustomerAddressUpdateType.ADD.getValue());
            customerAddressUpdateDTO.setAddress(
                Convert.toStr(cxrCustomerAddress.getProvice(), "") + Convert.toStr(cxrCustomerAddress.getCity(), "") + Convert.toStr(cxrCustomerAddress.getArea(), "")
                    + Convert.toStr(cxrCustomerAddress.getDetailDistributionAddress(), ""));
            alterRecord.setCustomerAddressUpdate(customerAddressUpdateDTO);
            addressHistoryMqDoMain.setAlterRecord(alterRecord);
            addressHistoryMqDoMain.setLoginInfo(LoginUtil.getLoginUser());
            addressHistoryMqDoMain.setUuid(java.util.UUID.randomUUID().toString());
            mqUtil.sendSyncMessageTransaction(CustomerAddressConstant.CUSTOMER_ADDRESS_TOPIC,
                CustomerAddressConstant.CUSTOMER_ADDRESS_HISTORY_TAG, JSONUtil.toJsonStr(addressHistoryMqDoMain));
        }
        return customerAddressStatus;
    }

//    // 修改客户地址信息
//    public Boolean updateCustomerAddress(CxrCustomerAddress cxrCustomerAddress, CxrCustomerBo cxrCustomerBo) {
//
//        boolean customerAddressStatus = cxrCustomerAddressMapper.update(cxrCustomerAddress,
//            new LambdaUpdateWrapper<CxrCustomerAddress>()
//                .eq(CxrCustomerAddress::getId, cxrCustomerBo.getCxrCustomerAddressId())
//                .set(CxrCustomerAddress::getCxrCustomerId, cxrCustomerBo.getCxrCustomerId())
//                .set(CxrCustomerAddress::getReceiverName, cxrCustomerBo.getReceiverName())
//                .set(CxrCustomerAddress::getReceiverPhone, cxrCustomerBo.getReceiverPhone())
//                .set(cxrCustomerBo.getCxrResidentialQuartersId() != null,
//                    CxrCustomerAddress::getCxrResidentialQuartersId
//                    , cxrCustomerBo.getCxrResidentialQuartersId())
//                .set(CxrCustomerAddress::getCxrSiteId, cxrCustomerBo.getCxrSiteId())
//                .set(CxrCustomerAddress::getCxrEmployeeId, cxrCustomerBo.getCxrEmployeeId())
//                .set(CxrCustomerAddress::getDetailDistributionAddress, cxrCustomerBo.getDetailDistributionAddress())
//                .set(cxrCustomerBo.getRevision() != null, CxrCustomerAddress::getRevision,
//                    cxrCustomerBo.getRevision() + 1)) > 0;
//        if (customerAddressStatus) {
//
//            return customerAddressStatus ? true : false;
//        } else {
//            throw new ServiceException("修改客户地址信息失败");
//        }
//    }

    @Override
    public CxrCustomerCountVo detail(Long id) {
        // 查询客户信息
        CxrCustomerVo cxrCustomerVo = baseMapper.selectVoById(id);

        if (cxrCustomerVo == null) {
            throw new ServiceException("客户不存在");
        }
        //常温奶库存
        Integer integer = cxrLongMilkStockMapper.selectSumSurplusQuantity(id);
        //获取信息
        CxrCustomerCountVo cxrCustomerCountVo = this.baseMapper.selectByCustomerId(id,
            DeleteStatus.NOT_DELETED.getValue());
        cxrCustomerCountVo.setLongMilkSurplus(integer);
        //冻结奶数
        List<CxrOrderAfterSale> cxrOrderAfterSaleList = cxrOrderAfterSaleMapper.selectList(
            new LambdaQueryWrapper<CxrOrderAfterSale>()
                .eq(CxrOrderAfterSale::getCustomerId, id)
                .eq(CxrOrderAfterSale::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
        );
        //冻结奶数的合计数量
        Long total = cxrOrderAfterSaleList.stream()
            .mapToLong(order -> order.getReturnFreshMilkQty() != null ? order.getReturnFreshMilkQty() : 0).sum();
        cxrCustomerCountVo.setReturnFreshMilkQty(total);
        //账户剩余奶数
        cxrCustomerCountVo.setFreshMilkStock(cxrCustomerCountVo.getFreshMilkStock());

        cxrCustomerCountVo.setFreshMilkTotal(
            cxrCustomerCountVo.getFreshMilkGiveTotal() + cxrCustomerCountVo.getFreshMilkOrderTotal()
        );

        Integer sumApplyMilk = cxrLongMilkStockMapper.sumApplyMilk(id);
        Integer sumNotApplyMilk = cxrLongMilkStockMapper.sumNotApplyMilk(id);

        cxrCustomerCountVo.setLongMilkApplyTotal(sumApplyMilk);
        cxrCustomerCountVo.setLongMilkNOApplyTotal(sumNotApplyMilk);

        List<CxrCustomerStockLockDetail> lockDetails = stockLockDetailMapper.selectList(
            new LambdaQueryWrapper<CxrCustomerStockLockDetail>()
                .select(CxrCustomerStockLockDetail::getLockNum)
                .eq(CxrCustomerStockLockDetail::getCustomerId, id)
                .eq(CxrCustomerStockLockDetail::getStockType, 1)
                .eq(CxrCustomerStockLockDetail::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
        );

        if (CollUtil.isNotEmpty(lockDetails)) {
            Integer adjustFreshMilkStock = lockDetails.stream().mapToInt(CxrCustomerStockLockDetail::getLockNum).sum();
            cxrCustomerCountVo.setAdjustFreshMilkQty(adjustFreshMilkStock);
        }

        return cxrCustomerCountVo;
    }

    @Override
    public PageTableDataInfo<CxrCustomerListVo> myPage(CxrCustomerBo cxrCustomerBo, PageQuery pageQuery) {

        List<String> cxrCustomerIds = new ArrayList<>();

        boolean flag = Boolean.FALSE;
        // 先筛选省市区 和详细地址数据
        if (StringUtils.isNotBlank(cxrCustomerBo.getProvice())
            || StringUtils.isNotBlank(cxrCustomerBo.getCity())
            || StringUtils.isNotBlank(cxrCustomerBo.getArea())
            || StringUtils.isNotBlank(cxrCustomerBo.getDetailDistributionAddress())) {
            Page<String> page = cxrCustomerAddressMapper.queryCxrCustomerId(cxrCustomerBo, pageQuery.build());
            cxrCustomerIds = page.getRecords();
            flag = Boolean.TRUE;
        }
        if (flag && CollectionUtil.isEmpty(cxrCustomerIds)) {
            return PageTableDataInfo.build(new ArrayList<>());
        }

        Page<CxrCustomer> cxrCustomerPage = this.getBaseMapper().selectPage(pageQuery.build(),
            new LambdaQueryWrapper<CxrCustomer>()
                .in(CollectionUtil.isNotEmpty(cxrCustomerIds), CxrCustomer::getId, cxrCustomerIds)
                .like(StringUtils.isNotBlank(cxrCustomerBo.getName()), CxrCustomer::getName, cxrCustomerBo.getName())
                .like(StringUtils.isNotBlank(cxrCustomerBo.getPhone()), CxrCustomer::getPhone, cxrCustomerBo.getPhone())
                .orderByDesc(CxrCustomer::getCreateTime));

        List<CxrCustomer> cxrCustomerList = cxrCustomerPage.getRecords();
        Map<Long, CxrCustomerAddress> map = new HashMap<>();
        if (CollectionUtil.isNotEmpty(cxrCustomerList)) {
            List<Long> cxrCustomerids = cxrCustomerList.stream().map(CxrCustomer::getId).collect(Collectors.toList());
            List<CxrCustomerAddress> byiCxrCustomerids = cxrCustomerAddressMapper.findByiCxrCustomerids(cxrCustomerids);
            map = byiCxrCustomerids.stream().collect(Collectors.toMap(CxrCustomerAddress::getCxrCustomerId,
                Function.identity(), (v1, v2) -> v2));
        }

        final Map<Long, CxrCustomerAddress> finalMap = map;
        IPage<CxrCustomerListVo> returnPage = cxrCustomerPage.convert(x -> {
            CxrCustomerListVo cxrCustomerListVo = new CxrCustomerListVo();
            CxrCustomerAddress cxrCustomerAddress = finalMap.get(x.getId());
            if (ObjectUtil.isNotNull(cxrCustomerAddress)) {
                List<String> arr = new ArrayList<>();

                cxrCustomerListVo.setMergename(AreaUtils.getMergename(cxrCustomerAddress.getProvice(),
                    cxrCustomerAddress.getCity(), cxrCustomerAddress.getArea(), ","));
                cxrCustomerListVo.setDetailAddress(cxrCustomerAddress.getDetailDistributionAddress());
            }
            cxrCustomerListVo.setId(x.getId());
            cxrCustomerListVo.setName(x.getName());
            cxrCustomerListVo.setPhone(x.getPhone());
            cxrCustomerListVo.setWxHeadPortrait(x.getWxHeadPortrait());
            cxrCustomerListVo.setWxNickname(x.getWxNickname());
            cxrCustomerListVo.setUpdateByName(x.getUpdateByName());
            cxrCustomerListVo.setUpdateTime(x.getUpdateTime());
            return cxrCustomerListVo;
        });
        return PageTableDataInfo.build(returnPage);

        // 条件查询
//        MPJLambdaWrapper<CxrCustomer> cxrCustomerMPJLambdaWrapper = mpjLambdaWrapper();
//        cxrCustomerMPJLambdaWrapper = cxrCustomerMPJLambdaWrapper
//                                    .like(StringUtils.isNotBlank(cxrCustomerBo.getName()), CxrCustomer::getName,
//                                    cxrCustomerBo.getName())
//                                    .like(StringUtils.isNotBlank(cxrCustomerBo.getPhone()), CxrCustomer::getPhone,
//                                    cxrCustomerBo.getPhone())
//                                    .like(StringUtils.isNotBlank(cxrCustomerBo.getDetailDistributionAddress()),
//                                    CxrCustomerAddress::getDetailDistributionAddress, cxrCustomerBo
//                                    .getDetailDistributionAddress());
//
//        // 根据省市区查询站点
//        if(StringUtils.isNotEmpty(cxrCustomerBo.getProvice()) || StringUtils.isNotEmpty(cxrCustomerBo.getCity()) ||
//        StringUtils.isNotEmpty(cxrCustomerBo.getArea())){
//            List<SysArea> sysAreas = remoteAreaService.selectNormalChildrenAreaById(cxrCustomerBo.getProvice(),
//            cxrCustomerBo.getCity(), cxrCustomerBo.getArea(), true);
//
//            if(CollectionUtil.isNotEmpty(sysAreas)){
//                HashSet<Long> set = new HashSet<>();
//                for (SysArea sysArea : sysAreas) {
//                    set.add(sysArea.getId());
//                }
//
//                cxrCustomerMPJLambdaWrapper = cxrCustomerMPJLambdaWrapper.in(CxrCustomerAddress::getSysAreaId, set);
//            }
//        }
//
//        // 拦截sql，数据作为临时表，加上部门、删除状态条件
//        DataScope dataScope = new DataScope();
//        dataScope.setUserType(UserType.SYS_USER);
//        IPage<CxrCustomerListVo> cxrCustomerListVoIPage = baseMapper.selectJoinPage(pageQuery.build(),
//        CxrCustomerListVo.class, cxrCustomerMPJLambdaWrapper, dataScope);
//
//        if(CollectionUtil.isNotEmpty(cxrCustomerListVoIPage.getRecords())){
//            for (CxrCustomerListVo record : cxrCustomerListVoIPage.getRecords()) {
//
//                // 根据客户id查询客户的所有配送地址，列表取第一个地址和省市区作为展示
//                List<CxrCustomerAddressListVo> cxrCustomerAddressListVos = cxrCustomerAddressMapper.selectJoinList
//                (CxrCustomerAddressListVo.class,  new MPJLambdaWrapper<CxrCustomerAddress>()
//                    .selectAll(CxrCustomerAddress.class)
//                    .selectAs(SysArea::getMergename, CxrCustomerAddressVo::getMergename)
//                    .leftJoin(SysArea.class, SysArea::getId, CxrCustomerAddress::getSysAreaId)
//                    .eq(CxrCustomerAddress::getCxrCustomerId, record.getId())
//                    .eq(CxrCustomerAddress::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
//                    .eq(CxrCustomerAddress::getSysDeptId, LoginHelper.getDeptId()));
//
//                if(CollectionUtil.isNotEmpty(cxrCustomerAddressListVos)){
//                    CxrCustomerAddressListVo cxrCustomerAddressListVo = cxrCustomerAddressListVos.get(0);
//
//                    record.setDetailAddress(cxrCustomerAddressListVo.getDetailDistributionAddress());
//                    record.setMergename(cxrCustomerAddressListVo.getMergename());
//                }
//
//            }
//        }

//        return PageTableDataInfo.build(cxrCustomerListVoIPage);
    }

    @Override
    public String checkCustomerPhoneUnique(CxrCustomerBo cxrCustomerBo) {

        /**
         * 如果id 为 null 说明第一次添加
         */
        boolean exist = Boolean.FALSE;
        if (ObjectUtil.isNull(cxrCustomerBo.getId())) {
            exist = baseMapper.exists(new LambdaQueryWrapper<CxrCustomer>()
                .eq(CxrCustomer::getPhone, cxrCustomerBo.getPhone())
                .eq(CxrCustomer::getDeleteStatus, DeleteStatus.not_deleted)
                .ne(ObjectUtil.isNotNull(cxrCustomerBo.getId()), CxrCustomer::getId, cxrCustomerBo.getId()));
        } else {
            exist = baseMapper.exists(new LambdaQueryWrapper<CxrCustomer>()
                .eq(CxrCustomer::getPhone, cxrCustomerBo.getReceiverPhone())
                .eq(CxrCustomer::getDeleteStatus, DeleteStatus.not_deleted)
                .ne(ObjectUtil.isNotNull(cxrCustomerBo.getId()), CxrCustomer::getId, cxrCustomerBo.getId()));
        }
        if (exist) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(CxrCustomerBo cxrCustomerBo) {
        boolean flag = true;
        boolean isAddAdress = Boolean.FALSE;
        Boolean saveHistoryAddress = cxrCustomerBo.getSaveHistoryAddress();

        if (ObjectUtil.isNull(cxrCustomerBo.getSysAreaId())) {
            throw new ServiceException("配送地址必填");
        }

        AreaDTO areaDTO = remoteAreaService.queryMergenameArrById(cxrCustomerBo.getSysAreaId());
        // 当客户id不存在时，则是新增客户接口。客户id已存在，则是新增客户配送地址接口
        if (cxrCustomerBo.getId() == null) {
            // 新增客户
            CxrCustomer cxrCustomer = BeanUtil.toBean(cxrCustomerBo, CxrCustomer.class);
            cxrCustomer.setProvice(areaDTO.getProvice());
            cxrCustomer.setCity(areaDTO.getCity());
            cxrCustomer.setArea(areaDTO.getArea());
            flag = baseMapper.insert(cxrCustomer) > 0;

            if (flag) {
                cxrCustomerBo.setId(cxrCustomer.getId());
            } else {
                throw new ServiceException("新增客户失败");
            }
            isAddAdress = Boolean.TRUE;
        }
        long cxrCustomerAddressId = identifierGenerator.nextId(null).longValue();
        //判断客户有没有存在主地址
        Long count = cxrCustomerAddressMapper.selectCount(new LambdaQueryWrapper<CxrCustomerAddress>()
            .eq(CxrCustomerAddress::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            .eq(CxrCustomerAddress::getCxrCustomerId, cxrCustomerBo.getId())
            .eq(CxrCustomerAddress::getDefalutAccountAddress, SysYesNo.YES.getValue())
        );
        cxrCustomerBo.setCxrCustomerAddressId(cxrCustomerAddressId);
        CxrCustomerAddress cxrCustomerAddress = new CxrCustomerAddress();
        if (count > 0l) {
            cxrCustomerAddress.setDefalutAccountAddress(SysYesNo.NO.getValue());
        } else {
            cxrCustomerAddress.setDefalutAccountAddress(SysYesNo.YES.getValue());
        }
        cxrCustomerAddress.setCxrCustomerId(cxrCustomerBo.getId());
        cxrCustomerAddress.setId(cxrCustomerAddressId);
        if (isAddAdress || cxrCustomerBo.getDefaultMainAddress()) {
            cxrCustomerAddress.setReceiverName(cxrCustomerBo.getName());
            cxrCustomerAddress.setReceiverPhone(cxrCustomerBo.getPhone());
        } else {
            cxrCustomerAddress.setReceiverName(cxrCustomerBo.getReceiverName());
            cxrCustomerAddress.setReceiverPhone(cxrCustomerBo.getReceiverPhone());
        }

        cxrCustomerAddress.setSysAreaId(cxrCustomerBo.getSysAreaId());
        cxrCustomerAddress.setDetailDistributionAddress(cxrCustomerBo.getDetailDistributionAddress());
        cxrCustomerAddress.setCxrSiteId(cxrCustomerBo.getCxrSiteId());
        cxrCustomerAddress.setCxrEmployeeId(cxrCustomerBo.getCxrEmployeeId());
        cxrCustomerAddress.setProvice(areaDTO.getProvice());
        cxrCustomerAddress.setCity(areaDTO.getCity());
        cxrCustomerAddress.setArea(areaDTO.getArea());

        addGetLngLat(cxrCustomerAddress);

        // 当小区id和小区名字都不为空，或者小区id不为空、小区名字为空,则小区已存在，不需要新增小区，但要绑定小区
        if (ObjectUtil.isNotNull(cxrCustomerBo.getCxrResidentialQuartersId())) {
            // 客户地址信息绑定小区
            cxrCustomerAddress.setCxrResidentialQuartersId(cxrCustomerBo.getCxrResidentialQuartersId());

            // 新增客户地址信息
            return addCustomerAddress(cxrCustomerAddress, flag, saveHistoryAddress);
        }
        // 当小区id为空和小区名字都为空，则小区不存在，新增小区并且绑定小区
        else if (ObjectUtil.isNull(cxrCustomerBo.getCxrResidentialQuartersId()) && StrUtil.isNotBlank(
            cxrCustomerBo.getCxrResidentialQuartersName())) {

            List<CxrResidentialQuarters> data = cxrResidentialQuartersMapper.selectList(
                Wrappers.lambdaQuery(CxrResidentialQuarters.class)
                    .eq(CxrResidentialQuarters::getName, cxrCustomerBo.getCxrResidentialQuartersName())
                    .eq(CxrResidentialQuarters::getSysAreaId, cxrCustomerBo.getSysAreaId())
                    .eq(CxrResidentialQuarters::getDetailAddress, cxrCustomerBo.getDetailDistributionAddress())
            );
            if (CollUtil.isNotEmpty(data)) {
                CxrResidentialQuarters cxrResidentialQuarters = data.get(0);
                // 客户地址信息绑定小区
                cxrCustomerAddress.setCxrResidentialQuartersId(cxrResidentialQuarters.getId());

                // 修改户地址信息
                return addCustomerAddress(cxrCustomerAddress, flag, saveHistoryAddress);
            } else {
                // 新增小区
                CxrResidentialQuarters cxrResidentialQuarters = new CxrResidentialQuarters();
                cxrResidentialQuarters.setName(cxrCustomerBo.getCxrResidentialQuartersName());
                cxrResidentialQuarters.setSysAreaId(cxrCustomerBo.getSysAreaId());
                if (cxrCustomerBo.getSaveflagResidentialQuartersAdress()) {
                    cxrResidentialQuarters.setDetailAddress(cxrCustomerBo.getDetailDistributionAddress());
                }

                boolean residentialQuartersStatus = cxrResidentialQuartersMapper.insert(cxrResidentialQuarters) > 0;
                if (residentialQuartersStatus) {
                    // 客户地址信息绑定小区
                    cxrCustomerAddress.setCxrResidentialQuartersId(cxrResidentialQuarters.getId());

                    // 新增客户地址信息
                    return addCustomerAddress(cxrCustomerAddress, flag, saveHistoryAddress);
                } else {
                    throw new ServiceException("新增小区失败");
                }
            }


        }
        // 其它情况，不新增小区，也不绑定小区
        else {
            // 新增客户地址信息
            return addCustomerAddress(cxrCustomerAddress, flag, saveHistoryAddress);
        }
    }

    private void addGetLngLat(CxrCustomerAddress address) {
        StringBuffer buffer = new StringBuffer();
        buffer.append(address.getProvice());
        buffer.append(address.getCity());
        buffer.append(address.getArea());
        buffer.append(address.getDetailDistributionAddress());

        //依据导入到数据来看详细地址里会有# ,#对它的去除
        int index = buffer.indexOf("#");
        while (index != -1) {
            buffer.deleteCharAt(index);
            index = buffer.indexOf("#");
        }

        String baseUrl = txMapConfig.getUrl(); // 基础URL
        String path = "?address=" + buffer.toString(); // 参数
        String query = "&key=" + txMapConfig.getAppKey(); // key值

        String url = baseUrl + path + query; // 构建完整的URL
        OkHttpClient client = new OkHttpClient();
        Request request = new Request.Builder()
            .url(url)
            .build();
        try {
            Response response = client.newCall(request).execute();
            String responseBody = response.body().string();

            JSONObject jsonObject = new JSONObject(responseBody);
            // 在这里对响应数据进行解析，获取需要的地址或坐标信息
            System.out.println(responseBody);

            Object lng = jsonObject.getJSONObject("result").getJSONObject("location").get("lng");
            Object lat = jsonObject.getJSONObject("result").getJSONObject("location").get("lat");
            Object level = jsonObject.getJSONObject("result").get("level");
            String levels = level.toString();
            address.setLevel(Integer.parseInt(levels));
            BigDecimal decimalValue = new BigDecimal(String.valueOf(lng));
            BigDecimal decimalValues = new BigDecimal(String.valueOf(lat));
            address.setLatitude(decimalValues);
            address.setLongitude(decimalValue);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 完善信息 修改客户信息
     */
    public void editCompleteInformation() {

    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean editCompleteInformation(CxrCustomerBo cxrCustomerBo) {

        // 修改客户
        if (cxrCustomerBo.getCxrCustomerAddressId() == null) {
            throw new ServiceException("数据不存在");
        }
        if (ObjectUtil.isNull(cxrCustomerBo.getSysAreaId())) {
            throw new ServiceException("配送地址必填");
        }

        AreaDTO areaDTO = remoteAreaService.queryMergenameArrById(cxrCustomerBo.getSysAreaId());

        // 修改客户地址信息
        CxrCustomerAddress cxrCustomerAddress = BeanUtil.toBean(cxrCustomerBo, CxrCustomerAddress.class);
        cxrCustomerAddress.setCxrCustomerId(cxrCustomerBo.getCxrCustomerId());
        cxrCustomerAddress.setId(cxrCustomerBo.getCxrCustomerAddressId());
        cxrCustomerAddress.setReceiverName(cxrCustomerBo.getReceiverName());
        cxrCustomerAddress.setReceiverPhone(cxrCustomerBo.getReceiverPhone());
        cxrCustomerAddress.setProvice(areaDTO.getProvice());
        cxrCustomerAddress.setCity(areaDTO.getCity());
        cxrCustomerAddress.setArea(areaDTO.getArea());

        // 当小区id和小区名字都不为空，或者小区id不为空、小区名字为空,则小区已存在，不需要新增小区，但要绑定小区
        if (ObjectUtil.isNotNull(cxrCustomerBo.getCxrResidentialQuartersId())) {
            // 客户地址信息绑定小区
            cxrCustomerAddress.setCxrResidentialQuartersId(cxrCustomerBo.getCxrResidentialQuartersId());

            // 修改客户地址信息
            updateCustomerAddress(cxrCustomerAddress, cxrCustomerBo);
        }
        // 当小区id为空和小区名字不为空，则小区不存在，新增小区并且绑定小区
        else if (ObjectUtil.isNull(cxrCustomerBo.getCxrResidentialQuartersId()) && StrUtil.isNotBlank(
            cxrCustomerBo.getCxrResidentialQuartersName())) {

            List<CxrResidentialQuarters> data = cxrResidentialQuartersMapper.selectList(
                Wrappers.lambdaQuery(CxrResidentialQuarters.class)
                    .eq(CxrResidentialQuarters::getName, cxrCustomerBo.getCxrResidentialQuartersName())
                    .eq(CxrResidentialQuarters::getSysAreaId, cxrCustomerBo.getSysAreaId())
                    .eq(CxrResidentialQuarters::getDetailAddress, cxrCustomerBo.getDetailDistributionAddress())
            );
            if (CollUtil.isNotEmpty(data)) {
                CxrResidentialQuarters cxrResidentialQuarters = data.get(0);
                // 客户地址信息绑定小区
                cxrCustomerAddress.setCxrResidentialQuartersId(cxrResidentialQuarters.getId());

                // 修改户地址信息
                updateCustomerAddress(cxrCustomerAddress, cxrCustomerBo);
            } else {
                // 新增小区
                CxrResidentialQuarters cxrResidentialQuarters = new CxrResidentialQuarters();
                cxrResidentialQuarters.setName(cxrCustomerBo.getCxrResidentialQuartersName());
                cxrResidentialQuarters.setSysAreaId(cxrCustomerBo.getSysAreaId());
                cxrResidentialQuarters.setDetailAddress(cxrCustomerBo.getDetailDistributionAddress());
                boolean residentialQuartersStatus = cxrResidentialQuartersMapper.insert(cxrResidentialQuarters) > 0;
                if (residentialQuartersStatus) {
                    // 客户地址信息绑定小区
                    cxrCustomerAddress.setCxrResidentialQuartersId(cxrResidentialQuarters.getId());

                    // 修改户地址信息
                    updateCustomerAddress(cxrCustomerAddress, cxrCustomerBo);
                } else {
                    throw new ServiceException("新增小区失败");
                }
            }

        }
        // 其它情况，不新增小区，也不绑定小区
        else {
            // 修改客户地址信息
            updateCustomerAddress(cxrCustomerAddress, cxrCustomerBo);
        }

        //根据 cxrCustomerAddress 修改配送表数据
        CxrSite site = iCxrSiteService.getById(cxrCustomerAddress.getCxrSiteId());
        CxrEmployee cxrEmployee = iCxrEmployeeService.getById(cxrCustomerAddress.getCxrEmployeeId());

        return true;

    }

    //    // 修改客户地址信息
    public Boolean updateCustomerAddress(CxrCustomerAddress cxrCustomerAddress, CxrCustomerBo cxrCustomerBo) {

        CxrCustomerAddress data = cxrCustomerAddressMapper.selectById(cxrCustomerAddress.getId());
        cxrCustomerAddress.setRevision(Convert.toLong(data.getRevision()) + 1);

        boolean customerAddressStatus = SqlHelper.retBool(cxrCustomerAddressMapper.updateById(cxrCustomerAddress));
        if (customerAddressStatus) {

            mqUtil.sendSyncMessage(GetLngLatConstant.GETLNGLAG_TOPIC,
                GetLngLatConstant.GETLNGLAG_LNGLAG_TAG,
                JSONUtil.toJsonStr(data));

            return customerAddressStatus ? true : false;
        } else {
            throw new ServiceException("修改客户地址信息失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(Set<Long> idSet) {

        //判断客户是否存在排奶计划
        List<CxrCustomerAddress> cxrCustomerAddresses = cxrCustomerAddressMapper.selectAddressByHave(idSet,
            SysYesNo.YES.getValue());
        //有排奶记录的也要进行逻辑的删除
        customerChangeRecordService.update(null, new LambdaUpdateWrapper<CxrCustomerChangeRecord>()
            .in(CxrCustomerChangeRecord::getCustomerAddressId, idSet)
            .eq(CxrCustomerChangeRecord::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            .set(CxrCustomerChangeRecord::getDeleteStatus, DeleteStatus.DELETED.getValue()));
        // 客户地址信息逻辑删除
        CxrCustomerAddress cxrCustomerAddress = new CxrCustomerAddress();
        cxrCustomerAddress.setDeleteStatus(DeleteStatus.DELETED.getValue());
        boolean b =
            cxrCustomerAddressMapper.update(cxrCustomerAddress, new LambdaUpdateWrapper<>(CxrCustomerAddress.class).
                in(CxrCustomerAddress::getId, idSet)) > 0;
        if (b) {
            //
            List<CxrCustomerAddress> cxrCustomerAddressList = cxrCustomerAddressMapper.selectBatchIds(idSet);
            for (CxrCustomerAddress customerAddress : cxrCustomerAddressList) {
                //保存历史
                AddressHistoryMqDoMain addressHistoryMqDoMain = new AddressHistoryMqDoMain();
                addressHistoryMqDoMain.setCxrCustomerAddressId(customerAddress.getId());
                addressHistoryMqDoMain.setCxrCustomerId(customerAddress.getCxrCustomerId());
                AlterRecord alterRecord = new AlterRecord();
                CustomerAddressUpdateDTO customerAddressUpdateDTO = new CustomerAddressUpdateDTO();
                customerAddressUpdateDTO.setUpdateType(CustomerAddressUpdateType.DELETE.getValue());
                customerAddressUpdateDTO.setAddress(
                    Convert.toStr(customerAddress.getProvice(), "") + Convert.toStr(customerAddress.getCity(), "") + Convert.toStr(customerAddress.getArea(), "")
                        + Convert.toStr(customerAddress.getDetailDistributionAddress(), ""));
                alterRecord.setCustomerAddressUpdate(customerAddressUpdateDTO);
                addressHistoryMqDoMain.setAlterRecord(alterRecord);
                addressHistoryMqDoMain.setLoginInfo(LoginUtil.getLoginUser());
                addressHistoryMqDoMain.setUuid(java.util.UUID.randomUUID().toString());
                mqUtil.sendSyncMessageTransaction(CustomerAddressConstant.CUSTOMER_ADDRESS_TOPIC,
                    CustomerAddressConstant.CUSTOMER_ADDRESS_HISTORY_TAG, JSONUtil.toJsonStr(addressHistoryMqDoMain));
            }

        }
        return b;
    }

    @Override
    public CxrCustomerVo customerLogin(String phone) {

        CxrCustomerVo cxrCustomerVo = baseMapper.selectVoOne(new LambdaQueryWrapper<CxrCustomer>()
            .eq(CxrCustomer::getPhone, phone)
            .eq(CxrCustomer::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));

        return cxrCustomerVo;
    }

    @Override
    public CxrCustomerVo customerLogin(Long id) {
        CxrCustomerVo cxrCustomerVo = baseMapper.selectVoOne(new LambdaQueryWrapper<CxrCustomer>()
            .eq(CxrCustomer::getId, id)
            .eq(CxrCustomer::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));
        return cxrCustomerVo;
    }

    @Override
    public CxrCustomer selectByWxOpenid(String wxOpenid) {

        CxrCustomer cxrCustomer = baseMapper.selectOne(new LambdaQueryWrapper<CxrCustomer>()
            .eq(CxrCustomer::getWxOpenId, wxOpenid)
            .eq(CxrCustomer::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));

        return cxrCustomer;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateByWxOpenid(String wxOpenId, String wxNickname, String wxHeadPortrait) {

        CxrCustomer cxrCustomer = new CxrCustomer();
        cxrCustomer.setWxOpenId(wxOpenId);
        cxrCustomer.setWxNickname(wxNickname);
        cxrCustomer.setWxHeadPortrait(wxHeadPortrait);

        return baseMapper.update(cxrCustomer, new LambdaUpdateWrapper<CxrCustomer>()
            .eq(CxrCustomer::getWxOpenId, cxrCustomer.getWxOpenId())
            .set(CxrCustomer::getWxNickname, cxrCustomer.getWxNickname())
            .set(CxrCustomer::getWxHeadPortrait, cxrCustomer.getWxHeadPortrait())
            .eq(CxrCustomer::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUnionIDByWxOpenid(String wxOpenId, String wxNickname, String wxHeadPortrait,
                                           String wxUnionid) {

        return baseMapper.update(null, new LambdaUpdateWrapper<CxrCustomer>()
            .eq(CxrCustomer::getWxOpenId, wxOpenId)
            .set(CxrCustomer::getWxNickname, wxNickname)
            .set(CxrCustomer::getWxHeadPortrait, wxHeadPortrait)
            .set(CxrCustomer::getWxUnionId, wxUnionid)
            .eq(CxrCustomer::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOpenidByPhone(String phone, String wxOpenId, String wxNickname, String wxHeadPortrait) {

        CxrCustomer cxrCustomer = new CxrCustomer();
        cxrCustomer.setPhone(phone);
        cxrCustomer.setWxOpenId(wxOpenId);
        cxrCustomer.setWxNickname(wxNickname);
        cxrCustomer.setWxHeadPortrait(wxHeadPortrait);

        return baseMapper.update(cxrCustomer, new LambdaUpdateWrapper<CxrCustomer>()
            .eq(CxrCustomer::getPhone, cxrCustomer.getPhone())
            .set(CxrCustomer::getWxOpenId, cxrCustomer.getWxOpenId())
            .set(CxrCustomer::getWxNickname, cxrCustomer.getWxNickname())
            .set(CxrCustomer::getWxHeadPortrait, cxrCustomer.getWxHeadPortrait())
            .eq(CxrCustomer::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUnionOpenidByPhone(String phone, String wxOpenId, String wxNickname, String wxHeadPortrait,
                                            String wxUnionid) {

        CxrCustomer cxrCustomer = new CxrCustomer();
        cxrCustomer.setPhone(phone);
        cxrCustomer.setWxOpenId(wxOpenId);
        cxrCustomer.setWxNickname(wxNickname);
        cxrCustomer.setWxHeadPortrait(wxHeadPortrait);
        cxrCustomer.setWxUnionId(wxUnionid);

        return baseMapper.update(cxrCustomer, new LambdaUpdateWrapper<CxrCustomer>()
            .eq(CxrCustomer::getPhone, cxrCustomer.getPhone())
            .set(CxrCustomer::getWxOpenId, cxrCustomer.getWxOpenId())
            .set(CxrCustomer::getWxNickname, cxrCustomer.getWxNickname())
            .set(CxrCustomer::getWxHeadPortrait, cxrCustomer.getWxHeadPortrait())
            .set(CxrCustomer::getWxUnionId, cxrCustomer.getWxUnionId())
            .eq(CxrCustomer::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOpenidByPhone(String phone, String wxOpenId) {

        CxrCustomer cxrCustomer = new CxrCustomer();
        cxrCustomer.setPhone(phone);
        cxrCustomer.setWxOpenId(wxOpenId);
        return baseMapper.update(cxrCustomer, new LambdaUpdateWrapper<CxrCustomer>()
            .eq(CxrCustomer::getPhone, cxrCustomer.getPhone())
            .set(CxrCustomer::getWxOpenId, cxrCustomer.getWxOpenId())
            .eq(CxrCustomer::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateName(@Param("phone") String phone, @Param("name") String name) {
        CxrCustomer cxrCustomer = new CxrCustomer();
        cxrCustomer.setPhone(phone);
        cxrCustomer.setName(name);
        return baseMapper.update(cxrCustomer, new LambdaUpdateWrapper<CxrCustomer>()
            .eq(CxrCustomer::getPhone, cxrCustomer.getPhone())
            .set(CxrCustomer::getName, cxrCustomer.getName())) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStock(@Param("phone") String phone, @Param("stock") Integer stock) {
        CxrCustomer cxrCustomer = new CxrCustomer();
        return baseMapper.update(cxrCustomer, new LambdaUpdateWrapper<CxrCustomer>()
            .eq(CxrCustomer::getPhone, phone)
            .set(CxrCustomer::getCustomerStock, stock)
            .set(stock == 0, CxrCustomer::getCustomerStockZeroTime, new Date())
        ) > 0;
    }

    /**
     * 检查客户手机号是否存在
     *
     * @param customerPhone
     * @return
     */
    @Override
    public boolean checkExistCustomerPhone(String customerPhone) {

        return baseMapper.exists(new LambdaQueryWrapper<CxrCustomer>()
            .eq(CxrCustomer::getPhone, customerPhone)
            .eq(CxrCustomer::getDeleteStatus, DeleteStatus.not_deleted));

    }

    /**
     * 根据手机号查询客户信息
     *
     * @return
     */
    @Override
    public CxrCustomer queryByPhone(String phone) {
        CxrCustomer cxrCustomer = baseMapper.selectOne(new LambdaQueryWrapper<CxrCustomer>()
            .eq(CxrCustomer::getPhone, phone)
            .eq(CxrCustomer::getDeleteStatus, DeleteStatus.not_deleted));

        return cxrCustomer;
    }

    /**
     * 更新主账户信息
     *
     * @param distributioninfoDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateMainAccount(CustomerDistributioninfoDTO distributioninfoDTO) {

        CxrCustomer cxrCustomer =
            getBaseMapper().selectOne(new LambdaQueryWrapper<CxrCustomer>().eq(CxrCustomer::getPhone,
                distributioninfoDTO.getCustomerPhone()));
        if (ObjectUtil.isNotNull(cxrCustomer)) {

            LambdaUpdateWrapper<CxrCustomer> wrapper = new LambdaUpdateWrapper<CxrCustomer>()
                .set(CxrCustomer::getName, distributioninfoDTO.getCustomerName())
                .eq(CxrCustomer::getPhone, distributioninfoDTO.getCustomerPhone());
            if (ObjectUtil.isNotNull(distributioninfoDTO.getSysAreaId()) && ObjectUtil.isNull(
                cxrCustomer.getSysAreaId())) {
                AreaDTO areaDTO = remoteAreaService.queryMergenameArrById(distributioninfoDTO.getSysAreaId());
                if (ObjectUtil.isNull(areaDTO)) {
                    throw new ServiceException("地址区域信息不存在");
                }
//                wrapper.set(CxrCustomer::getSysAreaId,distributioninfoDTO.getSysAreaId());
                wrapper.set(CxrCustomer::getProvice, areaDTO.getProvice());
                wrapper.set(CxrCustomer::getCity, areaDTO.getCity());
                wrapper.set(CxrCustomer::getArea, areaDTO.getArea());
            }
            boolean flag = this.update(null, wrapper);
            return flag;
        }
        return false;
    }


    /**
     * 分页条件查询
     *
     * @param bo
     * @return
     */
    @Override
    public PageTableDataInfo<CxrCustomerListVo> customerPage(CxrCustomerAddressABo bo) {
        //以客户为单位
        IPage<CxrCustomerListVo> cxrCustomerListVo = null;
        //以地址为单位
        Page<CxrCustomerAddress> cxrCustomerAddressPage = null;

        //最终
        IPage<CxrCustomerListVo> cxrCustomerListVoIPage = null;
        Long quantity = 0L;
        Long originStockCountTotal = 0L;
        Long orderCountTotal = 0L;
        Long freshMilkRoadWaySentCountTotal = 0L;
        Long freshMilkSentCountTotal = 0L;
        if (StringUtils.isNotBlank(bo.getDefalutAccountAddress())) {//客户默认地址

            List<Long> siteIds = null;
            //站点
            if (StringUtils.isNotBlank(bo.getProvice()) || StringUtils.isNotBlank(bo.getCity())
                || StringUtils.isNotBlank(bo.getArea()) || StringUtils.isNotBlank(bo.getBigAreaName())
                || StringUtils.isNotBlank(bo.getCxrSiteName())) {
                List<CxrSite> cxrSites = iCxrSiteService.getBaseMapper().selectList(new LambdaQueryWrapper<CxrSite>()
                    .like(StringUtils.isNotBlank(bo.getProvice()), CxrSite::getProvice, bo.getProvice())
                    .like(StringUtils.isNotBlank(bo.getCity()), CxrSite::getCity, bo.getCity())
                    .like(StringUtils.isNotBlank(bo.getArea()), CxrSite::getArea, bo.getArea())
                    .like(StringUtils.isNotBlank(bo.getBigAreaName()), CxrSite::getCxrRootRegionName,
                        bo.getBigAreaName())
                    .like(StringUtils.isNotBlank(bo.getCxrSiteName()), CxrSite::getName, bo.getCxrSiteName())
                );
                if (CollectionUtil.isEmpty(cxrSites) || cxrSites.size() < 1) {
                    List<CxrCustomerListVo> list = new ArrayList<>();
                    return new PageTableDataInfo(list, 0);
                }
                siteIds = cxrSites.stream().map(CxrSite::getId).collect(Collectors.toList());
            }

            //解决深度翻页问题
            Page<Object> page = bo.build();
            List<Long> siteIdList = siteIds;
            CompletableFuture<CountAndSumQuantity> sumQuantityFuture = CompletableFuture.supplyAsync(() -> baseMapper.countCustomerTwoV2(siteIdList, bo, DeleteStatus.NOT_DELETED.getValue()));

            //通过 offset   Size  自己分页 减少回表率
            // 因为不支持 select * from cxrcustomer   where  id  in  (select id from  cxrcustomer  limit  ?)
            //所以使用 select * from cxrcustomer   inner join   (select id from  cxrcustomer  limit  ?) temp using (id);
            List<CxrCustomerListVo> customerListVos = baseMapper.customerPageTwo(siteIds, bo,
                DeleteStatus.NOT_DELETED.getValue(),
                page.offset(), page.getSize());

            //本来就要sum 库存  顺便count total
            CountAndSumQuantity sumQuantity = sumQuantityFuture.join();
            //组装数据
            cxrCustomerListVo = new Page<CxrCustomerListVo>(bo.getPageNum(), bo.getPageSize(),
                sumQuantity.getCountQuantity());
            cxrCustomerListVo.setRecords(customerListVos);

            quantity = sumQuantity.getSumQuantity();

            orderCountTotal = sumQuantity.getOrderCountTotal();
            originStockCountTotal = ObjectUtil.defaultIfNull(sumQuantity.getOriginStockCountTotal(), 0L);
            freshMilkSentCountTotal = ObjectUtil.defaultIfNull(sumQuantity.getFreshMilkSentTotal(), 0L);
            freshMilkRoadWaySentCountTotal = ObjectUtil.defaultIfNull(sumQuantity.getFreshMilkRoadWaySentTotal(), 0L);

        } else if (ObjectUtil.isNotEmpty(bo.getId())) {
            //详情分页
            List<CxrCustomerAddress> cxrCustomerAddresses = iCxrCustomerAddressService.getBaseMapper()
                .selectList(new LambdaQueryWrapper<CxrCustomerAddress>()
                    .eq(CxrCustomerAddress::getCxrCustomerId, bo.getId())
                    .eq(CxrCustomerAddress::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                );

            cxrCustomerAddressPage = new Page<>();
            cxrCustomerAddressPage.setRecords(cxrCustomerAddresses);
        } else {
//            地址分页
            List<Long> siteIds = null;
            //站点
            if (StringUtils.isNotBlank(bo.getProvice()) || StringUtils.isNotBlank(bo.getCity())
                || StringUtils.isNotBlank(bo.getArea()) || StringUtils.isNotBlank(bo.getBigAreaName())
                || StringUtils.isNotBlank(bo.getCxrSiteName())) {
                List<CxrSite> cxrSites = iCxrSiteService.getBaseMapper().selectList(new LambdaQueryWrapper<CxrSite>()
                    .like(StringUtils.isNotBlank(bo.getProvice()), CxrSite::getProvice, bo.getProvice())
                    .like(StringUtils.isNotBlank(bo.getCity()), CxrSite::getCity, bo.getCity())
                    .like(StringUtils.isNotBlank(bo.getArea()), CxrSite::getArea, bo.getArea())
                    .like(StringUtils.isNotBlank(bo.getBigAreaName()), CxrSite::getCxrRootRegionName,
                        bo.getBigAreaName())
                    .like(StringUtils.isNotBlank(bo.getCxrSiteName()), CxrSite::getName, bo.getCxrSiteName())
                );
                if (CollectionUtil.isEmpty(cxrSites) || cxrSites.size() < 1) {
                    List<CxrCustomerListVo> list = new ArrayList<>();
                    return new PageTableDataInfo(list, 0);
                }
                siteIds = cxrSites.stream().map(CxrSite::getId).collect(Collectors.toList());
            }
            //配送员
            List<Long> employeeIds = null;
            if (StringUtils.isNotBlank(bo.getEmployeeName())) {
                List<CxrEmployee> cxrEmployees = iCxrEmployeeService.getBaseMapper()
                    .selectList(new LambdaQueryWrapper<CxrEmployee>()
                        .like(CxrEmployee::getName, bo.getEmployeeName())
                    );
                if (CollectionUtil.isEmpty(cxrEmployees) || cxrEmployees.size() < 1) {
                    List<CxrCustomerListVo> list = new ArrayList<>();
                    return new PageTableDataInfo(list, 0);
                }
                employeeIds = cxrEmployees.stream().map(CxrEmployee::getId).collect(Collectors.toList());
            }
            //地址
            cxrCustomerAddressPage = iCxrCustomerAddressService.getBaseMapper()
                .selectPage(bo.build(), new LambdaQueryWrapper<CxrCustomerAddress>()
                    .like(StringUtils.isNotBlank(bo.getCustomerAdress()),
                        CxrCustomerAddress::getDetailDistributionAddress, bo.getCustomerAdress())
                    .likeRight(StringUtils.isNotBlank(bo.getReceiverPhone()), CxrCustomerAddress::getReceiverPhone,
                        bo.getReceiverPhone())
                    .like(StringUtils.isNotBlank(bo.getName()), CxrCustomerAddress::getReceiverName, bo.getName())
                    .likeRight(StringUtils.isNotBlank(bo.getPhone()), CxrCustomerAddress::getReceiverPhone,
                        bo.getPhone())
                    .in(CollectionUtil.isNotEmpty(employeeIds) && employeeIds.size() > 0,
                        CxrCustomerAddress::getCxrEmployeeId, employeeIds)
                    .in(CollectionUtil.isNotEmpty(siteIds) && siteIds.size() > 0, CxrCustomerAddress::getCxrSiteId,
                        siteIds)
                    .eq(CxrCustomerAddress::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                );


        }

        //处理数据
        if (ObjectUtil.isNotEmpty(cxrCustomerListVo) && CollectionUtil.isNotEmpty(cxrCustomerListVo.getRecords())
            && cxrCustomerListVo.getRecords().size() > 0) {
            //客户地址
            List<Long> cusIds = cxrCustomerListVo.getRecords().stream().map(CxrCustomerListVo::getId)
                .collect(Collectors.toList());
            cusIds.add(-1L);
            List<CxrCustomerAddress> cxrCustomerAddresses = iCxrCustomerAddressService.getBaseMapper()
                .selectList(new LambdaQueryWrapper<CxrCustomerAddress>()
                        .in(CxrCustomerAddress::getCxrCustomerId, cusIds)
                        .eq(CxrCustomerAddress::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
//                    .eq(CxrCustomerAddress::getDefalutAccountAddress, SysYesNo.YES.getValue())
                );
            Map<Long, CxrCustomerAddress> addressMap = cxrCustomerAddresses.stream().filter(a -> StrUtil.equals(SysYesNo.YES.getValue(), a.getDefalutAccountAddress()))
                .collect(Collectors.toMap(CxrCustomerAddress::getCxrCustomerId, Function.identity(), (v1, v2) -> v2));
            Map<Long, List<CxrCustomerAddress>> allAddressesSiteMap = cxrCustomerAddresses.stream()
                .collect(Collectors.groupingBy(CxrCustomerAddress::getCxrCustomerId));
            //员工
            List<Long> emIds = cxrCustomerAddresses.stream().map(CxrCustomerAddress::getCxrEmployeeId)
                .collect(Collectors.toList());
            emIds.add(-1L);
            List<CxrEmployee> cxrEmployees = iCxrEmployeeService.getBaseMapper()
                .selectList(new LambdaQueryWrapper<CxrEmployee>()
                    .in(CxrEmployee::getId, emIds)
                );
            Map<Long, CxrEmployee> employeeMap = cxrEmployees.stream()
                .collect(Collectors.toMap(CxrEmployee::getId, Function.identity(), (v1, v2) -> v2));

            //站点
            List<Long> cxrSiteIds = cxrCustomerAddresses.stream().map(CxrCustomerAddress::getCxrSiteId)
                .collect(Collectors.toList());
            cxrSiteIds.add(-1L);
            List<CxrSite> cxrSites = iCxrSiteService.getBaseMapper().selectList(new LambdaQueryWrapper<CxrSite>()
                .in(CxrSite::getId, cxrSiteIds)
            );
            Map<Long, CxrSite> siteMap = cxrSites.stream()
                .collect(Collectors.toMap(CxrSite::getId, Function.identity(), (v1, v2) -> v2));

            List<CxrCustomerRemark> customerRemarkList =
                cxrCustomerRemarkMapper.selectList(new LambdaQueryWrapper<CxrCustomerRemark>()
                    .in(CxrCustomerRemark::getCxrCustomerId, cusIds)
                    .eq(CxrCustomerRemark::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                    .orderByDesc(CxrCustomerRemark::getUpdateTime)
                    .last("limit 1")
                );

            Map<Long, CxrCustomerRemark> remarkMap =
                customerRemarkList.stream().collect(Collectors.toMap(CxrCustomerRemark::getCxrCustomerId,
                    Function.identity(), (v1, v2) -> v2));

            List<MallUserEntity> mallUserList = mallUserMapper.selectList(new LambdaQueryWrapper<MallUserEntity>()
                .select(MallUserEntity::getErpId, MallUserEntity::getOpenId, MallUserEntity::getUnionId)
                .in(MallUserEntity::getErpId, cusIds)
            );

            Map<Long, MallUserEntity> mallUserMap =
                mallUserList.stream().collect(Collectors.toMap(MallUserEntity::getErpId,
                    Function.identity(), (v1, v2) -> v2));

            //主账户
            List<CompletableFuture<CxrCustomerListVo>> completableFutures = cxrCustomerListVo.getRecords().stream()
                .map(s -> {
                    return CompletableFuture.supplyAsync(() -> {
                        Integer freshMilkRoadWaySentTotal = s.getFreshMilkRoadWaySentTotal();
                        Integer freshMilkSentTotal = s.getFreshMilkSentTotal();
                        s.setFreshMilkRoadWaySendAndOrderSend(freshMilkSentTotal + freshMilkRoadWaySentTotal);
                        if (CollectionUtil.isNotEmpty(addressMap)) {
                            CxrCustomerAddress address = addressMap.get(s.getId());
                            if (ObjectUtil.isNotEmpty(address)) {
                                s.setCxrEmployeeId(address.getCxrEmployeeId());
                                if (CollectionUtil.isNotEmpty(employeeMap)) {
                                    CxrEmployee cxrEmployee = employeeMap.get(address.getCxrEmployeeId());
                                    if (ObjectUtil.isNotEmpty(cxrEmployee)) {
                                        s.setEmployeeName(cxrEmployee.getName());
                                        s.setCxrEmployeeJobNumber(cxrEmployee.getJobNumber());
                                        s.setCxrEmployeeLevelType(cxrEmployee.getEmployeeLevelType());
                                    }
                                }
                                if (CollectionUtil.isNotEmpty(siteMap)) {
                                    CxrSite cxrSite = siteMap.get(address.getCxrSiteId());
                                    if (ObjectUtil.isNotEmpty(cxrSite)) {
                                        s.setCxrSiteId(address.getCxrSiteId());
                                        s.setProvice(cxrSite.getProvice());
                                        s.setCity(cxrSite.getCity());
                                        s.setArea(cxrSite.getArea());
                                        s.setBigAreaName(cxrSite.getCxrRootRegionName());
                                        s.setSiteName(cxrSite.getName());
                                        s.setDetailAddress(cxrSite.getDetailAddress());
                                        s.setSiteMark(cxrSite.getSiteMark());
                                    }
                                }

                                s.setCustomerAdress(address.getDetailDistributionAddress());
                                s.setCxrCustomerAddressId(address.getId());
                                s.setAddressCreateTime(address.getCreateTime());
                                s.setDefalutAccountAddress(address.getDefalutAccountAddress());
                                s.setSysAreaId(address.getSysAreaId());
                                s.setReceiverName(address.getReceiverName());
                                s.setReceiverPhone(address.getReceiverPhone());

                                s.setAddressCreateByType(address.getCreateByType());
                            }
                        }
                        if (CollectionUtil.isNotEmpty(remarkMap)) {
                            CxrCustomerRemark cxrRemarkMap = remarkMap.get(s.getId());
                            if (ObjectUtil.isNotEmpty(cxrRemarkMap)) {
                                s.setRemark(cxrRemarkMap.getRemark());
                            }
                        }
                        if (CollectionUtil.isNotEmpty(mallUserMap)) {
                            MallUserEntity mallUserEntity = mallUserMap.get(s.getId());
                            if (ObjectUtil.isNotEmpty(mallUserEntity)) {
                                s.setXcxWxOpenId(mallUserEntity.getOpenId());
                                s.setXcxWxOpenId(mallUserEntity.getUnionId());
                            }
                        }

                        if (CollectionUtil.isNotEmpty(allAddressesSiteMap)) {
                            List<CxrCustomerAddress> addressList = allAddressesSiteMap.get(s.getId());
                            if (CollUtil.isNotEmpty(addressList)) {
                                List<DeliverySiteDTO> siteDTO = new ArrayList<>();
                                Set<Long> siteIdCustomerAddress = addressList.stream().map(CxrCustomerAddress::getCxrSiteId).collect(Collectors.toSet());
                                for (Long siteId : siteIdCustomerAddress) {
                                    DeliverySiteDTO dto = new DeliverySiteDTO();
                                    if (CollectionUtil.isNotEmpty(siteMap)) {
                                        CxrSite cxrSite = siteMap.get(siteId);
                                        dto.setSiteId(siteId);
                                        if (ObjectUtil.isNotEmpty(cxrSite)) {
                                            dto.setSiteName(cxrSite.getName());
                                        }
                                        siteDTO.add(dto);
                                    }
                                }
                                s.setDeliverySiteDTOS(CollUtil.isNotEmpty(siteDTO) ? siteDTO : null);
                            }
                        }

                        return s;
                    });
                }).collect(Collectors.toList());

            CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).join();
            List<CxrCustomerListVo> customerListVos = completableFutures.stream().map(CompletableFuture::join)
                .collect(Collectors.toList());

            cxrCustomerListVo.setRecords(customerListVos);
            cxrCustomerListVoIPage = cxrCustomerListVo;
        } else if (ObjectUtil.isNotEmpty(cxrCustomerAddressPage) && CollectionUtil.isNotEmpty(
            cxrCustomerAddressPage.getRecords()) && cxrCustomerAddressPage.getRecords().size() > 0) {
            //以地址为单位的
            List<CxrCustomerAddress> records = cxrCustomerAddressPage.getRecords();
            if (CollectionUtil.isNotEmpty(records)) {

                //员工
                List<Long> emIds = records.stream().map(CxrCustomerAddress::getCxrEmployeeId)
                    .collect(Collectors.toList());
                List<CxrEmployee> cxrEmployees = iCxrEmployeeService.getBaseMapper()
                    .selectList(new LambdaQueryWrapper<CxrEmployee>()
                        .in(CxrEmployee::getId, emIds)
                    );

                Map<Long, CxrEmployee> employeeMap = cxrEmployees.stream()
                    .collect(Collectors.toMap(CxrEmployee::getId, Function.identity(), (v1, v2) -> v2));

                //站点
                List<Long> cxrSiteIds = records.stream().map(CxrCustomerAddress::getCxrSiteId)
                    .collect(Collectors.toList());
                List<CxrSite> cxrSites = iCxrSiteService.getBaseMapper().selectList(new LambdaQueryWrapper<CxrSite>()
                    .in(CxrSite::getId, cxrSiteIds)
                );
                Map<Long, CxrSite> siteMap = cxrSites.stream()
                    .collect(Collectors.toMap(CxrSite::getId, Function.identity(), (v1, v2) -> v2));
                //主账户
                List<Long> cusId = records.stream().map(CxrCustomerAddress::getCxrCustomerId)
                    .collect(Collectors.toList());
                List<CxrCustomer> cxrCustomers = baseMapper.selectList(new LambdaQueryWrapper<CxrCustomer>()
                    .in(CxrCustomer::getId, cusId));
                Map<Long, CxrCustomer> customerMap = cxrCustomers.stream()
                    .collect(Collectors.toMap(CxrCustomer::getId, Function.identity(), (v1, v2) -> v2));

                List<CompletableFuture<CxrCustomerListVo>> completableFutures = records.stream()
                    .map(s -> CompletableFuture.supplyAsync(() -> {
                        CxrCustomerListVo vo = new CxrCustomerListVo();

                        if (CollectionUtil.isNotEmpty(customerMap)) {
                            CxrCustomer cxrCustomer = customerMap.get(s.getCxrCustomerId());
                            if (ObjectUtil.isNotEmpty(cxrCustomer)) {
                                vo.setId(cxrCustomer.getId());
                                vo.setName(cxrCustomer.getName());
                                vo.setPhone(cxrCustomer.getPhone());
                                vo.setCustomerStock(cxrCustomer.getCustomerStock());
                                vo.setFreshMilkOrderTotal(cxrCustomer.getFreshMilkOrderTotal());
                                vo.setFreshMilkGiveTotal(cxrCustomer.getFreshMilkGiveTotal());
                                vo.setFreshMilkSentTotal(cxrCustomer.getFreshMilkSentTotal());
                                vo.setLongMilkGiveTotal(cxrCustomer.getLongMilkGiveTotal());
                                vo.setFreshMilkRoadWaySentTotal(cxrCustomer.getFreshMilkRoadWaySentTotal());
                                vo.setOriginStock(cxrCustomer.getOriginStock().intValue());
                                vo.setFreshMilkRoadWaySendAndOrderSend(
                                    vo.getFreshMilkRoadWaySentTotal() + vo.getFreshMilkSentTotal());
                            }
                        }

                        if (CollectionUtil.isNotEmpty(employeeMap)) {
                            CxrEmployee cxrEmployee = employeeMap.get(s.getCxrEmployeeId());
                            if (ObjectUtil.isNotEmpty(cxrEmployee)) {
                                vo.setEmployeeName(cxrEmployee.getName());
                                vo.setCxrEmployeeId(cxrEmployee.getId());
                                vo.setCxrEmployeeLevelType(cxrEmployee.getEmployeeLevelType());
                                vo.setCxrEmployeeJobNumber(cxrEmployee.getJobNumber());
                            }
                        }

                        vo.setCxrCustomerAddressId(s.getId());
                        vo.setReceiverName(s.getReceiverName());
                        vo.setReceiverPhone(s.getReceiverPhone());
                        vo.setAddressCreateTime(s.getCreateTime());
                        vo.setProvice(Convert.toStr(s.getProvice(), ""));
                        vo.setCity(Convert.toStr(s.getCity(), ""));
                        vo.setArea(Convert.toStr(s.getArea(), ""));
                        if (CollectionUtil.isNotEmpty(siteMap)) {
                            CxrSite cxrSite = siteMap.get(s.getCxrSiteId());
                            if (ObjectUtil.isNotEmpty(cxrSite)) {
                                vo.setSiteName(cxrSite.getName());
                                vo.setDetailAddress(cxrSite.getDetailAddress());
                                vo.setSiteMark(cxrSite.getSiteMark());
                                vo.setCxrSiteId(cxrSite.getId());
                                vo.setBigAreaName(cxrSite.getCxrRootRegionName());
                            }
                        }

                        vo.setUpdateTime(s.getUpdateTime());
                        vo.setUpdateByName(s.getUpdateByName());
                        vo.setCustomerAdress(s.getDetailDistributionAddress());
                        vo.setDefalutAccountAddress(s.getDefalutAccountAddress());
                        vo.setSysAreaId(Convert.toLong(s.getSysAreaId(), -1L));
                        vo.setRemark(s.getRemark());
                        vo.setCreateByType(s.getCreateByType());
                        vo.setAddressCreateByType(s.getCreateByType());

                        return vo;
                    })).collect(Collectors.toList());
                CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).join();
                List<CxrCustomerListVo> customerListVos = completableFutures.stream().map(CompletableFuture::join)
                    .collect(Collectors.toList());
                cxrCustomerListVo = new Page<CxrCustomerListVo>();
                cxrCustomerListVo.setRecords(customerListVos);
                cxrCustomerListVo.setTotal(cxrCustomerAddressPage.getTotal());
                cxrCustomerListVoIPage = cxrCustomerListVo;

                //当根据客户id去查询的时候  将默认地址放前面
                if (bo.getId() != null) {
                    List<CxrCustomerListVo> rs = cxrCustomerListVoIPage.getRecords();
                    rs = rs.stream()
                        .sorted(Comparator.comparing(CxrCustomerListVo::getDefalutAccountAddress).reversed())
                        .collect(Collectors.toList());
                    cxrCustomerListVoIPage.setRecords(rs);
                }
            }
        }

        if (ObjectUtil.isEmpty(cxrCustomerListVoIPage)) {
            cxrCustomerListVoIPage = new Page<>();
        }
        PageTableDataInfo<CxrCustomerListVo> dataInfo = PageTableDataInfo.build(cxrCustomerListVoIPage);
        dataInfo.setCurr(bo.getPageNum());
        dataInfo.setSize(bo.getPageSize());
        dataInfo.setTotalQuantity(quantity);//客户库存数
        dataInfo.setOriginStockCountTotal(originStockCountTotal);//初始库存数
        dataInfo.setFreshMilkRoadWaySendAndOrderSendTotal(
            freshMilkSentCountTotal + freshMilkRoadWaySentCountTotal);//已送数
        dataInfo.setOrderCountTotal(orderCountTotal);//累计订购数
        return dataInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addressAdd(CxrCustomerAddrBo bo) {
        return iCxrCustomerAddressService.add(bo);
    }

    @Override
    public CxrCustomerAddrVo adressDetail(Long id) {
        return cxrCustomerAddressMapper.selectAddressById(id, DeleteStatus.NOT_DELETED.getValue());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean editAdress(CxrCustomerAddrBo bo) {

        //插入原本的
        CxrCustomerAddress Address = iCxrCustomerAddressService.getById(bo.getId());
        if (Address == null) {
            throw new ServiceException("您修改的地址不存在 !");
        }

        //修改地址将 排奶滞空修改
        setNullMilks(bo);

        CxrCustomerAddress cxrCustomerAddress = BeanUtil.copyProperties(bo, CxrCustomerAddress.class);
        boolean b = cxrCustomerAddressMapper.updateById(cxrCustomerAddress) > 0;

        if (b) {    //不需要了
            //保存历史
            AddressHistoryMqDoMain addressHistoryMqDoMain = new AddressHistoryMqDoMain();
            addressHistoryMqDoMain.setThisAddress(
                Convert.toStr(bo.getProvice(), "") + Convert.toStr(bo.getCity(), "") + Convert.toStr(bo.getArea(), "")
                    + Convert.toStr(bo.getDetailDistributionAddress(), ""));
            addressHistoryMqDoMain.setCxrCustomerAddressId(bo.getId());
            addressHistoryMqDoMain.setCxrCustomerId(Address.getCxrCustomerId());
            addressHistoryMqDoMain.setAlterRecord(bo.getAlterRecord());
            addressHistoryMqDoMain.setLoginInfo(LoginUtil.getLoginUser());
            addressHistoryMqDoMain.setUuid(UUID.randomUUID().toString());
            mqUtil.sendSyncMessage(CustomerAddressConstant.CUSTOMER_ADDRESS_TOPIC,
                CustomerAddressConstant.CUSTOMER_ADDRESS_HISTORY_TAG, JSONUtil.toJsonStr(addressHistoryMqDoMain));

            //解析
            getCustomerAddressLngLat(cxrCustomerAddress, Address);

        }
//        直接插入  多线程直接将没更新的 插入到里面了
//        CxrCustomerAddress address = iCxrCustomerAddressService.getById(cxrCustomerAddress.getId());
//        iCxrCustomerAddressHistoryService.insertAddressHistory(address);

        return true;
    }

    private void getCustomerAddressLngLat(CxrCustomerAddress bo, CxrCustomerAddress address) {
        StringBuffer buffer = new StringBuffer();
        buffer.append(bo.getProvice());
        buffer.append(bo.getCity());
        buffer.append(bo.getArea());
        buffer.append(bo.getDetailDistributionAddress());
        //依据导入到数据来看详细地址里会有# ,#对它的去除
        int index = buffer.indexOf("#");
        while (index != -1) {
            buffer.deleteCharAt(index);
            index = buffer.indexOf("#");
        }

        String baseUrl = txMapConfig.getUrl(); // 基础URL
        String path = "?address=" + buffer.toString(); // 参数
        String query = "&key=" + txMapConfig.getAppKey(); // key值

        String url = baseUrl + path + query; // 构建完整的URL
        OkHttpClient client = new OkHttpClient();
        Request request = new Request.Builder()
            .url(url)
            .build();
        try {
            Response response = client.newCall(request).execute();
            String responseBody = response.body().string();

            JSONObject jsonObject = new JSONObject(responseBody);
            // 在这里对响应数据进行解析，获取需要的地址或坐标信息
            Object lng = jsonObject.getJSONObject("result").getJSONObject("location").get("lng");
            Object lat = jsonObject.getJSONObject("result").getJSONObject("location").get("lat");
            Object level = jsonObject.getJSONObject("result").get("level");
            BigDecimal decimalValues = new BigDecimal(String.valueOf(lng));
            BigDecimal decimalValue = new BigDecimal(String.valueOf(lat));
            String levels = level.toString();
            cxrCustomerAddressMapper.update(null, new LambdaUpdateWrapper<CxrCustomerAddress>()
                .eq(CxrCustomerAddress::getId, bo.getId())
                .set(CxrCustomerAddress::getLongitude, decimalValues)
                .set(CxrCustomerAddress::getLatitude, decimalValue)
                .set(CxrCustomerAddress::getLevel, Integer.parseInt(levels))
            );

            CxrSite cxrSite = iCxrSiteService.getById(bo.getCxrSiteId());
            Map<String, Object> data = new HashMap<>();
            data.put("erpUserAddressId", bo.getId());
            data.put("latitude", decimalValue);
            data.put("longitude", decimalValues);
            data.put("provinceName", bo.getProvice());
            data.put("cityName", bo.getCity());
            data.put("areaName", bo.getArea());
            data.put("mobile", bo.getReceiverPhone());
            data.put("detailInfo", bo.getDetailDistributionAddress());

            data.put("usedProvice", address.getProvice());
            data.put("usedCity", address.getCity());
            data.put("usedArea", address.getArea());
            data.put("usedDetailDistributionAddress", address.getDetailDistributionAddress());
            data.put("usedUserName", address.getReceiverName());
            data.put("usedMobile", address.getReceiverPhone());

            if (ObjectUtil.isNotNull(cxrSite)) {
                data.put("countyName", cxrSite.getCxrRootRegionName());
            }
            data.put("userName", bo.getReceiverName());
            HttpResponse execute = HttpRequest
                .post(appletConfig.getSendUpdateAddress())
                .body(JSONUtil.toJsonStr(data))
                .execute();
            log.info("打印发请求的数据{}", JSONUtil.toJsonStr(execute.body()));
            log.info("数据{}", address.getReceiverPhone());

        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private void setNullMilks(CxrCustomerAddrBo bo) {
        bo.setDefalutAccountAddress(null);
        bo.setAmDistributionInfo(null);
        bo.setPmDistributionInfo(null);
        bo.setPmDistributionStatus(null);
        bo.setPmDistributionStartDeliveryTime(null);
        bo.setAmDistributionStatus(null);
        bo.setAmDistributionStartDeliveryTime(null);
        bo.setAmDistributionSuspendEndTime(null);
    }


    @Override
    @DSTransactional
    public boolean removeBanding(Long id, Integer flag) {
        boolean b = false;
        CxrCustomer cxrCustomer = this.baseMapper.selectById(id);
        if (cxrCustomer == null) {
            throw new ServiceException("没有找到用户!");
        }
        if (ObjectUtil.isNotEmpty(flag) && (flag.equals(1) || flag.equals(3))) {
            if (cxrCustomer.getWxOpenId() == null && cxrCustomer.getWxUnionId() == null) {
                throw new ServiceException("用户未绑定微信!");
            }

            b = update(new LambdaUpdateWrapper<CxrCustomer>()
                .set(CxrCustomer::getWxUnionId, null)
                .set(CxrCustomer::getWxHeadPortrait, null)
                .set(CxrCustomer::getWxNickname, null)
                .set(CxrCustomer::getWxOpenId, null)
                .eq(CxrCustomer::getId, id)
            );
            if (b) {
                //同步修改历史
                AddressHistoryMqDoMain addressHistoryMqDoMain = new AddressHistoryMqDoMain();
                addressHistoryMqDoMain.setCxrCustomerId(id);
                AlterRecord alterRecord = new AlterRecord();
                CustomerBindUpdateDTO customerUnbindUpdate = new CustomerBindUpdateDTO();
                customerUnbindUpdate.setTerminalType(UserType.cxr_customer);
                customerUnbindUpdate.setPhone(cxrCustomer.getPhone());
                alterRecord.setCustomerUnbindUpdate(customerUnbindUpdate);
                addressHistoryMqDoMain.setAlterRecord(alterRecord);
                addressHistoryMqDoMain.setLoginInfo(LoginUtil.getLoginUser());
                addressHistoryMqDoMain.setUuid(java.util.UUID.randomUUID().toString());
                mqUtil.sendSyncMessage(CustomerAddressConstant.CUSTOMER_ADDRESS_TOPIC,
                    CustomerAddressConstant.CUSTOMER_ADDRESS_HISTORY_TAG, JSONUtil.toJsonStr(addressHistoryMqDoMain));
            }
        }
        if (ObjectUtil.isNotEmpty(flag) && (flag.equals(2) || flag.equals(3))) {
            MallUserEntity mallUser = mallUserMapper.selectOne(new LambdaQueryWrapper<MallUserEntity>()
                .eq(MallUserEntity::getErpId, id)
            );
            if (ObjectUtil.isEmpty(mallUser)) {
                throw new ServiceException("小程序找不到这个客户!");
            }

            if (mallUser.getOpenId() == null && mallUser.getUnionId() == null) {
                throw new ServiceException("用户未绑定微信!");
            }
            b = mallUserMapper.update(null, new LambdaUpdateWrapper<MallUserEntity>()
                .eq(MallUserEntity::getId, mallUser.getId())
                .set(MallUserEntity::getNickname, null)
                .set(MallUserEntity::getHeadImgUrl, null)
                .set(MallUserEntity::getOpenId, null)
                .set(MallUserEntity::getUnionId, null)
            ) > 0;
            if (b) {
                //同步修改历史
                AddressHistoryMqDoMain addressHistoryMqDoMain = new AddressHistoryMqDoMain();
                addressHistoryMqDoMain.setCxrCustomerId(id);
                AlterRecord alterRecord = new AlterRecord();
                CustomerBindUpdateDTO customerUnbindUpdate = new CustomerBindUpdateDTO();
                customerUnbindUpdate.setTerminalType(UserType.applet_user);
                customerUnbindUpdate.setPhone(cxrCustomer.getPhone());
                alterRecord.setCustomerUnbindUpdate(customerUnbindUpdate);
                addressHistoryMqDoMain.setAlterRecord(alterRecord);
                addressHistoryMqDoMain.setLoginInfo(LoginUtil.getLoginUser());
                addressHistoryMqDoMain.setUuid(java.util.UUID.randomUUID().toString());
                mqUtil.sendSyncMessage(CustomerAddressConstant.CUSTOMER_ADDRESS_TOPIC,
                    CustomerAddressConstant.CUSTOMER_ADDRESS_HISTORY_TAG, JSONUtil.toJsonStr(addressHistoryMqDoMain));
            }
        }
        return b;
    }

    /**
     * 单独后台的排奶 与配送端不一样
     *
     * @param bos
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean milkDistribution(List<CxrCustomerAddrBo> bos) {
        LoginInfo loginUser = LoginUtil.getLoginUser();
        boolean isCheckDate = bos.get(0).getIsCheckDate();
        //校验排奶 时间
        checkChangeTime(bos, true, isCheckDate);

        //用户信息
        LoginInfo loginInfo = LoginUtil.getLoginUser();

        List<Message<String>> messageList = new ArrayList<>();
        //将除了排奶的信息滞空
        List<CxrCustomerAddress> addresses = bos.stream().map(s ->
        {
            //设置更换新人
            s.setUpdateByName(loginUser.getUserName());
            s.setUpdateBy(LoginHelper.getLoginUser().getUserId());
            s.setUpdateByType(loginUser.getUserType());

            List<CxrSaleProduct> cxrSaleProducts = iCxrSaleProductService.queryProductBySiteId(s.getCxrSiteId());

            //排奶维度转换
            s.setAmDistributionInfo(PlanMilkDataConvertUtil.convetProductIdToDate(s.getAmList(), cxrSaleProducts));
            s.setPmDistributionInfo(PlanMilkDataConvertUtil.convetProductIdToDate(s.getPmList(), cxrSaleProducts));

            //根据配送状态滞空时间  --上午
            if (Convert.toStr(s.getAmDistributionStatus(), SysYesNo.NO.getValue()).equals(SysYesNo.YES.getValue())) {
                s.setAmDistributionSuspendStartTime(null);
                s.setAmDistributionSuspendEndTime(null);
            } else {
                s.setAmDistributionStartDeliveryTime(null);
            }
            //根据配送状态滞空时间  --上午
            if (Convert.toStr(s.getPmDistributionStatus(), SysYesNo.NO.getValue()).equals(SysYesNo.YES.getValue())) {
                s.setPmDistributionSuspendStartTime(null);
                s.setPmDistributionSuspendEndTime(null);
            } else {
                s.setPmDistributionStartDeliveryTime(null);
            }

            //查询  地址
            CxrCustomerAddress customerAddress = iCxrCustomerAddressService.getById(s.getId());

            if (DateUtils.getLocalDateFromDate(customerAddress.getCreateTime()).compareTo(LocalDate.now()) == 0) {
                redisTemplate.opsForValue().set(
                    StrUtil.format(UpdateAddressRedisKey.CXR_UPDATE_CUSTOMER_ADDRESS_ID.getKey(),
                        customerAddress.getId()), 1, 1, TimeUnit.DAYS);
            }

            //拿出地址原来信息

            CxrCustomerChangeRecord record = getRecord(s.getId());

            CxrCustomerAddress address = null;
            if (ObjectUtil.isEmpty(record)) {
                address = customerAddress;
            } else {
                address = BeanUtil.copyProperties(record, CxrCustomerAddress.class);
                address.setId(s.getId());
            }
            validateMilkDistributionChanges(s, address);
            //准备痕迹数据
            AddressHistoryMqDoMain addressHistoryMqDoMain = new AddressHistoryMqDoMain();
            addressHistoryMqDoMain.setAlterAmTaste(s.getAlterAmTaste());
            addressHistoryMqDoMain.setAlterPmTaste(s.getAlterPmTaste());
            addressHistoryMqDoMain.setCxrCustomerAddressId(s.getId());
            addressHistoryMqDoMain.setCxrCustomerId(customerAddress.getCxrCustomerId());
            addressHistoryMqDoMain.setAlterRecord(s.getAlterRecord());
            addressHistoryMqDoMain.setLoginInfo(loginInfo);
            addressHistoryMqDoMain.setAmSendDate(s.getAmDistributionStartDeliveryTime());
            addressHistoryMqDoMain.setAmStopStartDate(s.getAmDistributionSuspendStartTime());
            addressHistoryMqDoMain.setAmStopEndDate(s.getAmDistributionSuspendEndTime());
            addressHistoryMqDoMain.setPmSendDate(s.getPmDistributionStartDeliveryTime());
            addressHistoryMqDoMain.setPmStopStartDate(s.getPmDistributionSuspendStartTime());
            addressHistoryMqDoMain.setPmStopEndDate(s.getPmDistributionSuspendEndTime());
            addressHistoryMqDoMain.setAmAddressMilkDistributionInfo(address.getAmDistributionInfo());
            addressHistoryMqDoMain.setPmAddressMilkDistributionInfo(address.getPmDistributionInfo());
            addressHistoryMqDoMain.setAmDistributionStatus(s.getAmDistributionStatus());
            addressHistoryMqDoMain.setPmDistributionStatus(s.getPmDistributionStatus());
            //旧的时间跟状态
            addressHistoryMqDoMain.setOldAmDistributionStatus(address.getAmDistributionStatus());
            addressHistoryMqDoMain.setOldPmDistributionStatus(address.getPmDistributionStatus());

            addressHistoryMqDoMain.setOldAmSendDate(address.getAmDistributionStartDeliveryTime());
            addressHistoryMqDoMain.setOldPmSendDate(address.getPmDistributionStartDeliveryTime());

            addressHistoryMqDoMain.setOldAmStopStartDate(address.getAmDistributionSuspendStartTime());
            addressHistoryMqDoMain.setOldAmStopEndDate(address.getAmDistributionSuspendEndTime());

            addressHistoryMqDoMain.setOldPmStopStartDate(address.getPmDistributionSuspendStartTime());
            addressHistoryMqDoMain.setOldPmStopEndDate(address.getPmDistributionSuspendEndTime());
            addressHistoryMqDoMain.setUuid(UUID.randomUUID().toString());
            messageList.add(MessageBuilder.withPayload(JsonUtils.toJsonString(addressHistoryMqDoMain)).build());

            return BeanUtil.copyProperties(s, CxrCustomerAddress.class);
        }).collect(Collectors.toList());

//        获取主线程数据
//        异步线程添加历史
//        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
//        CompletableFuture.runAsync(() -> {
//            RequestContextHolder.setRequestAttributes(attributes);
//            iCxrCustomerAddressHistoryService.insertListAddressHistory(addresses);
//        }, scheduledExecutorService);

        if (CollectionUtil.isNotEmpty(messageList)) {
            String destination = StrUtil.format("{}:{}", CustomerAddressConstant.CUSTOMER_ADDRESS_TOPIC,
                CustomerAddressConstant.CUSTOMER_ADDRESS_HISTORY_TAG);
            SendResult sendResult = rocketMQTemplate.syncSend(destination, messageList);
            if (SendStatus.SEND_OK != sendResult.getSendStatus()) {
                throw new ServiceException("同步 修改历史异常");
            }
        }

        LocalTime time = LocalTime.now();
        addresses.stream().forEach(item -> {
            CxrSite cxrSite = iCxrSiteService.getCxrSite(item.getCxrSiteId());
            LocalTime endTime = cxrSite.getChangeEndTime();

//            cxrCustomerAddressMapper.updatemilkSentStop(Arrays.asList(item));
            if (time.isBefore(endTime)) {// 可能出现跨省配送的客户
                if (!isCheckDate || cxrSite.getChangeIntervalDays() == 1) {   // 后台设置起送暂停时间 异动时间大于选择时间则实时生效
                    int updatemilkInfo = cxrCustomerAddressMapper.updatemilkInfo(Arrays.asList(item));
                    if (updatemilkInfo > 0) {
                        cxrCustomerAddressMapper.updatemilkInfoCustomerChange(Arrays.asList(item));
//                        checkUpdataChangeCustomer(item.getId());
                        // 作废之前等待生效时间
                    }
                } else {

                    CxrCustomerChangeRecord customerChangeRecord = BeanUtil.toBean(item, CxrCustomerChangeRecord.class);
                    customerChangeRecord.setCustomerAddressId(item.getId());
                    customerChangeRecord.setSiteId(item.getCxrSiteId());
//                    mqUtil.sendSyncMessage(CustomerAddressConstant.CUSTOMER_ADDRESS_TOPIC,
//                        CustomerAddressConstant.CUSTOMER_SEPARATE_CHANGE_ADDRESS_SAVA_TAG,
//                        JSONUtil.toJsonStr(customerChangeRecord));
                    customerChangeRecordService.customerSeparateCchangeAddressSave(customerChangeRecord);
                }

            } else {
                CxrCustomerChangeRecord customerChangeRecord = BeanUtil.toBean(item, CxrCustomerChangeRecord.class);
                customerChangeRecord.setCustomerAddressId(item.getId());
                customerChangeRecord.setSiteId(item.getCxrSiteId());
                customerChangeRecordService.customerSeparateCchangeAddressSave(customerChangeRecord);
            }
        });

        //更新  null字段不会更新  设置为空的话可能会有问题
        return true;
    }

    private CxrCustomerChangeRecord getRecord(Long id) {
        return customerChangeRecordService.getBaseMapper()
            .selectOne(new LambdaQueryWrapper<CxrCustomerChangeRecord>()
                .eq(CxrCustomerChangeRecord::getCustomerAddressId, id)
                .eq(CxrCustomerChangeRecord::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .orderByDesc(CxrCustomerChangeRecord::getEffectTime).last("limit 1")
            );
    }

    @Override
    public void checkUpdataChangeCustomer(Long id) {

        customerChangeRecordService.getBaseMapper().update(null, new LambdaUpdateWrapper<CxrCustomerChangeRecord>()
            .eq(CxrCustomerChangeRecord::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            .eq(CxrCustomerChangeRecord::getCustomerAddressId, id)
            .le(CxrCustomerChangeRecord::getCreateTime, LocalDateTime.now())
            .set(CxrCustomerChangeRecord::getDeleteStatus, DeleteStatus.DELETED.getValue())
        );

    }

    @Override
    public int updateChangeMilkChangeRecord(CxrAddressBo bo) {
        return cxrCustomerAddressMapper.updateChangeMilkChangeCustomer(bo);
    }

    @Override
    public Page<CxrCustomer> selectCustomerWarningJobUpdateExpirationTime(IPage page) {
        try {
            //2024-7-11 优化
            Page<CxrCustomer> cxrCustomerPage = baseMapper.selectCustomerWarningJobUpdateExpirationTimePageIds(page);
            List<CxrCustomer> records = cxrCustomerPage.getRecords();
            //提取ID
            List<Long> collect = records.stream().map(CxrCustomer::getId).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(collect)) {
                List<CxrCustomer> cxrCustomers = baseMapper.selectCustomerWarningJobUpdateExpirationTimeByIds(collect);
                cxrCustomerPage.setRecords(cxrCustomers);
            }
            return cxrCustomerPage;
        } catch (Exception e) {
            log.error("客户预警查询异常", e);
            //没测，防止出错，保留旧版
            return baseMapper.selectCustomerWarningJobUpdateExpirationTime(page);
        }
    }

    /**
     * 校验异动时间
     */
    public void checkChangeTime(List<CxrCustomerAddrBo> bos, Boolean isBaft, Boolean isCheckDate) {
        //地址id
        List<Long> addressIds = bos.stream().map(CxrCustomerAddrBo::getId).collect(Collectors.toList());
        addressIds.add(-1L);
        //查询的地址id
        List<CxrCustomerAddress> cxrCustomerAddresses =
            cxrCustomerAddressMapper.selectList(new LambdaQueryWrapper<CxrCustomerAddress>()
                .in(CxrCustomerAddress::getId, addressIds)
                .eq(CxrCustomerAddress::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            );
        if (CollectionUtil.isEmpty(cxrCustomerAddresses)) {
            throw new ServiceException("查询不到您所排奶的地址,请刷新重试!");
        }
        //如果是isCheckDate =false  不校验时间
        if (!isCheckDate) {
            for (CxrCustomerAddrBo s : bos) {
                changeStatus(s);
            }
            return;
        }
        //搞成map集合
        Map<Long, CxrCustomerAddress> addressMap =
            cxrCustomerAddresses.stream().collect(Collectors.toMap(CxrCustomerAddress::getId, Function.identity(),
                (v1, v2) -> v2));

        int i = 0;
        for (CxrCustomerAddrBo bo : bos) {

            Long siteId = bo.getCxrSiteId();

            CxrSite cxrSite = iCxrSiteService.getById(siteId);
            LocalTime changeEndTime = cxrSite.getChangeEndTime();
            Long changeIntervalDays = cxrSite.getChangeIntervalDays();
            Long changeIntervalDaysAfter = cxrSite.getChangeIntervalDaysAfter();
            if (ObjectUtil.isNull(changeEndTime) || ObjectUtil.isNull(changeIntervalDays) || ObjectUtil.isNull(
                changeIntervalDaysAfter)) {
                throw new ServiceException("站点异动时间未配置,请联系管理员进行配置");
            }

            LocalTime now = LocalTime.now();
            Long intervalDay = 0L;

            boolean falg = true;
            if (isBaft) {
                //小于等于
                if (now.compareTo(LocalTime.of(20, 00)) <= 0) {
                    falg = false;
                    // 8点前
                    intervalDay = changeIntervalDays;
                } else {
                    //8点后
                    intervalDay = changeIntervalDaysAfter;
                }
            } else {
                //小于等于
                if (now.compareTo(changeEndTime) <= 0) {
                    falg = false;
                    // 8点前
                    intervalDay = changeIntervalDays;
                } else {
                    //8点后
                    intervalDay = changeIntervalDaysAfter;
                }
            }

            // 校验起送时间
            LocalDate localDate = LocalDate.now().plusDays(intervalDay);
            LocalDate nowDate = LocalDate.now().plusDays(changeIntervalDaysAfter);// 异动时间后后
            LocalDate oneDate = LocalDate.now().plusDays(changeIntervalDays);// 异动时间点前
            DateTimeFormatter dtf = DateTimeFormatter.ofPattern("MM月dd号");
            changeStatus(bo);

            //根据每次循环拿出  原始的 数据
            CxrCustomerAddress address = addressMap.get(bo.getId());
            amTimeCheck(i, bo, falg, localDate, nowDate, oneDate, dtf, address);
            pmTimeCheck(i, bo, falg, localDate, nowDate, oneDate, dtf, address);
            i++;
        }

    }

    private void changeStatus(CxrCustomerAddrBo bo) {
        //替换值
        if ("1".equals(bo.getIsShowAmDistribution())) {
            bo.setIsShowAmDistribution(SysYesNo.YES.getValue());
        } else {
            bo.setIsShowAmDistribution(SysYesNo.NO.getValue());
        }

        if ("1".equals(bo.getIsShowPmDistribution())) {
            bo.setIsShowPmDistribution(SysYesNo.YES.getValue());
        } else {
            bo.setIsShowPmDistribution(SysYesNo.NO.getValue());
        }

        if ("1".equals(bo.getAmDistributionStatus())) {
            bo.setAmDistributionStatus(SysYesNo.YES.getValue());
        } else {
            bo.setAmDistributionStatus(SysYesNo.NO.getValue());
        }
        if ("1".equals(bo.getPmDistributionStatus())) {
            bo.setPmDistributionStatus(SysYesNo.YES.getValue());
        } else {
            bo.setPmDistributionStatus(SysYesNo.NO.getValue());
        }
    }

    private void amTimeCheck(int i, CxrCustomerAddrBo bo, boolean falg, LocalDate localDate, LocalDate nowDate,
                             LocalDate oneDate, DateTimeFormatter dtf, CxrCustomerAddress address) {

        if (Convert.toStr(address.getAmDistributionStatus(), SysYesNo.NO.getValue())
            .equals(bo.getAmDistributionStatus())) { //配送状态一样
            if (bo.getAmDistributionStatus().equals("Y")) {//起送状态

                LocalDate amSendDate =
                    bo.getAmDistributionStartDeliveryTime();//上午起送时间
                if (ObjectUtil.isEmpty(amSendDate)) {
                    throw new ServiceException(StrUtil.format("第{}个地址的上午启送日期不能为空", (i + 1)));
                }
                if (ObjectUtil.isNotEmpty(address.getAmDistributionStartDeliveryTime())
                    && address.getAmDistributionStartDeliveryTime().compareTo(amSendDate) != 0) {//判断
                    // 配送时间是否一样   ==0 一样   !=0不一样  就需要判断时间
//                        LocalDate localDate1 = DateUtil.toLocalDateTime(sendDate).toLocalDate();
                    if (localDate.compareTo(amSendDate) > 0) {
                        throw new ServiceException(StrUtil.format("第{}个地址的上午启送日期最早可以选择{}", (i + 1),
                            dtf.format(localDate)));
                    }
                }


            } else {//暂停状态    显示状态为Y
//                            上午暂停时间
                if (ObjectUtil.isEmpty(bo.getAmDistributionSuspendStartTime())) {
                    throw new ServiceException(StrUtil.format("第{}个地址的上午暂停日期不能为空", (i + 1)));
                }
                LocalDate startStopDate =
                    bo.getAmDistributionSuspendStartTime();//暂停时间

                if (ObjectUtil.isNotEmpty(address.getAmDistributionSuspendStartTime())
                    && address.getAmDistributionSuspendStartTime().compareTo(startStopDate) != 0) {//不一样
                    if (falg) {//八点以后
                        //八点后
                        if (nowDate.compareTo(startStopDate) > 0) {
                            throw new ServiceException(
                                StrUtil.format("第{}个地址的上午暂停开始日期最早可以选择{}号", (i + 1),
                                    dtf.format(nowDate)));
                        }
                    } else {
                        //八点前
                        if (oneDate.compareTo(startStopDate) > 0) {
                            throw new ServiceException(
                                StrUtil.format("第{}个地址的上午暂停开始日期最早可以选择{}号", (i + 1),
                                    dtf.format(oneDate)));
                        }
                    }
                }

                if (ObjectUtil.isNotEmpty(bo.getAmDistributionSuspendEndTime())) {//结束时间不等于null
                    // 需要判断是否大于开始时间
                    LocalDate stopEndDate =
                        bo.getAmDistributionSuspendEndTime();//暂停时间

                    if (startStopDate.compareTo(stopEndDate) > 0) {
                        throw new ServiceException(
                            StrUtil.format("第{}个地址的上午暂停结束日期不能大于暂停开始日期", (i + 1)));
                    }
                }
            }
        } else {
            //上午配送状态   不一样
            if (bo.getAmDistributionStatus().equals(SysYesNo.YES.getValue())) {//配送状态
                //配送状态不一样  并且 是起送状态 证明和数据库中的数据肯定不一样   所以直接判断就ok
                LocalDate amSendDate =
                    bo.getAmDistributionStartDeliveryTime();//上午起送时间
                if (ObjectUtil.isEmpty(amSendDate)) {
                    throw new ServiceException(StrUtil.format("第{}个地址的上午启送日期不能为空", (i + 1)));
                }

                if (localDate.compareTo(amSendDate) > 0) {
                    throw new ServiceException(StrUtil.format("第{}个地址的上午启送日期最早可以选择{}", (i + 1),
                        dtf.format(localDate)));
                }

            } else {//暂停状态
                //证明一开始是起送的状态  直接判断时间就ok
                if (ObjectUtil.isEmpty(bo.getAmDistributionSuspendStartTime())) {
                    throw new ServiceException(StrUtil.format("第{}个地址的上午暂停日期不能为空", (i + 1)));
                }
                LocalDate startStopDate =
                    bo.getAmDistributionSuspendStartTime();//暂停时间

                if (falg) {//八点以后
                    //八点后
                    if (nowDate.compareTo(startStopDate) > 0) {
                        throw new ServiceException(
                            StrUtil.format("第{}个地址的上午暂停开始日期最早可以选择{}号", (i + 1),
                                dtf.format(nowDate)));
                    }
                } else {
                    //八点前
                    if (oneDate.compareTo(startStopDate) > 0) {
                        throw new ServiceException(
                            StrUtil.format("第{}个地址的上午暂停开始日期最早可以选择{}号", (i + 1),
                                dtf.format(oneDate)));
                    }
                }

                if (ObjectUtil.isNotEmpty(bo.getAmDistributionSuspendEndTime())) {//结束时间不等于null
                    // 需要判断是否大于开始时间
                    LocalDate stopEndDate =
                        bo.getAmDistributionSuspendEndTime();//暂停时间

                    if (startStopDate.compareTo(stopEndDate) > 0) {
                        throw new ServiceException(
                            StrUtil.format("第{}个地址的上午暂停结束日期不能大于暂停开始日期", (i + 1)));
                    }
                }

            }
        }


    }

    private void pmTimeCheck(int i, CxrCustomerAddrBo bo, boolean falg, LocalDate localDate, LocalDate nowDate,
                             LocalDate oneDate, DateTimeFormatter dtf, CxrCustomerAddress address) {

        if (Convert.toStr(address.getPmDistributionStatus(), SysYesNo.NO.getValue())
            .equals(bo.getPmDistributionStatus())) { //配送状态一样
            if (bo.getPmDistributionStatus().equals("Y")) {//起送状态

                LocalDate pmSendDate =
                    bo.getPmDistributionStartDeliveryTime();//上午起送时间
                if (ObjectUtil.isEmpty(pmSendDate)) {
                    throw new ServiceException(StrUtil.format("第{}个地址的下午启送日期不能为空", (i + 1)));
                }
                if (ObjectUtil.isNotEmpty(address.getPmDistributionStartDeliveryTime())
                    && address.getPmDistributionStartDeliveryTime().compareTo(pmSendDate) != 0) {//判断
                    // 配送时间是否一样   ==0 一样   !=0不一样  就需要判断时间
//                        LocalDate localDate1 = DateUtil.toLocalDateTime(sendDate).toLocalDate();
                    if (localDate.compareTo(pmSendDate) > 0) {
                        throw new ServiceException(StrUtil.format("第{}个地址的下午启送日期最早可以选择{}", (i + 1),
                            dtf.format(localDate)));
                    }
                }


            } else {//暂停状态    显示状态为Y
//                            上午暂停时间
                if (ObjectUtil.isEmpty(bo.getPmDistributionSuspendStartTime())) {
                    throw new ServiceException(StrUtil.format("第{}个地址的下午暂停日期不能为空", (i + 1)));
                }
                LocalDate startStopDate =
                    bo.getPmDistributionSuspendStartTime();//暂停时间

                if (ObjectUtil.isNotEmpty(address.getPmDistributionSuspendStartTime())
                    && address.getPmDistributionSuspendStartTime().compareTo(startStopDate) != 0) {//不一样
                    if (falg) {//八点以后
                        //八点后
                        if (nowDate.compareTo(startStopDate) > 0) {
                            throw new ServiceException(
                                StrUtil.format("第{}个地址的下午暂停开始日期最早可以选择{}号", (i + 1),
                                    dtf.format(nowDate)));
                        }
                    } else {
                        //八点前
                        if (oneDate.compareTo(startStopDate) > 0) {
                            throw new ServiceException(
                                StrUtil.format("第{}个地址的上午暂停开始日期最早可以选择{}号", (i + 1),
                                    dtf.format(oneDate)));
                        }
                    }
                }

                if (ObjectUtil.isNotEmpty(bo.getPmDistributionSuspendEndTime())) {//结束时间不等于null
                    // 需要判断是否大于开始时间
                    LocalDate stopEndDate =
                        bo.getPmDistributionSuspendEndTime();//暂停时间

                    if (startStopDate.compareTo(stopEndDate) > 0) {
                        throw new ServiceException(
                            StrUtil.format("第{}个地址的上午暂停结束日期不能大于暂停开始日期", (i + 1)));
                    }
                }
            }
        } else {
            //下午配送状态   不一样
            if (bo.getPmDistributionStatus().equals(SysYesNo.YES.getValue())) {//配送状态
                //配送状态不一样  并且 是起送状态 证明和数据库中的数据肯定不一样   所以直接判断就ok
                LocalDate pmSendDate =
                    bo.getPmDistributionStartDeliveryTime();//上午起送时间
                if (ObjectUtil.isEmpty(pmSendDate)) {
                    throw new ServiceException(StrUtil.format("第{}个地址的下午启送日期不能为空", (i + 1)));
                }

                if (localDate.compareTo(pmSendDate) > 0) {
                    throw new ServiceException(StrUtil.format("第{}个地址的下午启送日期最早可以选择{}", (i + 1),
                        dtf.format(localDate)));
                }

            } else {//暂停状态
                //证明一开始是起送的状态  直接判断时间就ok
                if (ObjectUtil.isEmpty(bo.getPmDistributionSuspendStartTime())) {
                    throw new ServiceException(StrUtil.format("第{}个地址的下午暂停日期不能为空", (i + 1)));
                }
                LocalDate startStopDate =
                    bo.getPmDistributionSuspendStartTime();//暂停时间

                if (falg) {//八点以后
                    //八点后
                    if (nowDate.compareTo(startStopDate) > 0) {
                        throw new ServiceException(
                            StrUtil.format("第{}个地址的下午暂停开始日期最早可以选择{}号", (i + 1),
                                dtf.format(nowDate)));
                    }
                } else {
                    //八点前
                    if (oneDate.compareTo(startStopDate) > 0) {
                        throw new ServiceException(
                            StrUtil.format("第{}个地址的下午暂停开始日期最早可以选择{}号", (i + 1),
                                dtf.format(oneDate)));
                    }
                }

                if (ObjectUtil.isNotEmpty(bo.getPmDistributionSuspendEndTime())) {//结束时间不等于null
                    // 需要判断是否大于开始时间
                    LocalDate stopEndDate =
                        bo.getPmDistributionSuspendEndTime();//暂停时间

                    if (startStopDate.compareTo(stopEndDate) > 0) {
                        throw new ServiceException(
                            StrUtil.format("第{}个地址的下午暂停结束日期不能大于暂停开始日期", (i + 1)));
                    }
                }

            }
        }


    }

    private void amTimeChecks(int i, CxrCustomerAddrBo bo, boolean falg, LocalDate localDate, LocalDate nowDate,
                              LocalDate oneDate, DateTimeFormatter dtf, CxrCustomerAddress address) {

        if (Convert.toStr(address.getAmDistributionStatus(), SysYesNo.NO.getValue())
            .equals(bo.getAmDistributionStatus())) { //配送状态一样
            if (bo.getAmDistributionStatus().equals("Y")) {//起送状态

                LocalDate amSendDate =
                    bo.getAmDistributionStartDeliveryTime();//上午起送时间
                if (ObjectUtil.isEmpty(amSendDate)) {
                    throw new ServiceException(StrUtil.format("第{}个地址的上午启送日期不能为空", (i + 1)));
                }
                if (ObjectUtil.isNotEmpty(address.getAmDistributionStartDeliveryTime())
                    && address.getAmDistributionStartDeliveryTime().compareTo(amSendDate) != 0) {//判断
//                             配送时间是否一样   ==0 一样   !=0不一样  就需要判断时间
//                        LocalDate localDate1 = DateUtil.toLocalDateTime(sendDate).toLocalDate();
                    if (falg) {
                        if (nowDate.compareTo(amSendDate) > 0) {
                            throw new ServiceException(StrUtil.format("第{}个地址的上午启送日期最早可以选择{}", (i + 1),
                                dtf.format(nowDate)));
                        }
                    } else {
                        if (oneDate.compareTo(amSendDate) > 0) {
                            throw new ServiceException(StrUtil.format("第{}个地址的上午启送日期最早可以选择{}", (i + 1),
                                dtf.format(oneDate)));
                        }
                    }

                }


            } else {//暂停状态    显示状态为Y
//                            上午暂停时间
                if (ObjectUtil.isEmpty(bo.getAmDistributionSuspendStartTime())) {
                    throw new ServiceException(StrUtil.format("第{}个地址的上午暂停日期不能为空", (i + 1)));
                }
                LocalDate startStopDate =
                    bo.getAmDistributionSuspendStartTime();//暂停时间

                if (ObjectUtil.isNotEmpty(address.getAmDistributionSuspendStartTime())
                    && address.getAmDistributionSuspendStartTime().compareTo(startStopDate) != 0) {//不一样
                    if (falg) {//八点以后
                        //八点后
                        if (nowDate.compareTo(startStopDate) > 0) {
                            throw new ServiceException(
                                StrUtil.format("第{}个地址的上午暂停开始日期最早可以选择{}号", (i + 1),
                                    dtf.format(nowDate)));
                        }
                    } else {
//                                //八点前
                        if (oneDate.compareTo(startStopDate) > 0) {
                            throw new ServiceException(
                                StrUtil.format("第{}个地址的上午暂停开始日期最早可以选择{}号", (i + 1),
                                    dtf.format(oneDate)));
                        }
                    }
                }

                if (ObjectUtil.isNotEmpty(bo.getAmDistributionSuspendEndTime())) {//结束时间不等于null
                    // 需要判断是否大于开始时间
                    LocalDate stopEndDate =
                        bo.getAmDistributionSuspendEndTime();//暂停时间

                    if (startStopDate.compareTo(stopEndDate) > 0) {
                        throw new ServiceException(
                            StrUtil.format("第{}个地址的上午暂停结束日期不能大于暂停开始日期", (i + 1)));
                    }
                }
            }
        } else {
            //上午配送状态   不一样
            if (bo.getAmDistributionStatus().equals(SysYesNo.YES.getValue())) {//配送状态
                //配送状态不一样  并且 是起送状态 证明和数据库中的数据肯定不一样   所以直接判断就ok
                LocalDate amSendDate =
                    bo.getAmDistributionStartDeliveryTime();//上午起送时间
                if (ObjectUtil.isEmpty(amSendDate)) {
                    throw new ServiceException(StrUtil.format("第{}个地址的上午启送日期不能为空", (i + 1)));
                }

                if (falg) {
                    if (nowDate.compareTo(amSendDate) > 0) {
                        throw new ServiceException(StrUtil.format("第{}个地址的上午启送日期最早可以选择{}", (i + 1),
                            dtf.format(nowDate)));
                    }
                } else {
                    if (oneDate.compareTo(amSendDate) > 0) {
                        throw new ServiceException(StrUtil.format("第{}个地址的上午启送日期最早可以选择{}", (i + 1),
                            dtf.format(oneDate)));
                    }
                }


            } else {//暂停状态
                //证明一开始是起送的状态  直接判断时间就ok
                if (ObjectUtil.isEmpty(bo.getAmDistributionSuspendStartTime())) {
                    throw new ServiceException(StrUtil.format("第{}个地址的上午暂停日期不能为空", (i + 1)));
                }
                LocalDate startStopDate =
                    bo.getAmDistributionSuspendStartTime();//暂停时间

                if (falg) {//八点以后
                    //八点后
                    if (nowDate.compareTo(startStopDate) > 0) {
                        throw new ServiceException(
                            StrUtil.format("第{}个地址的上午暂停开始日期最早可以选择{}号", (i + 1),
                                dtf.format(nowDate)));
                    }
                } else {
                    //八点前
                    if (oneDate.compareTo(startStopDate) > 0) {
                        throw new ServiceException(
                            StrUtil.format("第{}个地址的上午暂停开始日期最早可以选择{}号", (i + 1),
                                dtf.format(oneDate)));
                    }
                }

                if (ObjectUtil.isNotEmpty(bo.getAmDistributionSuspendEndTime())) {//结束时间不等于null
                    // 需要判断是否大于开始时间
                    LocalDate stopEndDate =
                        bo.getAmDistributionSuspendEndTime();//暂停时间

                    if (startStopDate.compareTo(stopEndDate) > 0) {
                        throw new ServiceException(
                            StrUtil.format("第{}个地址的上午暂停结束日期不能大于暂停开始日期", (i + 1)));
                    }
                }

            }
        }


    }

    private void pmTimeChecks(int i, CxrCustomerAddrBo bo, boolean falg, LocalDate localDate, LocalDate nowDate,
                              LocalDate oneDate, DateTimeFormatter dtf, CxrCustomerAddress address) {

        if (Convert.toStr(address.getPmDistributionStatus(), SysYesNo.NO.getValue())
            .equals(bo.getPmDistributionStatus())) { //配送状态一样
            if (bo.getPmDistributionStatus().equals("Y")) {//起送状态

                LocalDate pmSendDate =
                    bo.getPmDistributionStartDeliveryTime();//上午起送时间
                if (ObjectUtil.isEmpty(pmSendDate)) {
                    throw new ServiceException(StrUtil.format("第{}个地址的下午启送日期不能为空", (i + 1)));
                }
                if (ObjectUtil.isNotEmpty(address.getPmDistributionStartDeliveryTime())
                    && address.getPmDistributionStartDeliveryTime().compareTo(pmSendDate) != 0) {//判断
                    // 配送时间是否一样   ==0 一样   !=0不一样  就需要判断时间
//                        LocalDate localDate1 = DateUtil.toLocalDateTime(sendDate).toLocalDate();
                    if (falg) {
                        if (nowDate.compareTo(pmSendDate) > 0) {
                            throw new ServiceException(StrUtil.format("第{}个地址的下午启送日期最早可以选择{}", (i + 1),
                                dtf.format(nowDate)));
                        }
                    } else {
                        if (oneDate.compareTo(pmSendDate) > 0) {
                            throw new ServiceException(StrUtil.format("第{}个地址的下午启送日期最早可以选择{}", (i + 1),
                                dtf.format(oneDate)));
                        }
                    }

                }


            } else {//暂停状态    显示状态为Y
//                            上午暂停时间
                if (ObjectUtil.isEmpty(bo.getPmDistributionSuspendStartTime())) {
                    throw new ServiceException(StrUtil.format("第{}个地址的下午暂停日期不能为空", (i + 1)));
                }
                LocalDate startStopDate =
                    bo.getPmDistributionSuspendStartTime();//暂停时间

                if (ObjectUtil.isNotEmpty(address.getPmDistributionSuspendStartTime())
                    && address.getPmDistributionSuspendStartTime().compareTo(startStopDate) != 0) {//不一样
                    if (falg) {//八点以后
                        //八点后
                        if (nowDate.compareTo(startStopDate) > 0) {
                            throw new ServiceException(
                                StrUtil.format("第{}个地址的下午暂停开始日期最早可以选择{}号", (i + 1),
                                    dtf.format(nowDate)));
                        }
                    } else {
                        //八点前
                        if (oneDate.compareTo(startStopDate) > 0) {
                            throw new ServiceException(
                                StrUtil.format("第{}个地址的上午暂停开始日期最早可以选择{}号", (i + 1),
                                    dtf.format(oneDate)));
                        }
                    }
                }

                if (ObjectUtil.isNotEmpty(bo.getPmDistributionSuspendEndTime())) {//结束时间不等于null
                    // 需要判断是否大于开始时间
                    LocalDate stopEndDate =
                        bo.getPmDistributionSuspendEndTime();//暂停时间

                    if (startStopDate.compareTo(stopEndDate) > 0) {
                        throw new ServiceException(
                            StrUtil.format("第{}个地址的上午暂停结束日期不能大于暂停开始日期", (i + 1)));
                    }
                }
            }
        } else {
            //下午配送状态   不一样
            if (bo.getPmDistributionStatus().equals(SysYesNo.YES.getValue())) {//配送状态
                //配送状态不一样  并且 是起送状态 证明和数据库中的数据肯定不一样   所以直接判断就ok
                LocalDate pmSendDate =
                    bo.getPmDistributionStartDeliveryTime();//上午起送时间

                if (ObjectUtil.isEmpty(pmSendDate)) {
                    throw new ServiceException(StrUtil.format("第{}个地址的下午启送日期不能为空", (i + 1)));
                }

                if (falg) {//八点以后
                    if (nowDate.compareTo(pmSendDate) > 0) {
                        throw new ServiceException(StrUtil.format("第{}个地址的下午启送日期最早可以选择{}", (i + 1),
                            dtf.format(nowDate)));
                    }
                } else {
                    if (oneDate.compareTo(pmSendDate) > 0) {
                        throw new ServiceException(StrUtil.format("第{}个地址的下午启送日期最早可以选择{}", (i + 1),
                            dtf.format(oneDate)));
                    }
                }


            } else {//暂停状态
                //证明一开始是起送的状态  直接判断时间就ok
                if (ObjectUtil.isEmpty(bo.getPmDistributionSuspendStartTime())) {
                    throw new ServiceException(StrUtil.format("第{}个地址的下午暂停日期不能为空", (i + 1)));
                }
                LocalDate startStopDate =
                    bo.getPmDistributionSuspendStartTime();//暂停时间

                if (falg) {//八点以后
                    //八点后
                    if (nowDate.compareTo(startStopDate) > 0) {
                        throw new ServiceException(
                            StrUtil.format("第{}个地址的下午暂停开始日期最早可以选择{}号", (i + 1),
                                dtf.format(nowDate)));
                    }
                } else {
                    //八点前
                    if (oneDate.compareTo(startStopDate) > 0) {
                        throw new ServiceException(
                            StrUtil.format("第{}个地址的下午暂停开始日期最早可以选择{}号", (i + 1),
                                dtf.format(oneDate)));
                    }
                }

                if (ObjectUtil.isNotEmpty(bo.getPmDistributionSuspendEndTime())) {//结束时间不等于null
                    // 需要判断是否大于开始时间
                    LocalDate stopEndDate =
                        bo.getPmDistributionSuspendEndTime();//暂停时间

                    if (startStopDate.compareTo(stopEndDate) > 0) {
                        throw new ServiceException(
                            StrUtil.format("第{}个地址的下午暂停结束日期不能大于暂停开始日期", (i + 1)));
                    }
                }

            }
        }


    }


    @Override
    public Object milkDistributionDetailed(MilkDistributionDetailedBo bo) {

        if (ObjectUtil.isEmpty(bo.getCustomerId())) {//  || ObjectUtil.isEmpty(bo.getYearDate())
            throw new ServiceException("参数异常！");
        }
        LoginEmployee loginUser = CustomerLoginHelper.getLoginUser();
        MilkDistributionDetaileVo milkDistributionDetaileVo = new MilkDistributionDetaileVo();
        // 当前登入人
        //第一次访问页面
        List<CxrCustomerAddress> customerAddressList =
            cxrCustomerAddressMapper.selectList(new LambdaUpdateWrapper<CxrCustomerAddress>()
                .eq(CxrCustomerAddress::getCxrCustomerId, loginUser.getUserId())
                .eq(CxrCustomerAddress::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            );
        if (customerAddressList.size() == 0) {
            throw new ServiceException("暂无可用地址！");
        }

        CxrCustomerAddress address = null;
        milkDistributionDetaileVo.setCustomerAddressesList(customerAddressList);
        if (ObjectUtil.isEmpty(bo.getAddressId())) {
            address = customerAddressList.get(0);
            bo.setAddressId(address.getId());
        } else {
            address = cxrCustomerAddressMapper.selectById(bo.getAddressId());
        }
        LocalDate localDate = LocalDate.now();
        bo.setPhone(address.getReceiverPhone());
        CxrEmployee employee = cxrEmployeeMapper.selectById(address.getCxrEmployeeId());
        if (employee != null) {
            milkDistributionDetaileVo.setDistributionName(employee.getName());
            milkDistributionDetaileVo.setDistributionPhone(employee.getPhone());
        } else {
            milkDistributionDetaileVo.setDistributionName(StrUtil.EMPTY);
            milkDistributionDetaileVo.setDistributionPhone(StrUtil.EMPTY);
        }
        milkDistributionDetaileVo.setAddressDetails(
            address.getProvice() + address.getCity() + address.getArea() + address.getDetailDistributionAddress());

        milkDistributionDetaileVo.setCreateByType(address.getCreateByType());
        List<CxrCustomerDistributionListRecord> listRecords = recordMapper.selectList(
            new LambdaQueryWrapper<CxrCustomerDistributionListRecord>()
                .eq(CxrCustomerDistributionListRecord::getCustomerAddressId, bo.getAddressId()));
        List<CustomerDistributionListRecordVo> distributionDetail = new ArrayList<>();
        Long sum;
        if (CollectionUtil.isEmpty(listRecords)) {
            sum = 0l;
            milkDistributionDetaileVo.setDistributionDetail(distributionDetail);
        } else {

            List<CxrRoadWay> cxrRoadWays = cxrRoadWayMapper.selectList(new LambdaQueryWrapper<CxrRoadWay>()
                .select(CxrRoadWay::getId, CxrRoadWay::getDistributionDate, CxrRoadWay::getMilkDistributionTotal)
                .eq(CxrRoadWay::getStatus, DistributionStatus.SEND.getValue())
                .eq(CxrRoadWay::getDistributionDate, localDate)
                .eq(CxrRoadWay::getCxrCustomerAddressId, address.getId())
                .eq(CxrRoadWay::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            );
            if (CollUtil.isNotEmpty(cxrRoadWays)) {
                sum = cxrRoadWays.stream()
                    .collect(Collectors.summarizingLong(CxrRoadWay::getMilkDistributionTotal)).getSum();
            } else {
                sum = 0l;
            }
            int year = localDate.getYear();
            int month = localDate.getMonth().getValue();
            distributionDetail = BeanCollectionUtils.copyListProperties(listRecords,
                    CustomerDistributionListRecordVo::new, (a, b) -> {
                        String monthmark = a.getMonthmark().toString();
                        if (monthmark.length() == 1) {
                            monthmark = "0" + monthmark;
                        }
                        b.setDateMark(a.getYearkmar().toString() + "-" + monthmark + "-01");

                        if (ObjectUtil.equals(a.getYearkmar(), year) &&
                            ObjectUtil.equals(a.getMonthmark(), month)
                        ) {
                            setDDAteData(b, Convert.toInt(sum));
                        }
                    }).stream().sorted(Comparator.comparing(CustomerDistributionListRecordVo::getDateMark).reversed())
                .collect(Collectors.toList());

            milkDistributionDetaileVo.setDistributionDetail(distributionDetail);
        }

        bo.setGetToday(LocalDate.now().plusDays(1));
        CustomerTotalVo customerTotalVo = cxrCustomerAddressMapper.milkDistributionDetailedTotal(bo);

        Integer totalConversionQuantity = remoteOrderService.customerSwitchPhone(bo.getPhone());
        customerTotalVo.setTotalOrderQuantity(customerTotalVo.getTotalOrderQuantity() + totalConversionQuantity);
        customerTotalVo.setTotalSurplusQuantity(
            customerTotalVo.getTotalSurplusQuantity() + customerTotalVo.getTotalNotSentQuantity());

        milkDistributionDetaileVo.setCustomerTotalVo(customerTotalVo);
        milkDistributionDetaileVo.setLockQuantity(ObjectUtil.isEmpty(customerTotalVo.getLockStock()) ? 0 :
            customerTotalVo.getLockStock());

        List<CxrRoadWay> RoadWaysTotal = cxrRoadWayMapper.selectList(new LambdaQueryWrapper<CxrRoadWay>()
            .select(CxrRoadWay::getId, CxrRoadWay::getDistributionDate, CxrRoadWay::getMilkDistributionTotal)
            .eq(CxrRoadWay::getStatus, DistributionStatus.SEND.getValue())
            .eq(CxrRoadWay::getDistributionDate, localDate)
            .eq(CxrRoadWay::getCxrCustomerId, address.getCxrCustomerId())
            .eq(CxrRoadWay::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
        );
        if (CollUtil.isNotEmpty(RoadWaysTotal)) {
            Long sum1 = RoadWaysTotal.stream()
                .collect(Collectors.summarizingLong(CxrRoadWay::getMilkDistributionTotal)).getSum();
            customerTotalVo.setTotalFreshMilkSentQuantity(
                customerTotalVo.getTotalFreshMilkSentQuantity() + sum1.intValue());
        }

        return milkDistributionDetaileVo;
    }
    public CustomerStockSumVo customerStockInfo() {

        LoginEmployee loginUser = CustomerLoginHelper.getLoginUser();
        Long customerId = loginUser.getUserId();

        LocalDate localDate = LocalDate.now();
        CustomerStockSumVo customerTotalVo = baseMapper.customerStockSumInfo(customerId);

        customerTotalVo.setOrderQuantity(
            customerTotalVo.getOrderQuantity() +
            customerTotalVo.getFreshMilkGiveQuantity() -
                customerTotalVo.getFreshMilkReturnQuantity());
        customerTotalVo.setTotalSurplusQuantity(customerTotalVo.getTotalSurplusQuantity() + customerTotalVo.getNotSentTotal());

        List<CxrRoadWay> roadWays = cxrRoadWayMapper.selectList(new LambdaQueryWrapper<CxrRoadWay>()
            .select(CxrRoadWay::getId, CxrRoadWay::getDistributionDate, CxrRoadWay::getMilkDistributionTotal)
            .eq(CxrRoadWay::getStatus, DistributionStatus.SEND.getValue())
            .eq(CxrRoadWay::getDistributionDate, localDate)
            .eq(CxrRoadWay::getCxrCustomerId, customerId)
            .eq(CxrRoadWay::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
        );
        Integer milkDistributionSum = 0;
        if (CollUtil.isNotEmpty(roadWays)) {
            Long milkDistributionTotalSum = roadWays.stream().collect(Collectors.summingLong(CxrRoadWay::getMilkDistributionTotal));
            milkDistributionSum  = milkDistributionTotalSum.intValue();
        }
        customerTotalVo.setFreshMilkSentQuantity(customerTotalVo.getFreshMilkSentQuantity() + customerTotalVo.getOrderFreshMilkSentQuantity() + milkDistributionSum);
        return customerTotalVo;
    }

    @Override
    public List<CxrCustomerAddrVo> milkDistributionDetail(Set<Long> id) {

        List<CxrCustomerAddrVo> cxrCustomerAddrVos = cxrCustomerAddressMapper.milkDistributionDetail(id,
            DeleteStatus.NOT_DELETED.getValue());

//        //设置员工名称
//        cxrCustomerAddrVos.stream().forEach(s -> {
//        });

        cxrCustomerAddrVos.stream().forEach(s -> {
            if (s.getCxrCustomerId() != null) {
                CxrEmployee cxrEmployee = iCxrEmployeeService.getById(s.getCxrEmployeeId());
                s.setCxrEmployeeName(cxrEmployee.getName());
            }
            CxrCustomerChangeRecord changeRecord = getRecord(s.getId());
            if (ObjectUtil.isNotEmpty(changeRecord)) {
                s.setAmDistributionInfo(changeRecord.getAmDistributionInfo());
                s.setAmDistributionStatus(changeRecord.getAmDistributionStatus());
                s.setAmDistributionStartDeliveryTime(changeRecord.getAmDistributionStartDeliveryTime());
                s.setAmDistributionSuspendStartTime(changeRecord.getAmDistributionSuspendStartTime());
                s.setAmDistributionSuspendEndTime(changeRecord.getAmDistributionSuspendEndTime());
                s.setPmDistributionInfo(changeRecord.getPmDistributionInfo());
                s.setPmDistributionStatus(changeRecord.getPmDistributionStatus());
                s.setPmDistributionStartDeliveryTime(changeRecord.getPmDistributionStartDeliveryTime());
                s.setPmDistributionSuspendStartTime(changeRecord.getPmDistributionSuspendStartTime());
                s.setPmDistributionSuspendEndTime(changeRecord.getPmDistributionSuspendEndTime());
                s.setIsShowAmDistribution(ObjectUtil.isEmpty(changeRecord.getIsShowAmDistribution()) ?
                    s.getIsShowAmDistribution() : changeRecord.getIsShowAmDistribution());
                s.setIsShowPmDistribution(ObjectUtil.isEmpty(changeRecord.getIsShowPmDistribution()) ?
                    s.getIsShowPmDistribution() : changeRecord.getIsShowPmDistribution());
            }
            if (SysYesNo.YES.getValue().equals(s.getAmDistributionStatus())) {
                s.setAmDistributionStatus("1");
            } else {
                s.setAmDistributionStatus("2");
            }
            if (SysYesNo.YES.getValue().equals(s.getPmDistributionStatus())) {
                s.setPmDistributionStatus("1");
            } else {
                s.setPmDistributionStatus("2");
            }

            if (SysYesNo.YES.getValue().equals(s.getIsShowAmDistribution())) {
                s.setIsShowAmDistribution("1");
            } else {
                s.setIsShowAmDistribution("2");
            }

            if (SysYesNo.YES.getValue().equals(s.getIsShowPmDistribution())) {
                s.setIsShowPmDistribution("3");
            } else {
                s.setIsShowPmDistribution("4");
            }
            //添加小区名称
            Long cxrResidentialQuartersId = s.getCxrResidentialQuartersId();
            if (cxrResidentialQuartersId != null) {
                CxrResidentialQuarters cxrResidentialQuarters =
                    cxrResidentialQuartersMapper.selectById(cxrResidentialQuartersId);
                s.setCxrResidentialQuartersName(cxrResidentialQuarters.getName());
            }
            //如果是null   new对象进去  变成有商品的数组
            if (s.getPmDistributionInfo() == null && s.getAmDistributionInfo() == null) {
                CustomerAddressMilkDistributionInfo pmInfo = new CustomerAddressMilkDistributionInfo();
                if (s.getCxrSaleProducts() != null) {
                    newMilkDistributionInfo(s, pmInfo);
                }

                s.setPmDistributionInfo(pmInfo);
                s.setAmDistributionInfo(pmInfo);
            } else if (s.getPmDistributionInfo() == null) {
                CustomerAddressMilkDistributionInfo pmInfo = new CustomerAddressMilkDistributionInfo();
                if (s.getCxrSaleProducts() != null) {
                    newMilkDistributionInfo(s, pmInfo);
                }
                s.setPmDistributionInfo(pmInfo);
            } else if (s.getAmDistributionInfo() == null) {

                CustomerAddressMilkDistributionInfo pmInfo = new CustomerAddressMilkDistributionInfo();
                if (s.getCxrSaleProducts() != null) {
                    newMilkDistributionInfo(s, pmInfo);
                }
                s.setAmDistributionInfo(pmInfo);
            } else {
                List<Long> saleIds =
                    s.getCxrSaleProducts().stream().map(SaleProductListVo::getId).collect(Collectors.toList());
                CustomerAddressMilkDistributionInfo pmDistributionInfo = s.getPmDistributionInfo();
                CustomerAddressMilkDistributionInfo amDistributionInfo = s.getAmDistributionInfo();
                useRemove(saleIds, pmDistributionInfo);
                useRemove(saleIds, amDistributionInfo);
            }

            List<SaleProductListVo> list =
                s.getCxrSaleProducts().stream().sorted(Comparator.comparing(SaleProductListVo::getProductAlias))
                    .collect(Collectors.toList());
            s.setCxrSaleProducts(list);
        });

        return cxrCustomerAddrVos;
    }

    //提取 zhouyi--周五  调用去除站点没有的口味
    private void useRemove(List<Long> saleIds, CustomerAddressMilkDistributionInfo DistributionInfo) {
        List<MilkDistributionInfo> friday = DistributionInfo.getFriday();
        List<MilkDistributionInfo> monday = DistributionInfo.getMonday();
        List<MilkDistributionInfo> saturday = DistributionInfo.getSaturday();
        List<MilkDistributionInfo> thursday = DistributionInfo.getThursday();
        List<MilkDistributionInfo> wednesday = DistributionInfo.getWednesday();
        List<MilkDistributionInfo> tuesday = DistributionInfo.getTuesday();
        if (CollectionUtil.isNotEmpty(friday)) {
            removeNotInSale(saleIds, friday);
        }
        if (CollectionUtil.isNotEmpty(monday)) {
            removeNotInSale(saleIds, monday);
        }
        if (CollectionUtil.isNotEmpty(saturday)) {
            removeNotInSale(saleIds, saturday);
        }
        if (CollectionUtil.isNotEmpty(thursday)) {
            removeNotInSale(saleIds, thursday);
        }
        if (CollectionUtil.isNotEmpty(wednesday)) {
            removeNotInSale(saleIds, wednesday);
        }
        if (CollectionUtil.isNotEmpty(tuesday)) {
            removeNotInSale(saleIds, tuesday);
        }
    }

    //去除站点没有的口味
    private void removeNotInSale(List<Long> saleIds, List<MilkDistributionInfo> days) {

        Iterator<MilkDistributionInfo> iterator = days.iterator();
        while (iterator.hasNext()) {
            MilkDistributionInfo s = iterator.next();
            if (!saleIds.contains(s.getProductId())) {
                iterator.remove();
            }
        }
    }

    /**
     * 从vo中拿商品信息  创建排奶初始信息
     *
     * @param s
     * @param pmInfo
     */
    private void newMilkDistributionInfo(CxrCustomerAddrVo s, CustomerAddressMilkDistributionInfo pmInfo) {
        List<MilkDistributionInfo> milkDistributionInfos = s.getCxrSaleProducts().stream().map(pr -> {
            MilkDistributionInfo milkDistributionInfo = new MilkDistributionInfo();
            milkDistributionInfo.setProductId(pr.getId());
            milkDistributionInfo.setProductName(pr.getName());
            milkDistributionInfo.setProductAlias(pr.getProductAlias());
            milkDistributionInfo.setQuantity(0L);
            return milkDistributionInfo;
        }).collect(Collectors.toList());
        pmInfo.setFriday(milkDistributionInfos);
        pmInfo.setMonday(milkDistributionInfos);
        pmInfo.setSaturday(milkDistributionInfos);
        pmInfo.setThursday(milkDistributionInfos);
        pmInfo.setTuesday(milkDistributionInfos);
        pmInfo.setWednesday(milkDistributionInfos);
    }


    @Override
    public List<CxrCustomerAddrVo> allMilkDistributionDetail(Long id) {
        List<CxrCustomerAddrVo> cxrCustomerAddrVos = cxrCustomerAddressMapper.allmilkDistributionDetail(id,
            DeleteStatus.NOT_DELETED.getValue());

//        cxrCustomerAddrVos.stream().forEach(s -> {
//
//        });
        cxrCustomerAddrVos.stream().forEach(s -> {
            //设置员工名称
            if (s.getCxrCustomerId() != null) {
                CxrEmployee cxrEmployee = iCxrEmployeeService.getById(s.getCxrEmployeeId());
                if (ObjectUtil.isNotEmpty(cxrEmployee)) {
                    s.setCxrEmployeeName(cxrEmployee.getName());
                }
            }
            CxrCustomerChangeRecord changeRecord = getRecord(s.getId());
            if (ObjectUtil.isNotEmpty(changeRecord)) {
                s.setAmDistributionInfo(changeRecord.getAmDistributionInfo());
                s.setAmDistributionStatus(changeRecord.getAmDistributionStatus());
                s.setAmDistributionStartDeliveryTime(changeRecord.getAmDistributionStartDeliveryTime());
                s.setAmDistributionSuspendStartTime(changeRecord.getAmDistributionSuspendStartTime());
                s.setAmDistributionSuspendEndTime(changeRecord.getAmDistributionSuspendEndTime());
                s.setPmDistributionInfo(changeRecord.getPmDistributionInfo());
                s.setPmDistributionStatus(changeRecord.getPmDistributionStatus());
                s.setPmDistributionStartDeliveryTime(changeRecord.getPmDistributionStartDeliveryTime());
                s.setPmDistributionSuspendStartTime(changeRecord.getPmDistributionSuspendStartTime());
                s.setPmDistributionSuspendEndTime(changeRecord.getPmDistributionSuspendEndTime());
                s.setIsShowAmDistribution(ObjectUtil.isEmpty(changeRecord.getIsShowAmDistribution()) ?
                    s.getIsShowAmDistribution() : changeRecord.getIsShowAmDistribution());
                s.setIsShowPmDistribution(ObjectUtil.isEmpty(changeRecord.getIsShowPmDistribution()) ?
                    s.getIsShowPmDistribution() : changeRecord.getIsShowPmDistribution());
            }
            //替换YN 让前端好判断
            if (SysYesNo.YES.getValue().equals(s.getAmDistributionStatus())) {
                s.setAmDistributionStatus("1");
            } else {
                s.setAmDistributionStatus("2");
            }
            if (SysYesNo.YES.getValue().equals(s.getPmDistributionStatus())) {
                s.setPmDistributionStatus("1");
            } else {
                s.setPmDistributionStatus("2");
            }

            if (SysYesNo.YES.getValue().equals(s.getIsShowAmDistribution())) {
                s.setIsShowAmDistribution("1");
            } else {
                s.setIsShowAmDistribution("2");
            }
            if (SysYesNo.YES.getValue().equals(s.getIsShowPmDistribution())) {
                s.setIsShowPmDistribution("3");
            } else {
                s.setIsShowPmDistribution("4");
            }

            //设置小区名称
            Long cxrResidentialQuartersId = s.getCxrResidentialQuartersId();
            if (cxrResidentialQuartersId != null) {
                CxrResidentialQuarters cxrResidentialQuarters =
                    cxrResidentialQuartersMapper.selectById(cxrResidentialQuartersId);
                s.setCxrResidentialQuartersName(cxrResidentialQuarters.getName());
            }
            //null的话设置 空数组
            if (s.getPmDistributionInfo() == null && s.getAmDistributionInfo() == null) {
                CustomerAddressMilkDistributionInfo pmInfo = new CustomerAddressMilkDistributionInfo();
                if (CollectionUtil.isNotEmpty(s.getCxrSaleProducts())) {
                    newMilkDistributionInfo(s, pmInfo);
                }
                s.setPmDistributionInfo(pmInfo);
                s.setAmDistributionInfo(pmInfo);
            } else if (s.getPmDistributionInfo() == null) {
                CustomerAddressMilkDistributionInfo pmInfo = new CustomerAddressMilkDistributionInfo();
                if (CollectionUtil.isNotEmpty(s.getCxrSaleProducts())) {
                    newMilkDistributionInfo(s, pmInfo);
                }
                s.setPmDistributionInfo(pmInfo);
            } else if (s.getAmDistributionInfo() == null) {
                CustomerAddressMilkDistributionInfo pmInfo = new CustomerAddressMilkDistributionInfo();
                if (CollectionUtil.isNotEmpty(s.getCxrSaleProducts())) {
                    newMilkDistributionInfo(s, pmInfo);
                }
                s.setAmDistributionInfo(pmInfo);
            } else {
                List<Long> saleIds =
                    s.getCxrSaleProducts().stream().map(SaleProductListVo::getId).collect(Collectors.toList());
                CustomerAddressMilkDistributionInfo pmDistributionInfo = s.getPmDistributionInfo();
                CustomerAddressMilkDistributionInfo amDistributionInfo = s.getAmDistributionInfo();

                useRemove(saleIds, pmDistributionInfo);
                useRemove(saleIds, amDistributionInfo);

            }

            List<SaleProductListVo> list =
                s.getCxrSaleProducts().stream().sorted(Comparator.comparing(SaleProductListVo::getProductAlias))
                    .collect(Collectors.toList());
            s.setCxrSaleProducts(list);
        });
        return cxrCustomerAddrVos;
    }

    @Override
    public List<CxrCustomerDistributionListRecord> selectEveryDayMilkTotal(RoadWayTotalBo bo) {

        //获取年份
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String format = simpleDateFormat.format(bo.getYear());
        String[] split = format.split("-");
        String yearString = split[0];

        List<CxrCustomerDistributionListRecord> list = cxrCustomerDistributionListRecordService.
            sumListRecordByCustomerId(bo.getCxrCustomerId(), yearString);

        LocalDate now = LocalDate.now();
        Integer year = now.getYear();
        CxrCustomerWayNumber wayNumber = null;
        if (ObjectUtil.equals(year.toString(), yearString)) {
            wayNumber = new LambdaQueryChainWrapper<>(cxrCustomerWayNumberMapper)
                .eq(CxrCustomerWayNumber::getCxrCustomerId, bo.getCxrCustomerId())
                .eq(CxrCustomerWayNumber::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .eq(CxrCustomerWayNumber::getDailyDate, now)
                .select(CxrCustomerWayNumber::getDailyBoxs)
                .one();
        }

        if (ObjectUtil.isEmpty(wayNumber)) {
            return list;
        }

        if (CollUtil.isNotEmpty(list)) {
            Map<String, CxrCustomerDistributionListRecord> recordMap = list.stream().collect(Collectors.toMap(s -> {
                    return s.getYearkmar() + "-" + s.getMonthmark();
                },
                Function.identity(), (v1, v2) -> v2));

            CxrCustomerDistributionListRecord cxrCustomerDistributionListRecord = recordMap.get(
                now.getYear() + "-" + now.getMonthValue());
            if (ObjectUtil.isNotEmpty(cxrCustomerDistributionListRecord)) {
                setDDAteData(cxrCustomerDistributionListRecord, wayNumber);
                cxrCustomerDistributionListRecord.setTotalSum(
                    cxrCustomerDistributionListRecord.getTotalSum());
            } else {
                cxrCustomerDistributionListRecord = new CxrCustomerDistributionListRecord();
                cxrCustomerDistributionListRecord.setYearkmar(now.getYear());
                cxrCustomerDistributionListRecord.setMonthmark(now.getMonthValue());
                cxrCustomerDistributionListRecord.setTotalSum(Convert.toLong(wayNumber.getDailyBoxs()));
                setDDAteData(cxrCustomerDistributionListRecord, wayNumber);
                list.add(cxrCustomerDistributionListRecord);
            }
        } else {
            list = new ArrayList<>();
            CxrCustomerDistributionListRecord cxrCustomerDistributionListRecord = new CxrCustomerDistributionListRecord();
            cxrCustomerDistributionListRecord.setYearkmar(now.getYear());
            cxrCustomerDistributionListRecord.setMonthmark(now.getMonthValue());
            cxrCustomerDistributionListRecord.setTotalSum(Convert.toLong(wayNumber.getDailyBoxs()));
            setDDAteData(cxrCustomerDistributionListRecord, wayNumber);
            list.add(cxrCustomerDistributionListRecord);
        }

        return list;
    }

    private static void setDDAteData(CxrCustomerDistributionListRecord cxrCustomerDistributionListRecord,
                                     CxrCustomerWayNumber wayNumber) {
        int dayOfMonth = LocalDate.now().getDayOfMonth();
        switch (dayOfMonth) {
            case 1:
                cxrCustomerDistributionListRecord.setD1Sum(Convert.toLong(wayNumber.getDailyBoxs(), 0L));
                break;
            case 2:
                cxrCustomerDistributionListRecord.setD2Sum(Convert.toLong(wayNumber.getDailyBoxs(), 0L));
                break;
            case 3:
                cxrCustomerDistributionListRecord.setD3Sum(Convert.toLong(wayNumber.getDailyBoxs(), 0L));
                break;
            case 4:
                cxrCustomerDistributionListRecord.setD4Sum(Convert.toLong(wayNumber.getDailyBoxs(), 0L));
                break;
            case 5:
                cxrCustomerDistributionListRecord.setD5Sum(Convert.toLong(wayNumber.getDailyBoxs(), 0L));
                break;
            case 6:
                cxrCustomerDistributionListRecord.setD6Sum(Convert.toLong(wayNumber.getDailyBoxs(), 0L));
                break;
            case 7:
                cxrCustomerDistributionListRecord.setD7Sum(Convert.toLong(wayNumber.getDailyBoxs(), 0L));
                break;
            case 8:
                cxrCustomerDistributionListRecord.setD8Sum(Convert.toLong(wayNumber.getDailyBoxs(), 0L));
                break;
            case 9:
                cxrCustomerDistributionListRecord.setD9Sum(Convert.toLong(wayNumber.getDailyBoxs(), 0L));
                break;
            case 10:
                cxrCustomerDistributionListRecord.setD10Sum(Convert.toLong(wayNumber.getDailyBoxs(), 0L));
                break;
            case 11:
                cxrCustomerDistributionListRecord.setD11Sum(Convert.toLong(wayNumber.getDailyBoxs(), 0L));
                break;
            case 12:
                cxrCustomerDistributionListRecord.setD12Sum(Convert.toLong(wayNumber.getDailyBoxs(), 0L));
                break;
            case 13:
                cxrCustomerDistributionListRecord.setD13Sum(Convert.toLong(wayNumber.getDailyBoxs(), 0L));
                break;
            case 14:
                cxrCustomerDistributionListRecord.setD14Sum(Convert.toLong(wayNumber.getDailyBoxs(), 0L));
                break;
            case 15:
                cxrCustomerDistributionListRecord.setD15Sum(Convert.toLong(wayNumber.getDailyBoxs(), 0L));
                break;
            case 16:
                cxrCustomerDistributionListRecord.setD16Sum(Convert.toLong(wayNumber.getDailyBoxs(), 0L));
                break;
            case 17:
                cxrCustomerDistributionListRecord.setD17Sum(Convert.toLong(wayNumber.getDailyBoxs(), 0L));
                break;
            case 18:
                cxrCustomerDistributionListRecord.setD18Sum(Convert.toLong(wayNumber.getDailyBoxs(), 0L));
                break;
            case 19:
                cxrCustomerDistributionListRecord.setD19Sum(Convert.toLong(wayNumber.getDailyBoxs(), 0L));
                break;
            case 20:
                cxrCustomerDistributionListRecord.setD20Sum(Convert.toLong(wayNumber.getDailyBoxs(), 0L));
                break;
            case 21:
                cxrCustomerDistributionListRecord.setD21Sum(Convert.toLong(wayNumber.getDailyBoxs(), 0L));
                break;
            case 22:
                cxrCustomerDistributionListRecord.setD22Sum(Convert.toLong(wayNumber.getDailyBoxs(), 0L));
                break;
            case 23:
                cxrCustomerDistributionListRecord.setD23Sum(Convert.toLong(wayNumber.getDailyBoxs(), 0L));
                break;
            case 24:
                cxrCustomerDistributionListRecord.setD24Sum(Convert.toLong(wayNumber.getDailyBoxs(), 0L));
                break;
            case 25:
                cxrCustomerDistributionListRecord.setD25Sum(Convert.toLong(wayNumber.getDailyBoxs(), 0L));
                break;
            case 26:
                cxrCustomerDistributionListRecord.setD26Sum(Convert.toLong(wayNumber.getDailyBoxs(), 0L));
                break;
            case 27:
                cxrCustomerDistributionListRecord.setD27Sum(Convert.toLong(wayNumber.getDailyBoxs(), 0L));
                break;
            case 28:
                cxrCustomerDistributionListRecord.setD28Sum(Convert.toLong(wayNumber.getDailyBoxs(), 0L));
                break;
            case 29:
                cxrCustomerDistributionListRecord.setD29Sum(Convert.toLong(wayNumber.getDailyBoxs(), 0L));
                break;
            case 30:
                cxrCustomerDistributionListRecord.setD30Sum(Convert.toLong(wayNumber.getDailyBoxs(), 0L));
                break;
            case 31:
                cxrCustomerDistributionListRecord.setD31Sum(Convert.toLong(wayNumber.getDailyBoxs(), 0L));
                break;
            default:
                break;
        }
    }


    @Override
    public Map<String, Object> selectYearMilkTotal(Long id, Date date) {
        //客户表中库存
        CxrCustomer cxrCustomer = baseMapper.getById(id);

        //增加客户地址
        CxrCustomerAddress cxrCustomerAddress = cxrCustomerAddressMapper.selectOne(
            new LambdaQueryWrapper<CxrCustomerAddress>()
                .eq(CxrCustomerAddress::getCxrCustomerId, id)
                .eq(CxrCustomerAddress::getDefalutAccountAddress, SysYesNo.YES.getValue())
                .last("limit 1")
        );
        String address = "--";
        if (ObjectUtil.isNotEmpty(cxrCustomerAddress)) {
            address =
                Convert.toStr(cxrCustomerAddress.getProvice(), "-") + Convert.toStr(cxrCustomerAddress.getCity(), "-")
                    + Convert.toStr(cxrCustomerAddress.getArea(), "-") + Convert.toStr(
                    cxrCustomerAddress.getDetailDistributionAddress(), "-");
        }
        Map<String, Object> map = new HashMap<>();
        map.put("sentTodal", Convert.toInt(cxrCustomer.getFreshMilkRoadWaySentTotal(), 0));  //鲜奶已送
        map.put("totalStock", Convert.toInt(cxrCustomer.getCustomerStock(), 0));//库存
        map.put("shouldSent", Convert.toInt(cxrCustomer.getShouldSend(), 0));//应送
        map.put("customerAddress", address);

        CustomerOrderStatisticsDTO customerOrderStatisticsDTO = remoteOrderService.customerOrderStatistics(id,
            DateUtils.getLocalDateFromDate(date).getYear());
        if (customerOrderStatisticsDTO == null) {
            customerOrderStatisticsDTO = new CustomerOrderStatisticsDTO();
        }

        map.put("orderQuantity", customerOrderStatisticsDTO.getOrderQuantity());//订购总数（）
        map.put("freshMilkOrderSentTotal", customerOrderStatisticsDTO.getFreshMilkSentQuantity()); //订单已送数

        map.put("freshMilkRoadWaySentTotal", customerOrderStatisticsDTO.getFreshMilkRoadWaySentTotal()); //路条已送数
        map.put("totalFreshMilkReturnQuantity", customerOrderStatisticsDTO.getFreshMilkReturnQuantity());     //退订数量
        map.put("totalMilkExchangeSum", customerOrderStatisticsDTO.getMilkExchangeSum());     //兑换数量
        map.put("tranInMilk", customerOrderStatisticsDTO.getTranInMilk());     //转入数量
        map.put("tranOutMilk", customerOrderStatisticsDTO.getTranOutMilk());     //转出数量
        return map;
    }

    @Override
    public List<CxrAddressHistoryVo> queryHistory(CxrCustomerAddressABo bo) {
//        List<CxrAddressHistory> cxrAddressHistories = cxrAddressHistoryService.getBaseMapper()
//            .selectList(new LambdaQueryWrapper<CxrAddressHistory>()
//                .eq(CxrAddressHistory::getCxrCustomerId, id)
//                .orderByDesc(CxrAddressHistory::getUpdateTime)
//
//            );
        List<CxrAddressHistoryVo> cxrAddressHistoryVos = cxrAddressHistoryMapper.list(bo);
        List<CxrCustomerAddress> customerAddressList = iCxrCustomerAddressService.lambdaQuery()
            .in(CxrCustomerAddress::getCxrCustomerId, bo.getId())
            .apply(" address_serial_number  is null ")
            .list();

        if (ObjectUtil.isNotEmpty(customerAddressList)) {
            cxrCustomerAddressMapper.updateAddressSerialNumber(bo.getId());
        }

//        List<CxrAddressHistoryVo> cxrAddressHistoryVos = BeanUtil.copyToList(cxrAddressHistories,
//            CxrAddressHistoryVo.class);
        List<Long> addressIds =
            cxrAddressHistoryVos.stream().map(CxrAddressHistoryVo::getCxrCustomerAddressId).distinct()
                .collect(Collectors.toList());

        Map<Long, CxrCustomerAddress> addressMap = null;
        if (CollectionUtil.isNotEmpty(addressIds)) {
            List<CxrCustomerAddress> customerAddresses =
                iCxrCustomerAddressService.lambdaQuery().in(CxrCustomerAddress::getId, addressIds).list();
            if (CollectionUtil.isNotEmpty(customerAddresses)) {
                addressMap =
                    customerAddresses.stream()
                        .collect(Collectors.toMap(CxrCustomerAddress::getId, Function.identity(), (v1, v2) -> v2));
            }
        }
        //没地址 用默认地址
        List<Long> notCusIdList =
            cxrAddressHistoryVos.stream().filter(s -> ObjectUtil.isEmpty(s.getCxrCustomerAddressId()))
                .map(CxrAddressHistoryVo::getCxrCustomerId).collect(Collectors.toList());
        Map<Long, CxrCustomerAddress> notAddrIdAddressMap = new HashMap<>(notCusIdList.size());
        if (CollectionUtil.isNotEmpty(notCusIdList)) {
            List<CxrCustomerAddress> notCusIdAddresses =
                iCxrCustomerAddressService.lambdaQuery()
                    .in(CxrCustomerAddress::getCxrCustomerId, notCusIdList)
                    .eq(CxrCustomerAddress::getDefalutAccountAddress, "Y")
                    .list();
            if (CollectionUtil.isNotEmpty(notCusIdAddresses)) {
                notAddrIdAddressMap =
                    notCusIdAddresses.stream()
                        .collect(Collectors.toMap(CxrCustomerAddress::getCxrCustomerId, Function.identity(), (v1, v2) -> v2));
            }
        }
        //处理数据
        Map<Long, CxrCustomerAddress> finalAddressMap = addressMap;
        Map<Long, CxrCustomerAddress> finalNotAddrIdAddressMap = notAddrIdAddressMap;
        List<CompletableFuture<Object>> completableFutures = cxrAddressHistoryVos.stream().map(s -> {
            return CompletableFuture.supplyAsync(() -> {

                if (CollectionUtil.isNotEmpty(finalAddressMap)) {
                    CxrCustomerAddress address = finalAddressMap.get(s.getCxrCustomerAddressId());
                    if (ObjectUtil.isNotEmpty(address)) {
                        s.setReceiverPhone(address.getReceiverPhone());
                        s.setReceiverName(address.getReceiverName());
                        s.setDetailDistributionAddress(address.getDetailDistributionAddress());
                        s.setAddressSerialNumber(address.getAddressSerialNumber());
                    } else {
                        //使用默认地址
                        CxrCustomerAddress cxrCustomerAddress = finalNotAddrIdAddressMap.get(s.getCxrCustomerId());
                        if (ObjectUtil.isNotEmpty(cxrCustomerAddress)) {
                            s.setReceiverPhone(cxrCustomerAddress.getReceiverPhone());
                            s.setReceiverName(cxrCustomerAddress.getReceiverName());
                            s.setDetailDistributionAddress(cxrCustomerAddress.getDetailDistributionAddress());
                            s.setAddressSerialNumber(cxrCustomerAddress.getAddressSerialNumber());
                        }
                    }
                }

                s.setAmMilkDistributionInfos(JSONUtil.toList(s.getAmMilkDistributionInfo(), MilkDistributionDTO.class));
                s.setPmMilkDistributionInfos(JSONUtil.toList(s.getPmMilkDistributionInfo(), MilkDistributionDTO.class));
                s.setAlterAmTastes(JSONUtil.toList(s.getAlterAmTaste(), MilkDistributionDTO.class));
                s.setAlterRecords(JSONUtil.toBean(s.getAlterRecord(), AlterRecord.class));
                s.setAlterPmTastes(JSONUtil.toList(s.getAlterPmTaste(), MilkDistributionDTO.class));
                boolean amFlag = true;
                boolean pmFlag = true;
                l:
                for (MilkDistributionDTO i : s.getAlterAmTastes()) {
                    if (i.isFrdc() || i.isModc() || i.isSadc() || i.isThdc() || i.isTudc() || i.isWedc()) {
                        amFlag = false;
                        break l;
                    }
                }

                a:
                for (MilkDistributionDTO i : s.getAlterPmTastes()) {
                    if (i.isFrdc() || i.isModc() || i.isSadc() || i.isThdc() || i.isTudc() || i.isWedc()) {
                        pmFlag = false;
                        break a;
                    }
                }

                if (amFlag) {
                    s.setAlterAmTastes(null);
                    s.setAmMilkDistributionInfos(null);
                }
                if (pmFlag) {
                    s.setAlterPmTastes(null);
                    s.setPmMilkDistributionInfos(null);
                }

                return null;
            });
        }).collect(Collectors.toList());

        CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).join();

        return cxrAddressHistoryVos;
    }

    @Override
    @CustomerAfter
    @Transactional(rollbackFor = Exception.class)
    public Long customerAdd(CxrCustomerAddrBo bo) {
        CxrCustomer cxr = this.baseMapper.queryByPhone(bo.getReceiverPhone(),
            DeleteStatus.NOT_DELETED.getValue());
        if (cxr != null) {
            throw new ServiceException("该客户手机号已存在!");
        }

        //保存客户
        CxrCustomer cxrCustomer = new CxrCustomer();
        cxrCustomer.setProvice(bo.getProvice());
        cxrCustomer.setCity(bo.getCity());
        cxrCustomer.setArea(bo.getArea());
        cxrCustomer.setDetailDistributionAddress(bo.getDetailDistributionAddress());
        cxrCustomer.setName(bo.getReceiverName());
        cxrCustomer.setGenderType(GenderType.UNKNOWN.getValue());
        cxrCustomer.setPhone(bo.getReceiverPhone());
        cxrCustomer.setSourceType("后台");
        this.save(cxrCustomer);
        //增加客户默认地址
        bo.setDefalutAccountAddress(SysYesNo.YES.getValue());
        bo.setCxrCustomerId(cxrCustomer.getId());
        addressAdd(bo);
        return cxrCustomer.getId();
    }

//  @Override
//  public List<String> getDate(Long id) {
//    List<String> years = detailMapper.getDateByCustomerId(id);
//    if (CollectionUtil.isEmpty(years) || years.size() < 1) {
//      Integer year = LocalDate.now().getYear();
//      years.add(year.toString());
//    }
//
//    return years;
//  }


    @Override
    public PageTableDataInfo<CxrAddressHistoryVo> findByStaffUserId(CxrAddressHistoryBo bo) {
        // 根据登录人id查询关联客户地址id
        Long userId = StaffLoginHelper.getLoginUser().getUserId();
        //员工名下没有客户地址 直接返回null
        //查找客户地址
//        List<CxrCustomerAddress> addrList = iCxrCustomerAddressService.list(new LambdaQueryWrapper<CxrCustomerAddress>()
//            .eq(CxrCustomerAddress::getCxrEmployeeId, userId)
//            .like(ObjectUtil.isNotNull(bo.getReceiverPhone()), CxrCustomerAddress::getReceiverPhone, bo.getReceiverPhone())
//            .and(ObjectUtil.isNotEmpty(bo.getDetailDistributionAddress()),wrapper -> {
//                    String keyword = bo.getDetailDistributionAddress();
//                    wrapper.like(CxrCustomerAddress::getDetailDistributionAddress, keyword)
//                        .or().like(CxrCustomerAddress::getProvice, keyword)
//                        .or().like(CxrCustomerAddress::getCity, keyword)
//                        .or().like(CxrCustomerAddress::getArea, keyword);
//            })
//            .last("limit 1000")
//        );
        bo.setUserId(userId);
        Page<CxrAddressHistory> cxrAddressHistoryPage = cxrCustomerAddressMapper.getAddressIdByEmIdAndBo(userId,
            DeleteStatus.NOT_DELETED.getValue(), bo, bo.build());
        if (CollectionUtil.isEmpty(cxrAddressHistoryPage.getRecords())) {
            List<CxrAddressHistoryVo> list = new ArrayList<>();
            return new PageTableDataInfo<CxrAddressHistoryVo>(list, 0L);
        }
        List<Long> ids = cxrAddressHistoryPage.getRecords().stream().map(CxrAddressHistory::getId)
            .collect(Collectors.toList());
        bo.setIds(ids);
        List<CxrAddressHistory> cxrAddressHistoryList = cxrCustomerAddressMapper.getAddressIdByEmIdAndBoNew(bo);
        cxrAddressHistoryPage.setRecords(cxrAddressHistoryList);
        log.info("历史分页" +
            "--------");
//        Page<CxrAddressHistory> cxrAddressHistoryPage = cxrAddressHistoryService.page(bo.build(),
//            new LambdaQueryWrapper<CxrAddressHistory>()
//                .in(CxrAddressHistory::getCxrCustomerAddressId, addressId)
//                .orderByDesc(CxrAddressHistory::getUpdateTime)
//        );

        if (CollectionUtil.isEmpty(cxrAddressHistoryPage.getRecords())) {
            return new PageTableDataInfo<CxrAddressHistoryVo>(new ArrayList<CxrAddressHistoryVo>(), 0L);
        }

        List<CxrAddressHistoryVo> cxrAddressHistoryVos = BeanUtil.copyToList(cxrAddressHistoryPage.getRecords(),
            CxrAddressHistoryVo.class);

        List<CxrAddressHistoryVo> addressHistoryVos = cxrAddressHistoryVos.stream()
            .sorted(Comparator.comparing(CxrAddressHistoryVo::getUpdateTime).reversed()).collect(Collectors.toList());

        List<Long> addressIds = cxrAddressHistoryPage.getRecords().stream().map(s -> s.getCxrCustomerAddressId())
            .collect(Collectors.toList());

        List<CxrCustomerAddress> cxrCustomerAddresses = cxrCustomerAddressMapper.selectList(
            new LambdaQueryWrapper<CxrCustomerAddress>()
                .in(CxrCustomerAddress::getId, addressIds)
        );

        Map<Long, CxrCustomerAddress> addressMap = cxrCustomerAddresses.stream()
            .collect(Collectors.toMap(CxrCustomerAddress::getId, Function.identity(), (v1, v2) -> v2));
        List<Long> notAddressIds = cxrAddressHistoryPage.getRecords().stream()
            .filter(s -> s.getCxrCustomerAddressId() == null)
            .map(CxrAddressHistory::getCxrCustomerId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(notAddressIds)) {
            List<CxrCustomerAddress> notCusIdAddresses =
                iCxrCustomerAddressService.lambdaQuery()
                    .in(CxrCustomerAddress::getCxrCustomerId, notAddressIds)
                    .eq(CxrCustomerAddress::getDefalutAccountAddress, "Y")
                    .list();
            if (CollectionUtil.isNotEmpty(notCusIdAddresses)) {
                for (CxrCustomerAddress notCusIdAddress : notCusIdAddresses) {
                    if (addressMap.containsKey(notCusIdAddress.getCxrCustomerId())) continue;
                    addressMap.put(notCusIdAddress.getCxrCustomerId(), notCusIdAddress);
                }
            }
        }
        for (CxrAddressHistoryVo s : addressHistoryVos) {
            Long addrId = s.getCxrCustomerAddressId() == null ? s.getCxrCustomerId() : s.getCxrCustomerAddressId();
            CxrCustomerAddress cxrCustomerAddress = addressMap.get(addrId);
            if (cxrCustomerAddress == null) {
                continue;
            }
            s.setReceiverName(cxrCustomerAddress.getReceiverName());
            s.setReceiverPhone(cxrCustomerAddress.getReceiverPhone());
            s.setDetailDistributionAddress(cxrCustomerAddress.getDetailDistributionAddress());
        }
        PageTableDataInfo<CxrAddressHistoryVo> cxrAddressHistoryVoPageTableDataInfo = new PageTableDataInfo<>(
            addressHistoryVos, cxrAddressHistoryPage.getTotal());
        cxrAddressHistoryVoPageTableDataInfo.setCurr(cxrAddressHistoryPage.getCurrent());
        cxrAddressHistoryVoPageTableDataInfo.setSize(cxrAddressHistoryPage.getSize());

        //进行地址 分页   in有修改的地址id
        return cxrAddressHistoryVoPageTableDataInfo;
    }

    @Override
    public List<CxrCustomerAddressHistory> queryListHistoryByIdAndDate(Long id, Date updateTime) {
        List<CxrCustomerAddressHistory> cxrCustomerAddressHistories =
            iCxrCustomerAddressHistoryService.selectByAddressIdAndTheNew(id, updateTime);

        cxrCustomerAddressHistories.forEach(s -> {
            if (s.getCxrResidentialQuartersId() != null) {
                CxrResidentialQuarters cxrResidentialQuarters =
                    cxrResidentialQuartersMapper.selectById(s.getCxrResidentialQuartersId());
                s.setCxrResidentialQuartersName(cxrResidentialQuarters.getName());
            }
        });

        return cxrCustomerAddressHistories;


    }


    @Override
    public CxrAddressHistoryVo getHistoryById(Long id) {
        CxrAddressHistory cxrAddressHistory = cxrAddressHistoryService.getById(id);
        CxrCustomerAddress cxrCustomerAddress = null;
        if (cxrAddressHistory.getCxrCustomerAddressId() == null) {
            cxrCustomerAddress =
                iCxrCustomerAddressService.lambdaQuery()
                    .in(CxrCustomerAddress::getCxrCustomerId, cxrAddressHistory.getCxrCustomerId())
                    .eq(CxrCustomerAddress::getDefalutAccountAddress, "Y")
                    .one();
        } else {
            cxrCustomerAddress = iCxrCustomerAddressService.getById(
                cxrAddressHistory.getCxrCustomerAddressId());
        }

        CxrAddressHistoryVo cxrAddressHistoryVo = BeanUtil.copyProperties(cxrAddressHistory, CxrAddressHistoryVo.class);
        if (ObjectUtil.isNotEmpty(cxrCustomerAddress)) {
            if (ObjectUtil.isEmpty(cxrCustomerAddress.getAddressSerialNumber())) {
                boolean b = cxrCustomerAddressMapper.updateAddressSerialNumber(cxrAddressHistory.getCxrCustomerId());
                if (b) {
                    cxrCustomerAddress = iCxrCustomerAddressService.getById(
                        cxrAddressHistory.getCxrCustomerAddressId());
                } else {
                    cxrCustomerAddressMapper.updateAddressSerialNumber(cxrAddressHistory.getCxrCustomerAddressId());
                    cxrCustomerAddress = iCxrCustomerAddressService.getById(cxrAddressHistory.getCxrCustomerId());
                }
            }
            cxrAddressHistoryVo.setAddressSerialNumber(Convert.toInt(cxrCustomerAddress.getAddressSerialNumber()));
            cxrAddressHistoryVo.setDetailDistributionAddress(
                Convert.toStr(cxrCustomerAddress.getDetailDistributionAddress(), "--"));
            cxrAddressHistoryVo.setReceiverName(cxrCustomerAddress.getReceiverName());
            cxrAddressHistoryVo.setReceiverPhone(cxrCustomerAddress.getReceiverPhone());

        }

        //转换    好判断
        cxrAddressHistoryVo.setAmMilkDistributionInfos(
            JSONUtil.toList(cxrAddressHistoryVo.getAmMilkDistributionInfo(), MilkDistributionDTO.class));
        cxrAddressHistoryVo.setPmMilkDistributionInfos(
            JSONUtil.toList(cxrAddressHistoryVo.getPmMilkDistributionInfo(), MilkDistributionDTO.class));
        cxrAddressHistoryVo.setAlterAmTastes(
            JSONUtil.toList(cxrAddressHistoryVo.getAlterAmTaste(), MilkDistributionDTO.class));
        cxrAddressHistoryVo.setAlterRecords(JSONUtil.toBean(cxrAddressHistoryVo.getAlterRecord(), AlterRecord.class));
        cxrAddressHistoryVo.setAlterPmTastes(
            JSONUtil.toList(cxrAddressHistoryVo.getAlterPmTaste(), MilkDistributionDTO.class));

        //根据boolean 判断
        boolean amFlag = true;
        boolean pmFlag = true;
        l:
        for (MilkDistributionDTO i : cxrAddressHistoryVo.getAlterAmTastes()) {
            if (i.isFrdc() || i.isModc() || i.isSadc() || i.isThdc() || i.isTudc() || i.isWedc()) {
                amFlag = false;
                break l;
            }
        }

        a:
        for (MilkDistributionDTO i : cxrAddressHistoryVo.getAlterPmTastes()) {
            if (i.isFrdc() || i.isModc() || i.isSadc() || i.isThdc() || i.isTudc() || i.isWedc()) {
                pmFlag = false;
                break a;
            }
        }

        //将  没有变化  使用的字段  滞空
        if (amFlag) {
            cxrAddressHistoryVo.setAlterAmTaste(null);
            cxrAddressHistoryVo.setAlterAmTastes(null);
            cxrAddressHistoryVo.setAmMilkDistributionInfo(null);
            cxrAddressHistoryVo.setAmMilkDistributionInfos(null);
        }
        if (pmFlag) {
            cxrAddressHistoryVo.setAlterPmTaste(null);
            cxrAddressHistoryVo.setAlterPmTastes(null);
            cxrAddressHistoryVo.setPmMilkDistributionInfo(null);
            cxrAddressHistoryVo.setPmMilkDistributionInfos(null);
        }
        return cxrAddressHistoryVo;
    }

    @Override
    public Object createLabeled(CustomerLabeledBo bo) {
//        CxrCustomer cxrCustomer = baseMapper.getById(bo.getId());
        List<CxrLabeleDTO> labele = bo.getLabele();
        if (CollectionUtils.isNotEmpty(labele) && labele.size() > 20) {
            throw new ServiceException("最多只能添加20个标签");
        }
        CxrCustomer cxrCustomer = baseMapper.selectById(bo.getId());
        int count = baseMapper.update(null, new LambdaUpdateWrapper<CxrCustomer>()
            .eq(CxrCustomer::getId, bo.getId())
            .set(CxrCustomer::getLabeled, JSONUtil.toJsonStr(labele))
        );
        if (count > 0 && Objects.nonNull(cxrCustomer)) {
            this.syncUpdateCustomerTag(bo.getLabele(), cxrCustomer);
        }
        return count;
    }

    //新增客户的标签，根据名称写死
    @Override
    @RedisDistributedLock(value = "'cxr:customer:update:lable:' + #customerId")
    public void addCustomerLabeled(Long customerId, String labeledName) {
        CxrCustomer cxrCustomer = baseMapper.selectById(customerId);
        if (cxrCustomer == null) {
            throw new ServiceException("客户不存在");
        }
        //查询标签
        CxrLabeled cxrLabeled = cxrLabeledMapper.selectOne(new LambdaQueryWrapper<CxrLabeled>()
            .eq(CxrLabeled::getName, labeledName)
            .eq(CxrLabeled::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
        );
        if (cxrLabeled == null) {
            throw new ServiceException("标签不存在");
        }
        //判断是否已存在
        List<CxrLabeleDTO> labeledList = cxrCustomer.getLabeled();
        if (CollectionUtils.isNotEmpty(labeledList)) {
            for (CxrLabeleDTO cxrLabeleDTO : labeledList) {
                if (cxrLabeleDTO.getName().equals(labeledName)) {
                    return;
                }
            }
        }
        CxrLabeleDTO cxrLabeleDTO = new CxrLabeleDTO();
        cxrLabeleDTO.setId(cxrLabeled.getId());
        cxrLabeleDTO.setName(cxrLabeled.getName());
        if (CollectionUtils.isNotEmpty(labeledList)) {
            labeledList.add(cxrLabeleDTO);
        } else {
            labeledList = new ArrayList<>();
            labeledList.add(cxrLabeleDTO);
        }
        cxrCustomer.setLabeled(labeledList);
        baseMapper.update(null, new LambdaUpdateWrapper<CxrCustomer>()
            .eq(CxrCustomer::getId, customerId)
            .set(CxrCustomer::getLabeled, JSONUtil.toJsonStr(labeledList))
        );
    }

    //取消客户的标签，根据名称写死
    @Override
    public void delCustomerLabeled(Long customerId, String labeledName) {
        CxrCustomer cxrCustomer = baseMapper.selectById(customerId);
        if (cxrCustomer == null) {
            throw new ServiceException("客户不存在");
        }
        List<CxrLabeleDTO> labeledList = cxrCustomer.getLabeled();
        if (CollectionUtils.isNotEmpty(labeledList)) {
            labeledList.removeIf(item -> item.getName().equals(labeledName));
        }
        cxrCustomer.setLabeled(labeledList);
        baseMapper.update(null, new LambdaUpdateWrapper<CxrCustomer>()
            .eq(CxrCustomer::getId, customerId)
            .set(CxrCustomer::getLabeled, JSONUtil.toJsonStr(labeledList))
        );
    }

    public CxrCustomer selectById(Long id) {
        return baseMapper.selectOne(new LambdaQueryWrapper<CxrCustomer>().eq(CxrCustomer::getId, id).eq(CxrCustomer::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));
    }

    public int updateWxById(String openId, String unionId, Long customerId) {
        log.info("更新客户微信openId={},unionId={},customerId={}", openId, unionId, customerId);
        return baseMapper.update(null, new LambdaUpdateWrapper<CxrCustomer>()
            .eq(CxrCustomer::getId, customerId)
            .set(CxrCustomer::getWxOpenId, openId)
            .set(CxrCustomer::getWxUnionId, unionId)
        );
    }

    @Override
    public void syncUpdateCustomerTag(List<CxrLabeleDTO> newLabeleList, CxrCustomer cxrCustomer) {
        CompletableFuture.runAsync(new Runnable() {
            @Override
            public void run() {
                String wxOpenId = cxrCustomer.getWxOpenId();
                List<CxrLabeleDTO> oldLabeledList = cxrCustomer.getLabeled();
                com.alibaba.fastjson.JSONObject syncResult = new com.alibaba.fastjson.JSONObject();
                if (CollectionUtils.isNotEmpty(oldLabeledList)) {
                    oldLabeledList.forEach(item -> {
                        com.alibaba.fastjson.JSONObject deletetagging = remoteLabeledService.batchBindOrUnBindCustomerTags(
                            "batchuntagging", item.getId(),
                            StringUtils.isNotBlank(wxOpenId) ? Sets.newHashSet(wxOpenId) : null);
                        syncResult.put("errcode", deletetagging.get("errcode"));
                        syncResult.put("errmsg", deletetagging.get("errmsg"));
                        log.info("\n同步取消公众号用户[openId={}]标签[{}={}]结果：{}", wxOpenId, item.getId(),
                            item.getName(), deletetagging);
                    });
                }
                newLabeleList.forEach(item -> {
                    com.alibaba.fastjson.JSONObject reBindTag = remoteLabeledService.batchBindOrUnBindCustomerTags(
                        "batchtagging", item.getId(),
                        StringUtils.isNotBlank(wxOpenId) ? Sets.newHashSet(wxOpenId) : null);
                    syncResult.put("errcode", reBindTag.get("errcode"));
                    syncResult.put("errmsg", reBindTag.get("errmsg"));
                    log.info("\n同步重新绑定公众号用户[openId={}]标签[{}={}]结果：{}", wxOpenId, item.getId(),
                        item.getName(), reBindTag);
                });
                String errmsg = syncResult.getString("errmsg");
                int count = baseMapper.update(null, new LambdaUpdateWrapper<CxrCustomer>()
                    .eq(CxrCustomer::getId, cxrCustomer.getId())
                    .set(CxrCustomer::getLabeled, JSONUtil.toJsonStr(newLabeleList))
                    .set(Objects.isNull(wxOpenId) || !StringUtils.equals(errmsg, "ok"), CxrCustomer::getRemark,
                        Objects.isNull(wxOpenId) ? "同步公众号绑定标签失败:用户未绑定微信，openId为空"
                            : String.join(":", "同步公众号绑定用户标签失败", errmsg))
                );
                log.info("\n更新ERP用户[{}={}]标签结果：{}", cxrCustomer.getId(), cxrCustomer.getName(), count > 0);
            }
        });
    }

    @Override
    public PageTableDataInfo<CxrLabeledVo> labeledPage(CxrLabeledBo bo, PageQuery pageQuery) {
        if (pageQuery.getPageSize() == null) {
            pageQuery.setPageSize(10);
        }
        Page<CxrLabeled> labeledPage = cxrLabeledMapper.selectPage(pageQuery.build(),
            new LambdaQueryWrapper<CxrLabeled>()
                .like(ObjectUtil.isNotEmpty(bo.getLabeledName()), CxrLabeled::getName, bo.getLabeledName())
                .eq(CxrLabeled::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
        );
        PageTableDataInfo tableDataInfo = new PageTableDataInfo();

        List<CxrLabeledVo> cxrLabeledVos = BeanCollectionUtils.copyListProperties(labeledPage.getRecords(),
            CxrLabeledVo::new);
        tableDataInfo.setRows(cxrLabeledVos);
        tableDataInfo.setSize(pageQuery.getPageSize());
        tableDataInfo.setTotal(labeledPage.getTotal());
        tableDataInfo.setCurr(pageQuery.getPageNum());
        return tableDataInfo;
    }

    @Override
    public Boolean updateCustomerRemark(String remark, Long customerId) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        return baseMapper.update(null, new LambdaUpdateWrapper<CxrCustomer>().eq(CxrCustomer::getId, customerId)
            .set(CxrCustomer::getUpdateBy, loginUser.getUserId())
            .set(CxrCustomer::getUpdateByName, loginUser.getUserName())
            .set(CxrCustomer::getUpdateTime, new Date())
            .set(CxrCustomer::getUpdateByType, loginUser.getUserType())
            .set(CxrCustomer::getRemark, remark)) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateCustomerPhone(String phone, Long customerId,boolean unBind) {
        LoginUser loginUser = LoginHelper.getLoginUser();

        Long count = baseMapper.selectCount(
            new LambdaQueryWrapper<CxrCustomer>().eq(CxrCustomer::getPhone, phone.trim())
                .eq(CxrCustomer::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
        );

        if (count > 0) {
            throw new ServiceException("该账号已存在!");
        }

        //同步慧博手机号的修改
        //1.把之前的手机号传过去
        //2.把修改完之后的手机号
        CxrCustomer customer = baseMapper.selectOne(new LambdaQueryWrapper<CxrCustomer>()
            .eq(CxrCustomer::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            .eq(CxrCustomer::getId, customerId));

        int num = baseMapper.update(null, new LambdaUpdateWrapper<CxrCustomer>()
            .eq(CxrCustomer::getId, customerId)
            .set(unBind,CxrCustomer::getWxOpenId,null)
            .set(unBind,CxrCustomer::getWxUnionId,null)
            .set(CxrCustomer::getUpdateBy, loginUser.getUserId())
            .set(CxrCustomer::getUpdateByName, loginUser.getUserName())
            .set(CxrCustomer::getUpdateTime, new Date())
            .set(CxrCustomer::getUpdateByType, loginUser.getUserType())
            .set(CxrCustomer::getPhone, phone));
        if (num < 0) {
            throw new ServiceException("修改手机号码失败!");
        }

        //走mq修改
        CustomersVo cxrCustomerVo = new CustomersVo();
        cxrCustomerVo.setId(customer.getId());
        cxrCustomerVo.setName(loginUser.getUserName());
        cxrCustomerVo.setPhone(phone);
        cxrCustomerVo.setFolmerPhone(customer.getPhone());
        cxrCustomerVo.setUserType(loginUser.getUserType());

        //修改订单里面的手机号码
        mqUtil.sendSyncMessage(UpdatePhoneCustomerConstant.UPDATEPHONECUSTOMER_TOPIC,
            UpdatePhoneCustomerConstant.UPDATEPHONECUSTOMER_ORDER_TAG,
            JSONUtil.toJsonStr(cxrCustomerVo));

        //修改路条里面的手机号码
        mqUtil.sendSyncMessage(UpdatePhoneCustomerConstant.UPDATEPHONECUSTOMER_TOPIC,
            UpdatePhoneCustomerConstant.UPDATEPHONECUSTOMER_ROAD_TAG,
            JSONUtil.toJsonStr(cxrCustomerVo));

        //修改临时加奶
        mqUtil.sendSyncMessage(UpdatePhoneCustomerConstant.UPDATEPHONECUSTOMER_TOPIC,
            UpdatePhoneCustomerConstant.UPDATEPHONECUSTOMER_MILK_TAG,
            JSONUtil.toJsonStr(cxrCustomerVo));

        //修改转奶手机号码
        mqUtil.sendSyncMessage(UpdatePhoneCustomerConstant.UPDATEPHONECUSTOMER_TOPIC,
            UpdatePhoneCustomerConstant.UPDATETRANSFER_MILK_TAG,
            JSONUtil.toJsonStr(cxrCustomerVo));

        mqUtil.sendDelayMessage(CxrCustomerConstant.CUSTOMER_TOPIC, CxrCustomerConstant.YDS_CUSTOMER_SYNC_TAG, cxrCustomerVo.getId().toString(), 10);

        //手机号码的私域绑定
        CxrCustomer cxrCustomer = new CxrCustomer();
        cxrCustomer.setWxUnionId(customer.getWxUnionId());
        cxrCustomer.setWxOpenId(customer.getWxOpenId());
        cxrCustomer.setPhone(customer.getPhone());
        mqUtil.sendSyncMessage(HuiboConstant.HUIBO_TOPIC, HuiboConstant.HUIBO_CONSUMER_WX,
            JSONUtil.toJsonStr(cxrCustomer));

        //同步修改历史
        AddressHistoryMqDoMain addressHistoryMqDoMain = new AddressHistoryMqDoMain();
        addressHistoryMqDoMain.setCxrCustomerId(customerId);
        AlterRecord alterRecord = new AlterRecord();
        CustomerPhoneUpdateDTO customerLoginPhoneUpdate = new CustomerPhoneUpdateDTO();
        customerLoginPhoneUpdate.setNewPhone(phone);
        customerLoginPhoneUpdate.setOldPhone(customer.getPhone());
        alterRecord.setCustomerLoginPhoneUpdate(customerLoginPhoneUpdate);
        addressHistoryMqDoMain.setAlterRecord(alterRecord);
        addressHistoryMqDoMain.setLoginInfo(LoginUtil.getLoginUser());
        addressHistoryMqDoMain.setUuid(java.util.UUID.randomUUID().toString());
        mqUtil.sendSyncMessage(CustomerAddressConstant.CUSTOMER_ADDRESS_TOPIC,
            CustomerAddressConstant.CUSTOMER_ADDRESS_HISTORY_TAG, JSONUtil.toJsonStr(addressHistoryMqDoMain));

        //手机号码的修改
        Map<String, Object> map = new HashMap<>();
        map.put("platUserType", HuiBoPlatUserType.UNOINID.getValue());
        map.put("platUserId", customer.getWxUnionId());
        map.put("mobile", phone);
        map.put("brandId", "123456789000378189");
        long timestamp = System.currentTimeMillis();

        String appKey = huiBoConfig.getAppKey();
        String sellerNick = huiBoConfig.getSellerNick();
        String secret = huiBoConfig.getSecret();
        String jsonStr = JSONUtil.toJsonStr(map);
        StringBuffer sb = new StringBuffer();
        sb
            .append(appKey)
            .append(sellerNick)
            .append(timestamp)
            .append(jsonStr)
            .append(secret);

        String sign = null;
        try {
            sign = HuiBoSignUtil.getSign(sb.toString());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        String body = HttpRequest.post("https://api.jkcrm.cn/cloud/member/updateMob" +
                StrUtil.format("?app_key={}&&seller_nick={}&&timestamp={}&&sign={}", appKey, sellerNick, timestamp, sign))
            .header("Content-Type", "application/json")
            .body(jsonStr)
            .execute()
            .body();

        System.out.println(body);
        log.info("----------  同步慧博会员 mq 消费成功   ----------------");
        //小程序修改手机号
        updateXCXCustomerPhone(phone, customerId,unBind);
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    public R updateCustomerPhone(String phone, Long customerId, Long existCustomerId) {
        PhoneNumberValidator.isPhone(phone);
        CxrCustomer cxrCustomer = queryByPhone(phone);
        if (cxrCustomer != null) {
            if (existCustomerId == null) {
                return R.ok(HttpStatus.CONFIRM_CODE, "账号已经存在", cxrCustomer);
            }
            AbstractAssert.isTrue(!cxrCustomer.getId().equals(existCustomerId), "请传入正确的 existCustomerId");
            CxrCustomer customer = baseMapper.selectOne(new LambdaQueryWrapper<CxrCustomer>()
                .likeRight(CxrCustomer::getPhone, StrUtil.format("{}-", phone))
                .eq(CxrCustomer::getDeleteStatus, DeleteStatus.not_deleted)
                .orderByDesc(CxrCustomer::getPhone)
                .last("LIMIT 1"));
            long maxIndex = 1;
            if (customer != null) {
                String maxPhone = customer.getPhone();
                String lastIndex = maxPhone.substring(maxPhone.indexOf(StrUtil.DASHED) + 1);
                maxIndex = Long.parseLong(lastIndex) + 1;
            }
            String afterPhone = StrUtil.format("{}-{}", phone, maxIndex);
            updateCustomerPhone(afterPhone, existCustomerId,true);
        }
        updateCustomerPhone(phone, customerId,false);
        return R.ok();
    }

    @Override
    public Integer updateCustomerName(String name, Long customerId) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        int num = baseMapper.update(null, new LambdaUpdateWrapper<CxrCustomer>()
            .eq(CxrCustomer::getId, customerId)
            .set(CxrCustomer::getName, name)
            .set(CxrCustomer::getUpdateByName, loginUser.getUserName())
            .set(CxrCustomer::getUpdateTime, new Date())
            .set(CxrCustomer::getUpdateBy, loginUser.getUserId())
            .set(CxrCustomer::getUpdateByType, loginUser.getUserType()));
        if (num <= 0) {
            throw new ServiceException("用户名字修改失败!");
        }
        mqUtil.sendSyncMessage(CxrCustomerConstant.CUSTOMER_TOPIC, CxrCustomerConstant.YDS_CUSTOMER_SYNC_TAG,
            customerId.toString());
        return num;
    }

    private void updateXCXCustomerPhone(String phone, Long customerId,boolean unBind) {
        Map<String, Object> data = new HashMap<>();
        data.put("erpId", customerId);
        data.put("erpPhone", phone);
        data.put("unBind", unBind);
        HttpResponse execute = HttpRequest
            .post(appletConfig.getSendUpdatePhone())
            .body(JSONUtil.toJsonStr(data))
            .execute();
        log.info("打印发请求的数据{}", phone);
        log.info("打印发请求的数据{}", JSONUtil.toJsonStr(execute.body()));
    }


    @Override
    public CxrCustomerTransferMilkDetail queryCustomerTransferById(Long customerId) {
        CxrCustomer customer = lambdaQuery()
            .eq(CxrCustomer::getId, customerId)
            .eq(CxrCustomer::getDeleteStatus, DeleteStatus.not_deleted)
            .select(CxrCustomer::getCustomerStock)
            .one();
        Integer i = remoteServiceWorkOrderService.queryInApplicationNum(customerId);

        CxrCustomerTransferMilkDetail cxrCustomerTransferMilkDetail = new CxrCustomerTransferMilkDetail();

        cxrCustomerTransferMilkDetail.setInApplicationNum(Convert.toInt(i, 0));
        cxrCustomerTransferMilkDetail.setSurplusNum(Convert.toInt(customer.getCustomerStock(), 0));
        cxrCustomerTransferMilkDetail.setTransferableNum(
            Convert.toInt(customer.getCustomerStock(), 0) - Convert.toInt(i, 0));

        return cxrCustomerTransferMilkDetail;
    }

    @Override
    public boolean del(Collection<Long> ids) {
        int num = RandomUtil.randomInt(100, 999);

        LoginInfo loginUser = LoginUtil.getLoginUser();

        ids.forEach(id -> {
            if (baseMapper.del(id, num, loginUser) == 0) {
                throw new ServiceException("客户删除失败");
            }
            boolean isSuccess = iCxrCustomerAddressService.lambdaUpdate()
                .set(CxrCustomerAddress::getDeleteStatus, DeleteStatus.DELETED.getValue())
                .set(CxrCustomerAddress::getDeleteBy, loginUser.getUserId())
                .set(CxrCustomerAddress::getDeleteByName, loginUser.getUserName())
                .set(CxrCustomerAddress::getDeleteByType, loginUser.getUserType())
                .set(CxrCustomerAddress::getDeleteTime, new Date())
                .eq(CxrCustomerAddress::getCxrCustomerId, id).update();
            if (isSuccess) {
                CxrCustomer cxrCustomer = baseMapper.selectById(id);
                log.info("\n客户[{}]的openId:{}", cxrCustomer.getName(), cxrCustomer.getWxOpenId());
                List<CxrLabeleDTO> labeledList = cxrCustomer.getLabeled();
                if (Objects.nonNull(cxrCustomer.getWxOpenId()) && CollectionUtils.isNotEmpty(labeledList)) {
                    String wxOpenId = cxrCustomer.getWxOpenId();
                    labeledList.forEach(item -> {
                        com.alibaba.fastjson.JSONObject deletetagging = remoteLabeledService.batchBindOrUnBindCustomerTags(
                            "batchuntagging", item.getId(),
                            StringUtils.isNotBlank(wxOpenId) ? Sets.newHashSet(wxOpenId) : null);
                        log.info("\n同步取消公众号用户[{}={}]标签结果：{}", item.getId(), item.getName(), deletetagging);
                    });
                }
            }
        });
        return true;
    }

    @Override
    public Integer checkStock() {
        Long userId = CustomerLoginHelper.getLoginUser().getUserId();
        CxrCustomer cxrCustomer = baseMapper.getById(userId);
        Long availableQuantity = Long.valueOf(cxrCustomer.getCustomerStock()) - cxrCustomer.getLockStock();

        List<CxrCustomerStockLockDetail> stockLockDetails = stockLockDetailMapper.selectList(
            new LambdaQueryWrapper<CxrCustomerStockLockDetail>()
                .select(CxrCustomerStockLockDetail::getLockNum)
                .eq(CxrCustomerStockLockDetail::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .eq(CxrCustomerStockLockDetail::getCustomerId, userId)
                .eq(CxrCustomerStockLockDetail::getStockType, 1)
        );
        Long sum = 0l;
        if (CollUtil.isNotEmpty(stockLockDetails)) {
            sum = stockLockDetails.stream().mapToLong(CxrCustomerStockLockDetail::getLockNum).sum();
        }
        if ((availableQuantity - sum) <= 0l) {// ps 前端、产品协商这样处理 -1为调整奶  0为售后
            if (sum > 0l) {
                return -1;
            } else {
                return 0;
            }

        }
        return 1; // 库存为0 && lockStock<0  也放回true
    }

    /**
     * 查实际可用库存
     */
    @Override
    public Long queryActualStock(Long customerId) {
        CxrCustomer cxrCustomer = baseMapper.getById(customerId);
        Integer totalLockNum = ObjectUtil.defaultIfNull(cxrCustomerStockLockDetailService.totalLockNum(customerId), 0);
        return Long.valueOf(cxrCustomer.getCustomerStock()) - cxrCustomer.getLockStock() - totalLockNum;
    }

    @Override
    public boolean updateCustomerInfoByWxUnionId(MallUserInfoDTO mallUserInfoDTO) {
        if (StrUtil.isNotBlank(mallUserInfoDTO.getUnionId())) {
            String headImgUrl = mallUserInfoDTO.getHeadImgUrl();
            String nickname = mallUserInfoDTO.getNickname();
            return this.lambdaUpdate().eq(CxrCustomer::getWxUnionId, mallUserInfoDTO.getUnionId())
                .set(StrUtil.isNotBlank(headImgUrl), CxrCustomer::getWxHeadPortrait, headImgUrl)
                .set(StrUtil.isNotBlank(nickname), CxrCustomer::getWxNickname, nickname)
                .update();
        }
        return false;
    }

    @Override
    public boolean saveCxrCustomerChangeRecordDemo(CxrCustomerChangeRecord customerChangeRecord) {
        addressService.saveCxrCustomerChangeRecord(customerChangeRecord);
        return false;
    }

    private static void setDDAteData(CustomerDistributionListRecordVo cxrCustomerDistributionListRecord,
                                     Integer wayNumber) {
        int dayOfMonth = LocalDate.now().getDayOfMonth();
        switch (dayOfMonth) {
            case 1:
                cxrCustomerDistributionListRecord.setD1Sum(Convert.toLong(wayNumber, 0L));
                break;
            case 2:
                cxrCustomerDistributionListRecord.setD2Sum(Convert.toLong(wayNumber, 0L));
                break;
            case 3:
                cxrCustomerDistributionListRecord.setD3Sum(Convert.toLong(wayNumber, 0L));
                break;
            case 4:
                cxrCustomerDistributionListRecord.setD4Sum(Convert.toLong(wayNumber, 0L));
                break;
            case 5:
                cxrCustomerDistributionListRecord.setD5Sum(Convert.toLong(wayNumber, 0L));
                break;
            case 6:
                cxrCustomerDistributionListRecord.setD6Sum(Convert.toLong(wayNumber, 0L));
                break;
            case 7:
                cxrCustomerDistributionListRecord.setD7Sum(Convert.toLong(wayNumber, 0L));
                break;
            case 8:
                cxrCustomerDistributionListRecord.setD8Sum(Convert.toLong(wayNumber, 0L));
                break;
            case 9:
                cxrCustomerDistributionListRecord.setD9Sum(Convert.toLong(wayNumber, 0L));
                break;
            case 10:
                cxrCustomerDistributionListRecord.setD10Sum(Convert.toLong(wayNumber, 0L));
                break;
            case 11:
                cxrCustomerDistributionListRecord.setD11Sum(Convert.toLong(wayNumber, 0L));
                break;
            case 12:
                cxrCustomerDistributionListRecord.setD12Sum(Convert.toLong(wayNumber, 0L));
                break;
            case 13:
                cxrCustomerDistributionListRecord.setD13Sum(Convert.toLong(wayNumber, 0L));
                break;
            case 14:
                cxrCustomerDistributionListRecord.setD14Sum(Convert.toLong(wayNumber, 0L));
                break;
            case 15:
                cxrCustomerDistributionListRecord.setD15Sum(Convert.toLong(wayNumber, 0L));
                break;
            case 16:
                cxrCustomerDistributionListRecord.setD16Sum(Convert.toLong(wayNumber, 0L));
                break;
            case 17:
                cxrCustomerDistributionListRecord.setD17Sum(Convert.toLong(wayNumber, 0L));
                break;
            case 18:
                cxrCustomerDistributionListRecord.setD18Sum(Convert.toLong(wayNumber, 0L));
                break;
            case 19:
                cxrCustomerDistributionListRecord.setD19Sum(Convert.toLong(wayNumber, 0L));
                break;
            case 20:
                cxrCustomerDistributionListRecord.setD20Sum(Convert.toLong(wayNumber, 0L));
                break;
            case 21:
                cxrCustomerDistributionListRecord.setD21Sum(Convert.toLong(wayNumber, 0L));
                break;
            case 22:
                cxrCustomerDistributionListRecord.setD22Sum(Convert.toLong(wayNumber, 0L));
                break;
            case 23:
                cxrCustomerDistributionListRecord.setD23Sum(Convert.toLong(wayNumber, 0L));
                break;
            case 24:
                cxrCustomerDistributionListRecord.setD24Sum(Convert.toLong(wayNumber, 0L));
                break;
            case 25:
                cxrCustomerDistributionListRecord.setD25Sum(Convert.toLong(wayNumber, 0L));
                break;
            case 26:
                cxrCustomerDistributionListRecord.setD26Sum(Convert.toLong(wayNumber, 0L));
                break;
            case 27:
                cxrCustomerDistributionListRecord.setD27Sum(Convert.toLong(wayNumber, 0L));
                break;
            case 28:
                cxrCustomerDistributionListRecord.setD28Sum(Convert.toLong(wayNumber, 0L));
                break;
            case 29:
                cxrCustomerDistributionListRecord.setD29Sum(Convert.toLong(wayNumber, 0L));
                break;
            case 30:
                cxrCustomerDistributionListRecord.setD30Sum(Convert.toLong(wayNumber, 0L));
                break;
            case 31:
                cxrCustomerDistributionListRecord.setD31Sum(Convert.toLong(wayNumber, 0L));
                break;
            default:
                break;
        }
    }

    /**
     * 校验排奶变更状态是否一致
     * 比较前端传入的变更状态与后端计算的变更状态，如果不一致则抛出异常提醒用户刷新页面重试
     *
     * @param bo      前端传入的地址BO对象，包含amDistributionDTOList和pmDistributionDTOList
     * @param address 数据库中的地址信息，包含amDistributionInfo和pmDistributionInfo
     */
    private void validateMilkDistributionChanges(CxrCustomerAddrBo bo, CxrCustomerAddress address) {
        log.info("开始校验排奶变更状态，地址ID：{}", bo.getId());

        try {
            // 校验上午排奶变更状态
            validateDistributionChanges(
                bo.getAlterAmTaste(),
                bo.getAmDistributionInfo(),
                address.getAmDistributionInfo(),
                "上午"
            );

            // 校验下午排奶变更状态
            validateDistributionChanges(
                bo.getAlterPmTaste(),
                bo.getPmDistributionInfo(),
                address.getPmDistributionInfo(),
                "下午"
            );

            log.info("排奶变更状态校验通过，地址ID：{}", bo.getId());
        } catch (Exception e) {
            log.error("排奶变更状态校验失败，地址ID：{}，错误信息：{}", bo.getId(), e.getMessage());
            throw e;
        }
    }

    /**
     * 校验单个时段（上午或下午）的排奶变更状态
     *
     * @param frontendDTOList          前端传入的排奶DTO列表，包含变更状态标识
     * @param frontendDistributionInfo 前端传入的排奶信息
     * @param backendDistributionInfo  后端数据库中的排奶信息
     * @param timeSlot                 时段标识（上午/下午），用于错误提示
     */
    private void validateDistributionChanges(
        List<MilkDistributionDTO> frontendDTOList,
        CustomerAddressMilkDistributionInfo frontendDistributionInfo,
        CustomerAddressMilkDistributionInfo backendDistributionInfo,
        String timeSlot) {

        if (CollUtil.isEmpty(frontendDTOList)) {
            log.debug("{}排奶DTO列表为空，跳过校验", timeSlot);
            return;
        }

        // 计算后端的变更状态
        Map<Long, Boolean[]> backendChangeStatus = calculateBackendChangeStatus(
            frontendDistributionInfo, backendDistributionInfo);

        // 比较前端和后端的变更状态
        for (MilkDistributionDTO dto : frontendDTOList) {
            Long productId = dto.getProductId();
            Boolean[] backendChanges = backendChangeStatus.get(productId);

            if (backendChanges == null) {
                log.warn("{}排奶校验：产品ID {}在后端计算结果中不存在", timeSlot, productId);
                continue;
            }

            // 比较各个星期的变更状态
            // 索引对应：0-周一, 1-周二, 2-周三, 3-周四, 4-周五, 5-周六
            boolean[] frontendChanges = {
                dto.isModc(),  // 周一
                dto.isTudc(),  // 周二
                dto.isWedc(),  // 周三
                dto.isThdc(),  // 周四
                dto.isFrdc(),  // 周五
                dto.isSadc()   // 周六
            };

            String[] dayNames = {"周一", "周二", "周三", "周四", "周五", "周六"};

            for (int i = 0; i < frontendChanges.length; i++) {
                if (frontendChanges[i] != backendChanges[i]) {
                    String errorMsg = String.format(
                        "排奶变更状态不一致！%s产品[%s]的%s变更状态前后端不匹配，请刷新页面重试。前端状态：%s，后端状态：%s",
                        timeSlot, dto.getProductName(), dayNames[i],
                        frontendChanges[i], backendChanges[i]
                    );
                    log.error(errorMsg);
                    throw new ServiceException("排奶信息失败，请刷新页面重试");
                }
            }
        }

        log.debug("{}排奶变更状态校验通过", timeSlot);
    }

    /**
     * 计算后端的排奶变更状态
     * 通过比较前端传入的排奶信息与数据库中的排奶信息，计算出每个产品每天的变更状态
     *
     * @param frontendInfo 前端传入的排奶信息
     * @param backendInfo  数据库中的排奶信息
     * @return Map<产品ID, Boolean数组 [ 周一到周六的变更状态 ]>
     */
    private Map<Long, Boolean[]> calculateBackendChangeStatus(
        CustomerAddressMilkDistributionInfo frontendInfo,
        CustomerAddressMilkDistributionInfo backendInfo) {

        Map<Long, Boolean[]> changeStatus = new HashMap<>();

        if (frontendInfo == null || backendInfo == null) {
            log.debug("前端或后端排奶信息为空，返回空的变更状态");
            return changeStatus;
        }

        // 比较各个星期的排奶信息
        compareAndSetChangeStatus(changeStatus, frontendInfo.getMonday(), backendInfo.getMonday(), 0);
        compareAndSetChangeStatus(changeStatus, frontendInfo.getTuesday(), backendInfo.getTuesday(), 1);
        compareAndSetChangeStatus(changeStatus, frontendInfo.getWednesday(), backendInfo.getWednesday(), 2);
        compareAndSetChangeStatus(changeStatus, frontendInfo.getThursday(), backendInfo.getThursday(), 3);
        compareAndSetChangeStatus(changeStatus, frontendInfo.getFriday(), backendInfo.getFriday(), 4);
        compareAndSetChangeStatus(changeStatus, frontendInfo.getSaturday(), backendInfo.getSaturday(), 5);

        return changeStatus;
    }

    /**
     * 比较单天的排奶信息并设置变更状态
     *
     * @param changeStatus    变更状态Map
     * @param frontendDayInfo 前端单天排奶信息
     * @param backendDayInfo  后端单天排奶信息
     * @param dayIndex        星期索引（0-5对应周一到周六）
     */
    private void compareAndSetChangeStatus(
        Map<Long, Boolean[]> changeStatus,
        List<MilkDistributionInfo> frontendDayInfo,
        List<MilkDistributionInfo> backendDayInfo,
        int dayIndex) {

        // 将后端信息转换为Map便于查找
        Map<Long, Long> backendQuantityMap = new HashMap<>();
        if (CollUtil.isNotEmpty(backendDayInfo)) {
            for (MilkDistributionInfo info : backendDayInfo) {
                backendQuantityMap.put(info.getProductId(), info.getQuantity());
            }
        }

        // 比较前端信息
        if (CollUtil.isNotEmpty(frontendDayInfo)) {
            for (MilkDistributionInfo frontendInfo : frontendDayInfo) {
                Long productId = frontendInfo.getProductId();
                Long frontendQuantity = frontendInfo.getQuantity();
                Long backendQuantity = backendQuantityMap.getOrDefault(productId, 0L);

                // 初始化产品的变更状态数组
                Boolean[] productChangeStatus = changeStatus.computeIfAbsent(productId, k -> new Boolean[6]);
                for (int i = 0; i < 6; i++) {
                    if (productChangeStatus[i] == null) {
                        productChangeStatus[i] = false;
                    }
                }

                // 判断是否有变更
                boolean hasChange = !ObjectUtil.equals(frontendQuantity, backendQuantity);
                productChangeStatus[dayIndex] = hasChange;

                log.debug("产品ID：{}，星期{}，前端数量：{}，后端数量：{}，是否变更：{}",
                    productId, dayIndex + 1, frontendQuantity, backendQuantity, hasChange);
            }
        }
    }
}
