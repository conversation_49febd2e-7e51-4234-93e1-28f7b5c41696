package com.ruoyi.job.common.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.job.common.domain.CxrRegionStaitemDailyReport;
import com.ruoyi.job.common.service.CxrRegionStaitemDailyReportService;
import com.ruoyi.job.common.mapper.CxrRegionStaitemDailyReportMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【cxr_region_staitem_daily_report(区域单数统计明细日报表)】的数据库操作Service实现
* @createDate 2025-07-03 16:06:20
*/
@DS("ry_cloud")
@Service
public class CxrRegionStaitemDailyReportServiceImpl extends ServiceImpl<CxrRegionStaitemDailyReportMapper, CxrRegionStaitemDailyReport>
    implements CxrRegionStaitemDailyReportService{

}




