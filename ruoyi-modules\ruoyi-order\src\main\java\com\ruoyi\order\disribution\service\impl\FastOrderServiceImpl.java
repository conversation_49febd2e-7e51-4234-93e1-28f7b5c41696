package com.ruoyi.order.disribution.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.business.api.domain.vo.ShiftEmployeeDistributionTransferVo;
import com.ruoyi.business.api.dubbo.RemoteTransferPostApplyRemoteService;
import com.ruoyi.business.base.api.domain.CxrCustomer;
import com.ruoyi.business.base.api.domain.CxrCustomerAddress;
import com.ruoyi.business.base.api.domain.CxrEmployee;
import com.ruoyi.business.base.api.domain.CxrSite;
import com.ruoyi.business.base.api.domain.dto.CxrCustomerAddressDTO;
import com.ruoyi.business.base.api.domain.dto.CxrEmployeeInfoDTO;
import com.ruoyi.business.base.api.domain.dto.CxrSiteDTO;
import com.ruoyi.business.base.api.domain.json.BusinessAgent;
import com.ruoyi.business.base.api.domain.json.CustomerInfo;
import com.ruoyi.business.base.api.domain.vo.CustomerSentNumberVo;
import com.ruoyi.business.base.api.dubbo.*;
import com.ruoyi.business.base.api.enums.OrderTypeEnums;
import com.ruoyi.business.base.api.model.CustomerDistributioninfoDTO;
import com.ruoyi.business.base.api.model.LoginEmployee;
import com.ruoyi.common.core.annotation.HuiBoSynCxrUserOrder;
import com.ruoyi.common.core.enums.OccupationStatus;
import com.ruoyi.common.core.enums.SysYesNo;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.enums.TerminalTypeEnums;
import com.ruoyi.common.redis.annotation.RedisDistributedLock;
import com.ruoyi.common.rocketmq.calculate.CustomerAddressConstant;
import com.ruoyi.common.rocketmq.utils.MqUtil;
import com.ruoyi.common.satoken.utils.helper.StaffLoginHelper;
import com.ruoyi.common.security.handler.ConstantExceptionCode;
import com.ruoyi.core.base.mapper.CxrPayConfigMapper;
import com.ruoyi.order.api.enums.OrderAuditStatusEnums;
import com.ruoyi.order.api.enums.PayStatusEnums;
import com.ruoyi.order.api.enums.PerfectStatusEnums;
import com.ruoyi.order.common.domain.dto.CompleteInfoDTO;
import com.ruoyi.order.common.domain.dto.ExcessQuantityDTO;
import com.ruoyi.order.common.domain.vo.CxrUserOrderVO;
import com.ruoyi.order.common.domain.vo.LastUserOrderDayInfo;
import com.ruoyi.order.common.domain.vo.LastUserOrderVo;
import com.ruoyi.order.common.entity.CxrUserOrder;
import com.ruoyi.order.common.entity.CxrUserOrderSubscribeMilk;
import com.ruoyi.order.common.mapper.CxrUserOrderMapper;
import com.ruoyi.order.common.mapper.CxrUserOrderSubscribeMilkMapper;
import com.ruoyi.order.common.utils.ExcessQuantityUtil;
import com.ruoyi.order.common.utils.OrderUtil;
import com.ruoyi.order.disribution.common.WeiXinTokenRedis;
import com.ruoyi.order.disribution.domain.bo.UserFastOrderBo;
import com.ruoyi.order.disribution.mapper.CxrPaymentDetailsMapper;
import com.ruoyi.order.disribution.service.ActivityOrderService;
import com.ruoyi.order.disribution.service.CommonOrderService;
import com.ruoyi.order.disribution.service.FastOrderService;
import com.ruoyi.order.disribution.service.MessageService;
import com.ruoyi.order.manager.service.CxrUserOrderService;
import com.ruoyi.order.manager.strategy.behavior.AbstractCxrUserOrderBehavior;
import com.ruoyi.system.api.RemoteDeptService;
import com.ruoyi.system.api.RemoteSysConfigService;
import com.ruoyi.system.api.domain.SysDept;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
@GlobalTransactional(rollbackFor = Exception.class)
public class FastOrderServiceImpl implements FastOrderService {

    private final AbstractCxrUserOrderBehavior abstractCxrUserOrderBehavior;
    private final ExcessQuantityUtil excessQuantityUtil;
    private final CxrUserOrderSubscribeMilkMapper cxrUserOrderSubscribeMilkMapper;
    @DubboReference
    private RemoteCustomerService remoteCustomerService;
    @DubboReference
    private RemoteCustomerStockDetailService remoteCustomerStockDetailService;

    @DubboReference(timeout = 10000)
    private RemoteCustomerAddressService remoteCustomerAddressService;

    @Resource
    private CxrUserOrderMapper cxrUserOrderMapper;
    @Resource
    private CxrPayConfigMapper cxrPayConfigMapper;
    @Resource
    private CxrPaymentDetailsMapper cxrPaymentDetailsMapper;
    @Autowired
    private MessageService messageService;
    @Autowired
    private WeiXinTokenRedis weiXinTokenRedis;

    @DubboReference
    private RemoteEmployeeAchievementDetailService remoteEmployeeAchievementDetailService;

    @DubboReference
    private RemoteSiteService remoteSiteService;
    @DubboReference
    private RemoteDeptService remoteDeptService;
    @DubboReference
    private RemoteEmployeeService remoteEmployeeService;
    @DubboReference
    private RemoteSysConfigService remoteSysConfigService;
    @DubboReference
    private RemoteTransferPostApplyRemoteService remoteTransferPostApplyRemoteService;
    @Lazy
    @Autowired
    private CxrUserOrderService cxrUserOrderService;
    @DubboReference
    private RemoteResidentialQuarService remoteResidentialQuarService;

    private final CommonOrderService commonOrderService;
    private final ActivityOrderService activityOrderService;

    private static Pattern pattern = Pattern.compile("^[A-Z]{1}[0-9]{8}$");

    private final IdentifierGenerator identifierGenerator;
    private final MqUtil mqUtil;

    /**
     * 查询订单类型
     *
     * @param bo
     * @return
     */
    @Override
    public OrderTypeEnums queryOrderType(String phone) {

        if (StringUtils.isEmpty(phone)) {
            throw new ServiceException("请填写客户信息");
        }
        /**
         * 订单类型主要分成以下6种，选择不同的订单类型显示不同的页面信息
         *
         * <p>1、新订单 新订单分为三种，分别是“新订单”、“续订单”、“增订单”。客户首次订购为“新订单”。 按手机号判断是否新客户。 续订单和增订单分别有以下两种情况
         * 1、客户还没喝完奶，即账户中还有鲜奶数量 10天内（按订单下单时间计算），客户继续下了一笔订单，该笔订单为“增订单”；10天以上，客户继续下了一笔订单，该订单为续订单。
         * 2、客户已经喝完奶了，即账户中没有鲜奶数量 从客户账户剩余奶数为0的当天开始，15天内（含15天）客户继续下了一笔订单，该订单为续订单，15天以上，订单类型为新订单。
         * 后台管理人员只会按照新订单类型录入，录入后需要系统按照上面的规则自动判断该笔订单类型究竟为“续订单”、“增订单”还是“新订单”
         */
        //  根据手机号查 判断是否是新客户
        CxrCustomer customer = remoteCustomerService.queryByPhone(phone);
        if (ObjectUtil.isNull(customer)) {
            return OrderTypeEnums.NEW_ORDER;
        } else {
            Integer customerStock = Convert.toInt(customer.getCustomerStock(), 0);
            CxrUserOrder lastCxrUserOrder =
                cxrUserOrderService.queryLastOrder(phone, null);
            LocalDate now = LocalDate.now();

            // 增订单判断没有库存限制
            if (lastCxrUserOrder != null
                && now.isBefore(
                DateUtils.getLocalDateFromDate(lastCxrUserOrder.getOrderDate())
                    .plusDays(10))) {
                return OrderTypeEnums.INCREASE_ORDER;
            }

            // 账户还有鲜奶没喝完
            if (customerStock > 0) {
                if (ObjectUtil.isNull(lastCxrUserOrder)) {
                    return OrderTypeEnums.NEW_ORDER;
                } else {
                    return OrderTypeEnums.CONTINUE_ORDER;
                }

            } else {
                // 账户中没有鲜奶数量
                if (now.isBefore(
                    DateUtils.getLocalDateFromDate(customer.getCustomerStockZeroTime())
                        .plusDays(16))) {
                    if (ObjectUtil.isNull(lastCxrUserOrder)) {
                        return OrderTypeEnums.NEW_ORDER;
                    } else {
                        return OrderTypeEnums.CONTINUE_ORDER;
                    }
                } else {
                    return OrderTypeEnums.NEW_ORDER;
                }
            }
        }
    }

    @Override
    public String addFastOrder(UserFastOrderBo bo) {
        Date now = new Date();

        if (ObjectUtil.isNull(bo.getOrderType())) {
            throw new ServiceException("订单类型必填");
        }

        if (OrderTypeEnums.NEW_ORDER.getValue() == bo.getOrderType().intValue()
            || OrderTypeEnums.CONTINUE_ORDER.getValue() == bo.getOrderType().intValue()
            || OrderTypeEnums.INCREASE_ORDER.getValue() == bo.getOrderType().intValue()) {

            if (ObjectUtil.isEmpty(bo.getDistributionStationId())) {
                throw new ServiceException("请选择开单站点",
                    ConstantExceptionCode.BUSSINESS_EXCEPTION_CODE);
            }

            //            Matcher m = pattern.matcher(bo.getOrderNo());
            //            if(!m.matches()){
            //                throw new ServiceException("请输入正确的订单单据");
            //            }

            CxrSite site = remoteSiteService.queryId(bo.getDistributionStationId());
            CxrUserOrder cxrUserOrder = new CxrUserOrder();
            cxrUserOrder.setOrderType(bo.getOrderType());
            cxrUserOrder.setId(identifierGenerator.nextId(null).longValue());
            cxrUserOrder.setCompanyId(site.getCurrentDeptId());
            // 所属公司 id
            cxrUserOrder.setCompanyId(site.getCurrentDeptId());
            SysDept sysDept = remoteDeptService.queryById(site.getCurrentDeptId());
            if (ObjectUtil.isNull(sysDept)) {
                throw new ServiceException("站点没有关联所属部门");
            }
            // 站点数据设置
            cxrUserOrder.setBigAreaId(site.getCxrRootRegionId());
            cxrUserOrder.setBigAreaName(site.getCxrRootRegionName());

            cxrUserOrder.setSiteName(site.getName());
            cxrUserOrder.setSiteAdress(site.getDetailAddress());
            cxrUserOrder.setSiteId(site.getId());

            // 所属公司名称
            cxrUserOrder.setCompanyName(sysDept.getDeptName());
            cxrUserOrder.setAuditStatus(OrderAuditStatusEnums.NO_AUDIT.getValue());
            cxrUserOrder.setRemark(bo.getRemark());

            if (CollectionUtil.isEmpty(bo.getBusinessAgent())) {
                throw new ServiceException("请选择销售代理",
                    ConstantExceptionCode.BUSSINESS_EXCEPTION_CODE);
            }
            cxrUserOrder.setBusinessAgent(bo.getBusinessAgent());
            cxrUserOrder.getBusinessAgent().stream()
                .forEach(
                    s -> {
                        CxrEmployee cxrEmployee = remoteEmployeeService
                            .remoteDetail(s.getProxyId());
                        s.setLevel(Integer.valueOf(cxrEmployee.getEmployeeLevelType()));
                    });

            List<CustomerInfo> customerInfoList = bo.getCustomerInfoList();
            if (CollectionUtil.isEmpty(customerInfoList)) {
                throw new ServiceException("请填写客户信息");
            }
            CustomerInfo customerInfoFirst = customerInfoList.get(0);

            // 客户信息
            cxrUserOrder.setCustomerName(customerInfoFirst.getName());
            cxrUserOrder.setCustomerPhone(customerInfoFirst.getPhone());
            cxrUserOrder.setCustomerAdress(customerInfoFirst.getAdress());
            // 处理订单类型

            // 校验纸质单据号唯一
            //            long count = cxrUserOrderMapper.selectCount(new
            // LambdaQueryWrapper<CxrUserOrder>().eq(CxrUserOrder::getOrderNo, bo.getOrderNo()));
            //            if (count > 0) {
            //                throw new ServiceException("该纸质单号已经存在");
            //            }

            cxrUserOrder.setOrderNo(OrderUtil.getOrderNo(customerInfoFirst.getPhone())); // 纸质 单据号
            cxrUserOrder.setMerchantOrderNo(bo.getMerchantOrderNo());
            cxrUserOrder.setSurplusQuantity(bo.getSurplusQuantity());
            cxrUserOrder.setOrderQuantity(bo.getProductCount());
            cxrUserOrder.setFreshMilkGiveQuantity(bo.getFreshMilkGiveCount());
            cxrUserOrder.setLongMilkGiveQuantity(bo.getMilkGiveCount());
            cxrUserOrder.setFreshMilkSentQuantity(bo.getAlreadyGiveCount());
            cxrUserOrder.setLongMilkSentQuantity(0);
            cxrUserOrder.setAmount(bo.getAmount());
            cxrUserOrder.setUnitPrice(bo.getUnitPrice());
            cxrUserOrder.setOrderDate(now);
            cxrUserOrder.setOrderImages(bo.getOrderImages());
            cxrUserOrder.setPlayImages(bo.getPlayImages());

            /**
             * 判断该手机号的客户从下订单到当前时间是否是10天内，10天内续订的，只能选择上一次开单的销
             * 售代理进行录入订单。如果是选择其他销售代理录入订单，则toast提示“该笔订单只选择“XXX小A”录入！”
             */
            // 查询用户最近一次下单时间
            CxrUserOrder lastCxrUserOrder =
                cxrUserOrderService.queryLastOrder(customerInfoFirst.getPhone(), null);
            addLimit(lastCxrUserOrder, bo);
            //            if (ObjectUtil.isNotNull(lastCxrUserOrder)) {
            //                if (com.ruoyi.common.core.utils.DateUtils.addDays(new Date(), -10).getTime()
            // < lastCxrUserOrder.getOrderDate().getTime()) {
            //                    List<BusinessAgent> oldBusinessAgent =
            // lastCxrUserOrder.getBusinessAgent();
            //
            //                    Map<Long, BusinessAgent> oldBusinessAgentMap =
            // oldBusinessAgent.stream().collect(Collectors.toMap(BusinessAgent::getProxyId,
            // Function.identity(), (v1, v2) -> v2));
            //                    List<String> proxyNameList =
            // oldBusinessAgent.stream().map(BusinessAgent::getProxyName).collect(Collectors.toList());
            //
            //                    for (BusinessAgent businessAgent : bo.getBusinessAgent()) {
            //                        if
            // (ObjectUtil.isNull(oldBusinessAgentMap.get(businessAgent.getProxyId()))) {
            //                            throw new ServiceException(String.format("该笔订单只能选择[%s]录入",
            // CollectionUtil.join(proxyNameList, ",")));
            //                        }
            //                    }
            //
            //                } else {
            //                    if (com.ruoyi.common.core.utils.DateUtils.addDays(new Date(),
            // -15).getTime() < lastCxrUserOrder.getOrderDate().getTime()) {
            //                        List<CustomerInfo> oldCustomerInfoList =
            // lastCxrUserOrder.getCustomerInfoList();
            //                        Map<Long, CustomerInfo> mapOldCustomerInfo =
            // oldCustomerInfoList.stream().collect(Collectors.toMap(CustomerInfo::getDistributionId,
            // Function.identity(), (v1, v2) -> v2));
            //                        List<String> distributionNameList =
            // oldCustomerInfoList.stream().map(CustomerInfo::getDistributionName).filter(Objects::nonNull).collect(Collectors.toList());
            //                        for (BusinessAgent businessAgent : bo.getBusinessAgent()) {
            //                            if
            // (ObjectUtil.isNull(mapOldCustomerInfo.get(businessAgent.getProxyId()))) {
            //                                if(CollUtil.isNotEmpty(distributionNameList)){
            //                                    throw new
            // ServiceException(String.format("该笔订单只能选择[%s]录入", CollectionUtil.join(distributionNameList,
            // ",")));
            //                                }else{
            //                                    throw new ServiceException("该客户暂未分配配送员，请分配后重新录入");
            //                                }
            //
            //                            }
            //                        }
            //
            //                    }
            //                }
            //            }

            // 新订单：鲜奶赠送数、常温奶赠送数、订单已送数等字段移到完善订单中
            OrderTypeEnums orderType = OrderTypeEnums.getType(bo.getOrderType());

            Boolean zeroQuantityRenewal =
                cxrUserOrderService.zeroContinueFlag(customerInfoFirst.getPhone(), orderType);

            ExcessQuantityDTO excessQuantity;
            if (bo.getOrderType().shortValue() == OrderTypeEnums.INCREASE_ORDER.getValue()) {
                excessQuantity =
                    excessQuantityUtil.getExcessQuantity(
                        bo.getProductCount(),
                        bo.getFreshMilkGiveCount(),
                        bo.getMilkGiveCount(),
                        bo.getLastOrderSum());

                if (!NumberUtil
                    .equals(bo.getExcessQuantity(), excessQuantity.getExcessQuantity())) {
                    throw new ServiceException(
                        StrUtil.format(
                            "超送数量不一致,套餐数量区间{}-{},赠送区间为{}-{}",
                            excessQuantity.getMin(),
                            excessQuantity.getMax(),
                            excessQuantity.getGiveMin(),
                            excessQuantity.getGiveMax()));
                }
                bo.setOrderDate(now);
                checkPromotionalOrder(bo, customerInfoFirst);
                cxrUserOrder.setExcessQuantity(excessQuantity.getExcessQuantity());
            } else {

                if (orderType != OrderTypeEnums.NEW_ORDER) {
                    excessQuantity =
                        excessQuantityUtil.getExcessQuantity(
                            bo.getProductCount(), bo.getFreshMilkGiveCount(),
                            bo.getMilkGiveCount());
                    if (!NumberUtil
                        .equals(bo.getExcessQuantity(), excessQuantity.getExcessQuantity())) {
                        throw new ServiceException(
                            StrUtil.format(
                                "超送数量不一致,套餐数量区间{}-{},赠送区间为{}-{}",
                                excessQuantity.getMin(),
                                excessQuantity.getMax(),
                                excessQuantity.getGiveMin(),
                                excessQuantity.getGiveMax()));
                    }
                    cxrUserOrder.setExcessQuantity(excessQuantity.getExcessQuantity());
                } else {
                    cxrUserOrder.setExcessQuantity(null);
                }
            }

            cxrUserOrder.setPromotionalOrderFlag(bo.getPromotionalOrderFlag());
            cxrUserOrder.setApprenticeOrderFlag(bo.getApprenticeOrderFlag());

            cxrUserOrder.setTerminalType(TerminalTypeEnums.disribution.getValue());

            if (remoteCustomerService.existsCustomerAccount(customerInfoFirst.getPhone())) {
                cxrUserOrder.setPerfectStatus(PerfectStatusEnums.PERFECT.getValue());
                cxrUserOrder.setNewCustomerFlag(SysYesNo.NO.getValue());
            } else {
                cxrUserOrder.setPerfectStatus(PerfectStatusEnums.NO_PERFECT.getValue());
                cxrUserOrder.setNewCustomerFlag(SysYesNo.YES.getValue());
            }

            cxrUserOrder.setCustomerInfoList(customerInfoList);
            cxrUserOrder.setProvince(site.getProvice());
            cxrUserOrder.setCity(site.getCity());
            cxrUserOrder.setArea(site.getArea());

            cxrUserOrder.setPayStatus(PayStatusEnums.WAIT_PAY.getValue());
            cxrUserOrder.setZeroQuantityRenewal(zeroQuantityRenewal);
            LoginEmployee loginUser = StaffLoginHelper.getLoginUser();
            cxrUserOrder.setCreateByCode(loginUser.getJobNumber());
            cxrUserOrderMapper.insert(cxrUserOrder);
            return cxrUserOrder.getOrderNo();
        } else {
            throw new ServiceException("订单类型错误");
        }
    }

    public void addLimit(CxrUserOrder lastCxrUserOrder, UserFastOrderBo bo) {
        String phone = bo.getCustomerInfoList().get(0).getPhone();
        CxrCustomer customer =
            remoteCustomerService.queryByPhone(phone);

        if (ObjectUtil.isNotNull(lastCxrUserOrder)) {

            // 十天内只能由A继续开单,十天后可以由配送员录单，按订单日期支付日期计算,增订单和库存无关
            if (LocalDate.now()
                .isBefore(
                    DateUtils.getLocalDateFromDate(lastCxrUserOrder.getOrderDate()).plusDays(10))) {
                List<Long> proxyIds =
                    lastCxrUserOrder.getBusinessAgent().stream()
                        .map(BusinessAgent::getProxyId)
                        .collect(Collectors.toList());

                List<CxrEmployee> employeeList = remoteEmployeeService.getEmployeeIds(proxyIds);

                employeeList.removeIf(
                    e ->
                        e.getQuitTime() != null
                            && !DateUtils.getLocalDateFromDate(e.getQuitTime())
                            .isAfter(LocalDate.now()));
                proxyIds = employeeList.stream().map(CxrEmployee::getId)
                    .collect(Collectors.toList());

                //
                if (CollectionUtil.isEmpty(employeeList)) {
                    addLimit(bo, lastCxrUserOrder.getCustomerId());
                } else {
                    Boolean lastPromotionalFlag = true;
                    if (bo.getOrderType() == OrderTypeEnums.INCREASE_ORDER.getValue()) {
                        CxrUserOrder lastPromotionalOrder = cxrUserOrderService.queryLastPromotionalOrderOrder(phone, null);//最近一笔促销单
                        if (ObjectUtil.isNotEmpty(lastPromotionalOrder) &&
                            (ObjectUtil.isNotEmpty(lastPromotionalOrder.getPromotionalOrderFlag()) && ObjectUtil.equals(lastPromotionalOrder.getPromotionalOrderFlag(), true) ||
                                ObjectUtil.isNotEmpty(lastPromotionalOrder.getApprenticeOrderFlag()) && ObjectUtil.equals(lastPromotionalOrder.getApprenticeOrderFlag(), true))) {
                            Long count = cxrUserOrderService.queryZeroQuantityRenewal(lastCxrUserOrder.getCustomerId(), lastCxrUserOrder.getOrderDate());
                            if (count == 0l) {
                                lastPromotionalFlag = false;
                            }
                        }
                    }
                    for (BusinessAgent businessAgent : bo.getBusinessAgent()) {
                        // 所填的销售代理必须包含在上一单中
                        if (!proxyIds.contains(businessAgent.getProxyId()) &&
                            lastPromotionalFlag) {
                            throw new ServiceException(
                                String.format(
                                    "该笔订单只能选择[%s]录入",
                                    employeeList.stream()
                                        .map(CxrEmployee::getName)
                                        .collect(Collectors.joining(","))));
                        }
                    }
                }
            } else if (customer != null) {

                Integer customerStock = Convert.toInt(customer.getCustomerStock(), 0);
                if (customerStock > 0) {
                    // 十天后只能由配送员录单
                    addLimit(bo, lastCxrUserOrder.getCustomerId());
                } else {

                    // 没库存的情况下:库存为0十五天内只能配送员录单库存为0十五天后任意销售代理都可以录单，按库存为零日期计算

                    if (LocalDate.now()
                        .isBefore(
                            DateUtils.getLocalDateFromDate(customer.getCustomerStockZeroTime())
                                .plusDays(16))) {
                        Long customerId = lastCxrUserOrder.getCustomerId();
                        addLimit(bo, customerId);
                    }
                }
            }
        }
    }

    public void addLimit(UserFastOrderBo bo, Long customerId) {
        //1：先判断是否是有转单的，是的话走转单逻辑
        //2：如果不是当天转单 ，走原来的逻辑
        List<CxrCustomerAddress> addresses =
            remoteCustomerAddressService.cxrCustomerAddressList(customerId);
        log.debug("bo = {}", JSONUtil.toJsonStr(bo));
        if (CollectionUtil.isNotEmpty(addresses)) {
            List<Long> employeeIds =
                addresses.stream()
                    .map(CxrCustomerAddress::getCxrEmployeeId)
                    .collect(Collectors.toList());
            List<Long> addressIds =
                addresses.stream()
                    .map(CxrCustomerAddress::getId)
                    .collect(Collectors.toList());
            //当allowRecording = true时允许销售员录单，如果等于false抛异常不允许下单
            boolean allowRecording = true;
            List<CxrCustomerAddressDTO> addressesDTO =
                remoteCustomerAddressService.cxrCustomerAddressDTOList(customerId);

            String allowRecordingName = addressesDTO.stream()
                .map(CxrCustomerAddressDTO::getCxrEmployeeName)
                .collect(Collectors.joining(","));
            //如果销售员不是配送员，不允许录单
            //每个销售员都需要独立判断
            loop:
            for (BusinessAgent businessAgent : bo.getBusinessAgent()) {
                if (!employeeIds.contains(businessAgent.getProxyId())) {
                    //命中，不允许下单，因为销售员不在配送员列表中（提示配送员名称）
                    log.debug("1,命中，不允许下单(原逻辑)");
                    allowRecording = false;

                    //add 20230608 如果存在以下情况(配送员当天有客户的线路并当天线路转拨成功的)，即使销售员不是配送员也允许录单
                    log.debug("siteIds = {}", JSONUtil.toJsonStr(addressIds));
                    log.debug("businessAgent.getProxyId() = {}", businessAgent.getProxyId());
                    ShiftEmployeeDistributionTransferVo employeeDistributionTransferVo =
                        remoteTransferPostApplyRemoteService
                            .selectEmployeeDistributionTransfer(addressIds,
                                businessAgent.getProxyId());
                    log.debug("employeeDistributionTransferVo = {}",
                        JSONUtil.toJsonStr(employeeDistributionTransferVo));
                    if (employeeDistributionTransferVo != null) {
                        //命中，该配送员当天有线路转拨，所以又允许下单
                        log.debug("2,命中，该配送员当天有线路转拨，所以又允许下单");
                        allowRecording = true;

                        CxrEmployeeInfoDTO cxrEmployeeInfoDTO = remoteEmployeeService
                            .detail(businessAgent.getProxyId());
                        log.debug("cxrEmployeeInfoDTO1 = {}",
                            JSONUtil.toJsonStr(cxrEmployeeInfoDTO));
                        if (OccupationStatus.PEND_ONESELF.getValue()
                            .equals(cxrEmployeeInfoDTO.getOccupationStatus()) ||
                            OccupationStatus.QUIT_ONESELF.getValue()
                                .equals(cxrEmployeeInfoDTO.getOccupationStatus()) ||
                            OccupationStatus.QUIT_PROCEDURE.getValue()
                                .equals(cxrEmployeeInfoDTO.getOccupationStatus())) {
                            //命中，该配送员虽然允许下单，但是又离职了，所有又不允许下单（提示接受线路转拨配送员名称）
                            log.debug(
                                "3,命中，该配送员虽然允许下单，但是又离职了，所有又不允许下单（提示接受线路转拨配送员名称）");
                            allowRecording = false;

                            //修改提示语  去掉自己（因为离职了），加上接受线路转播的那个人
                            log.debug("修改提示语  去掉自己（因为离职了），加上接受线路转播的那个人");
                            //查询线路转播的用户，是否有再转播给其他人

                            CxrCustomerAddressDTO cca = new CxrCustomerAddressDTO();
                            cca.setCxrEmployeeName(
                                employeeDistributionTransferVo.getShiftDistributionName());
                            addressesDTO.add(cca);
                            allowRecordingName = addressesDTO.stream()
                                .filter(dto -> !businessAgent.getProxyName()
                                    .equals(dto.getCxrEmployeeName()))
                                .map(CxrCustomerAddressDTO::getCxrEmployeeName)
                                .collect(Collectors.joining(","));
                        }
                    } else {
                        List<CxrCustomerAddressDTO> addressesReal = new ArrayList<CxrCustomerAddressDTO>();
                        //用户可能有多个地址，每个地址都会有一个配送员，需要验证每个配送员，这里客户地址不会太多，一般都是1-2个
                        for (CxrCustomerAddressDTO customerAddress : addressesDTO) {
                            ShiftEmployeeDistributionTransferVo checkEmployee =
                                remoteTransferPostApplyRemoteService
                                    .selectShiftEmployeeDistributionTransfer(addressIds,
                                        customerAddress.getCxrEmployeeId());
                            CxrCustomerAddressDTO cca = new CxrCustomerAddressDTO();
                            if (checkEmployee != null) {
                                CxrEmployeeInfoDTO cxrEmployeeInfoDTO = remoteEmployeeService
                                    .detail(checkEmployee.getDistributionId());
                                log.debug("cxrEmployeeInfoDTO2 = {}",
                                    JSONUtil.toJsonStr(cxrEmployeeInfoDTO));
                                if (OccupationStatus.PEND_ONESELF.getValue()
                                    .equals(cxrEmployeeInfoDTO.getOccupationStatus()) ||
                                    OccupationStatus.QUIT_ONESELF.getValue()
                                        .equals(cxrEmployeeInfoDTO.getOccupationStatus()) ||
                                    OccupationStatus.QUIT_PROCEDURE.getValue()
                                        .equals(cxrEmployeeInfoDTO.getOccupationStatus())) {

                                    ShiftEmployeeDistributionTransferVo checkEmployee2 =
                                        remoteTransferPostApplyRemoteService
                                            .selectShiftEmployeeDistributionTransfer(addressIds,
                                                checkEmployee.getDistributionId());
                                    if (checkEmployee2 != null) {
                                        cca.setCxrEmployeeName(
                                            checkEmployee2.getDistributionName());
                                        addressesReal.add(cca);
                                    } else {
                                        cca.setCxrEmployeeName(
                                            checkEmployee.getShiftDistributionName());
                                        addressesReal.add(cca);
                                    }
                                } else {
                                    cca.setCxrEmployeeName(checkEmployee.getDistributionName());
                                    addressesReal.add(cca);
                                }
                            } else {
                                cca.setCxrEmployeeName(customerAddress.getCxrEmployeeName());
                                addressesReal.add(cca);
                            }
                        }
                        allowRecordingName = addressesReal.stream()
                            .map(CxrCustomerAddressDTO::getCxrEmployeeName)
                            .collect(Collectors.joining(","));
                    }
                } else {
                    //即使是在地址列表里允许配送的，也要查看是否是当天才转到的，如果是，也不允许录单
                    ShiftEmployeeDistributionTransferVo employeeDistributionTransferVo =
                        remoteTransferPostApplyRemoteService
                            .selectShiftEmployeeDistributionTransfer(addressIds,
                                businessAgent.getProxyId());
                    log.debug("employeeDistributionTransferVo = {}",
                        employeeDistributionTransferVo);
                    if (employeeDistributionTransferVo != null) {
                        //命中，因为是单天线路转拨的，所以不允许下单，提示线路转拨的配送员名称
                        log.debug("5,命中，因为是单天线路转拨的，所以不允许下单，提示线路转拨的配送员名称");
                        allowRecording = false;

                        //虽然是单天线路转拨的不允许下单，但是还有一个下单的机会，就是线路转拨的转出配送员离职了
                        CxrEmployeeInfoDTO cxrEmployeeInfoDTO = remoteEmployeeService
                            .detail(employeeDistributionTransferVo.getDistributionId());
                        log.debug("cxrEmployeeInfoDTO3 = {}",
                            JSONUtil.toJsonStr(cxrEmployeeInfoDTO));
                        if (OccupationStatus.PEND_ONESELF.getValue()
                            .equals(cxrEmployeeInfoDTO.getOccupationStatus()) ||
                            OccupationStatus.QUIT_ONESELF.getValue()
                                .equals(cxrEmployeeInfoDTO.getOccupationStatus()) ||
                            OccupationStatus.QUIT_PROCEDURE.getValue()
                                .equals(cxrEmployeeInfoDTO.getOccupationStatus())) {
                            //命中，允许下单
                            allowRecording = true;
                            log.debug("6,命中，允许下单");

                            //还得确定离职的是否是当天的线路接受人
                            ShiftEmployeeDistributionTransferVo checkEmployee =
                                remoteTransferPostApplyRemoteService
                                    .selectShiftEmployeeDistributionTransfer(addressIds,
                                        employeeDistributionTransferVo.getDistributionId());
                            if (checkEmployee != null) {
                                //命中，允许下单
                                allowRecording = false;
                                log.debug("7,命中，不允许下单");
                                log.debug("修改提示语 去除自己（因为单天接受转播），加上发起线路转播的那个人");
                                //修改提示语 去除自己（因为单天接受转播），加上发起线路转播的那个人
                                CxrCustomerAddressDTO cca = new CxrCustomerAddressDTO();
                                cca.setCxrEmployeeName(
                                    checkEmployee.getDistributionName());
                                addressesDTO.add(cca);
                                allowRecordingName = addressesDTO.stream()
                                    .filter(dto -> !businessAgent.getProxyName()
                                        .equals(dto.getCxrEmployeeName()))
                                    .map(CxrCustomerAddressDTO::getCxrEmployeeName)
                                    .collect(Collectors.joining(","));
                            }
                        } else {
                            log.debug("修改提示语 去除自己（因为单天接受转播），加上发起线路转播的那个人");
                            //修改提示语 去除自己（因为单天接受转播），加上发起线路转播的那个人
                            CxrCustomerAddressDTO cca = new CxrCustomerAddressDTO();
                            cca.setCxrEmployeeName(
                                employeeDistributionTransferVo.getDistributionName());
                            addressesDTO.add(cca);
                            allowRecordingName = addressesDTO.stream()
                                .filter(dto -> !businessAgent.getProxyName()
                                    .equals(dto.getCxrEmployeeName()))
                                .map(CxrCustomerAddressDTO::getCxrEmployeeName)
                                .collect(Collectors.joining(","));
                        }
                    }
                }
                //如果不允许下单，返回并提示
                if (!allowRecording) {
                    throw new ServiceException(
                        String.format(
                            "该笔订单只能选择[%s]录入",
                            allowRecordingName));
                }
            }
        }
    }

    /**
     * 校验快单
     *
     * @param userFastOrderBo
     */
    @Override
    public void verifyFastOrderParam(UserFastOrderBo userFastOrderBo, TerminalTypeEnums typeEnums) {

        // 订购数
        Integer productCount = userFastOrderBo.getProductCount();
        // 常温奶赠送数
        Integer milkGiveCount = userFastOrderBo.getMilkGiveCount();

        if (userFastOrderBo.getOrderType() == null) {
            throw new ServiceException("请选择订单类型");
        }
        if (userFastOrderBo.getDistributionStationId() == null) {
            throw new ServiceException("请选择站点");
        }
        if (userFastOrderBo.getBusinessAgent() == null
            || userFastOrderBo.getBusinessAgent().size() == 0) {
            throw new ServiceException("请选择业务代理");
        }

        if (userFastOrderBo.getProductCount() == null) {
            throw new ServiceException("请输入订购数量");
        }
        if (userFastOrderBo.getProductCount() <= 0) {
            throw new ServiceException("订购数量不能小于或等于0");
        }

        if (TerminalTypeEnums.disribution == typeEnums) {
            commonOrderService.checkBusinessAgent(userFastOrderBo.getBusinessAgent());
        }

        // 新订单：鲜奶赠送数、常温奶赠送数、订单已送数等字段移到完善订单中
        OrderTypeEnums orderType = OrderTypeEnums.getType(userFastOrderBo.getOrderType());
        if (orderType == OrderTypeEnums.NEW_ORDER && TerminalTypeEnums.disribution == typeEnums) {
            userFastOrderBo.setFreshMilkGiveCount(null);
            userFastOrderBo.setMilkGiveCount(null);
            userFastOrderBo.setAlreadyGiveCount(null);
        } else {

            if (userFastOrderBo.getFreshMilkGiveCount() == null) {
                throw new ServiceException("请输入鲜奶赠送数量");
            }
            if (userFastOrderBo.getFreshMilkGiveCount() < 0) {
                throw new ServiceException("鲜奶赠送数量不能小于0");
            }

            if (milkGiveCount == null) {
                throw new ServiceException("请输入常温奶赠送数量");
            }
            if (milkGiveCount < 0) {
                throw new ServiceException("常温奶赠送数量不能小于0");
            }
        }

        if (userFastOrderBo.getAmount() == null) {
            throw new ServiceException("请输入订购金额");
        }
        if (userFastOrderBo.getAmount().compareTo(BigDecimal.ZERO) == -1) {
            throw new ServiceException("订购金额不能小于0");
        }

        Long userOrderId = userFastOrderBo.getUserOrderId();
        Integer daySumQty = 0;
        CxrUserOrder order = null;
        if (userOrderId != null) {
            order = cxrUserOrderService.getById(userOrderId);
            daySumQty = cxrUserOrderService.getDaySumQty(order);
        }

        // 单价标准：低于100盒单价11元/盒，100盒以上单价10元/盒；

        if (userFastOrderBo.getOrderType().shortValue() == OrderTypeEnums.INCREASE_ORDER
            .getValue()) {

            CxrCustomer customer;
            if (order != null && NumberUtil.isGreater(NumberUtil.toBigDecimal(order.getCustomerId()),
                BigDecimal.ZERO)) {
                customer = remoteCustomerService.getById(order.getCustomerId());
            } else {
                customer =
                    remoteCustomerService.queryByPhone(
                        userFastOrderBo.getCustomerInfoList().get(0).getPhone());
            }

            LastUserOrderVo userOrder =
                cxrUserOrderService.queryLastTenDayOrder(customer.getId(), userOrderId);
            userFastOrderBo.setUnitPrice(OrderUtil.getUnitPrice(userFastOrderBo, userOrder));
            if (TerminalTypeEnums.disribution == typeEnums) {
                excessQuantityUtil.giveCheck(
                    daySumQty,
                    productCount,
                    userFastOrderBo.getFreshMilkGiveCount(),
                    milkGiveCount,
                    ExcessQuantityUtil.KDORDERTYPE,
                    userOrder,
                    userFastOrderBo.getDistributionStationId());
            }
            userFastOrderBo.setLastOrderSum(userOrder);
        } else {
            userFastOrderBo.setUnitPrice(OrderUtil.getUnitPrice(userFastOrderBo.getProductCount()));

            if (TerminalTypeEnums.manager == typeEnums) {
                excessQuantityUtil.getExcessQuantityNotLimit(productCount, userFastOrderBo.getFreshMilkGiveCount(),
                    milkGiveCount);
            } else if (!(orderType == OrderTypeEnums.NEW_ORDER
                && TerminalTypeEnums.disribution == typeEnums)) {
                excessQuantityUtil.giveCheck(
                    daySumQty,
                    productCount,
                    userFastOrderBo.getFreshMilkGiveCount(),
                    milkGiveCount,
                    ExcessQuantityUtil.KDORDERTYPE,
                    userFastOrderBo.getDistributionStationId());
            }
            userFastOrderBo.setAmount(OrderUtil.getAmount(userFastOrderBo.getProductCount()));
        }
    }

    @Override
    public CxrUserOrderVO detail(Long id) {
        CxrUserOrderVO cxrUserOrderVO = abstractCxrUserOrderBehavior.executeOrderDetail(id);

        CxrSite cxrSite = remoteSiteService.queryId(cxrUserOrderVO.getSiteId());
        CxrSiteDTO cxrSiteDTO = BeanUtil.toBean(cxrSite, CxrSiteDTO.class);
        cxrUserOrderVO.setSiteDTO(cxrSiteDTO);

        // 获取客户信息
        List<CustomerInfo> customerInfoList = cxrUserOrderVO.getCustomerInfoList();
        if (CollectionUtil.isNotEmpty(customerInfoList)) {
            CxrUserOrderSubscribeMilk cxrUserOrderSubscribeMilk =
                cxrUserOrderSubscribeMilkMapper.selectOne(
                    Wrappers.lambdaQuery(CxrUserOrderSubscribeMilk.class)
                        .eq(CxrUserOrderSubscribeMilk::getCxrUserOrderId, id));
            List<CustomerDistributioninfoDTO> customerDistributioninfoDTOList = new ArrayList<>();
            Boolean subscribeFlag = true;
            if (ObjectUtil.isNotEmpty(cxrUserOrderSubscribeMilk)) {
                customerDistributioninfoDTOList = cxrUserOrderSubscribeMilk.getSubscribeInfo();

            } else {
                subscribeFlag = false;
                // 获取主账户信息
                Long customerId = cxrUserOrderVO.getCustomerId();
                customerDistributioninfoDTOList =
                    remoteCustomerAddressService.queryCustomerDistributioninfo(customerId);
            }

            if (NumberUtil
                .equals(PayStatusEnums.PAY_SUCCEEDED.getValue(), cxrUserOrderVO.getPayStatus())) {
                // 当天进到详情修改 获取最新的
                LocalDateTime localDateTime = DateUtil.toLocalDateTime(cxrUserOrderVO.getPayTime());
                LocalDate payDay = localDateTime.toLocalDate();
                LocalDate now = LocalDate.now();
                if (payDay.compareTo(now) == 0 && subscribeFlag) {
                    Long customerId = cxrUserOrderVO.getCustomerId();
                    customerDistributioninfoDTOList =
                        remoteCustomerAddressService.queryCustomerDistributioninfo(customerId);
                }
            }

            if (CollectionUtil.isNotEmpty(customerDistributioninfoDTOList)) {
                List<CustomerInfo> returnCustomerInfoList = new ArrayList<>();

                Map<String, List<CustomerDistributioninfoDTO>> map =
                    customerDistributioninfoDTOList.stream()
                        .collect(
                            Collectors.groupingBy(CustomerDistributioninfoDTO::getCustomerPhone));
                for (int i = 0; i < customerInfoList.size(); i++) {
                    CustomerInfo info = customerInfoList.get(i);
                    CustomerDistributioninfoDTO customerDistributioninfoDTO = null;
                    if (subscribeFlag) {
                        customerDistributioninfoDTO = CollUtil
                            .get(customerDistributioninfoDTOList, i);
                    } else {
                        List<CustomerDistributioninfoDTO> customerDistributioninfoDTOS =
                            map.get(info.getPhone());
                        customerDistributioninfoDTO =
                            customerDistributioninfoDTOS.stream()
                                .filter(
                                    x ->
                                        StrUtil.equalsAnyIgnoreCase(
                                            SysYesNo.YES.getValue(), x.getDefalutAccountAddress()))
                                .findFirst()
                                .orElseGet(null);
                    }

                    if (ObjectUtil.isNotNull(customerDistributioninfoDTO)) {
                        if (StrUtil.isBlank(info.getName())) {

                            info.setName(customerDistributioninfoDTO.getCustomerName());
                            info.setPhone(customerDistributioninfoDTO.getCustomerPhone());
                            info.setSysAreaId(customerDistributioninfoDTO.getSysAreaId());
                            info.setProvice(customerDistributioninfoDTO.getProvince());
                            info.setCity(customerDistributioninfoDTO.getCity());
                            info.setArea(customerDistributioninfoDTO.getArea());
                            info.setResidentialQuartersId(
                                customerDistributioninfoDTO.getResidentialQuartersId());
                            info.setResidentialQuartersName(
                                customerDistributioninfoDTO.getResidentialQuartersName());
                            info.setAdress(customerDistributioninfoDTO.getAddress());
                            info.setSiteId(customerDistributioninfoDTO.getSiteId());
                            info.setSiteName(customerDistributioninfoDTO.getSiteName());
                            info.setDistributionId(customerDistributioninfoDTO.getDistributionId());
                            info.setDistributionName(
                                customerDistributioninfoDTO.getDistributionName());
                        }

                        info.setCustomerDistributioninfoDTO(customerDistributioninfoDTO);
                        returnCustomerInfoList.add(info);
                    } else {
                        returnCustomerInfoList.add(info);
                    }
                }
                cxrUserOrderVO.setCustomerInfoList(returnCustomerInfoList);
            }
        }

        if (cxrUserOrderVO.getOrderType() == OrderTypeEnums.CONTRACT_ORDER.getValue()) {
            List<CustomerInfo> customerInfoLists = cxrUserOrderVO.getCustomerInfoList();
            for (CustomerInfo customerInfo : customerInfoLists) {
                // && cxrUserOrderVO.getPerfectStatus()!=PerfectStatusEnums.PERFECT.getValue()
                //                if
                // (cxrUserOrderVO.getPerfectStatus()==PerfectStatusEnums.PERFECT.getValue()){
                ////                    remoteCustomerAddressService.get
                //
                // customerInfo.getCustomerDistributioninfoDTO().setAmMilkDistributionList();
                //
                // customerInfo.getCustomerDistributioninfoDTO().setPmMilkDistributionList();
                //                }
                customerInfo.setAmount(cxrUserOrderVO.getAmount());
                customerInfo.setOrderQuantity(cxrUserOrderVO.getOrderQuantity());
                customerInfo.setLongMilkGiveQuantity(cxrUserOrderVO.getLongMilkGiveQuantity());
                customerInfo.setFreshMilkSentQuantity(cxrUserOrderVO.getFreshMilkSentQuantity());
                customerInfo.setFreshMilkGiveQuantity(cxrUserOrderVO.getFreshMilkGiveQuantity());
            }
            cxrUserOrderVO.setCustomerInfoList(customerInfoLists);
        }

        activityOrderService.queryActivityById(cxrUserOrderVO);
        return cxrUserOrderVO;
    }

    /**
     * 更新
     *
     * @param bo
     * @return
     */
    @HuiBoSynCxrUserOrder(value = "#bo.userOrderId")
    @RedisDistributedLock(value = "'cxr:user:order:update' + #bo.userOrderId")
    @Override
    public boolean updateNewOrder(UserFastOrderBo bo) {
        verifyFastOrderParam(bo, TerminalTypeEnums.disribution);

        if (ObjectUtil.isNull(bo.getOrderType())) {
            throw new ServiceException("订单类型必填");
        }

        CxrUserOrder userOrder = cxrUserOrderMapper.selectById(bo.getUserOrderId());

        LocalDate orderTomorrow = DateUtils.getLocalDateFromDate(userOrder.getPayTime())
            .plusDays(1);
        if (!LocalDate.now().isBefore(orderTomorrow)) {
            throw new ServiceException("订单不可编辑，订单只允许当天编辑");
        }

        OrderTypeEnums orderType = OrderTypeEnums.getType(bo.getOrderType());

        boolean activityFlag = userOrder.getActivityId() > 0;
        CompleteInfoDTO completeBo = new CompleteInfoDTO();
        if (activityFlag) {
            completeBo.setActivityId(userOrder.getActivityId());
            completeBo.setBusinessAgent(bo.getBusinessAgent());
            completeBo.setUserOrderId(bo.getUserOrderId());
            completeBo.setFreshMilkGiveCount(bo.getFreshMilkGiveCount());
            completeBo.setMilkGiveCount(bo.getMilkGiveCount());
            completeBo.setAlreadyGiveCount(bo.getAlreadyGiveCount());
            completeBo.setCxrActivitySetMealVo(bo.getCxrActivitySetMealVo());
            activityOrderService.checkActivity(completeBo);
        } else {
            completeBo.setIfGiveLongMilk(true);
            completeBo.setIfGiveMilk(true);
        }

        if (OrderTypeEnums.NEW_ORDER == orderType
            || OrderTypeEnums.CONTINUE_ORDER == orderType
            || OrderTypeEnums.INCREASE_ORDER == orderType) {

            if (ObjectUtil.isEmpty(bo.getDistributionStationId())) {
                throw new ServiceException("请选择开单站点",
                    ConstantExceptionCode.BUSSINESS_EXCEPTION_CODE);
            }
            if (ObjectUtil.isNull(bo.getUserOrderId())) {
                throw new ServiceException("订单不存在!");
            }
            CxrSite site = remoteSiteService.queryId(bo.getDistributionStationId());
            CxrUserOrder cxrUserOrder = new CxrUserOrder();
            cxrUserOrder.setOrderType(bo.getOrderType());
            cxrUserOrder.setId(bo.getUserOrderId());
            cxrUserOrder.setCompanyId(site.getCurrentDeptId());
            // 所属公司 id
            cxrUserOrder.setCompanyId(site.getCurrentDeptId());
            SysDept sysDept = remoteDeptService.queryById(site.getCurrentDeptId());
            if (ObjectUtil.isNull(sysDept)) {
                throw new ServiceException("站点没有关联所属部门");
            }
            // 站点数据设置
            cxrUserOrder.setBigAreaId(site.getCxrRootRegionId());
            cxrUserOrder.setBigAreaName(site.getCxrRootRegionName());
            cxrUserOrder.setProvince(site.getProvice());
            cxrUserOrder.setCity(site.getCity());
            cxrUserOrder.setArea(site.getArea());
            cxrUserOrder.setSiteName(site.getName());
            cxrUserOrder.setSiteAdress(site.getDetailAddress());
            cxrUserOrder.setSiteId(site.getId());
            // 所属公司名称
            cxrUserOrder.setCompanyName(sysDept.getDeptName());
            cxrUserOrder.setAuditStatus(OrderAuditStatusEnums.NO_AUDIT.getValue());
            cxrUserOrder.setRemark(bo.getRemark());

            if (CollectionUtil.isEmpty(bo.getBusinessAgent())) {
                throw new ServiceException("请选择销售代理",
                    ConstantExceptionCode.BUSSINESS_EXCEPTION_CODE);
            }
            cxrUserOrder.setBusinessAgent(bo.getBusinessAgent());
            List<CustomerInfo> customerInfoList = bo.getCustomerInfoList();
            if (CollectionUtil.isEmpty(customerInfoList)) {
                throw new ServiceException("请填写客户信息");
            }

            CustomerInfo customerInfoFirst = customerInfoList.get(0);

            // 客户信息
            cxrUserOrder.setCustomerName(customerInfoFirst.getName());
            cxrUserOrder.setCustomerPhone(customerInfoFirst.getPhone());
            cxrUserOrder.setCustomerAdress(customerInfoFirst.getAdress());
            // 处理订单类型

            if (ObjectUtil.isNull(bo.getUserOrderId())) {
                // 校验纸质单据号唯一
                throw new ServiceException("订单不存储!");
            }

            long count =
                cxrUserOrderMapper.selectCount(
                    new LambdaQueryWrapper<CxrUserOrder>()
                        .eq(CxrUserOrder::getOrderNo, bo.getOrderNo())
                        .ne(CxrUserOrder::getId, bo.getUserOrderId()));
            if (count > 0) {
                throw new ServiceException("该纸质单号已经存在");
            }

            cxrUserOrder.setOrderNo(bo.getOrderNo()); // 纸质 单据号
            cxrUserOrder.setMerchantOrderNo(bo.getMerchantOrderNo());

            cxrUserOrder.setSurplusQuantity(bo.getSurplusQuantity());
            cxrUserOrder.setFreshMilkGiveQuantity(bo.getFreshMilkGiveCount());
            cxrUserOrder.setLongMilkGiveQuantity(bo.getMilkGiveCount());
            cxrUserOrder.setExcessQuantity(bo.getExcessQuantity());
            cxrUserOrder.setFreshMilkSentQuantity(bo.getAlreadyGiveCount());
            cxrUserOrder.setLongMilkSentQuantity(0);
            cxrUserOrder.setSurplusQuantity(bo.getSurplusQuantity());
            cxrUserOrder.setOrderImages(bo.getOrderImages());
            cxrUserOrder.setPlayImages(bo.getPlayImages());


            /**
             * 判断该手机号的客户从下订单到当前时间是否是10天内，10天内续订的，只能选择上一次开单的销
             * 售代理进行录入订单。如果是选择其他销售代理录入订单，则toast提示“该笔订单只选择“XXX小A”录入！”
             */
            // 查询用户最近一次下单时间
            CxrUserOrder lastCxrUserOrder =
                cxrUserOrderService
                    .queryLastOrder(customerInfoFirst.getPhone(), bo.getUserOrderId());
            addLimit(lastCxrUserOrder, bo);
            if (orderType == OrderTypeEnums.INCREASE_ORDER) {
                checkPromotionalOrder(bo, customerInfoFirst);
            }
            if (completeBo.getIfGiveMilk() || completeBo.getIfGiveLongMilk()) {

                if (orderType != OrderTypeEnums.NEW_ORDER) {

                    ExcessQuantityDTO excessQuantity = null;
                    if (orderType == OrderTypeEnums.INCREASE_ORDER) {
                        excessQuantity =
                            excessQuantityUtil.getExcessQuantity(
                                bo.getProductCount(),
                                bo.getFreshMilkGiveCount(),
                                bo.getMilkGiveCount(),
                                bo.getLastOrderSum());

                    } else {
                        excessQuantity =
                            excessQuantityUtil.getExcessQuantity(
                                bo.getProductCount(), bo.getFreshMilkGiveCount(), bo.getMilkGiveCount());
                    }
                    if (!NumberUtil.equals(bo.getExcessQuantity(), excessQuantity.getExcessQuantity())) {
                        throw new ServiceException(
                            StrUtil.format(
                                "超送数量不一致,套餐数量区间{}-{},赠送区间为{}-{}",
                                excessQuantity.getMin(),
                                excessQuantity.getMax(),
                                excessQuantity.getGiveMin(),
                                excessQuantity.getGiveMax()));
                    }
                    cxrUserOrder.setExcessQuantity(excessQuantity.getExcessQuantity());

                }
            }

            if (activityFlag) {
                cxrUserOrder.setGiveGiftList(JSONObject.toJSONString(completeBo.getGiveGiftDTOS()));
                activityOrderService.updateCheckSetMealDetail(completeBo);
            }

            cxrUserOrder.setPromotionalOrderFlag(bo.getPromotionalOrderFlag());
            cxrUserOrder.setApprenticeOrderFlag(bo.getApprenticeOrderFlag());
            cxrUserOrder.setTerminalType(TerminalTypeEnums.disribution.getValue());
            cxrUserOrder.setId(bo.getUserOrderId());

            if (remoteCustomerService.existsCustomerAccount(customerInfoFirst.getPhone())) {
                cxrUserOrder.setPerfectStatus(PerfectStatusEnums.PERFECT.getValue());
                cxrUserOrder.setNewCustomerFlag(SysYesNo.NO.getValue());
            } else {
                cxrUserOrder.setPerfectStatus(PerfectStatusEnums.NO_PERFECT.getValue());
                cxrUserOrder.setNewCustomerFlag(SysYesNo.YES.getValue());
            }
            cxrUserOrder.setCustomerInfoList(customerInfoList);

            cxrUserOrderMapper.updateById(cxrUserOrder);

            if (userOrder != null
                && (userOrder.getPayStatus().intValue() == PayStatusEnums.PAY_SUCCEEDED.getValue()
                || (userOrder.getAuditStatus().intValue()
                == OrderAuditStatusEnums.AUDITED.getValue()))) {
                CompleteInfoDTO completeInfoDTO = new CompleteInfoDTO();
                completeInfoDTO.setFreshMilkGiveCount(bo.getFreshMilkGiveCount());
                completeInfoDTO.setMilkGiveCount(bo.getMilkGiveCount());
                completeInfoDTO.setAlreadyGiveCount(bo.getAlreadyGiveCount());
                cxrUserOrder.setCustomerId(userOrder.getCustomerId());

                // 废弃之前的业绩
                remoteEmployeeAchievementDetailService.achievemenDiscard(
                    cxrUserOrder.getId(), "配送端订单修改废弃审核的订单业绩");
                cxrUserOrderService.orderAfterEmployeeHandler(cxrUserOrder.getId());

                cxrUserOrderService.updateCustomerStockDiff(userOrder, completeInfoDTO);
            }

            if (ObjectUtil.isNotEmpty(bo.getAlreadyGiveCount())
            ) {
                int sentNumber = bo.getAlreadyGiveCount();
                CustomerSentNumberVo customerSentNumberVo = new CustomerSentNumberVo();
                log.info("增订单：{}", "5555555555555555555");
                customerSentNumberVo.setLaoMilkDistributionTotal(
                    Long.valueOf(userOrder.getFreshMilkSentQuantity()));
                customerSentNumberVo.setLaoMilkDistributionTotal(Long.valueOf(userOrder.getFreshMilkSentQuantity()));
                customerSentNumberVo.setMilkDistributionTotal(Long.valueOf(sentNumber));
                customerSentNumberVo.setCxrCustomerId(cxrUserOrder.getCustomerId());
                customerSentNumberVo
                    .setDistributionDate(ObjectUtil.isEmpty(cxrUserOrder.getOrderDate()) ?
                        bo.getOrderDate() : cxrUserOrder.getOrderDate());
                customerSentNumberVo.setReceiverPhone(cxrUserOrder.getCustomerPhone());
                customerSentNumberVo.setCustomerAddress(cxrUserOrder.getCustomerAdress());
                customerSentNumberVo.setIsEdit(true);
                mqUtil.sendSyncMessage(CustomerAddressConstant.CUSTOMER_ADDRESS_TOPIC,
                    CustomerAddressConstant.CUSTOMER_ORDER_SENT_SUM_CHANGE_TAG,
                    JSONUtil.toJsonStr(customerSentNumberVo));
            }

        } else {
            throw new ServiceException("订单类型错误");
        }

        return true;
    }

    @Override
    public LastUserOrderVo queryLastTenDayOrder(CustomerInfo bo) {
        CxrCustomer customer = remoteCustomerService.queryByPhone(bo.getPhone());
        LastUserOrderVo userOrderVo = cxrUserOrderService.queryLastTenDayOrder(customer.getId(), bo.getOrderId());
        return userOrderVo;
    }

    @Override
    public OrderTypeEnums queryOrderTypeNotInId(String phone, Long orderId) {

        if (StringUtils.isEmpty(phone)) {
            throw new ServiceException("请填写客户信息");
        }

        CxrCustomer customer = remoteCustomerService.queryByPhone(phone);
        if (ObjectUtil.isNull(customer)) {
            return OrderTypeEnums.NEW_ORDER;
        } else {
            Integer customerStock = Convert.toInt(customer.getCustomerStock(), 0);
            CxrUserOrder lastCxrUserOrder =
                cxrUserOrderService.queryLastOrder(phone, orderId);
            CxrUserOrder lastCxrUserOrderNotContinue =
                cxrUserOrderService.queryLastOrderInfoNotContinue(phone);
            LocalDate now = LocalDate.now();

            // 增订单判断没有库存限制
            if (lastCxrUserOrderNotContinue != null
                && now.isBefore(
                DateUtils.getLocalDateFromDate(lastCxrUserOrderNotContinue.getOrderDate())
                    .plusDays(10))) {
                return OrderTypeEnums.INCREASE_ORDER;
            }

            // 账户还有鲜奶没喝完
            if (customerStock > 0) {
                if (ObjectUtil.isNull(lastCxrUserOrder)) {
                    return OrderTypeEnums.NEW_ORDER;
                } else {
                    //0数量15天内续单
                    Date zeroTime = remoteCustomerStockDetailService.lastCustomerStockZeroTime(customer.getId());
                    if (zeroTime != null && now.isBefore(DateUtils.getLocalDateFromDate(zeroTime).plusDays(16))) {
                        return OrderTypeEnums.CONTINUE_ORDER;
                    } else {
                        CxrUserOrder lastUserOrder =
                            cxrUserOrderService.queryLastOrder(phone, null);
//                        Integer sum =
//                            lastUserOrder.getOrderQuantity() + lastUserOrder.getFreshMilkGiveQuantity()
//                                - lastUserOrder.getFreshMilkSentQuantity();
                        if (lastUserOrder != null &&
                            customerStock - (lastUserOrder.getOrderQuantity() + lastUserOrder.getFreshMilkGiveQuantity()
                                - lastUserOrder.getFreshMilkSentQuantity()) > 0) {
                            return OrderTypeEnums.CONTINUE_ORDER;
                        } else {
                            return OrderTypeEnums.NEW_ORDER;
                        }
                    }
                }

            } else {
                // 账户中没有鲜奶数量
                if (now.isBefore(
                    DateUtils.getLocalDateFromDate(customer.getCustomerStockZeroTime())
                        .plusDays(16))) {
                    if (ObjectUtil.isNull(lastCxrUserOrder)) {
                        return OrderTypeEnums.NEW_ORDER;
                    } else {
                        return OrderTypeEnums.CONTINUE_ORDER;
                    }
                } else {
                    return OrderTypeEnums.NEW_ORDER;
                }
            }
        }
    }

    @Override
    public Object queryLastOrderBusinessAgent(CustomerInfo bo) {
        CxrUserOrder lastCxrUserOrder =
            cxrUserOrderService.queryFirstDayOrder(bo.getPhone(), null);
        if (ObjectUtil.isNotEmpty(lastCxrUserOrder)
            && (ObjectUtil.isNotEmpty(lastCxrUserOrder.getPromotionalOrderFlag())
            && lastCxrUserOrder.getPromotionalOrderFlag() == true ||
            ObjectUtil.isNotEmpty(lastCxrUserOrder.getPromotionalOrderFlag())
                && lastCxrUserOrder.getApprenticeOrderFlag() == true
        )) {
            Map<String, Object> data = new HashMap<>();
            data.put("data", lastCxrUserOrder.getBusinessAgent());
            data.put("promotionalOrderFlag", lastCxrUserOrder.getPromotionalOrderFlag());
            data.put("apprenticeOrderFlag", lastCxrUserOrder.getApprenticeOrderFlag());
            return data;
        }
        return null;
    }

    @Override
    public Object queryLastTenDayOrderInfo(CustomerInfo bo) {
        CxrCustomer customer = remoteCustomerService.queryByPhone(bo.getPhone());
        List<LastUserOrderDayInfo> userOrders = cxrUserOrderService.queryLastTenDayOrderInfo(bo, customer.getId());

        Map<String, Object> data = new HashMap<>();
        data.put("data", userOrders);
        LastUserOrderVo lastUserOrderVo = new LastUserOrderVo();
        if (CollUtil.isNotEmpty(userOrders)) {
            lastUserOrderVo.setOrderQuantity(userOrders.stream().map(LastUserOrderDayInfo::getOrderQuantity).reduce(Integer::sum).orElse(0));
            lastUserOrderVo.setFreshMilkGiveQuantity(userOrders.stream().map(LastUserOrderDayInfo::getFreshMilkGiveQuantity).reduce(Integer::sum).orElse(0));
            lastUserOrderVo.setLongMilkGiveQuantity(userOrders.stream().map(LastUserOrderDayInfo::getLongMilkGiveQuantity).reduce(Integer::sum).orElse(0));
            lastUserOrderVo.setAmount(userOrders.stream().map(LastUserOrderDayInfo::getAmount).reduce(BigDecimal::add).get());
        }
        data.put("sum", lastUserOrderVo);
        return data;
    }

    private void checkPromotionalOrder(UserFastOrderBo bo, CustomerInfo customerInfoFirst) {

        CxrUserOrder lastCxrUserOrder = cxrUserOrderService.queryLastPromotionalOrderOrder(customerInfoFirst.getPhone(), null);//最近一笔促销单
        if ((ObjectUtil.isNotEmpty(bo.getPromotionalOrderFlag()) && ObjectUtil.equals(bo.getPromotionalOrderFlag(), 1) ||
            ObjectUtil.isNotEmpty(bo.getApprenticeOrderFlag()) && ObjectUtil.equals(bo.getApprenticeOrderFlag(), 1) ||
            bo.getOrderType() == OrderTypeEnums.INCREASE_ORDER.getValue())
            && ObjectUtil.isNotEmpty(lastCxrUserOrder)
        ) {

            // 是否续订
            Long count = cxrUserOrderService.queryZeroQuantityRenewal(lastCxrUserOrder.getCustomerId(), lastCxrUserOrder.getOrderDate());
            if (count == 0l) {
                // 一天内多次增订取第一次增订
                List<BusinessAgent> lastBusinessAgent = lastCxrUserOrder.getBusinessAgent();
                LocalDate lastOrderDate = lastCxrUserOrder.getOrderDate().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();
                CxrUserOrder firstDayOrder = cxrUserOrderService.queryFirstDayOrder(customerInfoFirst.getPhone(), null);

                Boolean firstDayOrderFalag = false;
                if (ObjectUtil.isNotEmpty(firstDayOrder) &&
                    !ObjectUtil.equals(firstDayOrder.getId(), lastCxrUserOrder.getId())) {
                    lastBusinessAgent = firstDayOrder.getBusinessAgent();
                    lastOrderDate = firstDayOrder.getOrderDate().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDate();
                    firstDayOrderFalag = true;
                }
                List<BusinessAgent> businessAgent = bo.getBusinessAgent();

                LocalDate orderDate = bo.getOrderDate().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();


                if (ObjectUtil.equals(orderDate, lastOrderDate)) {
                    if (bo.getBusinessAgent().size() > lastBusinessAgent.size()) {
                        throw new ServiceException(StrUtil.format("客户{},只能由'{}'录单", customerInfoFirst.getPhone(), lastBusinessAgent.stream().map(BusinessAgent::getProxyName).collect(Collectors.joining(","))));
                    }
                    Map<Long, BusinessAgent> agentMap = lastBusinessAgent.stream().collect(Collectors.toMap(a -> a.getProxyId(), a -> a, (v1, v2) -> v1));
                    int matching = 0;
                    int lastSize = lastBusinessAgent.size();
                    int sizeBo = bo.getBusinessAgent().size();
                    for (BusinessAgent agent : businessAgent) {
                        if (agentMap.get(agent.getProxyId()) != null) {
                            matching++;
                        }
                    }
                    if (!firstDayOrderFalag && ObjectUtil.equals(lastCxrUserOrder.getApprenticeOrderFlag(), false)
                        && ObjectUtil.equals(lastCxrUserOrder.getPromotionalOrderFlag(), false)) {

                    } else {
                        String proxyNames = lastBusinessAgent.stream().map(BusinessAgent::getProxyName).collect(Collectors.joining(","));

                        if (sizeBo < lastSize) {
                            if (matching == sizeBo) {
                                log.info("客户 {} 跳过录单", customerInfoFirst.getCustomerPhone());
                            } else {
                                throw new ServiceException(StrUtil.format("客户{},只能由'{}'录单", customerInfoFirst.getPhone(), proxyNames));
                            }
                        } else {
                            if (matching != lastSize) {
                                throw new ServiceException(StrUtil.format("客户{},只能由'{}'录单", customerInfoFirst.getPhone(), proxyNames));
                            }
                        }
                    }

                } else if (orderDate.isAfter(lastOrderDate)) {
                    Long customerId = lastCxrUserOrder.getCustomerId();
                    List<CxrCustomerAddressDTO> addressesDTO =
                        remoteCustomerAddressService.cxrCustomerAddressDTOList(customerId);
                    Map<Long, String> agentMap = addressesDTO.stream().collect(Collectors.toMap(a -> a.getCxrEmployeeId(), a -> a.getCxrEmployeeName(), (v1, v2) -> v1));

                    if (CollUtil.isNotEmpty(addressesDTO)) {

                        Set<String> names = addressesDTO.stream().map(CxrCustomerAddressDTO::getCxrEmployeeName).collect(Collectors.toSet());

                        if (bo.getBusinessAgent().size() > names.size()) {
                            throw new ServiceException(StrUtil.format("客户{},只能由'{}'录单", customerInfoFirst.getPhone(), addressesDTO.stream().map(CxrCustomerAddressDTO::getCxrEmployeeName).distinct().collect(Collectors.joining(","))));
                        }
                        int matching = 0;
                        int sizeBo = bo.getBusinessAgent().size();
                        int addressesSize = addressesDTO.stream().map(CxrCustomerAddressDTO::getCxrEmployeeId).collect(Collectors.toSet()).size();
                        for (BusinessAgent agent : businessAgent) {
                            if (agentMap.get(agent.getProxyId()) != null) {
                                matching++;
                            }
                        }

                        String proxyNames = addressesDTO.stream().map(CxrCustomerAddressDTO::getCxrEmployeeName).distinct().collect(Collectors.joining(","));

                        if (sizeBo < addressesSize) {
                            if (matching == sizeBo) {
                                log.info("客户 {} 跳过录单", customerInfoFirst.getCustomerPhone());
                            } else {
                                throw new ServiceException(StrUtil.format("客户{},只能由配送员'{}'录单", customerInfoFirst.getPhone(), proxyNames));
                            }
                        } else {
                            if (matching != addressesSize) {
                                throw new ServiceException(StrUtil.format("客户{},只能由配送员'{}'录单", customerInfoFirst.getPhone(), proxyNames));
                            }
                        }
                    }
                }
            }
        }
    }
}
