<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.job.common.mapper.CxrRegionStaitemDailyReportMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.job.common.domain.CxrRegionStaitemDailyReport">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="reportDate" column="report_date" jdbcType="DATE"/>
            <result property="weekDay" column="week_day" jdbcType="VARCHAR"/>
            <result property="dimensionType" column="dimension_type" jdbcType="VARCHAR"/>
            <result property="companyId" column="company_id" jdbcType="BIGINT"/>
            <result property="companyName" column="company_name" jdbcType="VARCHAR"/>
            <result property="rootRegionId" column="root_region_id" jdbcType="BIGINT"/>
            <result property="rootRegionName" column="root_region_name" jdbcType="VARCHAR"/>
            <result property="regionId" column="region_id" jdbcType="BIGINT"/>
            <result property="regionName" column="region_name" jdbcType="VARCHAR"/>
            <result property="regionCode" column="region_code" jdbcType="VARCHAR"/>
            <result property="siteId" column="site_id" jdbcType="BIGINT"/>
            <result property="siteName" column="site_name" jdbcType="VARCHAR"/>
            <result property="siteCode" column="site_code" jdbcType="VARCHAR"/>
            <result property="agentId" column="agent_id" jdbcType="BIGINT"/>
            <result property="agentName" column="agent_name" jdbcType="VARCHAR"/>
            <result property="agentCode" column="agent_code" jdbcType="VARCHAR"/>
            <result property="agentLevel" column="agent_level" jdbcType="VARCHAR"/>
            <result property="newOrderCount" column="new_order_count" jdbcType="DECIMAL"/>
            <result property="continueOrderCount" column="continue_order_count" jdbcType="DECIMAL"/>
            <result property="increaseOrderCount" column="increase_order_count" jdbcType="DECIMAL"/>
            <result property="returnOrderCount" column="return_order_count" jdbcType="DECIMAL"/>
            <result property="totalOrderCount" column="total_order_count" jdbcType="DECIMAL"/>
            <result property="newOrderAchievement" column="new_order_achievement" jdbcType="DECIMAL"/>
            <result property="continueOrderAchievement" column="continue_order_achievement" jdbcType="DECIMAL"/>
            <result property="increaseOrderAchievement" column="increase_order_achievement" jdbcType="DECIMAL"/>
            <result property="returnOrderAchievement" column="return_order_achievement" jdbcType="DECIMAL"/>
            <result property="totalAchievement" column="total_achievement" jdbcType="DECIMAL"/>
            <result property="newOrderRatio" column="new_order_ratio" jdbcType="DECIMAL"/>
            <result property="continueOrderRatio" column="continue_order_ratio" jdbcType="DECIMAL"/>
            <result property="increaseOrderRatio" column="increase_order_ratio" jdbcType="DECIMAL"/>
            <result property="returnOrderRatio" column="return_order_ratio" jdbcType="DECIMAL"/>
            <result property="newOrderUnitPrice" column="new_order_unit_price" jdbcType="DECIMAL"/>
            <result property="continueOrderUnitPrice" column="continue_order_unit_price" jdbcType="DECIMAL"/>
            <result property="increaseOrderUnitPrice" column="increase_order_unit_price" jdbcType="DECIMAL"/>
            <result property="avgUnitPrice" column="avg_unit_price" jdbcType="DECIMAL"/>
            <result property="newOrderCountYoy" column="new_order_count_yoy" jdbcType="DECIMAL"/>
            <result property="continueOrderCountYoy" column="continue_order_count_yoy" jdbcType="DECIMAL"/>
            <result property="increaseOrderCountYoy" column="increase_order_count_yoy" jdbcType="DECIMAL"/>
            <result property="returnOrderCountYoy" column="return_order_count_yoy" jdbcType="DECIMAL"/>
            <result property="totalOrderCountYoy" column="total_order_count_yoy" jdbcType="DECIMAL"/>
            <result property="newOrderAchievementYoy" column="new_order_achievement_yoy" jdbcType="DECIMAL"/>
            <result property="continueOrderAchievementYoy" column="continue_order_achievement_yoy" jdbcType="DECIMAL"/>
            <result property="increaseOrderAchievementYoy" column="increase_order_achievement_yoy" jdbcType="DECIMAL"/>
            <result property="returnOrderAchievementYoy" column="return_order_achievement_yoy" jdbcType="DECIMAL"/>
            <result property="totalAchievementYoy" column="total_achievement_yoy" jdbcType="DECIMAL"/>
            <result property="newOrderCountMom" column="new_order_count_mom" jdbcType="DECIMAL"/>
            <result property="continueOrderCountMom" column="continue_order_count_mom" jdbcType="DECIMAL"/>
            <result property="increaseOrderCountMom" column="increase_order_count_mom" jdbcType="DECIMAL"/>
            <result property="returnOrderCountMom" column="return_order_count_mom" jdbcType="DECIMAL"/>
            <result property="totalOrderCountMom" column="total_order_count_mom" jdbcType="DECIMAL"/>
            <result property="newOrderAchievementMom" column="new_order_achievement_mom" jdbcType="DECIMAL"/>
            <result property="continueOrderAchievementMom" column="continue_order_achievement_mom" jdbcType="DECIMAL"/>
            <result property="increaseOrderAchievementMom" column="increase_order_achievement_mom" jdbcType="DECIMAL"/>
            <result property="returnOrderAchievementMom" column="return_order_achievement_mom" jdbcType="DECIMAL"/>
            <result property="totalAchievementMom" column="total_achievement_mom" jdbcType="DECIMAL"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="deleteStatus" column="delete_status" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,report_date,week_day,dimension_type,
        company_id,company_name,root_region_id,
        root_region_name,region_id,region_name,
        region_code,site_id,site_name,
        site_code,agent_id,agent_name,
        agent_code,agent_level,new_order_count,
        continue_order_count,increase_order_count,return_order_count,
        total_order_count,new_order_achievement,continue_order_achievement,
        increase_order_achievement,return_order_achievement,total_achievement,
        new_order_ratio,continue_order_ratio,increase_order_ratio,
        return_order_ratio,new_order_unit_price,continue_order_unit_price,
        increase_order_unit_price,avg_unit_price,new_order_count_yoy,
        continue_order_count_yoy,increase_order_count_yoy,return_order_count_yoy,
        total_order_count_yoy,new_order_achievement_yoy,continue_order_achievement_yoy,
        increase_order_achievement_yoy,return_order_achievement_yoy,total_achievement_yoy,
        new_order_count_mom,continue_order_count_mom,increase_order_count_mom,
        return_order_count_mom,total_order_count_mom,new_order_achievement_mom,
        continue_order_achievement_mom,increase_order_achievement_mom,return_order_achievement_mom,
        total_achievement_mom,create_time,update_time,
        delete_status
    </sql>
</mapper>
