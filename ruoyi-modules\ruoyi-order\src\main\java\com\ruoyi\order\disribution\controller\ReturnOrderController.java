package com.ruoyi.order.disribution.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.business.base.api.domain.CxrCustomer;
import com.ruoyi.business.base.api.domain.bo.CxrCustomerAddressBo;
import com.ruoyi.business.base.api.domain.vo.CustomerMilkHistoryVO;
import com.ruoyi.business.base.api.dubbo.RemoteCustomerAddressService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.UserType;
import com.ruoyi.common.core.validate.PageGroup;
import com.ruoyi.order.common.domain.bo.CxrUserReturnOrderBO;
import com.ruoyi.order.common.entity.CxrUserReturnOrder;
import com.ruoyi.order.disribution.domain.vo.OrderVO;
import com.ruoyi.order.disribution.service.ReturnOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.lang.reflect.InvocationTargetException;

// CxrUserReturnOrder
@Api(
    value = "配送端退订单",
    tags = {"配送端退订单"})
@RestController
@RequiredArgsConstructor
@RequestMapping("/disribution/returnOrder")
public class ReturnOrderController {

    private final ReturnOrderService returnOrderService;

    @DubboReference
    private RemoteCustomerAddressService remoteCustomerAddressService;

    @ApiOperation("退订单添加")
    @SaCheckPermission(value = "order:returnOrder:add", type = UserType.cxr_staff)
    @PostMapping("/add")
    public R<?> addOrder(@RequestBody CxrUserReturnOrder cxrUserReturnOrder) {
        returnOrderService.add(cxrUserReturnOrder);
        return R.ok();
    }

    @ApiOperation("查询客户鲜奶")
//    @SaCheckPermission(value = "order:returnOrder:queryHistory", type = UserType.cxr_staff)
    @PostMapping("/queryHistory")
    public R<CustomerMilkHistoryVO> queryHistory(
        @ApiParam("用户id主键") @NotNull(message = "主键不能为空") @RequestParam Long customerId) {
        return R.ok(returnOrderService.queryHistory(customerId));
    }

    // 传收货人地址，收货人电话
    @ApiOperation("收获人地址电话")
    @SaCheckPermission(value = "order:returnOrder:acquireCustomer", type = UserType.cxr_staff)
    @PostMapping("/acquireCustomer/{pageNum}/{pageSize}")
    public R requireUser(
        @RequestBody @Validated(PageGroup.class) CxrCustomerAddressBo cxrCustomerAddress,
        @PathVariable Integer pageNum,
        @PathVariable Integer pageSize) {
        IPage<CxrCustomer> page = new Page(pageNum, pageSize);
        return R.ok(remoteCustomerAddressService.pageWithQuarters(page, cxrCustomerAddress));
    }
    //    ReturnOrderService  detail

    /**
     * 订单那
     */
    @ApiOperation("细节")
//    @SaCheckPermission(value = "order:returnOrder:detailOrder", type = UserType.cxr_staff)
    @PostMapping("/detailOrder")
    public R detailBindingOrder(
        @ApiParam("主键") @NotNull(message = "主键不能为空") @RequestParam Long orderId)
        throws InvocationTargetException, IllegalAccessException {
        OrderVO o = returnOrderService.detail(orderId);
        return R.ok(o);
    }

    @ApiOperation("编辑退订单")
    //    @SaCheckPermission(value = "order:returnOrder:edit", type = UserType.cxr_staff)
    @PostMapping("/edit")
    public R<Void> edit(@RequestBody CxrUserReturnOrderBO bo) {

        boolean flag = returnOrderService.edit(bo);

        return flag ? R.ok() : R.fail();
    }

    @ApiOperation("重新提交")
    @PostMapping("/resubmitOrder")
    public R resubmitOrder(
        @RequestBody CxrUserReturnOrderBO bo) {
        return R.ok(returnOrderService.resubmitOrder(bo));
    }
}
