package com.ruoyi.calculate.dubbo;

import com.ruoyi.calculate.customerOrderStatistics.service.CustomerOrderStatisticsService;
import com.ruoyi.order.api.RemoteCustomerOrderStatisticsService;
import com.ruoyi.order.api.domain.bo.CustomerOrderStatisticsBo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;

@Slf4j
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteCustomerOrderStatisticsServiceImpl implements RemoteCustomerOrderStatisticsService {

    private final CustomerOrderStatisticsService customerOrderStatisticsService;


    @Override
    public void orderStatisticsExport(CustomerOrderStatisticsBo bo, HttpServletResponse response) {
        customerOrderStatisticsService.orderStatisticsExport(bo, response);
    }
}
