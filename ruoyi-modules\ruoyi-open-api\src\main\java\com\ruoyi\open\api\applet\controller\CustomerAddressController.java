package com.ruoyi.open.api.applet.controller;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.business.base.api.domain.CxrCustomerAddress;
import com.ruoyi.business.base.api.domain.bo.CxrCustomerAddressAppletBo;
import com.ruoyi.business.base.api.dubbo.RemoteCustomerAddressService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.RSAEncrypt;
import com.ruoyi.common.rocketmq.constant.customer.CxrCustomerConstant;
import com.ruoyi.common.rocketmq.utils.MqUtil;
import com.ruoyi.open.api.applet.domain.bo.AppCustomerAddressVo;
import com.ruoyi.open.api.applet.service.AppletCustomerAddressService;
import com.ruoyi.open.api.applet.service.NewOrderMatchManager;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(value = "小程序客户")
@RequiredArgsConstructor
@RestController
@RequestMapping("/wxApplet/customerAddress")
@Slf4j
public class CustomerAddressController {

    private final AppletCustomerAddressService appletCustomerAddressService;

    private final NewOrderMatchManager newOrderMatchManager;

    @DubboReference
    private RemoteCustomerAddressService remoteCustomerAddressService;

    @Autowired
    private MqUtil mqUtil;


    @ApiOperation("获取用户地址")
    @PostMapping("/addCustomerAddress")
    public R<AppCustomerAddressVo> getCustomerAddress(@RequestBody CxrCustomerAddressAppletBo bo) {
        Integer count = appletCustomerAddressService.addCustomerAddress(bo);
        if(count>0){
            CxrCustomerAddress cxrCustomerAddress = remoteCustomerAddressService.getCustomerAddressById(bo.getCustomerAddressId());
            if(Objects.nonNull(cxrCustomerAddress)){
                BigDecimal longitude = new BigDecimal(bo.getLongitude()).setScale(6, RoundingMode.HALF_DOWN);
                BigDecimal latitude = new BigDecimal(bo.getLatitude()).setScale(6, RoundingMode.HALF_DOWN);
                appletCustomerAddressService.calculateUpdateCustomerSiteAndEmployee(cxrCustomerAddress,bo.getNearSiteId(),bo.getNearSiteDistance(),longitude,latitude);
            }
        }
        AppCustomerAddressVo vo=new AppCustomerAddressVo();
        vo.setCount(count);
        vo.setId(bo.getCustomerAddressId());
        mqUtil.sendDelayMessageYds(CxrCustomerConstant.CUSTOMER_TOPIC, CxrCustomerConstant.YDS_CUSTOMER_SYNC_TAG, bo.getCxrCustomerId().toString(),new Date(System.currentTimeMillis() + 20000));
        return R.ok(vo);
    }

    @ApiOperation("获取加密数据")
    @PostMapping("/getJiaMiData")
    public R<JSONObject> getCustomerAddress(@RequestBody JSONObject bo) {
        String publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvQDK2gpw6LkXDlRuemqLG5AsLMP0BT9DUrsY5MvC90d02DR2g7l0uugZiLvdfCmaEMlzeECkoyi0hnPkk/Q5Yo9aO6dmUi8gnh7wFPwPXdtXAkri2934QkRW+6qneF9VczPfWi942jCWnQDDabjfPDJvEKQP2Co6K5pLZ2I4GuR7ZiIweMQwPuhVlc2xq/YP/3sZ3ZxA1gaqhpu15byVPLurK3K/VwL1mWCB7385On/2l88Pma3ztIAmmGCr3IQiqwZ7kQpz7u/UsliPABsGAv8E4MSFZc807yioOK7t5AvHBPgGMS8ntYAI4/6wbFME/pnVYCKxm1rVX6dv0SEqiQIDAQAB";
        String decrypt = null;
        try {
            decrypt = RSAEncrypt.encrypt2(bo.toJSONString(), publicKey);
            System.out.println("加密后数据：" + decrypt);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.ok(decrypt);
    }


    @ApiOperation("小程序修改erp的地址")
    @PostMapping("/updateAppCustomerAddress")
    public R<AppCustomerAddressVo> updateAppCustomerAddress(@RequestBody CxrCustomerAddressAppletBo bo) {
        Integer count = appletCustomerAddressService.updateAppCustomerAddress(bo);
        if(count>0){
            CxrCustomerAddress cxrCustomerAddress = remoteCustomerAddressService.getCustomerAddressById(bo.getCustomerAddressId());
            if(Objects.nonNull(cxrCustomerAddress)){
                BigDecimal longitude = cxrCustomerAddress.getLongitude().setScale(6, RoundingMode.HALF_DOWN);
                BigDecimal latitude = cxrCustomerAddress.getLongitude().setScale(6, RoundingMode.HALF_DOWN);
                appletCustomerAddressService.calculateUpdateCustomerSiteAndEmployee(cxrCustomerAddress,bo.getNearSiteId(),bo.getNearSiteDistance(),longitude,latitude);
                log.info("已进 cxrCustomerAddress{}",cxrCustomerAddress);
            }
        }
        AppCustomerAddressVo vo=new AppCustomerAddressVo();
        vo.setCount(count);
        vo.setId(bo.getCustomerAddressId());
        mqUtil.sendSyncMessage(CxrCustomerConstant.CUSTOMER_TOPIC, CxrCustomerConstant.YDS_CUSTOMER_SYNC_TAG, bo.getCxrCustomerId().toString());
        return R.ok(vo);
    }

}
