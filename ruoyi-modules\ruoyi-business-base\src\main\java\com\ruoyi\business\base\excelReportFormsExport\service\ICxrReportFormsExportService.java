package com.ruoyi.business.base.excelReportFormsExport.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.business.api.domain.bo.CxrReportFormsExportBO;
import com.ruoyi.business.api.domain.vo.CxrReportFormsExportVo;
import com.ruoyi.business.base.api.domain.CxrReportFormsExport;
import com.ruoyi.common.mybatis.core.page.PageTableDataInfo;

import javax.servlet.http.HttpServletResponse;

/**
 * 文件下载(CxrReportFormsExport)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-27 16:54:37
 */
public interface ICxrReportFormsExportService extends IService<CxrReportFormsExport> {

    PageTableDataInfo<CxrReportFormsExportVo> queryPage(CxrReportFormsExportBO cxrReportFormsExportBO);

    void downLoad(Long id, HttpServletResponse response) throws Exception;

    PageTableDataInfo<CxrReportFormsExportVo> reportFormsExporPage(CxrReportFormsExportBO cxrReportFormsExportBO);
}
