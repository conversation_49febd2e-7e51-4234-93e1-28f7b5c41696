package com.ruoyi.business.base.employeeAchievementDetail.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.business.base.api.domain.CxrEmployeeAchievementDetail;
import com.ruoyi.business.base.api.domain.CxrEmployeePost;
import com.ruoyi.business.base.api.domain.dto.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>Description: </p>
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/8/10 17:52
 **/
public interface CxrEmployeeAchievementDetailMapper extends BaseMapper<CxrEmployeeAchievementDetail> {

    DailyPerformanceDto sumPerformanceEveryDay(@Param("now") LocalDate now, @Param("del") String del,
        @Param("id") Long cxrEmployeeId);


    BigDecimal sumMonthAchievement(@Param("start") LocalDate start, @Param("end") LocalDate end,
        @Param("employeeId") Long employeeId);
    List<CxrEmployeeAchievementDetail> monthAchievementList(@Param("start") LocalDate start, @Param("end") LocalDate end,
                                        @Param("employeeId") Long employeeId);

    CxrEmployeeAchievementDetailDTO sumMonthAchievementAll(@Param("start") LocalDate start, @Param("end") LocalDate end,
        @Param("employeeId") Long employeeId);

    BigDecimal sumSiteMonthAchievement(@Param("start") LocalDate start, @Param("end") LocalDate end,
        @Param("siteId") Long siteId);
    BigDecimal currentDayAchievementSiteNoGroup(@Param("start") LocalDate start, @Param("end") LocalDate end,
        @Param("siteId") Long siteId);


    BigDecimal sumSiteMonthAchievementFore(@Param("posts") List<CxrEmployeePost> posts);

    DailyIntergerDto sumboxsEveryDay(@Param("now") LocalDate now, @Param("del") String value,
        @Param("id") Long cxrEmployeeId);

    List<DailyIntergerDto> sumboxsEveryDayGroupSite(@Param("now") LocalDate now, @Param("del") String value);

    List<DailyIntergerDto> sumPerformanceByDayGroupSite(@Param("now") LocalDate now, @Param("del") String value,
        @Param("ids") List<Long> ids);

    List<DailyPerformanceDto> selectSaleAchievement(@Param("orderIds") List<Long> orderIds,
        @Param("employeeId") Long employeeId);

    CxrEmployeeAchievementDetail employeeMonthAchievementSum(@Param("start") LocalDate start,
        @Param("end") LocalDate end,
        @Param("employeeId") Long employeeId);

    CxrEmployeeAchievementDetail employeeMonthAchievementSumSite(@Param("start") LocalDate start,
        @Param("end") LocalDate end,
        @Param("employeeId") Long employeeId,@Param("siteId") Long siteId);


    void saveOrUpdate(@Param("bo") CxrEmployeeAchievementDetail bo);
    int delBySiteDate(@Param("siteId") Long siteId, @Param("orderDate") LocalDate date);
    int saveBySiteDate(@Param("siteId") Long siteId, @Param("orderDate") LocalDate date);

    void saveOrUpdateAll(@Param("date") LocalDate date);

    void saveOrUpdateBySiteId(@Param("date") LocalDate date, @Param("siteId") Long siteId);

    BigDecimal sumEmployeeNewOrders(@Param("employeeId") Long employeeId, @Param("time") String time);

    BigDecimal sumEmployeeTotalPerformance(@Param("employeeId") Long employeeId, @Param("time") String time);

    BigDecimal sumEmployeeDeliveredOrders(@Param("proxyId") Long employeeId, @Param("time") String time);

    int batchUpdateOrderFmQty(@Param("list")List<AchievementDetailUpdateDto> employeeAchievementDetails);

    List<CxrEmployeeAchievementDetail> employeeMonthAchievementNoGroup(@Param("first") LocalDate first, @Param("nextFirst") LocalDate nextFirst);

    int delByDate(@Param("list") Collection<CxrEmployeeAchievementDetail> list);

    int saveByDate(@Param("jsonList") String jsonList);
}
