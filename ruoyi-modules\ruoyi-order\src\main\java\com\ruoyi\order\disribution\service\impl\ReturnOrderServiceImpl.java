package com.ruoyi.order.disribution.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.business.base.api.domain.*;
import com.ruoyi.business.base.api.domain.vo.CustomerMilkHistoryVO;
import com.ruoyi.business.base.api.dubbo.*;
import com.ruoyi.business.base.api.enums.OrderTypeEnums;
import com.ruoyi.business.base.api.model.LoginEmployee;
import com.ruoyi.common.core.enums.DeleteStatus;
import com.ruoyi.common.core.enums.OccupationStatus;
import com.ruoyi.common.core.enums.SaleStatus;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.service.DictService;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.enums.AuditStatusEnums;
import com.ruoyi.common.enums.TerminalTypeEnums;
import com.ruoyi.common.rocketmq.constant.message.SendMessageReturnOrderConstant;
import com.ruoyi.common.rocketmq.utils.MqUtil;
import com.ruoyi.common.satoken.utils.helper.StaffLoginHelper;
import com.ruoyi.core.base.domain.CxrOrderAfterSale;
import com.ruoyi.core.base.domain.vo.CxrOrderAfterSaleVo;
import com.ruoyi.core.base.mapper.CxrOrderAfterSaleMapper;
import com.ruoyi.order.api.enums.OrderAuditStatusEnums;
import com.ruoyi.order.common.domain.bo.CxrUserReturnOrderBO;
import com.ruoyi.order.common.domain.vo.CxrUserReturnOrderVO;
import com.ruoyi.order.common.entity.CxrUserOrder;
import com.ruoyi.order.common.entity.CxrUserReturnOrder;
import com.ruoyi.order.common.mapper.CxrUserOrderMapper;
import com.ruoyi.order.common.mapper.CxrUserReturnOrderMapper;
import com.ruoyi.order.common.utils.OrderUtil;
import com.ruoyi.order.disribution.domain.vo.OrderVO;
import com.ruoyi.order.disribution.service.CommonOrderService;
import com.ruoyi.order.disribution.service.ReturnOrderService;
import com.ruoyi.order.extend.mapper.CxrEmployeeMapper;
import com.ruoyi.order.manager.strategy.behavior.AbstractCxrUserOrderBehavior;
import com.ruoyi.system.api.RemoteDeptService;
import com.ruoyi.system.api.domain.SysDept;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.InvocationTargetException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
@GlobalTransactional
public class ReturnOrderServiceImpl extends ServiceImpl<CxrUserOrderMapper, CxrUserOrder>
    implements ReturnOrderService {

    private final CxrUserReturnOrderMapper cxrUserReturnOrderMapper;
    private final CxrUserOrderMapper cxrUserOrderMapper;
    @DubboReference
    private RemoteEmployeeService remoteEmployeeService;
    @DubboReference
    private RemoteSiteService remoteSiteServicel;
    @DubboReference
    private RemoteCustomerService remoteCustomerService;

    @DubboReference
    private RemoteCustomerAddressService remoteCustomerAddressService;

    private final AbstractCxrUserOrderBehavior abstractCxrUserOrderBehavior;

    @DubboReference
    private RemoteResidentialQuarService remoteResidentialQuarService;

    @DubboReference
    private RemoteDeptService remoteDeptService;

    private final CommonOrderService commonOrderService;

    private final CxrOrderAfterSaleMapper cxrOrderAfterSaleMapper;

    private final CxrEmployeeMapper employeeMapper;

    @Autowired
    private MqUtil mqUtil;

    @Override
    public void add(CxrUserReturnOrder cxrUserReturnOrder) {

        Long customerId = cxrUserReturnOrder.getCustomerId();
        if (ObjectUtil.isEmpty(customerId)) {
            throw new ServiceException("参数异常！");
        }
        commonOrderService.selectEmployeeLimit(cxrUserReturnOrder.getBusinessAgent(), null);
        commonOrderService.selectCustomerLimit(customerId);
        CxrCustomer cxrCustomer = remoteCustomerService.getById(customerId);
        if (cxrCustomer.getCustomerStock() == 0) {
            throw new ServiceException("客户库存为0不能开退订单!");
        }
        cxrUserReturnOrder.setOrderDate(new Date());
        CustomerMilkHistoryVO customerMilkHistoryVO =
            this.queryHistory(customerId);
        cxrUserReturnOrder.setTotalQuantity(customerMilkHistoryVO.getFreshMilkOrderTotal());
        cxrUserReturnOrder.setLongMilkSentQuantity(customerMilkHistoryVO.getLongMilkSentTotal());
        cxrUserReturnOrder.setLongMilkGiveQuantity(customerMilkHistoryVO.getLongMilkGiveTotal());
        cxrUserReturnOrder.setFreshMilkGiveQuantity(customerMilkHistoryVO.getFreshMilkGiveTotal());
        cxrUserReturnOrder.setFreshMilkSentQuantity(customerMilkHistoryVO.getFreshMilkSentTotal());
        Long orderId = 0L;
        if (0L != (orderId = saveAsUserOrder(cxrUserReturnOrder))) {
            cxrUserReturnOrder.setUserOrderId(orderId); // 从方法获得orderId
            cxrUserReturnOrder.getBusinessAgent().stream()
                .forEach(
                    s -> {
                        CxrEmployee cxrEmployee = remoteEmployeeService.remoteDetail(s.getProxyId());
                        s.setLevel(Integer.valueOf(cxrEmployee.getEmployeeLevelType()));
                        if (ObjectUtil.isEmpty(s.getSiteName())) {
                            s.setSiteName(cxrEmployee.getSiteName());
                        }
                    });
            int insert = cxrUserReturnOrderMapper.insert(cxrUserReturnOrder);
            if (insert > 0) {
                // 站点主管 查询代理所在站点的主管
                // 配送员收到消息
                mqUtil.sendSyncMessage(SendMessageReturnOrderConstant.MESSAGE_CENTER_CXR_CUSTOMER_RETURN_ORDER_TOPIC,
                    SendMessageReturnOrderConstant.CUSTOMER_CREATE_RETURN_ORDER_MSG_TAG, cxrUserReturnOrder.getUserOrderId() + "");
            }
            Long afterSalesId = cxrUserReturnOrder.getAfterSalesId();
            if (afterSalesId != null) {

                CxrOrderAfterSale cxrOrderAfterSale = cxrOrderAfterSaleMapper.selectById(afterSalesId);
                if (cxrOrderAfterSale == null) {
                    throw new ServiceException("售后单不存在");
                }
                if (!customerId.equals(cxrOrderAfterSale.getCustomerId())) {
                    throw new ServiceException("当前退订单和售后工单客户手机号不一致!");
                }
                if (cxrOrderAfterSale.getGoodsType() == 3) {

                } else {
                    // 售后单关联退订单
                    cxrOrderAfterSaleMapper.update(null, new LambdaUpdateWrapper<CxrOrderAfterSale>()
                        .set(CxrOrderAfterSale::getRefundOrderId, orderId)
                        .set(CxrOrderAfterSale::getSaleStatus, SaleStatus.PENDING.getValue())
                        .eq(CxrOrderAfterSale::getId, afterSalesId));
                }
            }

        }
        // 插入到主订单表
        // 审核通过的时候会有扣钱的方法 ,扣回客户的钱的方法不用管,写在了审核方法.

    }

    @Override
    public CustomerMilkHistoryVO queryHistory(Long customerId) {
        CustomerMilkHistoryVO customerMilkHistoryVO =
            remoteCustomerService.customerMilkHistoryVO(customerId);
        return customerMilkHistoryVO;
    }

    @Override
    public OrderVO detail(Long orderId) throws InvocationTargetException, IllegalAccessException {
        CxrUserOrder remoteOrder = cxrUserOrderMapper.selectById(orderId);
        CxrUserReturnOrderVO cxrUserReturnOrderVO = new CxrUserReturnOrderVO();

        CxrUserReturnOrder cxrUserReturnOrder =
            cxrUserReturnOrderMapper.selectOne(
                new LambdaQueryWrapper<CxrUserReturnOrder>()
                    .eq(CxrUserReturnOrder::getUserOrderId, orderId)
                    .eq(CxrUserReturnOrder::getDeleteStatus, DeleteStatus.not_deleted));
        Long afterSalesId = Objects.nonNull(cxrUserReturnOrder) ? cxrUserReturnOrder.getAfterSalesId() : null;
        OrderVO returnOrderVO = new OrderVO();
        BeanUtils.copyProperties(returnOrderVO, remoteOrder);
        if (Objects.nonNull(cxrUserReturnOrder)) {
            BeanUtils.copyProperties(cxrUserReturnOrderVO, cxrUserReturnOrder);
        }

        returnOrderVO.setCxrUserReturnOrderVO(cxrUserReturnOrderVO);
        returnOrderVO.setRefundNoAuditReasons(remoteOrder.getRefundNoAuditReasons());
        if (afterSalesId != null) {
            CxrOrderAfterSale cxrOrderAfterSale = cxrOrderAfterSaleMapper.selectById(afterSalesId);
            if (cxrOrderAfterSale != null) {
                cxrUserReturnOrderVO.setAfterSalesNo(cxrOrderAfterSale.getAfterSaleNo());
                CxrOrderAfterSaleVo cxrOrderAfterSaleVo = new CxrOrderAfterSaleVo();
                org.springframework.beans.BeanUtils.copyProperties(cxrOrderAfterSale, cxrOrderAfterSaleVo);
                cxrUserReturnOrderVO.setCxrOrderAfterSaleVo(cxrOrderAfterSaleVo);
                cxrUserReturnOrderVO.setOrderMoney(
                    cxrOrderAfterSale.getTotalAmount().subtract(cxrOrderAfterSale.getDiscountsAmount()));
            }
        }

        if (ObjectUtil.isEmpty(cxrUserReturnOrderVO.getOrderMoney()) && ObjectUtil.isNotEmpty(cxrUserReturnOrder.getPayOrderId())) {
            CxrUserOrder order = cxrUserOrderMapper.selectById(cxrUserReturnOrder.getPayOrderId());
            cxrUserReturnOrderVO.setOrderMoney(order.getAmount());
        }
        DictService dictService = SpringUtils.getBean(DictService.class);
        String rrCode = cxrUserReturnOrderVO.getRrCode();
        if (StringUtils.isNotBlank(rrCode)) {
            cxrUserReturnOrderVO.setRrName(dictService.getDictLabel("order_refund_reason", rrCode, ","));
        }

        // 给客户的小区 ,和 小区 id
        CxrCustomerAddress cxrCustomerAddress =
            remoteCustomerAddressService.queryCustomerAddressByPhone(remoteOrder.getCustomerPhone());
        returnOrderVO.setCxrCustomerAddress(cxrCustomerAddress);

        CxrResidentialQuarters cxrResidentialQuarters = null;
        String quartersName = "";
        // 用小区id去查, 小区名字的字符串
        if (cxrCustomerAddress != null
            && ObjectUtil.isNotEmpty(cxrCustomerAddress.getCxrResidentialQuartersId())) {
            cxrResidentialQuarters =
                remoteResidentialQuarService.queryResidentialQuartersById(
                    cxrCustomerAddress.getCxrResidentialQuartersId());
        }
        // 小区
        if (null != cxrResidentialQuarters) {
            if (null != cxrResidentialQuarters.getName()) {
                quartersName = cxrResidentialQuarters.getName();
            }
            returnOrderVO.setQuartersName(quartersName);
            returnOrderVO.setCxrResidentialQuartersId(cxrCustomerAddress.getCxrResidentialQuartersId());
        }

        return returnOrderVO;
    }

    //    public void countRebates(CxrUserReturnOrder cxrUserReturnOrder){
    //
    //        //如果有多个销售需要把她们拆分 扣减业绩按照退奶
    //        List<BusinessAgent> businessAgents=cxrUserReturnOrder.getBusinessAgent();
    //        List<CxrEmployeeAchievementDetail> employeeAchievementDetails = new ArrayList<>();
    //        BigDecimal amount = cxrUserReturnOrder.getAmount();
    //        int n=0;
    //
    //        // 鲜奶退订总数量 = 客户退款盒数 + 鲜奶赠送数（由销售代理手动输入）
    //        // 盒数扣减业绩？
    //        if((n=businessAgents.size())>1){
    //            BigDecimal avg = NumberUtil.div(amount, n, 2);
    //
    //            for(int i=0;i<n;i++){
    //                amount = amount.subtract(avg);
    //                BusinessAgent tempBussinessAgent=businessAgents.get(i);
    //                //如果是最后一次
    //                if(i == businessAgents.size()-1){
    //                    avg=amount;
    //                }
    //
    //                CxrEmployeeAchievementDetail cxrEmployeeAchievementDetail = new
    // CxrEmployeeAchievementDetail();
    //                cxrEmployeeAchievementDetail.setEmployeeId(tempBussinessAgent.getProxyId());
    //                cxrEmployeeAchievementDetail.setAchievementValue(avg.negate());
    //                cxrEmployeeAchievementDetail.setSouceId(cxrUserReturnOrder.getId());
    //                cxrEmployeeAchievementDetail .setSource(SourceEnums.return_order.getValue());
    //
    // cxrEmployeeAchievementDetail.setTerminalType(TerminalTypeEnums.manager.getValue());
    //
    //                employeeAchievementDetails.add(cxrEmployeeAchievementDetail);
    //            }
    //        }
    //        applicationEventPublisher.publishEvent(new
    // EmployeeAchievemenEvent(this,employeeAchievementDetails));
    //
    //    }

    //   ps:这个订单可能也是审核成功以后再记录的 , 但我发觉是一起保存的 ,订单设置为未审核状态
    public Long saveAsUserOrder(CxrUserReturnOrder cxrUserReturnOrder) {

        // bindingOrderMapper.
        CxrUserOrder userOrder = new CxrUserOrder();
        // 设置订单类型
        userOrder.setOrderType((short) OrderTypeEnums.RETURN_ORDER.getValue());
        userOrder.setOrderNo(OrderUtil.getOrderNo(cxrUserReturnOrder.getCustomerPhone()));
        //        userOrder.setOrderNo(cxrUserReturnOrder.getOrderNo());
        if (null == cxrUserReturnOrder.getSiteId()) {
            throw new ServiceException("请输入站点信息");
        }
        userOrder.setSiteId(cxrUserReturnOrder.getSiteId()); // 根据 todo siteId 查出 siteName
        userOrder.setBusinessAgent(cxrUserReturnOrder.getBusinessAgent());
        // 客户信息
        userOrder.setCustomerName(cxrUserReturnOrder.getCustomerName());
        userOrder.setCustomerPhone(cxrUserReturnOrder.getCustomerPhone());
        userOrder.setCustomerAdress(cxrUserReturnOrder.getCustomerAdress());
        userOrder.setCustomerId(cxrUserReturnOrder.getCustomerId());
        // 插入 binding的总退奶数
        userOrder.setFreshMilkReturnQuantity(cxrUserReturnOrder.getTotalQuantity());
        //        userOrder.setLongMilkGiveQuantity(cxrUserReturnOrder.getLongMilkGiveQuantity());
        userOrder.setLongMilkGiveQuantity(cxrUserReturnOrder.getLongMilkCancelQuantity());
        // 退订金额
        userOrder.setAmount(cxrUserReturnOrder.getAmount());
        // 之前约定这个值是用来表示退奶的
        userOrder.setFreshMilkReturnQuantity(cxrUserReturnOrder.getFreshMilkCancelQuantity());
        userOrder.setAuditStatus(AuditStatusEnums.ToAudit.code()); // '1 待审核 、 2 已审核 、 3.已拒绝
        userOrder.setTerminalType((short) TerminalTypeEnums.disribution.getValue()); // 来自于配送端的数据
        // 查询站点
        CxrSite cxrSite = remoteSiteServicel.queryId(cxrUserReturnOrder.getSiteId());
        if (ObjectUtil.isEmpty(cxrSite)) {
            throw new ServiceException("站点不存在!");
        }
        // 站点名称
        userOrder.setSiteName(cxrSite.getName());
        // 省,市,区
        userOrder.setProvince(cxrSite.getProvice());
        userOrder.setCity(cxrSite.getCity());
        userOrder.setArea(cxrSite.getArea());

        Long currentDeptId = cxrSite.getCurrentDeptId();
        userOrder.setCompanyId(currentDeptId);
        SysDept sysDept = remoteDeptService.queryById(currentDeptId);
        userOrder.setCompanyName(sysDept == null ? null : sysDept.getDeptName());
        LoginEmployee loginUser = StaffLoginHelper.getLoginUser();
        userOrder.setCreateByCode(loginUser.getJobNumber());
        // 所属大区
        userOrder.setBigAreaId(cxrSite.getCxrRootRegionId());
        userOrder.setBigAreaName(cxrSite.getCxrRootRegionName());
        // 时间
        userOrder.setOrderDate(cxrUserReturnOrder.getOrderDate());
        // 备注

        userOrder.setRemark(cxrUserReturnOrder.getRemark());
        userOrder.setDeliverySites(CollUtil.isNotEmpty(cxrUserReturnOrder.getDeliverySites())
            ? JSONUtil.toJsonStr(cxrUserReturnOrder.getDeliverySites()) : null);
        // 代理等级
        userOrder.getBusinessAgent().stream()
            .forEach(
                s -> {
                    CxrEmployee cxrEmployee = remoteEmployeeService.remoteDetail(s.getProxyId());
                    if (null != cxrEmployee
                        && StringUtils.isNotBlank(cxrEmployee.getEmployeeLevelType())) {
                        s.setLevel(Integer.valueOf(cxrEmployee.getEmployeeLevelType()));
                    }
                    if (cxrEmployee != null) {
                        s.setOccupationStatus(
                            OccupationStatus.INDUCTION.getValue().equals(cxrEmployee.getOccupationStatus()));
                    }
                });

        if (save(userOrder)) {
            return userOrder.getId();
        }

        return 0L;
    }

    @Override
    public CxrUserReturnOrder getByOrderId(Long id) {
        CxrUserReturnOrder cxrUserReturnOrder =
            cxrUserReturnOrderMapper.selectOne(
                new LambdaQueryWrapper<CxrUserReturnOrder>()
                    .eq(CxrUserReturnOrder::getUserOrderId, id));
        return cxrUserReturnOrder;
    }

    @Override
    public Map<Long, CxrUserReturnOrder> getRefundSuccessByOrderIds(Collection<Long> ids) {
        List<CxrUserReturnOrder> cxrUserReturnOrders =
            cxrUserReturnOrderMapper.selectList(
                new LambdaQueryWrapper<CxrUserReturnOrder>()
                    .in(CxrUserReturnOrder::getUserOrderId, ids));
        if (CollectionUtils.isNotEmpty(cxrUserReturnOrders)) {
            return cxrUserReturnOrders.stream().collect(Collectors.toMap(CxrUserReturnOrder::getUserOrderId, a -> a));
        }
        return new HashMap<>();
    }

    @Override
    public boolean edit(CxrUserReturnOrderBO bo) {
        commonOrderService.selectEmployeeLimit(bo.getBusinessAgent(), bo.getUserOrderId());
        LoginEmployee loginUser = StaffLoginHelper.getLoginUser();
        bo.setUpdateBy(loginUser.getUserId());
        bo.setUpdateByName(loginUser.getUserName());
        bo.setUpdateTime(new Date());
        bo.getBusinessAgent().stream()
            .forEach(
                s -> {
                    if (ObjectUtil.isEmpty(s.getSiteName())) {
                        CxrEmployee cxrEmployee = remoteEmployeeService.remoteDetail(s.getProxyId());
                        s.setSiteName(cxrEmployee.getSiteName());
                    }
                });
        OrderTypeEnums type = OrderTypeEnums.getType(bo.getOrderType());
        return abstractCxrUserOrderBehavior.disributionUpdate(bo, type);
    }

    @Override
    public Integer updateReturnPhone(String phone, Long customerId, String folmerPhone) {
        int num =
            cxrUserReturnOrderMapper.update(
                null,
                new LambdaUpdateWrapper<CxrUserReturnOrder>()
                    .eq(CxrUserReturnOrder::getCustomerId, customerId)
                    .eq(CxrUserReturnOrder::getCustomerPhone, folmerPhone)
                    .set(CxrUserReturnOrder::getCustomerPhone, phone));
        return num;
    }

    @Override
    public List<CxrUserReturnOrder> selectByCustomerId(Long customerId) {
        List<CxrUserReturnOrder> selectByCustomerId =
            cxrUserReturnOrderMapper.selectList(
                new LambdaQueryWrapper<CxrUserReturnOrder>()
                    .eq(CxrUserReturnOrder::getCustomerId, customerId)
                    .eq(CxrUserReturnOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));
        return selectByCustomerId;
    }

    @Override
    public void updateByReturnPhone(String newPhone, String folmerPhone, Long userId, String customerName) {
        cxrUserReturnOrderMapper.update(null, new LambdaUpdateWrapper<CxrUserReturnOrder>()
            .eq(CxrUserReturnOrder::getCustomerId, userId)
            .eq(CxrUserReturnOrder::getCustomerPhone, folmerPhone)
            .eq(CxrUserReturnOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            .set(CxrUserReturnOrder::getCustomerPhone, newPhone)
            .set(CxrUserReturnOrder::getUpdateBy, userId)
            .set(CxrUserReturnOrder::getUpdateByName, customerName)
            .set(CxrUserReturnOrder::getUpdateTime, new Date())
        );
    }

    @Override
    public void updateRefundSuccessFlag(Long id) {
        cxrUserReturnOrderMapper.update(null,
            new LambdaUpdateWrapper<CxrUserReturnOrder>().set(CxrUserReturnOrder::getRefundSuccessFlag, true)
                .eq(CxrUserReturnOrder::getId, id));
        cxrUserOrderMapper.update(null,
            new LambdaUpdateWrapper<CxrUserOrder>().set(CxrUserOrder::getRefundSuccessFlag, true)
                .eq(CxrUserOrder::getId, id));
    }

    @Override
    public CxrUserReturnOrder getByReturnAfterSale(Long afterSaleId) {
        return cxrUserReturnOrderMapper.selectOne(new LambdaQueryWrapper<CxrUserReturnOrder>()
            .eq(CxrUserReturnOrder::getAfterSalesId, afterSaleId)
        );
    }

    @Override
    public List<CxrUserReturnOrder> queryOrderIds(List<Long> tikTokOrderIds) {
        if (CollectionUtils.isEmpty(tikTokOrderIds)) {
            return null;
        }
        return cxrUserReturnOrderMapper.selectList(Wrappers.<CxrUserReturnOrder>lambdaQuery().select(CxrUserReturnOrder::getId, CxrUserReturnOrder::getOrderId)
            .in(CxrUserReturnOrder::getOrderId, tikTokOrderIds));
    }

    @Override
    public List<CxrUserReturnOrder> queryOrderIdList(Long id) {
        return cxrUserReturnOrderMapper.selectList(new LambdaQueryWrapper<CxrUserReturnOrder>()
            .eq(CxrUserReturnOrder::getOrderId, id)
            .eq(CxrUserReturnOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
        );
    }

    @Override
    public CxrUserReturnOrder queryUserOrderId(Long id) {
        return cxrUserReturnOrderMapper.selectOne(new LambdaQueryWrapper<CxrUserReturnOrder>()
            .eq(CxrUserReturnOrder::getUserOrderId, id)
            .eq(CxrUserReturnOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
        );
    }

    @Override
    public boolean resubmitOrder(CxrUserReturnOrderBO bo) {
        Long userOrderId = bo.getUserOrderId();
        commonOrderService.selectEmployeeLimit(bo.getBusinessAgent(), bo.getId());
        LoginEmployee loginUser = StaffLoginHelper.getLoginUser();
        bo.setUpdateBy(loginUser.getUserId());
        bo.setUpdateByName(loginUser.getUserName());
        bo.setUpdateTime(new Date());
        bo.getBusinessAgent().stream()
            .forEach(
                s -> {
                    if (ObjectUtil.isEmpty(s.getSiteName())) {
                        CxrEmployee cxrEmployee = remoteEmployeeService.remoteDetail(s.getProxyId());
                        s.setSiteName(cxrEmployee.getSiteName());
                    }
                });
        OrderTypeEnums type = OrderTypeEnums.getType(bo.getOrderType());
        boolean update = abstractCxrUserOrderBehavior.disributionUpdate(bo, type);
        if (update) {
            return cxrUserOrderMapper.update(null, new LambdaUpdateWrapper<CxrUserOrder>()
                .eq(CxrUserOrder::getId, bo.getId())
                .set(CxrUserOrder::getAuditStatus, OrderAuditStatusEnums.WAIT_AUDIT.getValue())
                .set(CxrUserOrder::getRefundNoAuditReasons, null)
                .set(CxrUserOrder::getOrderDate, LocalDateTime.now())
                .set(CxrUserOrder::getAuditTime, null)
                .set(CxrUserOrder::getAuditBy, null)
                .set(CollUtil.isNotEmpty(bo.getDeliverySites()), CxrUserOrder::getDeliverySites,
                    JSONUtil.toJsonStr(bo.getDeliverySites()))
                .set(CxrUserOrder::getAuditByName, null)
                .set(CxrUserOrder::getUpdateTime, new Date())
                .set(CxrUserOrder::getUpdateBy, loginUser.getUserId())
                .set(CxrUserOrder::getUpdateByName, loginUser.getUserName())
            ) > 0;
        }
        return false;
    }
}
