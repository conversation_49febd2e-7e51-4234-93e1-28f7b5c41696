package com.ruoyi.order.manager.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.business.base.api.domain.CxrEmployee;
import com.ruoyi.business.base.api.domain.NewOrderDetermineTemplateDto;
import com.ruoyi.business.base.api.domain.dto.TikTokOrderGoodsDTO;
import com.ruoyi.business.base.api.dubbo.RemoteTikTokService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.DeleteStatus;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.idempotent.annotation.RepeatSubmit;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.order.common.domain.bo.CxrUserOrderQueryBo;
import com.ruoyi.order.extend.mapper.CxrEmployeeMapper;
import com.ruoyi.order.manager.service.CxrUserOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Description:
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/8/8 19:35
 */
@Api(
    value = "后台管理端-客户订单列表",
    tags = {"后台管理端-客户订单列表"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/cxrUserOrder")
public class CxrUserOrderController extends BaseController {

    private final CxrUserOrderService cxrUserOrderService;

    @Autowired
    private CxrEmployeeMapper cxrEmployeeMapper;

    @DubboReference
    private RemoteTikTokService remoteTikTokService;


    /**
     * 分页查询《用户订单》
     *
     * @param cxrUserOrderBo 实例化Bo对象封装的查询参数
     * @param
     * @return
     */
    @ApiOperation("分页查询《用户订单》")
    @SaCheckPermission("order:cxrUserOrder:page")
    @PostMapping("/page")
    public R<Object> page(@RequestBody CxrUserOrderQueryBo cxrUserOrderBo) {
        return R.ok(cxrUserOrderService.pageQuery(cxrUserOrderBo));
    }

    /**
     * 订单 反审核
     */
    @Log
    @ApiOperation("订单 反审核")
    @SaCheckPermission("order:cxrUserOrder:audit")
    @PostMapping("/reverseAudit")
    public R<Void> reverseAudit(@ApiParam("主键") @NotNull(message = "主键不能为空") @RequestParam Long id) {
        return toAjax(cxrUserOrderService.reverseAudit(id) ? 1 : 0);
    }

    /**
     * 客户订单列表审核接口
     *
     * @param id
     * @return
     */
    @ApiOperation("客户订单列表审核接口")
    @SaCheckPermission("order:cxrUserOrder:audit")
    @PostMapping("/audit")
    public R<Void> audit(@ApiParam("主键") @NotNull(message = "主键不能为空") @RequestParam Long id,
                         @RequestParam(value = "auditType", required = false) Integer auditType,
                         @RequestParam(value = "refundNoAuditReasons", required = false) String refundNoAuditReasons
    ) {
        return toAjax(cxrUserOrderService.audit(id, false, auditType, refundNoAuditReasons) ? 1 : 0);
    }

    /**
     * 删除数据
     *
     * @param id 主键
     * @return
     */
    @ApiOperation("删除订单")
    @SaCheckPermission("order:cxrUserOrder:remove")
    @DeleteMapping("/remove/{id}")
    @RepeatSubmit(interval = 2, timeUnit = TimeUnit.SECONDS, message = "禁止重复提交!")
    public R<Void> remove(@PathVariable("id") Long id) {
        return toAjax(cxrUserOrderService.deleteOrder(id) ? 1 : 0);
    }


    /**
     * 分页导出《订单》
     *
     * @param cxrUserOrderBo 实例化Bo对象封装的查询参数
     * @param response       响应
     */
    @ApiOperation("分页导出《订单》")
    @SaCheckPermission("order:cxrUserOrder:export")
    @PostMapping("/export")
    public R<Void> export(@RequestBody CxrUserOrderQueryBo cxrUserOrderBo, HttpServletResponse response) {
        cxrUserOrderService.export(cxrUserOrderBo, response);
        return R.ok();
    }

    @ApiOperation("商品详情查询")
    @SaCheckPermission("order:cxrUserOrder:goodsDetails")
    @GetMapping("/goodsDetails")
    public R<TikTokOrderGoodsDTO> goodsDetails(@RequestParam Long cxrOrderId) {
        TikTokOrderGoodsDTO dto = remoteTikTokService.getOrderGoods(cxrOrderId);
        CxrEmployee cxrEmployee = cxrEmployeeMapper.selectOne(new LambdaQueryWrapper<CxrEmployee>()
            .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            .eq(CxrEmployee::getJobNumber, dto.getStaffId())
            .last("limit 1")
        );
        if (ObjectUtil.isNotEmpty(cxrEmployee)) {
            dto.setIdentifier(Boolean.TRUE);
            dto.setStaffName(cxrEmployee.getName());
        } else {
            dto.setIdentifier(Boolean.FALSE);
        }
        return R.ok(dto);
    }

    @ApiOperation("重算业绩中的订单鲜奶盒数")
    @SaCheckPermission("order:cxrUserOrder:orderFmQty")
    @GetMapping("/achievement/orderFmQty/reCompute")
    public R<Object> reComputeAchievementOrderFmQty() {
        cxrUserOrderService.reComputeAchievementOrderFmQty();
        return R.ok("success");
    }

    @ApiOperation("查看匹配记录")
//    @SaCheckPermission("order:cxrUserOrder:queryOrderDetermineTemplate")
    @GetMapping("/queryOrderDetermineTemplate")
    public R<NewOrderDetermineTemplateDto> queryOrderDetermineTemplate(@RequestParam Long cxrOrderId) {
        NewOrderDetermineTemplateDto dto = cxrUserOrderService.queryOrderDetermineTemplate(cxrOrderId);
        return R.ok(dto);
    }


    @PostMapping("/recalculate")
    public R recalculate(@RequestBody List<String> list) {
        cxrUserOrderService.recalculate(list.get(0));
        return R.ok();
    }

    @PostMapping("/cleaningRegionHistory")
    public R cleaningRegionHistory(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate, @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        cxrUserOrderService.cleaningRegionHistory(startDate, endDate);
        return R.ok();
    }

}
