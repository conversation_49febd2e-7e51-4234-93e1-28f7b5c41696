package com.ruoyi.order.manager.strategy.behavior;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.business.base.api.domain.CxrSite;
import com.ruoyi.business.base.api.domain.dto.GiveGiftDTO;
import com.ruoyi.business.base.api.domain.json.CustomerInfo;
import com.ruoyi.business.base.api.domain.vo.CustomerSentNumberVo;
import com.ruoyi.business.base.api.dubbo.RemoteSiteService;
import com.ruoyi.business.base.api.enums.OrderTypeEnums;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.enums.AuditStatusEnums;
import com.ruoyi.common.rocketmq.calculate.CustomerAddressConstant;
import com.ruoyi.common.rocketmq.utils.MqUtil;
import com.ruoyi.common.security.handler.ConstantExceptionCode;
import com.ruoyi.order.api.enums.OrderAuditStatusEnums;
import com.ruoyi.order.api.enums.PayStatusEnums;
import com.ruoyi.order.common.domain.bo.ContractOrderCustomerInfoBO;
import com.ruoyi.order.common.domain.bo.ContractOrderHandlerBO;
import com.ruoyi.order.common.domain.bo.CxrUserNewOrderInfoBO;
import com.ruoyi.order.common.domain.dto.CompleteInfoDTO;
import com.ruoyi.order.common.entity.CxrUserOrder;
import com.ruoyi.order.common.entity.CxrUserReturnOrder;
import com.ruoyi.order.common.event.OrderCustomerIdWriteBackEvent;
import com.ruoyi.order.disribution.service.ActivityOrderService;
import com.ruoyi.order.manager.service.CxrUserOrderService;
import com.ruoyi.order.manager.service.CxrUserReturnOrderService;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Description:
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/8/6 11:11
 */
@Slf4j
public abstract class AbstractCxrUserOrderBehavior implements CxrUserOrderBehavior {

    @Autowired
    private MqUtil mqUtil;

    @DubboReference
    private RemoteSiteService remoteSiteService;


    /**
     * 订单保存
     *
     * @param cxrUserOrder
     * @return
     */
    @Override
    public boolean orderSaveOrUpdate(CxrUserOrder cxrUserOrder) {

        CxrUserOrderService cxrUserOrderService = SpringUtils.getBean(CxrUserOrderService.class);

        boolean flag = false;
        if (ObjectUtil.isNotNull(cxrUserOrder.getId())) {
            flag = cxrUserOrderService.updateById(cxrUserOrder);
        } else {
            flag = cxrUserOrderService.save(cxrUserOrder);
        }
        if (NumberUtil.equals(
            OrderTypeEnums.NEW_ORDER.getValue(), cxrUserOrder.getOrderType().shortValue())
            || NumberUtil.equals(
            OrderTypeEnums.CONTINUE_ORDER.getValue(), cxrUserOrder.getOrderType().shortValue())
            || NumberUtil.equals(
            OrderTypeEnums.INCREASE_ORDER.getValue(), cxrUserOrder.getOrderType().shortValue())) {
            SpringUtils.publishEvent(new OrderCustomerIdWriteBackEvent(cxrUserOrder.getId(), this));
        }
        return flag;
    }

    /**
     * 订单更新 审核 审核方法前面有拦截 已审核数据的操作
     *
     * @param cxrUserOrder
     * @return
     */
    @Override
    public boolean orderUpdate(Object obj, CxrUserOrder cxrUserOrder) {

        CxrUserOrderService cxrUserOrderService = SpringUtils.getBean(CxrUserOrderService.class);
        CxrUserOrder dataCxrUserOrder = cxrUserOrderService.getById(cxrUserOrder.getId());
        if (ObjectUtil.isNull(dataCxrUserOrder)) {
            throw new ServiceException("订单不存在");
        }

        // 如果已经审核,判断该订单是否超过下个月10号    已支付的也可以进
        if (OrderAuditStatusEnums.AUDITED.getValue() == dataCxrUserOrder.getAuditStatus().intValue()
            || NumberUtil.equals(
            PayStatusEnums.PAY_SUCCEEDED.getValue(), dataCxrUserOrder.getPayStatus())) {
            Date dataCreateTime = dataCxrUserOrder.getCreateTime();
            Date nextMonth10day = DateUtils.nextMonth10day(dataCreateTime);
            if (System.currentTimeMillis() > nextMonth10day.getTime()) {
                throw new ServiceException("该单号已经超过修改时间范围,不允许修改");
            }
            // 已经审核的修改 业绩也要修改
            OrderTypeEnums type = OrderTypeEnums.getType(dataCxrUserOrder.getOrderType());
            /** 判断类型 废弃之前的业绩 生成新的业绩没有支付和审核之分 */
            auditAfterChange(obj, type);
        }

        boolean b = cxrUserOrderService.updateById(cxrUserOrder);
        updateReturnOrderDate(cxrUserOrder, dataCxrUserOrder, b);
        return b;
    }

    private void updateReturnOrderDate(
        CxrUserOrder cxrUserOrder, CxrUserOrder dataCxrUserOrder, boolean b) {
        if (b
            && NumberUtil.equals(
            dataCxrUserOrder.getOrderType(), OrderTypeEnums.RETURN_ORDER.getValue())) {
            // 获取service
            CxrUserReturnOrderService returnOrderService =
                SpringUtils.getBean(CxrUserReturnOrderService.class);
            Date orderDate = cxrUserOrder.getOrderDate();
            // 更新
            returnOrderService.update(
                new LambdaUpdateWrapper<CxrUserReturnOrder>()
                    .eq(CxrUserReturnOrder::getUserOrderId, cxrUserOrder.getId())
                    .set(CxrUserReturnOrder::getOrderDate, orderDate));
        }
    }

    /**
     * 订单更新 更新 审核方法前面有拦截 已审核数据的操作 已审核,已支付的都是更新入口
     *
     * @param cxrUserOrder
     * @return
     */
    @Override
    public boolean orderUpdates(Object obj, CxrUserOrder cxrUserOrder) {

        CxrUserOrderService cxrUserOrderService = SpringUtils.getBean(CxrUserOrderService.class);
        CxrUserOrder dataCxrUserOrder = cxrUserOrderService.getById(cxrUserOrder.getId());
        if (ObjectUtil.isNull(dataCxrUserOrder)) {
            throw new ServiceException("订单不存在");
        }

        if (obj instanceof CxrUserNewOrderInfoBO) {
            CxrUserNewOrderInfoBO cxrUserNewOrderInfoBo = (CxrUserNewOrderInfoBO) obj;
            OrderTypeEnums orderTypeEnums = OrderTypeEnums
                .getType(cxrUserNewOrderInfoBo.getOrderType());
            if (orderTypeEnums == OrderTypeEnums.NEW_ORDER
                || orderTypeEnums == OrderTypeEnums.INCREASE_ORDER
                || orderTypeEnums == OrderTypeEnums.CONTINUE_ORDER) {
                cxrUserOrder
                    .setFreshMilkGiveQuantity(cxrUserNewOrderInfoBo.getFreshMilkGiveQuantity());
                cxrUserOrder
                    .setFreshMilkSentQuantity(cxrUserNewOrderInfoBo.getFreshMilkSentQuantity());
                cxrUserOrder
                    .setLongMilkGiveQuantity(cxrUserNewOrderInfoBo.getLongMilkGiveQuantity());
                cxrUserOrder.setExcessQuantity(cxrUserNewOrderInfoBo.getExcessQuantity());
                cxrUserOrder.setRemark(cxrUserNewOrderInfoBo.getRemark());
                cxrUserOrder.setApprenticeOrderFlag(cxrUserNewOrderInfoBo.getApprenticeOrderFlag());
                cxrUserOrder
                    .setPromotionalOrderFlag(cxrUserNewOrderInfoBo.getPromotionalOrderFlag());
                log.debug("update gift = {} ", JSONUtil.toJsonStr(
                    cxrUserNewOrderInfoBo.getCxrActivitySetMealVo()));
                if (cxrUserNewOrderInfoBo.getCxrActivitySetMealVo() != null) {
                    //只保存选中的
                    List<GiveGiftDTO> listCheck = new ArrayList<>();
                    for (GiveGiftDTO giveGiftDTO : cxrUserNewOrderInfoBo.getCxrActivitySetMealVo()
                        .getGiveGiftList()) {
                        if (giveGiftDTO.getCheck()) {
                            listCheck.add(giveGiftDTO);
                        }
                    }
                    cxrUserOrder.setGiveGiftList(
                        JSONUtil.toJsonStr(listCheck));
                    ActivityOrderService activityOrderService = SpringUtils
                        .getBean(ActivityOrderService.class);
                    activityOrderService.updateOrderActivityDetail(cxrUserNewOrderInfoBo);
                }

                List<CustomerInfo> boCustomerInfoList = cxrUserNewOrderInfoBo.getCustomerInfoList();
                CustomerInfo customerInfoBo = boCustomerInfoList.get(0);

                List<CustomerInfo> customerInfoList = dataCxrUserOrder.getCustomerInfoList();
                CustomerInfo customerInfo = customerInfoList.get(0);
                customerInfo.setPhone(customerInfoBo.getPhone());
                customerInfo.setName(customerInfoBo.getName());
                customerInfo.setProvice(customerInfoBo.getProvice());
                customerInfo.setProvince(customerInfoBo.getProvince());
                customerInfo.setArea(customerInfoBo.getArea());
                customerInfo.setCity(customerInfoBo.getCity());
                customerInfo.setSiteId(customerInfoBo.getSiteId());
                customerInfo.setSiteName(customerInfoBo.getSiteName());
                customerInfo.setDistributionId(customerInfoBo.getDistributionId());
                customerInfo.setDistributionName(customerInfoBo.getDistributionName());
                customerInfo.setAddress(customerInfoBo.getAddress());
                customerInfo.setAdress(customerInfoBo.getAdress());
                customerInfo.setAddressId(customerInfoBo.getAddressId());
                customerInfo.setSysAreaId(customerInfoBo.getSysAreaId());

                cxrUserOrder.setCustomerInfoList(customerInfoList);
                cxrUserOrder.setCustomerName(customerInfo.getName());
                cxrUserOrder.setCustomerPhone(customerInfo.getPhone());
                cxrUserOrder.setCustomerAdress(customerInfo.getAdress());


                Long siteId = cxrUserNewOrderInfoBo.getSiteId();
                if (ObjectUtil.isEmpty(siteId)) {
                    throw new ServiceException("请选择开单站点",
                        ConstantExceptionCode.BUSSINESS_EXCEPTION_CODE);
                }
                CxrSite cxrSite = remoteSiteService.queryId(siteId);
                // 站点数据设置
                cxrUserOrder.setBigAreaId(cxrSite.getCxrRootRegionId());
                cxrUserOrder.setBigAreaName(cxrSite.getCxrRootRegionName());
                cxrUserOrder.setProvince(cxrSite.getProvice());
                cxrUserOrder.setCity(cxrSite.getCity());
                cxrUserOrder.setArea(cxrSite.getArea());
                cxrUserOrder.setSiteName(cxrSite.getName());
                cxrUserOrder.setSiteAdress(cxrSite.getDetailAddress());
                cxrUserOrder.setSiteId(cxrSite.getId());

                //                if (StringUtils.isNotBlank(customerInfo.getPhone())
                //                    && StringUtils.isNotBlank(customerInfo.getName())
                //                    && StringUtils.isNotBlank(customerInfo.getAdress())
                //                    && customerInfo.getDistributionId() != null
                //                    && customerInfo.getSysAreaId() != null
                //                    && customerInfo.getSiteId() != null
                //                    && (
                //                        (dataCxrUserOrder.getPayStatus() != null &&
                // dataCxrUserOrder.getPayStatus().intValue() == PayStatusEnums.PAY_SUCCEEDED.getValue())
                //                       || (dataCxrUserOrder.getAuditStatus() != null &&
                // dataCxrUserOrder.getAuditStatus().intValue() == OrderAuditStatusEnums.AUDITED.getValue())
                //                      )
                //                    && PerfectStatusEnums.NO_PERFECT.getValue() ==
                // dataCxrUserOrder.getPerfectStatus().intValue()
                //                ){
                //                    cxrUserOrder.setPerfectStatus(PerfectStatusEnums.PERFECT.getValue());
                //                }
            }
        }

        boolean b = cxrUserOrderService.updateById(cxrUserOrder);

        // 如果已经审核,判断该订单是否超过下个月10号    已支付的也可以进
        if (OrderAuditStatusEnums.AUDITED.getValue() == dataCxrUserOrder.getAuditStatus().intValue()
            || NumberUtil.equals(
            PayStatusEnums.PAY_SUCCEEDED.getValue(), dataCxrUserOrder.getPayStatus())) {
            Date dataCreateTime = dataCxrUserOrder.getCreateTime();
            Date nextMonth5day = DateUtils.nextMonth5day(dataCreateTime);
            if (System.currentTimeMillis() <= nextMonth5day.getTime()) {
                // 已经审核的修改 业绩也要修改
                OrderTypeEnums type = OrderTypeEnums.getType(dataCxrUserOrder.getOrderType());
                /** 判断类型 废弃之前的业绩 生成新的业绩没有支付和审核之分 */
                auditAfterChange(obj, type);
            }

            if (obj instanceof CxrUserNewOrderInfoBO) {
                CxrUserNewOrderInfoBO cxrUserNewOrderInfoBo = (CxrUserNewOrderInfoBO) obj;
                OrderTypeEnums orderTypeEnums =
                    OrderTypeEnums.getType(cxrUserNewOrderInfoBo.getOrderType());
                if (orderTypeEnums == OrderTypeEnums.NEW_ORDER
                    || orderTypeEnums == OrderTypeEnums.INCREASE_ORDER
                    || orderTypeEnums == OrderTypeEnums.CONTINUE_ORDER) {
//                    if (cxrUserNewOrderInfoBo.getTerminalType() == 4) {
//                        cxrUserNewOrderInfoBo.setTerminalType(3);
//                    }
                    CompleteInfoDTO completeInfoDTO =
                        BeanUtil.toBean(cxrUserNewOrderInfoBo, CompleteInfoDTO.class);
                    completeInfoDTO
                        .setFreshMilkGiveCount(cxrUserNewOrderInfoBo.getFreshMilkGiveQuantity());
                    completeInfoDTO
                        .setMilkGiveCount(cxrUserNewOrderInfoBo.getLongMilkGiveQuantity());
                    completeInfoDTO
                        .setAlreadyGiveCount(cxrUserNewOrderInfoBo.getFreshMilkSentQuantity());
                    cxrUserOrder.setCustomerId(cxrUserNewOrderInfoBo.getCustomerId());
                    cxrUserOrderService.updateCustomerStockDiff(dataCxrUserOrder, completeInfoDTO);
                }
            }
        }

        if (obj instanceof CxrUserNewOrderInfoBO) {
            CxrUserNewOrderInfoBO contractOrderHandlerBO = (CxrUserNewOrderInfoBO) obj;
            if (ObjectUtil.isNotEmpty(contractOrderHandlerBO.getFreshMilkSentQuantity())
                && contractOrderHandlerBO.getOrderType() != OrderTypeEnums.RETURN_ORDER
                .getValue()) {
                if (NumberUtil
                    .equals(dataCxrUserOrder.getAuditStatus(), AuditStatusEnums.Audit.code()) ||
                    NumberUtil.equals(dataCxrUserOrder.getPayStatus(),
                        PayStatusEnums.PAY_SUCCEEDED.getValue())) {

                    int sentNumber =
                        contractOrderHandlerBO.getFreshMilkSentQuantity();
                    CustomerSentNumberVo customerSentNumberVo = new CustomerSentNumberVo();
                    customerSentNumberVo.setLaoMilkDistributionTotal(
                        Long.valueOf(dataCxrUserOrder.getFreshMilkSentQuantity()));
                    customerSentNumberVo.setMilkDistributionTotal(Long.valueOf(sentNumber));
                    customerSentNumberVo.setCxrCustomerId(dataCxrUserOrder.getCustomerId());
                    customerSentNumberVo.setDistributionDate(dataCxrUserOrder.getOrderDate());
                    customerSentNumberVo.setReceiverPhone(dataCxrUserOrder.getCustomerPhone());
                    customerSentNumberVo.setCustomerAddress(dataCxrUserOrder.getCustomerAdress());
                    customerSentNumberVo.setIsEdit(true);
                    mqUtil.sendSyncMessage(CustomerAddressConstant.CUSTOMER_ADDRESS_TOPIC,
                        CustomerAddressConstant.CUSTOMER_ORDER_SENT_SUM_CHANGE_TAG,
                        JSONUtil.toJsonStr(customerSentNumberVo));
                }
            }
        }
        if (obj instanceof ContractOrderHandlerBO) {
            ContractOrderHandlerBO contractOrderHandlerBO = (ContractOrderHandlerBO) obj;
            if (ObjectUtil.isNotEmpty(contractOrderHandlerBO.getFreshMilkSentQuantity())
            ) {
                if (NumberUtil
                    .equals(dataCxrUserOrder.getAuditStatus(), AuditStatusEnums.Audit.code()) ||
                    NumberUtil.equals(dataCxrUserOrder.getPayStatus(),
                        PayStatusEnums.PAY_SUCCEEDED.getValue())) {
                    List<ContractOrderCustomerInfoBO> customerInfoBOList = contractOrderHandlerBO
                        .getContractOrderCustomerInfoBOList();
                    Integer sentQuantity = customerInfoBOList.get(0).getFreshMilkSentQuantity();
                    CustomerSentNumberVo customerSentNumberVo = new CustomerSentNumberVo();
                    customerSentNumberVo.setLaoMilkDistributionTotal(
                        Long.valueOf(contractOrderHandlerBO.getFreshMilkSentQuantity()));
                    customerSentNumberVo.setMilkDistributionTotal(Long.valueOf(sentQuantity));
                    customerSentNumberVo.setCxrCustomerId(dataCxrUserOrder.getCustomerId());
                    customerSentNumberVo.setDistributionDate(dataCxrUserOrder.getOrderDate());
                    customerSentNumberVo.setReceiverPhone(dataCxrUserOrder.getCustomerPhone());
                    customerSentNumberVo.setCustomerAddress(dataCxrUserOrder.getCustomerAdress());
                    customerSentNumberVo.setIsEdit(true);
                    mqUtil.sendSyncMessage(CustomerAddressConstant.CUSTOMER_ADDRESS_TOPIC,
                        CustomerAddressConstant.CUSTOMER_ORDER_SENT_SUM_CHANGE_TAG,
                        JSONUtil.toJsonStr(customerSentNumberVo));
                }
            }
        }

        return b;
    }

    public abstract void auditAfterChange(Object obj, OrderTypeEnums orderTypeEnums);

    public abstract boolean disributionUpdate(Object obj, OrderTypeEnums type);
}
