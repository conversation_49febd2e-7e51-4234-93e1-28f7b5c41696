package com.ruoyi.business.base.cxrEmployeeChangeRecord.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.business.base.api.domain.CxrEmployeeChangeRecord;
import com.ruoyi.business.base.api.domain.vo.EmployeePostVo;
import com.ruoyi.business.base.cxrEmployeeChangeRecord.mapper.CxrEmployeeChangeRecordMapper;
import com.ruoyi.business.base.cxrEmployeeChangeRecord.service.ICxrEmployeeChangeRecordService;
import com.ruoyi.common.core.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 员工变动记录表, 涉及后台站点变动、小组变动、调岗站点变动(CxrEmployeeChangeRecord)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-11-13 16:47:50
 */
@Service("cxrEmployeeChangeRecordService")
@RequiredArgsConstructor
@Slf4j
public class CxrEmployeeChangeRecordService extends
    ServiceImpl<CxrEmployeeChangeRecordMapper, CxrEmployeeChangeRecord> implements ICxrEmployeeChangeRecordService {
    @Override
    public List<CxrEmployeeChangeRecord> queryTimeRange(Long employeeId, LocalDate start, LocalDate end) {
        return this.baseMapper.queryTimeRange(employeeId, start, end);
    }

    /**
     * @param employeeId
     * @return
     */
    @Override
    public Boolean queryLatestDataByEmployeeId(Long employeeId, LocalDate executeLocalDate) {
        return baseMapper.queryLatestDataByEmployeeId(employeeId, executeLocalDate);
    }

    @Override
    public List<EmployeePostVo> queryRirectorList(Long siteId, LocalDate start, LocalDate end) {
        return baseMapper.queryRirectorList(siteId, start, end);
    }

    @Override
    public CxrEmployeeChangeRecord selectGetGroupName(Long emIds, String mouth, Long cxrSiteId) {
        return baseMapper.selectGetGroupName(emIds, mouth, cxrSiteId);
    }

    @Override
    public CxrEmployeeChangeRecord selectGetGroupNameByDays(Long cxrEmployeeId, Date startTime,
                                                            Date endTime, Long cxrSiteId) {
        LocalDate starDate = DateUtils.getLocalDateFromDate(startTime);
        LocalDate endDate = DateUtils.getLocalDateFromDate(endTime);
        return baseMapper.selectGetGroupNameByDays(cxrEmployeeId, starDate, endDate, cxrSiteId);
    }

    @Override
    public CxrEmployeeChangeRecord selectGetGroupNameByEmpty(Long cxrEmployeeId, Long cxrSiteId) {
        return baseMapper.selectGetGroupNameByEmpty(cxrEmployeeId, cxrSiteId);
    }

    @Override
    public List<CxrEmployeeChangeRecord> employeeChangeRecordLists(String mouth, List<Long> collects, Long cxrSiteId) {
        return baseMapper.employeeChangeRecordLists(mouth, collects, cxrSiteId);
    }
}
