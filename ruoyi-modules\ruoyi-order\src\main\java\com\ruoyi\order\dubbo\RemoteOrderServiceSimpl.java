package com.ruoyi.order.dubbo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.ruoyi.business.api.dubbo.RemoteMilkTransferMessageService;
import com.ruoyi.business.base.api.domain.*;
import com.ruoyi.business.base.api.domain.bo.CxrChannelCommissionConfigBo;
import com.ruoyi.business.base.api.domain.dto.DailyIntergerDto;
import com.ruoyi.business.base.api.domain.dto.YDSCustomerDTO;
import com.ruoyi.business.base.api.domain.json.BusinessAgent;
import com.ruoyi.business.base.api.domain.json.ContractOrderCustomerInfo;
import com.ruoyi.business.base.api.domain.json.CustomerInfo;
import com.ruoyi.business.base.api.domain.vo.BeseEmployeeVo;
import com.ruoyi.business.base.api.domain.vo.CommonExchangeOrderVo;
import com.ruoyi.business.base.api.domain.vo.CustomersVo;
import com.ruoyi.business.base.api.dubbo.HuiBodubbo.RemoteCxrHuiboShopService;
import com.ruoyi.business.base.api.dubbo.*;
import com.ruoyi.business.base.api.enums.OrderTypeEnums;
import com.ruoyi.business.base.api.enums.longMilk.LongMilkApplyStatus;
import com.ruoyi.business.base.api.enums.longMilk.LongMilkStatus;
import com.ruoyi.business.base.api.utils.huibo.HuiBoSignUtil;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.*;
import com.ruoyi.common.core.validator.AbstractAssert;
import com.ruoyi.common.enums.AuditStatusEnums;
import com.ruoyi.common.enums.TerminalTypeEnums;
import com.ruoyi.common.order.constant.PayConstant;
import com.ruoyi.common.order.wap.entity.FuyouNotifyResponse;
import com.ruoyi.common.order.wap.entity.PayNotifyUrlResponse;
import com.ruoyi.common.rocketmq.constant.customer.CxrCustomerConstant;
import com.ruoyi.common.rocketmq.constant.order.OrderConstant;
import com.ruoyi.common.rocketmq.utils.MqUtil;
import com.ruoyi.common.sms.entity.SumOrderParams;
import com.ruoyi.core.base.domain.CxrOrderAfterSale;
import com.ruoyi.core.base.domain.CxrPaymentRefundRecord;
import com.ruoyi.core.base.domain.CxrServiceWorkOrder;
import com.ruoyi.core.base.enums.RefundStatusEnums;
import com.ruoyi.core.base.mapper.CxrOrderAfterSaleMapper;
import com.ruoyi.core.base.mapper.CxrXcxRetailOrderMapper;
import com.ruoyi.message.api.RemoteMessageService;
import com.ruoyi.order.api.RemoteCustomerOrderStatisticsService;
import com.ruoyi.order.api.RemoteOrderService;
import com.ruoyi.order.api.domain.bo.CustomerOrderStatisticsBo;
import com.ruoyi.order.api.domain.bo.OrderTradesBo;
import com.ruoyi.order.api.domain.bo.OrdersSynchronouslyBo;
import com.ruoyi.order.api.domain.dto.TikTokReturnOrderDTO;
import com.ruoyi.order.api.domain.dto.UserOrderDTO;
import com.ruoyi.order.api.domain.vo.*;
import com.ruoyi.order.api.enums.OrderAuditStatusEnums;
import com.ruoyi.order.api.enums.PayStatusEnums;
import com.ruoyi.order.api.enums.PerfectStatusEnums;
import com.ruoyi.order.common.config.HuiBoOrderConfig;
import com.ruoyi.order.common.domain.HuiBoResponse;
import com.ruoyi.order.common.entity.CxrUserOrder;
import com.ruoyi.order.common.entity.CxrUserOrderMirrorImage;
import com.ruoyi.order.common.entity.CxrUserReturnOrder;
import com.ruoyi.order.common.event.ContractOrderSplitEvent;
import com.ruoyi.order.common.mapper.CxrCustomerMapper;
import com.ruoyi.order.common.mapper.CxrUserOrderMapper;
import com.ruoyi.order.common.mapper.CxrUserOrderMirrorImageMapper;
import com.ruoyi.order.common.mapper.CxrUserReturnOrderMapper;
import com.ruoyi.order.common.utils.OrderUtil;
import com.ruoyi.order.cxrTransferMilk.mapper.CxrServiceWorkOrderMapper;
import com.ruoyi.order.cxrTransferMilk.service.CxrTransferMilkService;
import com.ruoyi.order.disribution.common.FuyouPayUtils;
import com.ruoyi.order.disribution.enums.CxrPayConfigType;
import com.ruoyi.order.disribution.enums.PaymentPlatform;
import com.ruoyi.order.disribution.mapper.CxrCustomerPayRecordMapper;
import com.ruoyi.order.disribution.service.ActivityOrderService;
import com.ruoyi.order.disribution.service.PayService;
import com.ruoyi.order.disribution.service.ReturnOrderService;
import com.ruoyi.order.employee.service.ICxrEmployeeOrderService;
import com.ruoyi.order.manager.domain.entity.MallOrderEntity;
import com.ruoyi.order.manager.mapper.MallOrderMapper;
import com.ruoyi.order.manager.service.CxrPaymentRefundRecordService;
import com.ruoyi.order.manager.service.CxrUserOrderCommonService;
import com.ruoyi.order.manager.service.CxrUserOrderService;
import com.ruoyi.order.manager.service.CxrUserReturnOrderService;
import com.ruoyi.order.manager.strategy.ReturnOrderRefundFactory;
import com.ruoyi.order.manager.strategy.behavior.CxrUserOrderBehavior;
import com.ruoyi.system.api.RemoteDeptService;
import com.ruoyi.system.api.domain.SysDept;
import io.seata.core.context.RootContext;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/8/23 11:14
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteOrderServiceSimpl implements RemoteOrderService {

    public final ApplicationEventPublisher applicationEventPublisher;
    private final PayService payService;
    private final CxrUserOrderService cxrUserOrderService;

    private final CxrXcxRetailOrderMapper cxrXcxRetailOrderMapper;

    private final ICxrEmployeeOrderService employeeOrderService;
    private final FuyouPayUtils fyPayUtils;
    private final CxrUserOrderMapper cxrUserOrderMapper;

    private final CxrCustomerPayRecordMapper cxrCustomerPayRecordMapper;

    private final ReturnOrderService returnOrderService;

    private final CxrOrderAfterSaleMapper cxrOrderAfterSaleMapper;
    private final CxrUserOrderCommonService cxrUserOrderCommonService;
    private final CxrUserReturnOrderMapper cxrUserReturnOrderMapper;

    private final CxrServiceWorkOrderMapper cxrServiceWorkOrderMapper;

    private final HuiBoOrderConfig huiBoOrderConfig;

    private final HuiBoOrderConfig huiBoConfig;

    private final CxrCustomerMapper cxrCustomerMapper;

    private final CxrUserReturnOrderService cxrUserReturnOrderService;

    private final CxrTransferMilkService transferMilkService;

    private final CxrUserOrderBehavior cxrUserOrderBehavior;

    private final CxrUserOrderMirrorImageMapper orderMirrorImageMapper;

    private final CxrPaymentRefundRecordService paymentRefundRecordService;

    @Autowired
    private ReturnOrderRefundFactory returnOrderRefundFactory;

    @Value("${mall.database.name:`cxr_mall`.}")
    private String mallDatabase;

    @Resource(name = "scheduledExecutorService")
    private ScheduledExecutorService scheduledExecutorService;

    @DubboReference
    private RemoteSiteService remoteSiteService;
    @DubboReference
    private RemoteCustomerService remoteCustomerService;
    @DubboReference
    private RemoteEmployeeService remoteEmployeeService;

    @DubboReference
    private RemoteEmployeePostService remoteEmployeePostService;

    @DubboReference
    private RemoteMessageService remoteMessageService;
    @DubboReference
    private RemoteExchangeService remoteExchangeService;
    @DubboReference
    private RemoteLongMilkStockService remoteLongMilkStockService;
    @DubboReference
    private RemoteDeptService remoteDeptService;
    @DubboReference
    private RemoteCustomerOrderStatisticsService remoteCustomerOrderStatisticsService;
    @DubboReference
    private RemoteHuiBoService remoteHuiBoService;
    @Autowired
    @Lazy
    private MqUtil mqUtil;

    @Autowired
    private MallOrderMapper mallOrderMapper;

    @DubboReference(timeout = 200000)
    private final RemoteCustomerAddressService remoteCustomerAddressService;

    @DubboReference(timeout = 200000)
    private final RemoteCxrHuiboShopService cxrHuiboShopService;


    @Autowired
    @Qualifier("futureThreadPool")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @DubboReference
    private RemoteMilkTransferMessageService remoteMilkTransferMessageService;
    @Lazy
    @Autowired
    private ActivityOrderService activityOrderService;


    /**
     * 返回查询支付状态
     *
     * @param orderNo
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public int verifyPayStatus(String orderNo) {
        return payService.verifyPayStatus(orderNo).getValue();
    }

    /**
     * 返回查询支付状态
     *
     * @param payNotifyUrlResponse
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void orderMQAfterHandler(PayNotifyUrlResponse payNotifyUrlResponse) {
        log.info(
            "------------------------------------- 收钱吧 mq orderMQAfterHandler-------------------------------------");
        log.info("  orderMQAfterHandler= {}", payNotifyUrlResponse);
        if (StrUtil.isBlank(payNotifyUrlResponse.getClient_sn())) {
            log.error(" 订单号为空 {}", payNotifyUrlResponse);
            return;
        }
        String userOrderNo = OrderUtil.getOrderNoV2(payNotifyUrlResponse.getClient_sn());
        log.info("orderMQAfterHandler >>>>>>>>>>>>>>>>>>>>>>> userOrderNo ={}", userOrderNo);
        CxrUserOrder cxrUserOrder =
            cxrUserOrderService.getOne(
                new LambdaQueryWrapper<CxrUserOrder>().eq(CxrUserOrder::getOrderNo, userOrderNo));
        if (ObjectUtil.isNull(cxrUserOrder)) {
            log.info(" 没有查询到该笔订单 = {}", payNotifyUrlResponse);
            return;
        }
        Integer tempPayStatus = Convert.toInt(cxrUserOrder.getPayStatus(), -1);
        log.info(" -------------------------------------  tempPayStatus= {}", tempPayStatus);
        // 检查数据库订单状态 是否支付成功 : 是 不需要重复更新支付状态 ， 否需要更新
        if (!NumberUtil.equals(PayStatusEnums.PAY_SUCCEEDED.getValue(), tempPayStatus)) {
            if (StrUtil.equals(payNotifyUrlResponse.getOrder_status(), PayConstant.PAID)) {

                cxrUserOrderService.paySucessMessage(PaymentPlatform.SHOU_QIAN_BA,
                    payNotifyUrlResponse.getSn(),
                    payNotifyUrlResponse.getPayway(),
                    payNotifyUrlResponse.getFinish_time(),
                    cxrUserOrder.getId(),
                    payNotifyUrlResponse.getClient_sn(),
                    payNotifyUrlResponse
                );

            } else {

                if (StrUtil.equals(payNotifyUrlResponse.getOrder_status(), PayConstant.CREATED)) {
                    log.info(" 该笔订单已经时创建  ");

                } else {
                    // 判断订单状态
                    log.info("收钱吧给到的状态不是支付成功");
                }
            }

        } else {
            log.info(" 该笔订单已经支付 ", cxrUserOrder);
        }
    }


    @Override
    public void fuyouPayResultHandler(FuyouNotifyResponse fuYouNotifyResponse) {
        Integer payStatus = PayStatusEnums.WAIT_PAY.getValue();
        if (fuYouNotifyResponse.getSrcOrderType() == 0) {
            payStatus = cxrUserOrderService.getById(fuYouNotifyResponse.getOrderId()).getPayStatus();
        } else {
            payStatus = employeeOrderService.getById(fuYouNotifyResponse.getOrderId()).getPayStatus();

        }
        if (NumberUtil.equals(PayStatusEnums.PAY_SUCCEEDED.getValue(), payStatus)) {
            return;
        }
        // 发送半事务消息
        cxrUserOrderService.paySucessMessage(PaymentPlatform.FU_YOU, null, null, null, fuYouNotifyResponse.getOrderId(),
            fuYouNotifyResponse.getMchntOrderNo(),
            fuYouNotifyResponse);
    }

    /**
     * 合订单 处理支付完成后 客户信息的创建 、销售代理业绩计算
     *
     * @param cxrUserOrderId
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void contractOrdrSplithandler(Long cxrUserOrderId) {
        applicationEventPublisher.publishEvent(new ContractOrderSplitEvent(cxrUserOrderId, this));
    }

    /**
     * 查询客户订单
     *
     * @param customerPhone
     */
    @Override
    public UserOrderDTO queryCustomerlastOrder(String customerPhone) {

        CxrUserOrder cxrUserOrder = this.cxrUserOrderService.queryLastOrderInfo(customerPhone);

        // CxrUserOrder cxrUserOrder = cxrUserOrderService.getBaseMapper().selectOne(new
        // LambdaQueryWrapper<CxrUserOrder>()
        //    .eq(CxrUserOrder::getCustomerPhone, customerPhone)
        //    .in(CxrUserOrder::getOrderType, OrderTypeEnums.NEW_ORDER.getValue(),
        //        OrderTypeEnums.CONTINUE_ORDER.getValue(),
        //        OrderTypeEnums.INCREASE_ORDER.getValue(), OrderTypeEnums.CONTRACT_ORDER.getValue())
        //    .and((wrapper) -> {
        //        wrapper.eq(CxrUserOrder::getPayStatus, PayStatusEnums.PAY_SUCCEEDED.getValue())
        //            .or()
        //            .eq(CxrUserOrder::getAuditStatus, OrderAuditStatusEnums.AUDITED.getValue());
        //    })
        //    .orderByDesc(CxrUserOrder::getOrderDate).last(" limit 1")
        // );
        UserOrderDTO userOrderDTO = BeanUtil.copyProperties(cxrUserOrder, UserOrderDTO.class);
        return userOrderDTO;
    }

    @Override
    public UserOrderDTO queryCustomerFirstOrderV2(String customerPhone, Long customerId) {

        CxrUserOrder cxrUserOrder = this.cxrUserOrderService.queryFirstOrderInfoV2(customerPhone, customerId);

        UserOrderDTO userOrderDTO = BeanUtil.copyProperties(cxrUserOrder, UserOrderDTO.class);
        return userOrderDTO;
    }

    @Override
    public UserOrderDTO queryCustomerFirstOrder(String customerPhone) {
        CxrUserOrder cxrUserOrder = this.cxrUserOrderService.queryFirstOrderInfo(customerPhone, null);

        UserOrderDTO userOrderDTO = BeanUtil.copyProperties(cxrUserOrder, UserOrderDTO.class);
        return userOrderDTO;
    }

    @Override
    public CxrUserOrder queryOrderById(Long id) {
        return cxrUserOrderService.getById(id);
    }

    @Override
    public String queryOrderNoById(Long id) {
        CxrUserOrder order = cxrUserOrderService.getById(id);
        return order == null ? "" : order.getOrderNo();
    }

    /**
     * 订单支持成功之后 客户处理
     *
     * @param cxrUserOrderId
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void orderMQAfterCustomerHandler(Long cxrUserOrderId) {
        cxrUserOrderService.orderAfterCustomerHandler(cxrUserOrderId);
        mqUtil.sendSyncMessage(CxrCustomerConstant.CUSTOMER_TOPIC, CxrCustomerConstant.YDS_CUSTOMER_PAY_TAG,
            cxrUserOrderId.toString());
    }

    /**
     * 订单支持成功之后 员工处理
     *
     * @param cxrUserOrderId
     */
    @Override
    public void orderMQAfterEmployeeHandler(Long cxrUserOrderId) {
        cxrUserOrderService.orderAfterEmployeeHandler(cxrUserOrderId);
    }

    /**
     * 发送微信消息 和短信消息
     *
     * @param userOrderId
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void sendOrderMessage(Long userOrderId) {
        CxrUserOrder cxrUserOrder = cxrUserOrderService.getById(userOrderId);

        // 新 增 续 盒
        if (NumberUtil.equals(
            OrderTypeEnums.NEW_ORDER.getValue(), cxrUserOrder.getOrderType().shortValue())
            || NumberUtil.equals(
            OrderTypeEnums.CONTINUE_ORDER.getValue(), cxrUserOrder.getOrderType().shortValue())
            || NumberUtil.equals(
            OrderTypeEnums.INCREASE_ORDER.getValue(), cxrUserOrder.getOrderType().shortValue())
            || NumberUtil.equals(
            cxrUserOrder.getOrderType(), OrderTypeEnums.CONTRACT_ORDER.getValue())) {
            String phone = cxrUserOrder.getCustomerPhone();
            CxrCustomer cxrCustomer = remoteCustomerService.queryByPhone(phone);
            Integer freshMilkQuantity =
                Convert.toInt(cxrUserOrder.getOrderQuantity(), 0)
                    + Convert.toInt(cxrUserOrder.getFreshMilkGiveQuantity(), 0)
                    - Convert.toInt(cxrUserOrder.getFreshMilkSentQuantity(), 0);
            Integer longMilkQuantity =
                Convert.toInt(cxrUserOrder.getLongMilkGiveQuantity(), 0)
                    - Convert.toInt(cxrUserOrder.getLongMilkSentQuantity(), 0);
            Integer freshMilkGiveQuantity = Convert.toInt(cxrUserOrder.getFreshMilkGiveQuantity());

            if (ObjectUtil.isNotEmpty(cxrCustomer) && StrUtil.isNotBlank(cxrCustomer.getWxOpenId())) {

                String userName = Convert.toStr(cxrCustomer.getName(), StrUtil.format("{}{}", phone, "用户"));

                remoteMessageService.sumWxOrderSend(
                    cxrCustomer.getWxOpenId(),
                    userName,
                    DateUtil.toLocalDateTime(cxrUserOrder.getOrderDate()),
                    Convert.toInt(cxrUserOrder.getOrderType()),
                    cxrUserOrder.getOrderNo(),
                    cxrUserOrder.getOrderQuantity(),
                    freshMilkGiveQuantity,
                    longMilkQuantity,
                    cxrUserOrder.getFreshMilkSentQuantity(),
                    cxrUserOrder.getAmount());
            }

            SumOrderParams sumOrderParams =
                SumOrderParams.builder()
                    .sum(cxrUserOrder.getOrderQuantity())
                    .freshMilkSum(freshMilkGiveQuantity)
                    .longMilkSum(longMilkQuantity)
                    .haveBeenSum(cxrUserOrder.getFreshMilkSentQuantity())
                    .price(cxrUserOrder.getAmount())
                    .orderType(cxrUserOrder.getOrderType())
                    .coType(cxrUserOrder.getCoType())
                    .phone(phone)
                    .id(cxrUserOrder.getId())
                    .build();

            mqUtil.sendSyncMessage(OrderConstant.ORDER_TOPIC, OrderConstant.SEND_ORDER_SMS_MESSAGE_TAG, sumOrderParams);
        }
    }


    public void sendOrderSmsMessage(SumOrderParams sumOrderParams) {
        OrderTypeEnums orderTypeEnums = OrderTypeEnums.getType(sumOrderParams.getOrderType());
        OrderSmsTemplateEnums templateEnums = OrderSmsTemplateEnums.REPEAT_ORDER;
        if (orderTypeEnums == OrderTypeEnums.NEW_ORDER) {
            templateEnums = OrderSmsTemplateEnums.FIRST_ORDER;
        } else if (orderTypeEnums == OrderTypeEnums.CONTRACT_ORDER) {
            OrderTypeEnums coType = OrderTypeEnums.getType(sumOrderParams.getCoType());
            if (coType == OrderTypeEnums.NEW_ORDER) {
                templateEnums = OrderSmsTemplateEnums.FIRST_ORDER;
            }
        }
        remoteMessageService.sumOrderSend(sumOrderParams.getPhone(), sumOrderParams, templateEnums);
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Long instrtAnyExchangeOrder(CommonExchangeOrderVo commonExchangeOrderVo) {
        // 从客户端提交
        if (ObjectUtil.isEmpty(commonExchangeOrderVo.getCustomerExchangeId())) { // 订单id
            throw new ServiceException("参数异常！");
        }
        CxrUserOrder cxrUserOrder = new CxrUserOrder();
        BeanUtils.copyProperties(commonExchangeOrderVo, cxrUserOrder);

        // 根据客户电话查询销售代理
        BeseEmployeeVo employee =
            remoteEmployeeService.queryEmployeeByCustomerPhone(commonExchangeOrderVo.getCustomerId());

        // 销售代理name
        // 代理电话
        CxrSite cxrSite = remoteSiteService.queryId(employee.getCxrSiteId());
        List<BusinessAgent> businessAgents = new ArrayList<>();
        BusinessAgent businessAgent = new BusinessAgent();
        businessAgent.setLevel(Integer.valueOf(employee.getEmployeeLevelType()));
        businessAgent.setProxyId(employee.getId());
        businessAgent.setProxyNo(employee.getJobNumber());
        businessAgent.setSiteName(employee.getSiteName());
        businessAgent.setProxyName(employee.getName());
        businessAgent.setRegionId(cxrSite.getCxrRootRegionId());
        businessAgent.setRegionName(cxrSite.getCxrRootRegionName());
        businessAgents.add(businessAgent);
        cxrUserOrder.setBusinessAgent(businessAgents);

        // 大区  big_area_name   big_area_id
        cxrUserOrder.setBigAreaId(employee.getCxrRegionId());
        cxrUserOrder.setBigAreaName(employee.getRegionName());
        // sys_dept_id

        SysDept sysDept = remoteDeptService.queryById(cxrSite.getCurrentDeptId());
        cxrUserOrder.setSysDeptId(sysDept.getDeptId());
        // 公司 company_id  company_name
        cxrUserOrder.setCompanyId(sysDept.getDeptId());
        cxrUserOrder.setCompanyName(sysDept.getDeptName());

        // 站点  site name  site_adress   site_id
        cxrUserOrder.setSiteName(employee.getSiteName());
        cxrUserOrder.setSiteAdress(employee.getSiteAdress());
        cxrUserOrder.setSiteId(employee.getCxrSiteId());

        cxrUserOrder.setSpec(JSONUtil.toJsonStr(commonExchangeOrderVo.getSpecData()));
        cxrUserOrder.setOrderType(OrderTypeEnums.EXCHANGE_ORDER.getValue());
        cxrUserOrder.setOrderNo(OrderUtil.getOrderNo(OrderTypeEnums.EXCHANGE_ORDER));
        cxrUserOrder.setOrderDate(new Date());

        cxrUserOrder.setCreateTime(new Date());
        cxrUserOrder.setAuditStatus(AuditStatusEnums.Audit.code());

        cxrUserOrder.setTerminalType(TerminalTypeEnums.customer.getValue());
        cxrUserOrder.setCustomerAdress(commonExchangeOrderVo.getCustomerAddress());
        // 更新id 到 customerExchange
        int insert = cxrUserOrderMapper.insert(cxrUserOrder);

        //        Boolean exchangeUpe =
        // remoteExchangeService.updateAnyCustomerExchange(commonExchangeOrderVo
        //        .getCustomerExchangeId(), cxrUserOrder.getId());

        if (insert == 0) {
            throw new ServiceException("未知错误");
        }
        return cxrUserOrder.getId();
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void delOrderById(Long userOrderId) {
        cxrUserOrderMapper.update(
            null,
            new LambdaUpdateWrapper<CxrUserOrder>()
                .eq(CxrUserOrder::getId, userOrderId)
                .set(CxrUserOrder::getDeleteStatus, DeleteStatus.DELETED.getValue()));
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void orderPayNotify(PayNotifyUrlResponse payNotifyUrlResponse) {
        log.info("预支付订单-----支付成功--回调----开始");
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void saveLongMilkStock(Long id) {
        try {
            log.info("保存--------常温奶---------库存,cxrUserOrderId={}", id);
            CxrUserOrder userOrder = cxrUserOrderService.getById(id);
            // 订单没有赠送常温奶    就不加到常温奶库存中
            if (userOrder.getLongMilkGiveQuantity() == 0) {
                log.info("常温奶库存为0  结束同步常温奶库存方法!");
                return;
            }
            // 查询用户 根据手机
            CxrCustomer cxrCustomer = remoteCustomerService.queryByPhone(userOrder.getCustomerPhone());
            if (ObjectUtil.isEmpty(cxrCustomer)) {
                log.info("查询不到用户,结束同步常温奶库存方法!");
                throw new Exception("查询不到用户,结束同步常温奶库存方法!");
            }

            CxrSite cxrSite = remoteSiteService.getCxrSite(userOrder.getSiteId());

            // 常温奶库存数据组装
            CxrLongMilkStockDTO dto = new CxrLongMilkStockDTO();
            dto.setUpdateBy(userOrder.getUpdateBy());
            dto.setUpdateByName(userOrder.getUpdateByName());
            dto.setUpdateTime(userOrder.getUpdateTime());
            dto.setUpdateByType(userOrder.getUpdateByType());
            dto.setCxrCustomerId(cxrCustomer.getId());
            dto.setBusinessAgent(userOrder.getBusinessAgent());
            dto.setApplyStatus(LongMilkApplyStatus.INITIAL.getValue());
            dto.setCxrSiteId(userOrder.getSiteId());
            dto.setCxrSiteName(cxrSite.getName());
            dto.setOrderTime(userOrder.getOrderDate());
            dto.setOrderType(userOrder.getOrderType()); //
            dto.setSentNum(userOrder.getLongMilkGiveQuantity());
            dto.setSurplusNum(userOrder.getLongMilkGiveQuantity());
            dto.setExpireTime(DateUtils.toDate(DateUtils.getLocalDateFromDate(userOrder.getOrderDate()).plusDays(30)));
            dto.setLongMilkStatus(LongMilkStatus.AVAILABLE.getValue());
            dto.setRemark(userOrder.getRemark());
            dto.setCxrUserOrderId(userOrder.getId());
            dto.setCxrUserOrderQuantity(userOrder.getOrderQuantity()); //
            dto.setProvice(cxrSite.getProvice());
            dto.setCity(cxrSite.getCity());
            dto.setArea(cxrSite.getArea());
            dto.setCxrRootRegionName(cxrSite.getCxrRootRegionName());
            dto.setCxrRootRegionId(cxrSite.getCxrRootRegionId());
            dto.setSysDeptId(cxrSite.getCurrentDeptId());

            boolean b = remoteLongMilkStockService.save(dto);
            log.info("\n常温奶库存保存结果：{};cxrUserOrderId={},customerId={}", b, id, cxrCustomer.getId());
            if (!b) {
                throw new Exception("常温奶库存保存失败");
            }
        } catch (Exception e) {
            throw new ServiceException(StrUtil.format("常温奶库存保存异常cxrUserOrderId={},{}", id, e.getMessage()));
        }
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void saveContractOrderLongMilk(Long id) {
        try {
            log.info("保存--------常温奶---------库存");
            CxrUserOrder userOrder = cxrUserOrderService.getById(id);
            // 订单没有赠送常温奶    就不加到常温奶库存中
            if (userOrder.getLongMilkGiveQuantity() == 0) {
                log.info("常温奶库存为0  结束同步常温奶库存方法!");
                return;
            }
            // 获取合订单客户数据
            List<ContractOrderCustomerInfo> contractOrderExt = userOrder.getContractOrderExt();

            Map<String, ContractOrderCustomerInfo> infoMap =
                contractOrderExt.stream()
                    .collect(
                        Collectors.toMap(
                            ContractOrderCustomerInfo::getPhone, Function.identity(), (v1, v2) -> v2));
            List<String> phones =
                contractOrderExt.stream()
                    .map(ContractOrderCustomerInfo::getPhone)
                    .collect(Collectors.toList());

            // 查询用户 根据手机
            List<CxrCustomer> cxrCustomers = remoteCustomerService.queryByPhones(phones);
            if (CollectionUtil.isEmpty(cxrCustomers)) {
                log.info("查询不到用户,结束同步常温奶库存方法!");
                throw new Exception("查询不到用户,结束同步常温奶库存方法!");
            }
            CxrSite cxrSite = remoteSiteService.getCxrSite(userOrder.getSiteId());

            // 常温奶库存数据组装
            List<CxrLongMilkStockDTO> dtos = new ArrayList<>();
            cxrCustomers.forEach(
                s -> {
                    CxrLongMilkStockDTO dto = new CxrLongMilkStockDTO();
                    dto.setUpdateBy(userOrder.getUpdateBy());
                    dto.setUpdateByName(userOrder.getUpdateByName());
                    dto.setUpdateTime(userOrder.getUpdateTime());
                    dto.setUpdateByType(userOrder.getUpdateByType());
                    dto.setCxrCustomerId(s.getId());
                    dto.setBusinessAgent(userOrder.getBusinessAgent());
                    dto.setApplyStatus(LongMilkApplyStatus.INITIAL.getValue());
                    dto.setCxrSiteId(userOrder.getSiteId());
                    dto.setCxrSiteName(cxrSite.getName());
                    dto.setOrderTime(userOrder.getOrderDate());
                    dto.setOrderType(userOrder.getOrderType());
                    ContractOrderCustomerInfo contractOrderCustomerInfo = infoMap.get(s.getPhone());
                    dto.setSentNum(contractOrderCustomerInfo.getLongMilkGiveQuantity());
                    dto.setSurplusNum(contractOrderCustomerInfo.getLongMilkGiveQuantity());
                    dto.setExpireTime(DateUtils.dateAdd(userOrder.getOrderDate(), 30));
                    dto.setLongMilkStatus(LongMilkStatus.AVAILABLE.getValue());
                    dto.setRemark(userOrder.getRemark());
                    dto.setCxrUserOrderId(userOrder.getId());
                    dto.setCxrUserOrderQuantity(userOrder.getOrderQuantity()); //
                    dto.setProvice(cxrSite.getProvice());
                    dto.setCity(cxrSite.getCity());
                    dto.setArea(cxrSite.getArea());
                    dto.setCxrRootRegionName(cxrSite.getCxrRootRegionName());
                    dto.setCxrRootRegionId(cxrSite.getCxrRootRegionId());
                    dto.setSysDeptId(cxrSite.getCurrentDeptId());

                    dtos.add(dto);
                });

            boolean b = remoteLongMilkStockService.saveBatch(dtos);
            if (!b) {
                throw new Exception("常温奶库存保存失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void returnOrderLongMilkStock(Long id) {

        try {
            CxrUserReturnOrder userOrder = returnOrderService.getByOrderId(id);
            // 退订奶数为0的话   不需要保存
            if (userOrder.getLongMilkCancelQuantity().equals(null)
                || userOrder.getLongMilkCancelQuantity().equals(0)) {
                log.info("退订单 退奶数为0! 常温奶库存不操作!");
                return;
            }
            CustomerInfoDTO customerInfoDTO = remoteCustomerService.queryById(userOrder.getCustomerId());
            if (ObjectUtil.isEmpty(customerInfoDTO)) {
                throw new Exception("查询不到用户,结束同步常温奶库存方法!");
            }
            CxrLongMilkStockDTO dto = new CxrLongMilkStockDTO();
            dto.setUpdateBy(userOrder.getUpdateBy());
            dto.setUpdateByName(userOrder.getUpdateByName());
            dto.setUpdateTime(userOrder.getUpdateTime());
            dto.setUpdateByType(userOrder.getUpdateByType());
            dto.setCxrCustomerId(customerInfoDTO.getCustomerId());
            dto.setCxrUserOrderId(id);
            dto.setSentNum(userOrder.getLongMilkCancelQuantity());
            dto.setOrderId(userOrder.getOrderId());
            log.info("退订数量----------------------------------{}", userOrder.getLongMilkCancelQuantity());
            log.info("库存dto-----------------------------------{}", dto);
            boolean b = remoteLongMilkStockService.updateReturnOrderLongMilkStock(dto);
            if (!b) {
                throw new Exception("退订单同步常温奶库存失败");
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public DailyIntergerDto sumOrderQuantity(LocalDate day, String phone) {
        List<Short> list = new ArrayList<>();

        list.add(OrderTypeEnums.CONTINUE_ORDER.getValue());
        list.add(OrderTypeEnums.NEW_ORDER.getValue());
        list.add(OrderTypeEnums.CONTRACT_ORDER.getValue());
        list.add(OrderTypeEnums.INCREASE_ORDER.getValue());
        // 鲜奶订购数量
        return cxrUserOrderMapper.sumOrderQuantity(
            day,
            list,
            TerminalTypeEnums.manager.getValue(),
            TerminalTypeEnums.disribution.getValue(),
            AuditStatusEnums.Audit.code(),
            PayStatusEnums.PAY_SUCCEEDED.getValue(),
            phone);
    }

    @Override
    public DailyIntergerDto returnMilk(LocalDate date, String phone) {
        // 退订单
        return cxrUserOrderMapper.sumReturnMilk(
            date, OrderTypeEnums.RETURN_ORDER.getValue(), AuditStatusEnums.Audit.code(), phone);
    }

    @Override
    public void saveTransferOrderLongMilk(Long transferOrderId) {
        CxrUserOrder userOrder = cxrUserOrderService.getById(transferOrderId);

        Long customerId =
            remoteCustomerService.queryCustomerIdByPhone(userOrder.getCustomerPhoneSwitch());
        if (ObjectUtil.isEmpty(customerId)) {
            log.error(StrUtil.format("客户手机号为>>>:{}的客户查不到,请检查!", userOrder.getCustomerPhoneSwitch()));
        }
        Date date = cxrUserOrderService.queryOrderDateByOrderNo(userOrder.getOutLongMilkOrderNo());
        if (ObjectUtil.isEmpty(date)) {
            log.error(
                StrUtil.format("订单单据为>>>>>:{}的订单查不到订单时间,请检查", userOrder.getOutLongMilkOrderNo()));
        }
        Date expireDate = DateUtils.dateAdd(date, 30);

        CxrLongMilkStock cxrLongMilkStock = new CxrLongMilkStock();
        cxrLongMilkStock.setCxrCustomerId(customerId);
        cxrLongMilkStock.setExpireTime(expireDate);
        cxrLongMilkStock.setCxrSiteId(userOrder.getSiteId());
        cxrLongMilkStock.setCxrSiteName(userOrder.getSiteName());
        cxrLongMilkStock.setOrderTime(userOrder.getOrderDate());
        cxrLongMilkStock.setOrderType(userOrder.getOrderType());
        cxrLongMilkStock.setSentNum(userOrder.getConversionQuantity());
        cxrLongMilkStock.setSurplusNum(userOrder.getConversionQuantity());
        cxrLongMilkStock.setLongMilkStatus(LongMilkStatus.AVAILABLE.getValue());
        cxrLongMilkStock.setApplyStatus(LongMilkApplyStatus.INITIAL.getValue());
        cxrLongMilkStock.setCxrUserOrderId(userOrder.getId());
        cxrLongMilkStock.setCxrUserOrderQuantity(0);
        cxrLongMilkStock.setProvice(userOrder.getProvince());
        cxrLongMilkStock.setCity(userOrder.getCity());
        cxrLongMilkStock.setArea(userOrder.getArea());
        cxrLongMilkStock.setCxrRootRegionId(userOrder.getBigAreaId());
        cxrLongMilkStock.setCxrRootRegionName(userOrder.getBigAreaName());
        cxrLongMilkStock.setRevision(0L);
        cxrLongMilkStock.setRemark(userOrder.getRemark());
        cxrLongMilkStock.setSysDeptId(userOrder.getCompanyId());

        boolean b = remoteLongMilkStockService.save(cxrLongMilkStock);
        if (!b) {
            log.error(StrUtil.format("保存常温奶失败!订单id为{}", transferOrderId));
        }
    }

    // 鲜奶转出数量
    @Override
    public DailyIntergerDto sumTranOutMilk(LocalDate date, String phone) {

        return cxrUserOrderMapper.sumTranOutMilk(
            date, OrderTypeEnums.CHANGE_ORDER.getValue(), AuditStatusEnums.Audit.code(), phone);
    }

    @Override
    public DailyIntergerDto sumTranInMilk(LocalDate date, String phone) {
        // 鲜奶转入数量
        return cxrUserOrderMapper.sumTranInMilk(
            date, OrderTypeEnums.CHANGE_ORDER.getValue(), AuditStatusEnums.Audit.code(), phone);
    }

    @Override
    public List<UserOrderListVo> queryOrderListByDate(LocalDate startDate, LocalDate endDate) {
        return cxrUserOrderMapper.selectListByTime(startDate, endDate);
    }

    @Override
    public List<UserOrderListVo> queryReturnOrderListByDate(LocalDate startDate, LocalDate endDate) {

        return cxrUserReturnOrderMapper.queryReturnOrderListByDate(startDate, endDate);
        //        List<CxrUserReturnOrder> cxrUserReturnOrders = cxrUserReturnOrderMapper.selectList(new
        // LambdaQueryWrapper<CxrUserReturnOrder>()
        //            .select(CxrUserReturnOrder::getBusinessAgent,
        // CxrUserReturnOrder::getFreshMilkCancelQuantity,
        // CxrUserReturnOrder::getLongMilkCancelQuantity)
        //            .ge(CxrUserReturnOrder::getOrderDate, startDate)
        //            .lt(CxrUserReturnOrder::getOrderDate, endDate)
        //            .eq(CxrUserReturnOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));
        //
        //        return cxrUserReturnOrders.stream().map(a -> {
        //            UserOrderListVo userOrderListVo = new UserOrderListVo();
        //            userOrderListVo.setBusinessAgent(a.getBusinessAgent());     //销售代理
        //            userOrderListVo.setFreshMilkCancelQuantity(a.getFreshMilkCancelQuantity());
        // //鲜奶退订数量
        //            userOrderListVo.setLongMilkCancelQuantity(a.getLongMilkCancelQuantity());
        // //常温奶退订数量
        //            return userOrderListVo;
        //        }).collect(Collectors.toList());
    }

    /**
     * 订单已送数量（开单的时候填写的已送数量）
     *
     * @param siteId
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Integer statisticUserOrder(Long siteId, LocalDateTime startTime, LocalDateTime endTime) {
        return this.cxrUserOrderService.statisticUserOrder(siteId, startTime, endTime);
    }

    @Override
    public List<BigDecimal> sumNewOrder(LocalDate now, short orderType, Long cxrEmployeeId) {

//        List<String> strings1 =
//            cxrUserOrderMapper.countNewOrderManager(
//                now,
//                orderType,
//                TerminalTypeEnums.manager.getValue(),
//                AuditStatusEnums.Audit.code(),
//                cxrEmployeeId);
//
//        BigDecimal toatal = getBigDecimal(cxrEmployeeId, strings1);
//
//        List<String> strings =
//            cxrUserOrderMapper.countNewOrderDisribution(
//                now,
//                orderType,
//                TerminalTypeEnums.disribution.getValue(),
//                PayStatusEnums.PAY_SUCCEEDED.getValue(),
//                cxrEmployeeId);

        List<String> strings = cxrUserOrderMapper.countMergePortNewOrder(
            now,
            orderType,
            cxrEmployeeId
        );
        BigDecimal bigDecimal = getBigDecimal(cxrEmployeeId, strings);
        List<BigDecimal> ts = new ArrayList<>();
        if (ObjectUtil.equals(OrderTypeEnums.NEW_ORDER.getValue(), orderType)) {
            BigDecimal toatal = cxrUserOrderMapper.countMergePortByContractOrderNewOrder(
                now,
                OrderTypeEnums.CONTRACT_ORDER.getValue(),
                cxrEmployeeId
            );
            BigDecimal add = toatal.add(bigDecimal);
            ts.add(add);
        } else {
            ts.add(bigDecimal);
        }
        return ts;
    }

    @NotNull
    private BigDecimal getBigDecimal(Long cxrEmployeeId, List<String> lists) {
        BigDecimal toatal = BigDecimal.ZERO;
        if (CollectionUtil.isNotEmpty(lists) && lists.size() > 0) {

            for (String s : lists) {
                if (StringUtils.isBlank(s)) {
                    continue;
                }
                List<Long> list = JSONUtil.toList(s, Long.class);

                //            长度等于1  直接赋值
                if (list.size() == 1) {
                    toatal = toatal.add(new BigDecimal(1));
                    continue;
                }
                //            平均数
                BigDecimal divide =
                    Convert.toBigDecimal(1, BigDecimal.ZERO)
                        .divide(
                            Convert.toBigDecimal(list.size(), BigDecimal.ZERO), 2, BigDecimal.ROUND_DOWN);

                //            获取下标
                if (list.indexOf(cxrEmployeeId) != list.size() - 1) {
                    //            平均值
                    toatal = toatal.add(divide);
                    continue;
                }

                toatal =
                    toatal.add(
                        new BigDecimal(1).subtract(divide.multiply(Convert.toBigDecimal(list.size() - 1))));
            }
        }

        return toatal;
    }

    @Override
    public List<DailyIntergerDto> sumReturnOrder(LocalDate now) {

        return cxrUserOrderMapper.countReturnOrder(
            now, OrderTypeEnums.RETURN_ORDER.getValue(), AuditStatusEnums.Audit.code());
    }

    //    @Override
    //    public List<DailyIntergerDto> sumRenewalOrder(LocalDate now,short orderType) {
    //        return cxrUserOrderMapper.countNewOrder(now, orderType,
    //            TerminalTypeEnums.disribution.getValue(), TerminalTypeEnums.manager.getValue(),
    //            PayStatusEnums.PAY_SUCCEEDED.getValue(), AuditStatusEnums.Audit.code());
    //    }

    @Override
    public List<BigDecimal> countValidOrder(LocalDate now, Long cxrEmployeeId) {

        List<Short> types = new ArrayList<>();
        types.add(OrderTypeEnums.NEW_ORDER.getValue());
        types.add(OrderTypeEnums.CONTRACT_ORDER.getValue());
        types.add(OrderTypeEnums.CONTINUE_ORDER.getValue());
        types.add(OrderTypeEnums.INCREASE_ORDER.getValue());
        List<String> strings =
            cxrUserOrderMapper.countValidOrder(
                now,
                TerminalTypeEnums.disribution.getValue(),
                TerminalTypeEnums.manager.getValue(),
                PayStatusEnums.PAY_SUCCEEDED.getValue(),
                AuditStatusEnums.Audit.code(),
                cxrEmployeeId,
                types);

        BigDecimal total = getBigDecimal(cxrEmployeeId, strings);

        List<BigDecimal> list = new ArrayList<>();
        list.add(total);
        return list;
    }

    @Override
    public IPage<CustomerOrderStatisticsVo> orderStatistics(
        CustomerOrderStatisticsBo bo, Page<Object> build) {
        return cxrUserOrderMapper.orderStatistics(bo, build);
    }

    @Override
    public CustomerOrderTotal TotalOrderStatistics(CustomerOrderStatisticsBo bo) {
        return cxrUserOrderMapper.TotalOrderStatistics(bo);
    }

    @Override
    public CustomerOrderStatisticsDTO customerOrderStatistics(Long customerId, Integer year) {
        return cxrUserOrderMapper.customerOrderStatistics(customerId, year);
    }

    @Override
    public List<DailyIntergerDto> sumGiveFreshMilkNumber(LocalDate min) {

        List<Short> orderTypes = new ArrayList<>();

        orderTypes.add(OrderTypeEnums.NEW_ORDER.getValue());
        orderTypes.add(OrderTypeEnums.CONTINUE_ORDER.getValue());
        orderTypes.add(OrderTypeEnums.INCREASE_ORDER.getValue());
        orderTypes.add(OrderTypeEnums.CONTRACT_ORDER.getValue());

        return cxrUserOrderMapper.sumGiveFreshMilkNumber(
            min,
            orderTypes,
            TerminalTypeEnums.disribution.getValue(),
            TerminalTypeEnums.manager.getValue(),
            PayStatusEnums.PAY_SUCCEEDED.getValue(),
            AuditStatusEnums.Audit.code());
    }

    @Override
    public DailyIntergerDto sumGiveFreshMilkNumberBySite(LocalDate min, Long siteId) {

        List<Short> orderTypes = new ArrayList<>();

        orderTypes.add(OrderTypeEnums.NEW_ORDER.getValue());
        orderTypes.add(OrderTypeEnums.CONTINUE_ORDER.getValue());
        orderTypes.add(OrderTypeEnums.INCREASE_ORDER.getValue());
        orderTypes.add(OrderTypeEnums.CONTRACT_ORDER.getValue());

        return cxrUserOrderMapper.sumGiveFreshMilkNumberBySite(
            min,
            orderTypes,
            TerminalTypeEnums.disribution.getValue(),
            TerminalTypeEnums.manager.getValue(),
            PayStatusEnums.PAY_SUCCEEDED.getValue(),
            AuditStatusEnums.Audit.code(),
            siteId);
    }

    @Override
    public List<DailyIntergerDto> sumReturnFreshMilkNumber(LocalDate min) {
        List<Long> ReturnOrderIds =
            cxrUserOrderMapper.queryAuditSuccess(
                min, OrderTypeEnums.RETURN_ORDER.getValue(), AuditStatusEnums.Audit.code());

        if (ObjectUtil.isEmpty(ReturnOrderIds)) {
            return null;
        }

        return cxrUserReturnOrderMapper.sumReturnFreshMilkNumber(
            min, ReturnOrderIds, DeleteStatus.NOT_DELETED.getValue());
    }

    @Override
    public DailyIntergerDto sumReturnFreshMilkNumberBySite(LocalDate min, Long siteId) {

        List<Long> ReturnOrderIds =
            cxrUserOrderMapper.queryAuditSuccess(
                min, OrderTypeEnums.RETURN_ORDER.getValue(), AuditStatusEnums.Audit.code());

        if (ObjectUtil.isEmpty(ReturnOrderIds)) {
            return null;
        }

        return cxrUserReturnOrderMapper.sumReturnFreshMilkNumberBySite(
            min, ReturnOrderIds, DeleteStatus.NOT_DELETED.getValue(), siteId);
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean addImportUserOrder(List<CxrOrderListImportVo> list) throws ParseException {

        List<CxrUserOrder> cxrOrderListVos = new ArrayList<>();

        for (CxrOrderListImportVo orderListImportVo : list) {
            CxrUserOrder cxrUserOrder = new CxrUserOrder();

            List<BusinessAgent> businessAgents = new ArrayList<>();

            BusinessAgent businessAgent = new BusinessAgent();
            if (ObjectUtil.isNotEmpty(orderListImportVo.getLevel())) {
                String substring = orderListImportVo.getLevel().substring(1, 2);
                businessAgent.setLevel(Integer.valueOf(substring));
            }

            businessAgent.setSiteName("中山小榄");
            businessAgent.setProxyNo(orderListImportVo.getEmployeeJobNumber());
            businessAgent.setProxyName(orderListImportVo.getEmployeeName());
            businessAgents.add(businessAgent);

            cxrUserOrder.setBusinessAgent(businessAgents);
            cxrUserOrder.setRemark(orderListImportVo.getRemark());
            Date date = new SimpleDateFormat("yyyy-MM-dd").parse(orderListImportVo.getOrderDate());
            cxrUserOrder.setOrderDate(date);
            cxrUserOrder.setOrderNo(orderListImportVo.getOrderNo());
            cxrUserOrder.setOrderType(OrderTypeEnums.getType(orderListImportVo.getOrderType()));
            cxrUserOrder.setBigAreaName(orderListImportVo.getRegionName());
            cxrUserOrder.setProvince(orderListImportVo.getProvice());
            cxrUserOrder.setCity(orderListImportVo.getCity());
            cxrUserOrder.setArea(orderListImportVo.getArea());

            cxrUserOrder.setAuditStatus(AuditStatusEnums.Audit.code());
            cxrUserOrder.setAuditTime(date);
            cxrUserOrder.setTerminalType(TerminalTypeEnums.manager.getValue());
            cxrUserOrder.setMerchantOrderNo(orderListImportVo.getMerchantOrderNo());

            // 站点相关
            cxrUserOrder.setSiteName("中山小榄");
            cxrUserOrder.setSiteId(1600768927515971585l);
            cxrUserOrder.setSiteAdress("小榄镇绩东一德联街28号地铺");

            // 所属公司
            cxrUserOrder.setCompanyId(1539516495603425282l);
            cxrUserOrder.setCompanyName("醇鲜然");

            // 客户信息 电话
            cxrUserOrder.setCustomerPhone(orderListImportVo.getPhone());
            cxrUserOrder.setCustomerName(orderListImportVo.getCustomerName());
            cxrUserOrder.setCustomerAdress(orderListImportVo.getCustomerAdress());
            cxrUserOrder.setOrderQuantity(Integer.valueOf(orderListImportVo.getOrderQuantity()));
            cxrUserOrder.setFreshMilkGiveQuantity(orderListImportVo.getFreshMilkGiveQuantity()); // 鲜奶赠送数量
            cxrUserOrder.setLongMilkGiveQuantity(
                Integer.valueOf(orderListImportVo.getLongMilkGiveQuantity()));
            cxrUserOrder.setExcessQuantity(orderListImportVo.getExcessQuantity());
            cxrUserOrder.setFreshMilkSentQuantity(orderListImportVo.getFreshMilkSentQuantity());
            cxrUserOrder.setLongMilkSentQuantity(orderListImportVo.getLongMilkSentQuantity());

            cxrUserOrder.setSurplusQuantity(0);
            //            cxrUserOrder.setConversionQuantity();
            //            cxrUserOrder.setConversionType();
            cxrUserOrder.setUnitPrice(orderListImportVo.getUnitPrice());
            cxrUserOrder.setAmount(new BigDecimal(orderListImportVo.getAmount()));

            cxrUserOrder.setCreateTime(date);
            //
            cxrUserOrder.setPromotionalOrderFlag(
                ObjectUtil.isNotEmpty(orderListImportVo.getPromotionalOrderFlag()) ? true : null);
            cxrUserOrder.setZeroQuantityRenewal(
                ObjectUtil.isNotEmpty(orderListImportVo.getZeroQuantityRenewal()) ? true : null);

            cxrUserOrder.setActivityGiveQuantity(orderListImportVo.getActivityGiveQuantity());
            cxrUserOrder.setActivitySentQuantity(orderListImportVo.getActivitySentQuantity());
            if (orderListImportVo.getOrderType().equals(OrderTypeEnums.NEW_ORDER.getDesc())) { //
                cxrUserOrder.setPayStatus(PayStatusEnums.PAY_SUCCEEDED.getValue());
                cxrUserOrder.setPayTime(date);
                cxrUserOrder.setPerfectStatus(PerfectStatusEnums.PERFECT.getValue());
            }

            if (orderListImportVo.getOrderType().equals(OrderTypeEnums.EXCHANGE_ORDER.getDesc())) { //
                cxrUserOrder.setExchangeSum(orderListImportVo.getConversionQuantity());
                cxrUserOrder.setExchangeProductName(orderListImportVo.getConversionType());
                cxrUserOrder.setMilkExchangeSum(orderListImportVo.getConversionQuantity() * 10);
                //                cxrUserOrder.setT
            }

            if (orderListImportVo.getOrderType().equals(OrderTypeEnums.RETURN_ORDER.getDesc())) { //

                CxrUserReturnOrder cxrUserReturnOrder =
                    BeanUtil.copyProperties(cxrUserOrder, CxrUserReturnOrder.class);
                //                cxrUserReturnOrderMapper.insert(cxrUserReturnOrder);

            }

            // 订单类型转义

            cxrUserOrder.setIsImport(true);
            cxrOrderListVos.add(cxrUserOrder);
        }

        return cxrUserOrderService.saveBatch(cxrOrderListVos);
    }

    @Override
    public List<UserOrderListVo> getPhone(String phone) {
        List<CxrUserOrder> cxrUserOrder = cxrUserOrderService.getPhone(phone);
        List<UserOrderListVo> userOrderListVo =
            BeanCollectionUtils.copyListProperties(cxrUserOrder, UserOrderListVo::new);
        log.info("打印一下数据{}", userOrderListVo);
        return userOrderListVo;
    }

    @Override
    public Integer customerSwitchPhone(String phone) {
        List<CxrUserOrder> userOrders =
            cxrUserOrderMapper.selectList(
                new LambdaQueryWrapper<CxrUserOrder>()
                    .eq(CxrUserOrder::getCustomerPhoneSwitch, phone)
                    .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));
        if (userOrders.size() > 0) {
            List<CxrUserOrder> cxrUserOrders =
                userOrders.stream()
                    .filter(a -> ObjectUtil.isNotEmpty(a.getConversionQuantity()))
                    .collect(Collectors.toList());
            if (cxrUserOrders.size() > 0) {
                return cxrUserOrders.stream().mapToInt(CxrUserOrder::getConversionQuantity).sum();
            }
        }
        return 0;
    }
    public Integer queryCustomerSwitch(Long customerId) {
        return cxrUserOrderMapper.queryCustomerSwitch(customerId);
    }

    @Override
    public void orderHandler(Long orderId) {
        cxrUserOrderService.orderHandler(orderId);
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    public void executorOrderPlus(List<CxrOrderListImportPlusVo> list, Long sysDept) {

        try {
            List<CxrUserOrder> cxrOrderListVos = new ArrayList<>();
            //        remoteDeptService.

            for (CxrOrderListImportPlusVo orderListImportVo : list) {
                CxrUserOrder cxrUserOrder = new CxrUserOrder();

                List<BusinessAgent> businessAgents = null;
                BusinessAgent businessAgent = null;
                try {
                    businessAgents = new ArrayList<>();

                    businessAgent = new BusinessAgent();
                    if (ObjectUtil.isNotEmpty(orderListImportVo.getLevel())) {
                        String substring = orderListImportVo.getLevel().substring(1, 2);
                        businessAgent.setLevel(Integer.valueOf(substring));
                    }

                    Long employeeId =
                        remoteEmployeeService.selectEmployeeJobNumber(
                            orderListImportVo.getEmployeeJobNumber());

                    businessAgent.setProxyId(employeeId);

                    businessAgent.setSiteName(orderListImportVo.getSiteName());
                    businessAgent.setProxyNo(orderListImportVo.getEmployeeJobNumber());
                    businessAgent.setProxyName(orderListImportVo.getEmployeeName());
                    businessAgents.add(businessAgent);
                } catch (NumberFormatException e) {
                    throw new RuntimeException(e);
                }

                cxrUserOrder.setBusinessAgent(businessAgents);
                cxrUserOrder.setRemark(orderListImportVo.getRemark());
                Date date = null;
                try {
                    date = new SimpleDateFormat("yyyy-MM-dd").parse(orderListImportVo.getOrderDate());
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
                cxrUserOrder.setOrderDate(date);
                cxrUserOrder.setOrderNo(orderListImportVo.getOrderNo());
                if (orderListImportVo.getOrderType().equals("促销单")) {
                    cxrUserOrder.setOrderType(OrderTypeEnums.NEW_ORDER.getValue());
                    cxrUserOrder.setPromotionalOrderFlag(true);
                } else {
                    cxrUserOrder.setOrderType(OrderTypeEnums.getType(orderListImportVo.getOrderType()));
                }
                CxrSite cxrSite = remoteSiteService.querySiteName(orderListImportVo.getSiteName());

                cxrUserOrder.setBigAreaName(orderListImportVo.getRegionName());
                cxrUserOrder.setProvince(orderListImportVo.getProvice());
                cxrUserOrder.setCity(orderListImportVo.getCity());
                cxrUserOrder.setArea(cxrSite.getArea());

                cxrUserOrder.setAuditStatus(AuditStatusEnums.Audit.code());
                cxrUserOrder.setAuditTime(date);
                cxrUserOrder.setTerminalType(TerminalTypeEnums.manager.getValue());
                cxrUserOrder.setMerchantOrderNo(orderListImportVo.getMerchantOrderNo());
                //

                // 站点相关
                cxrUserOrder.setSiteName(cxrSite.getName());
                cxrUserOrder.setSiteId(cxrSite.getId());
                cxrUserOrder.setSiteAdress(cxrSite.getDetailAddress());

                // 所属公司
                cxrUserOrder.setCompanyId(sysDept);
                cxrUserOrder.setCompanyName("醇鲜然");

                // 客户信息 电话
                cxrUserOrder.setCustomerPhone(orderListImportVo.getPhone());
                cxrUserOrder.setCustomerName(orderListImportVo.getCustomerName());
                cxrUserOrder.setCustomerAdress(orderListImportVo.getCustomerAdress());
                cxrUserOrder.setOrderQuantity(Integer.valueOf(orderListImportVo.getOrderQuantity()));
                cxrUserOrder.setFreshMilkGiveQuantity(
                    orderListImportVo.getFreshMilkGiveQuantity()); // 鲜奶赠送数量
                cxrUserOrder.setLongMilkGiveQuantity(
                    Integer.valueOf(orderListImportVo.getLongMilkGiveQuantity()));
                cxrUserOrder.setExcessQuantity(orderListImportVo.getExcessQuantity());
                cxrUserOrder.setFreshMilkSentQuantity(orderListImportVo.getFreshMilkSentQuantity());
                cxrUserOrder.setLongMilkSentQuantity(orderListImportVo.getLongMilkSentQuantity());

                cxrUserOrder.setSurplusQuantity(0);
                //            cxrUserOrder.setConversionQuantity();
                //            cxrUserOrder.setConversionType();
                cxrUserOrder.setUnitPrice(new BigDecimal(orderListImportVo.getUnitPrice()));
                cxrUserOrder.setAmount(new BigDecimal(orderListImportVo.getAmount()));

                cxrUserOrder.setCreateTime(date);

                // 客户地址2

                List<CustomerInfo> customerInfoList = new ArrayList<>();

                CustomerInfo customerInfo = new CustomerInfo();
                customerInfo.setPhone(orderListImportVo.getPhone2());
                customerInfo.setAdress(orderListImportVo.getCustomerAdress2());
                customerInfo.setProvice(orderListImportVo.getProvice());
                customerInfo.setCity(orderListImportVo.getCity());
                customerInfo.setSiteId(cxrSite.getId());
                customerInfo.setSiteName(cxrSite.getName());
                customerInfo.setArea(cxrSite.getArea());
                customerInfo.setDistributionId(businessAgent.getProxyId());
                customerInfo.setDistributionName(businessAgent.getProxyName());
                customerInfoList.add(customerInfo);

                cxrUserOrder.setCustomerInfoList(customerInfoList);

                // 转入信息
                //            cxrUserOrder.setCustomerNameSwitch();
                cxrUserOrder.setCustomerPhoneSwitch(orderListImportVo.getInputCustomerPhone());
                cxrUserOrder.setCustomerAdressSwitch(orderListImportVo.getInputCustomerAddress());
                // 活动
                cxrUserOrder.setActivityType(orderListImportVo.getActivityType());
                // 付款来源
                //            remoteDeptService.
                if ("二维码".equals(orderListImportVo.getPaymentSouce())) {
                    cxrUserOrder.setPaymentSouce("5");
                } else if ("收钱吧".equals(orderListImportVo.getPaymentSouce())) {
                    cxrUserOrder.setPaymentSouce("4");
                } else {
                    cxrUserOrder.setPaymentSouce("7");
                }

                // 刷卡金额

                cxrUserOrder.setCreditCardAmount(new BigDecimal(orderListImportVo.getCreditCardAmount()));

                //
                // cxrUserOrder.setPromotionalOrderFlag(ObjectUtil.isNotEmpty(orderListImportVo.getPromotionalOrderFlag())?true:null);
                cxrUserOrder.setZeroQuantityRenewal(
                    ObjectUtil.isNotEmpty(orderListImportVo.getZeroQuantityRenewal()) ? true : null);

                cxrUserOrder.setActivityGiveQuantity(orderListImportVo.getActivityGiveQuantity());
                cxrUserOrder.setActivitySentQuantity(orderListImportVo.getActivitySentQuantity());
                //            if
                // (orderListImportVo.getOrderType().equals(OrderTypeEnums.NEW_ORDER.getDesc())){     //
                //                cxrUserOrder.setPayStatus(PayStatusEnums.PAY_SUCCEEDED.getValue());
                //                cxrUserOrder.setPayTime(date);
                //                cxrUserOrder.setPerfectStatus(PerfectStatusEnums.PERFECT.getValue());
                //            }

                if (orderListImportVo.getOrderType().equals(OrderTypeEnums.EXCHANGE_ORDER.getDesc())) { //

                    cxrUserOrder.setExchangeSum(orderListImportVo.getConversionQuantity());
                    cxrUserOrder.setExchangeProductName(orderListImportVo.getConversionType());
                    cxrUserOrder.setMilkExchangeSum(orderListImportVo.getConversionQuantity() * 10);
                    //                cxrUserOrder.setT
                }

                if (orderListImportVo.getOrderType().equals(OrderTypeEnums.RETURN_ORDER.getDesc())) { //
                    cxrUserOrder.setOrderQuantity(0);
                    cxrUserOrder.setFreshMilkReturnQuantity(
                        Integer.valueOf(orderListImportVo.getOrderQuantity()));
                }

                // 订单类型转义

                cxrUserOrder.setIsImport(true);
                //            cxrUserOrderService.save(cxrUserOrder);
                cxrOrderListVos.add(cxrUserOrder);
            }

            //        }, scheduledExecutorService);

            cxrUserOrderService.saveBatch(cxrOrderListVos);
        } catch (RuntimeException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
//    @Transactional
    public boolean importUserOrderPlus(List<CxrOrderListImportPlusVo> list, Long sysDept)
        throws ParseException {

        List<CxrUserOrder> cxrOrderListVos = new ArrayList<>();
        //        remoteDeptService.

        for (CxrOrderListImportPlusVo orderListImportVo : list) {
            CxrUserOrder cxrUserOrder = new CxrUserOrder();

            List<BusinessAgent> businessAgents = new ArrayList<>();

            BusinessAgent businessAgent = new BusinessAgent();
            if (ObjectUtil.isNotEmpty(orderListImportVo.getLevel())) {
                String substring = orderListImportVo.getLevel().substring(1, 2);
                businessAgent.setLevel(Integer.valueOf(substring));
            }

            if (ObjectUtil.isEmpty(orderListImportVo.getOrderNo())) {
                orderListImportVo.setOrderNo(OrderUtil.getOrderNo(OrderTypeEnums.RETURN_ORDER));
            }
            if (ObjectUtil.equals(orderListImportVo.getOrderNo(), "0")) {
                orderListImportVo.setOrderNo(OrderUtil.getOrderNo(OrderTypeEnums.NEW_ORDER));
            }
            if (ObjectUtil.equals(orderListImportVo.getOrderNo(), "赠送单")) {
                orderListImportVo.setOrderNo(OrderUtil.getOrderNo(OrderTypeEnums.GIVE_ORDER));
            }

            if (ObjectUtil.equals(orderListImportVo.getOrderNo(), "退订单")) {
                orderListImportVo.setOrderNo(OrderUtil.getOrderNo(OrderTypeEnums.RETURN_ORDER));
            }
            long count = cxrUserOrderService.count(new LambdaQueryWrapper<CxrUserOrder>()
                .eq(CxrUserOrder::getOrderNo, orderListImportVo.getOrderNo()));
            if (count > 0l) {
                continue;
            }

            Long employeeId =
                remoteEmployeeService.selectEmployeeJobNumber(orderListImportVo.getEmployeeJobNumber());

            businessAgent.setProxyId(employeeId);
            businessAgent.setSiteName(orderListImportVo.getSiteName());
            businessAgent.setProxyNo(orderListImportVo.getEmployeeJobNumber());
            businessAgent.setProxyName(orderListImportVo.getEmployeeName());
            businessAgents.add(businessAgent);

            cxrUserOrder.setBusinessAgent(businessAgents);
            cxrUserOrder.setRemark(orderListImportVo.getRemark());
            Date date = null;
            try {
                date = new SimpleDateFormat("yyyy-MM-dd").parse(orderListImportVo.getOrderDate());
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
            cxrUserOrder.setOrderDate(date);
            cxrUserOrder.setOrderNo(orderListImportVo.getOrderNo());
            if (orderListImportVo.getOrderType().equals("促销单")) {
                cxrUserOrder.setOrderType(OrderTypeEnums.NEW_ORDER.getValue());
                cxrUserOrder.setPromotionalOrderFlag(true);
            } else {
                cxrUserOrder.setOrderType(OrderTypeEnums.getType(orderListImportVo.getOrderType()));
            }
            CxrSite cxrSite = remoteSiteService.querySiteName(orderListImportVo.getSiteName());

            cxrUserOrder.setBigAreaName(orderListImportVo.getRegionName());
            cxrUserOrder.setProvince(orderListImportVo.getProvice());
            cxrUserOrder.setCity(orderListImportVo.getCity());
            cxrUserOrder.setArea(cxrSite.getArea());

            cxrUserOrder.setAuditStatus(AuditStatusEnums.Audit.code());
            cxrUserOrder.setAuditTime(date);
            cxrUserOrder.setTerminalType(TerminalTypeEnums.manager.getValue());
            cxrUserOrder.setMerchantOrderNo(orderListImportVo.getMerchantOrderNo());
            //

            // 站点相关
            cxrUserOrder.setSiteName(cxrSite.getName());
            cxrUserOrder.setSiteId(cxrSite.getId());
            cxrUserOrder.setSiteAdress(cxrSite.getDetailAddress());

            // 所属公司
            cxrUserOrder.setCompanyId(sysDept);
            cxrUserOrder.setCompanyName("醇鲜然");

            // 客户信息 电话
            cxrUserOrder.setCustomerPhone(orderListImportVo.getPhone());
            cxrUserOrder.setCustomerName(orderListImportVo.getCustomerName());
            cxrUserOrder.setCustomerAdress(orderListImportVo.getCustomerAdress());
            cxrUserOrder.setOrderQuantity(Integer.valueOf(orderListImportVo.getOrderQuantity()));
            cxrUserOrder.setFreshMilkGiveQuantity(orderListImportVo.getFreshMilkGiveQuantity()); // 鲜奶赠送数量
            cxrUserOrder.setLongMilkGiveQuantity(
                Integer.valueOf(orderListImportVo.getLongMilkGiveQuantity()));
            cxrUserOrder.setExcessQuantity(orderListImportVo.getExcessQuantity());
            cxrUserOrder.setFreshMilkSentQuantity(orderListImportVo.getFreshMilkSentQuantity());
            cxrUserOrder.setLongMilkSentQuantity(orderListImportVo.getLongMilkSentQuantity());

            cxrUserOrder.setSurplusQuantity(0);
            //            cxrUserOrder.setConversionQuantity();
            //            cxrUserOrder.setConversionType();
            cxrUserOrder.setUnitPrice(new BigDecimal(orderListImportVo.getUnitPrice()));
            cxrUserOrder.setAmount(new BigDecimal(orderListImportVo.getAmount()));

            cxrUserOrder.setCreateTime(date);

            // 客户地址2

            List<CustomerInfo> customerInfoList = new ArrayList<>();

            CustomerInfo customerInfo = new CustomerInfo();
            customerInfo.setPhone(orderListImportVo.getPhone2());
            customerInfo.setAdress(orderListImportVo.getCustomerAdress2());
            customerInfo.setProvice(orderListImportVo.getProvice());
            customerInfo.setCity(orderListImportVo.getCity());
            customerInfo.setSiteId(cxrSite.getId());
            customerInfo.setSiteName(cxrSite.getName());
            customerInfo.setArea(cxrSite.getArea());
            customerInfo.setDistributionId(businessAgent.getProxyId());
            customerInfo.setDistributionName(businessAgent.getProxyName());
            customerInfoList.add(customerInfo);

            cxrUserOrder.setCustomerInfoList(customerInfoList);

            // 转入信息
            //            cxrUserOrder.setCustomerNameSwitch();
            cxrUserOrder.setCustomerPhoneSwitch(orderListImportVo.getInputCustomerPhone());
            cxrUserOrder.setCustomerAdressSwitch(orderListImportVo.getInputCustomerAddress());
            // 活动
            cxrUserOrder.setActivityType(orderListImportVo.getActivityType());
            // 付款来源
            //            remoteDeptService.
            if ("二维码".equals(orderListImportVo.getPaymentSouce())) {
                cxrUserOrder.setPaymentSouce("5");
            } else if ("收钱吧".equals(orderListImportVo.getPaymentSouce())) {
                cxrUserOrder.setPaymentSouce("4");
            } else {
                cxrUserOrder.setPaymentSouce("7");
            }

            // 刷卡金额

            cxrUserOrder.setCreditCardAmount(new BigDecimal(orderListImportVo.getCreditCardAmount()));

            //
            // cxrUserOrder.setPromotionalOrderFlag(ObjectUtil.isNotEmpty(orderListImportVo.getPromotionalOrderFlag())?true:null);
            cxrUserOrder.setZeroQuantityRenewal(
                ObjectUtil.isNotEmpty(orderListImportVo.getZeroQuantityRenewal()) ? true : null);

            cxrUserOrder.setActivityGiveQuantity(orderListImportVo.getActivityGiveQuantity());
            cxrUserOrder.setActivitySentQuantity(orderListImportVo.getActivitySentQuantity());
            //            if
            // (orderListImportVo.getOrderType().equals(OrderTypeEnums.NEW_ORDER.getDesc())){     //
            //                cxrUserOrder.setPayStatus(PayStatusEnums.PAY_SUCCEEDED.getValue());
            //                cxrUserOrder.setPayTime(date);
            //                cxrUserOrder.setPerfectStatus(PerfectStatusEnums.PERFECT.getValue());
            //            }

            if (orderListImportVo.getOrderType().equals(OrderTypeEnums.EXCHANGE_ORDER.getDesc())) { //
                cxrUserOrder.setExchangeSum(orderListImportVo.getConversionQuantity());
                cxrUserOrder.setExchangeProductName(orderListImportVo.getConversionType());
                cxrUserOrder.setMilkExchangeSum(orderListImportVo.getConversionQuantity() * 10);
                //                cxrUserOrder.setT
            }

            //            if
            // (orderListImportVo.getOrderType().equals(OrderTypeEnums.RETURN_ORDER.getDesc())){//
            //
            //                CxrUserReturnOrder cxrUserReturnOrder =
            // BeanUtil.copyProperties(cxrUserOrder, CxrUserReturnOrder.class);
            ////                cxrUserReturnOrderMapper.insert(cxrUserReturnOrder);
            //
            //            }

            // 订单类型转义

            cxrUserOrder.setIsImport(true);
            cxrUserOrderService.save(cxrUserOrder);
//            cxrOrderListVos.add(cxrUserOrder);
        }

        //        }, scheduledExecutorService);
        return false;
//        return cxrUserOrderService.saveBatch(cxrOrderListVos);
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean importExecutorUserOrderPlus(List<CxrOrderListImportPlusVo> list, Long sysDept) {
        try {
            ExecutorService touchWorker =
                Executors.newFixedThreadPool(20, Executors.defaultThreadFactory());

            List<List<CxrOrderListImportPlusVo>> split = CollectionUtil.split(list, 100);
            //        ExecutorService touchWorker = Executors.newFixedThreadPool(20,
            // Executors.defaultThreadFactory());
            long start = System.currentTimeMillis();

            for (List<CxrOrderListImportPlusVo> importListVos : split) {
                touchWorker.execute(() -> executorOrderPlus(importListVos, sysDept)); // 线程开始
            }
            touchWorker.shutdown(); // 关闭线程池
            while (true) {
                if (touchWorker.isTerminated()) {
                    break;
                }
            }

            long end = System.currentTimeMillis();
            System.out.println((end - start) / 1000);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return true;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void updataOrderProxyId() {

        List<CxrUserOrder> userOrders =
            cxrUserOrderMapper.selectList(
                new LambdaQueryWrapper<CxrUserOrder>()
                    .eq(CxrUserOrder::getIsImport, 1)
                    .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));

        ExecutorService touchWorker =
            Executors.newFixedThreadPool(20, Executors.defaultThreadFactory());
        int size = userOrders.size();
        long start = System.currentTimeMillis();
        if (size > 100) {
            int batch = size % 1000 == 0 ? size / 1000 : size / 1000 + 1; // 将List集合切片
            for (int j = 0; j < batch; j++) {
                int end = (j + 1) * 1000;
                if (end > size) {
                    end = size;
                }
                List<CxrUserOrder> subList =
                    userOrders.subList(j * 1000, end); // 截取每个小分片的数据 第一次是0-100的数据以此类推
                touchWorker.execute(() -> executorUpdataOrderProxyId(subList)); // 线程开始
            }
            touchWorker.shutdown(); // 关闭线程池
            while (true) {
                if (touchWorker.isTerminated()) {
                    break;
                }
            }
        } else { // 小于100 不走多线程
            executorUpdataOrderProxyId(userOrders);
        }

        long end = System.currentTimeMillis();
        System.out.println((end - start) / 1000);
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public void updataOrderCustomerInfoList() {
        List<CxrUserOrder> userOrders =
            cxrUserOrderMapper.selectList(
                new LambdaQueryWrapper<CxrUserOrder>()
                    .eq(CxrUserOrder::getIsImport, 1)
                    .in(CxrUserOrder::getOrderType, 0, 1, 3, 7)
                    .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));

        ExecutorService touchWorker =
            Executors.newFixedThreadPool(20, Executors.defaultThreadFactory());
        int size = userOrders.size();
        long start = System.currentTimeMillis();
        if (size > 100) {
            int batch = size % 1000 == 0 ? size / 1000 : size / 1000 + 1; // 将List集合切片
            for (int j = 0; j < batch; j++) {
                int end = (j + 1) * 1000;
                if (end > size) {
                    end = size;
                }
                List<CxrUserOrder> subList =
                    userOrders.subList(j * 1000, end); // 截取每个小分片的数据 第一次是0-100的数据以此类推
                touchWorker.execute(() -> executorUpdataOrderCustomerInfoList(subList)); // 线程开始
            }
            touchWorker.shutdown(); // 关闭线程池
            while (true) {
                if (touchWorker.isTerminated()) {
                    break;
                }
            }
        } else { // 小于100 不走多线程
            executorUpdataOrderCustomerInfoList(userOrders);
        }

        long end = System.currentTimeMillis();
        System.out.println((end - start) / 1000);
    }

    @Override
    public void huiBoSynchronouslyOrder() throws Exception {

        // 查询上一个小时的
        LocalDateTime startTime =
            LocalDateTime.of(LocalDate.now().minusDays(2), LocalTime.MAX); // 获取当天结束时间
        LocalDateTime endTime = LocalDateTime.of(LocalDate.now().minusDays(1), LocalTime.MAX);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<OrderTradesBo> orderTradesBos = new ArrayList<>();
        int size = 500;
        long pageCount = 1;
        for (int current = 1; current <= pageCount; current++) {
            IPage<CxrUserOrder> queryPage = new Page<>(current, size);

            IPage<CxrUserOrder> page =
                cxrUserOrderMapper.selectPage(
                    queryPage,
                    new LambdaQueryWrapper<CxrUserOrder>()
                        .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                        .notIn(CxrUserOrder::getOrderType, OrderTypeEnums.RETURN_ORDER.getValue(),
                            OrderTypeEnums.CHANGE_LONG_MILK_ORDER.getValue())
                        .between(CxrUserOrder::getUpdateTime, startTime, endTime)
                        .orderByAsc(CxrUserOrder::getId));
            long total = page.getTotal();
            pageCount = total % size > 0 ? (total / size + 1) : total / size;
            List<CxrUserOrder> cxrUserOrders = page.getRecords();

            for (CxrUserOrder userOrder : cxrUserOrders) {
                if (ObjectUtil.isNotEmpty(userOrder.getOrderNo())
                    && ObjectUtil.isNotEmpty(userOrder.getCustomerPhone())) {
                    OrderTradesBo orderTradesBo = new OrderTradesBo();
                    orderTradesBo.setCreated(sdf.format(userOrder.getOrderDate()));
                    orderTradesBo.setPayment(
                        ObjectUtil.isEmpty(userOrder.getAmount())
                            ? new BigDecimal(0)
                            : userOrder.getAmount());

                    if (userOrder.getAuditStatus().equals(AuditStatusEnums.Audit.code())) {
                        orderTradesBo.setStatus(HuiBoOrderStatusEnums.TRADE_FINISHED.toString()); // 未完成
                    } else if (userOrder.getPayStatus().equals(PayStatusEnums.PAY_SUCCEEDED.getValue())) {
                        orderTradesBo.setStatus(HuiBoOrderStatusEnums.TRADE_FINISHED.toString()); // 未完成
                    } else if (userOrder.getPayStatus().equals(PayStatusEnums.WAIT_PAY.getValue())) {
                        orderTradesBo.setStatus(HuiBoOrderStatusEnums.WAIT_BUYER_PAY.toString()); // 未完成
                    }
                    orderTradesBo.setSiteId(userOrder.getSiteId());
                    //
                    orderTradesBo.setReceiverMobile(userOrder.getCustomerPhone());
                    orderTradesBo.setPayTime(
                        ObjectUtil.isEmpty(userOrder.getPayTime())
                            ? sdf.format(userOrder.getOrderDate())
                            : sdf.format(userOrder.getPayTime()));
                    orderTradesBo.setTid(userOrder.getOrderNo());
                    orderTradesBo.setPlatUserId(userOrder.getCustomerPhone());
                    orderTradesBo.setReceiverName(userOrder.getCustomerName());
                    List<BusinessAgent> businessAgent = userOrder.getBusinessAgent();

                    if (ObjectUtil.isEmpty(businessAgent.get(0).getProxyNo())) {
                        orderTradesBo.setO2oGuideName(0 + "");
                    } else {
                        orderTradesBo.setO2oGuideName(businessAgent.get(0).getProxyNo() + "");
                    }

                    orderTradesBo.setReceiverState(userOrder.getProvince());
                    orderTradesBo.setReceiverCity(userOrder.getCity());
                    orderTradesBo.setReceiverDistrict(userOrder.getArea());
                    orderTradesBo.setReceiverAddress(userOrder.getCustomerAdress());

                    List<OrdersSynchronouslyBo> synchronouslyBos = new ArrayList<>();
                    OrdersSynchronouslyBo orders = new OrdersSynchronouslyBo();
                    orders.setOid(userOrder.getOrderNo());
                    orders.setPayment(
                        ObjectUtil.isEmpty(userOrder.getAmount())
                            ? new BigDecimal(0)
                            : userOrder.getAmount());

                    if (OrderTypeEnums.RETURN_ORDER.getValue() == userOrder.getOrderType()) {
                        orders.setNum(userOrder.getFreshMilkReturnQuantity());
                    } else if (OrderTypeEnums.GIVE_ORDER.getValue() == userOrder.getOrderType()) {
                        orders.setNum(userOrder.getFreshMilkGiveQuantity());
                    } else if (OrderTypeEnums.EXCHANGE_ORDER.getValue() == userOrder.getOrderType()) {
                        orders.setNum(userOrder.getMilkExchangeSum());
                    } else if (OrderTypeEnums.CHANGE_ORDER.getValue() == userOrder.getOrderType()) {
                        orders.setNum(userOrder.getConversionQuantity());
                    } else {
                        orders.setNum(userOrder.getOrderQuantity());
                    }
                    orders.setNumIid("鲜羊奶"); // 123456789000372176
                    orders.setTitle("鲜羊奶");
                    orders.setPrice(orders.getPrice());
                    if (!NumberUtil.equals(orders.getNum(), 0)) {
                        synchronouslyBos.add(orders);
                        orderTradesBo.setOrders(synchronouslyBos);
                        orderTradesBos.add(orderTradesBo);
                    }
                }
            }
        }

        // 更新或者创建的订单 同步给慧博
        String appKey = huiBoOrderConfig.getAppKey();
        String secret = huiBoOrderConfig.getSecret();
        String sellerNick = huiBoOrderConfig.getSellerNick();

        if (orderTradesBos.size() > 0) {

            Map<Long, List<OrderTradesBo>> orderMap =
                orderTradesBos.parallelStream().collect(Collectors.groupingBy(OrderTradesBo::getSiteId));

            Set<Long> longSet = orderMap.keySet();
            Map<Long, CxrHuiboShop> dataHuibo = remoteHuiBoService.selectByShopNos(longSet);
            for (Long siteId : orderMap.keySet()) {
                CxrHuiboShop cxrHuiboShop = dataHuibo.get(siteId);
                if (ObjectUtil.isNotEmpty(cxrHuiboShop)) {
                    List<OrderTradesBo> tradesBos = orderMap.get(siteId);
                    List<List<OrderTradesBo>> split = CollectionUtil.split(tradesBos, 99);
                    for (List<OrderTradesBo> tradesBoList : split) {
                        Map<String, Object> requestBody = new TreeMap<String, Object>();
                        requestBody.put("shopNo", cxrHuiboShop.getShopNo());
                        requestBody.put("trades", tradesBoList);

                        long timeMillis = System.currentTimeMillis();
                        String json = JSONObject.toJSONString(requestBody);
                        StringBuffer sb = new StringBuffer();
                        sb.append(appKey).append(sellerNick).append(timeMillis).append(json).append(secret);

                        String sign = null;
                        try {
                            sign = HuiBoSignUtil.getSign(sb.toString());
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }

                        StringBuilder queryString = new StringBuilder();
                        queryString
                            .append("app_key=")
                            .append(appKey)
                            .append("&seller_nick=")
                            .append(URLEncoder.encode(sellerNick, "utf-8"))
                            .append("&timestamp=")
                            .append(timeMillis)
                            .append("&sign=")
                            .append(sign); // 需要重新生成

                        String jsonSync =
                            OkHttpUtils.postJsonSync(
                                "https://api.jkcrm.cn/cloud/trade/saveBatch?" + queryString, json);
                        HuiBoResponse boResponse = JSONUtil.toBean(jsonSync, HuiBoResponse.class);
                        log.info("{}", boResponse);

                    }
                }
            }
        }
    }

    @Override
    public void huiBoRealTimeSynOrder(Long id) {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        CxrUserOrder userOrder = cxrUserOrderService.lambdaQuery().eq(CxrUserOrder::getId, id)
            .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            .notIn(CxrUserOrder::getOrderType, OrderTypeEnums.RETURN_ORDER.getValue(),
                OrderTypeEnums.CHANGE_LONG_MILK_ORDER.getValue()).one();

        if (ObjectUtil.isEmpty(userOrder.getOrderNo()) || ObjectUtil.isEmpty(userOrder.getCustomerPhone())) {
            log.info("订单id={}，订单号：{}，customerPhone：{}", id, userOrder.getOrderNo(), userOrder.getCustomerPhone());
            return;
        }

        if (PayStatusEnums.PAY_SUCCEEDED.getValue() != userOrder.getPayStatus()
            && OrderAuditStatusEnums.AUDITED.getValue() != userOrder.getAuditStatus()) {
            log.info("订单id={}，支付状态：{}，审核状态：{}", id, userOrder.getPayStatus(), userOrder.getAuditStatus());
            return;
        }

        OrderTradesBo orderTradesBo = new OrderTradesBo();
        orderTradesBo.setCreated(sdf.format(userOrder.getOrderDate()));
        orderTradesBo.setPayment(
            ObjectUtil.isEmpty(userOrder.getAmount())
                ? new BigDecimal(0)
                : userOrder.getAmount());

        if (userOrder.getAuditStatus().equals(AuditStatusEnums.Audit.code())) {
            orderTradesBo.setStatus(HuiBoOrderStatusEnums.TRADE_FINISHED.toString()); // 未完成
        } else if (userOrder.getPayStatus().equals(PayStatusEnums.PAY_SUCCEEDED.getValue())) {
            orderTradesBo.setStatus(HuiBoOrderStatusEnums.TRADE_FINISHED.toString()); // 未完成
        } else if (userOrder.getPayStatus().equals(PayStatusEnums.WAIT_PAY.getValue())) {
            orderTradesBo.setStatus(HuiBoOrderStatusEnums.WAIT_BUYER_PAY.toString()); // 未完成
        }
        orderTradesBo.setSiteId(userOrder.getSiteId());
        //
        orderTradesBo.setReceiverMobile(userOrder.getCustomerPhone());
        orderTradesBo.setPayTime(
            ObjectUtil.isEmpty(userOrder.getPayTime())
                ? sdf.format(userOrder.getOrderDate())
                : sdf.format(userOrder.getPayTime()));
        orderTradesBo.setTid(userOrder.getOrderNo());
        orderTradesBo.setPlatUserId(userOrder.getCustomerPhone());
        orderTradesBo.setReceiverName(userOrder.getCustomerName());
        List<BusinessAgent> businessAgent = userOrder.getBusinessAgent();

        if (ObjectUtil.isEmpty(businessAgent.get(0).getProxyNo())) {
            orderTradesBo.setO2oGuideName(0 + "");
        } else {
            orderTradesBo.setO2oGuideName(businessAgent.get(0).getProxyNo() + "");
        }

        orderTradesBo.setReceiverState(userOrder.getProvince());
        orderTradesBo.setReceiverCity(userOrder.getCity());
        orderTradesBo.setReceiverDistrict(userOrder.getArea());
        orderTradesBo.setReceiverAddress(userOrder.getCustomerAdress());

        List<OrdersSynchronouslyBo> synchronouslyBos = new ArrayList<>();
        OrdersSynchronouslyBo orders = new OrdersSynchronouslyBo();
        orders.setOid(userOrder.getOrderNo());
        orders.setPayment(
            ObjectUtil.isEmpty(userOrder.getAmount())
                ? new BigDecimal(0)
                : userOrder.getAmount());

        if (OrderTypeEnums.RETURN_ORDER.getValue() == userOrder.getOrderType()) {
            orders.setNum(userOrder.getFreshMilkReturnQuantity());
        } else if (OrderTypeEnums.GIVE_ORDER.getValue() == userOrder.getOrderType()) {
            orders.setNum(userOrder.getFreshMilkGiveQuantity());
        } else if (OrderTypeEnums.EXCHANGE_ORDER.getValue() == userOrder.getOrderType()) {
            orders.setNum(userOrder.getMilkExchangeSum());
        } else if (OrderTypeEnums.CHANGE_ORDER.getValue() == userOrder.getOrderType()) {
            orders.setNum(userOrder.getConversionQuantity());
        } else {
            orders.setNum(userOrder.getOrderQuantity());
        }
        orders.setNumIid("鲜羊奶"); // 123456789000372176
        orders.setTitle("鲜羊奶");
        orders.setPrice(orders.getPrice());
        if (!NumberUtil.equals(orders.getNum(), 0)) {
            synchronouslyBos.add(orders);
            orderTradesBo.setOrders(synchronouslyBos);
        }

        // 更新或者创建的订单 同步给慧博
        String appKey = huiBoOrderConfig.getAppKey();
        String secret = huiBoOrderConfig.getSecret();
        String sellerNick = huiBoOrderConfig.getSellerNick();

        Long siteId = orderTradesBo.getSiteId();
        CxrHuiboShop cxrHuiboShop = remoteHuiBoService.selectByShopNo(siteId);

        if (ObjectUtil.isNotEmpty(cxrHuiboShop)) {

            Map<String, Object> requestBody = new TreeMap<String, Object>();
            requestBody.put("shopNo", cxrHuiboShop.getShopNo());
            requestBody.put("trades", Arrays.asList(orderTradesBo));

            long timeMillis = System.currentTimeMillis();
            String json = JSONObject.toJSONString(requestBody);
            StringBuilder sb = new StringBuilder();
            sb.append(appKey).append(sellerNick).append(timeMillis).append(json).append(secret);

            try {

                String sign = HuiBoSignUtil.getSign(sb.toString());

                StringBuilder queryString = new StringBuilder();

                queryString
                    .append("app_key=")
                    .append(appKey)
                    .append("&seller_nick=")
                    .append(URLEncoder.encode(sellerNick, "utf-8"))
                    .append("&timestamp=")
                    .append(timeMillis)
                    .append("&sign=")
                    .append(sign); // 需要重新生成

                String jsonSync = OkHttpUtils.postJsonSync("https://api.jkcrm.cn/cloud/trade/saveBatch?" + queryString,
                    json);
                log.info("【慧博】同步订单，返回：{}", jsonSync);
            } catch (UnsupportedEncodingException e) {
                log.error("【慧博】同步订单,异常1：{}", e.getMessage());
            } catch (Exception e) {
                log.error("【慧博】同步订单,异常2：{}", e.getMessage());
            }
        }
    }

    public void deleteNotPayCxrUserOrder() {
        // 物理删除2天前逻辑删除的订单
        cxrUserOrderMapper.delete(
            new LambdaQueryWrapper<CxrUserOrder>()
                .lt(CxrUserOrder::getDeleteTime, LocalDate.now().plusDays(-2))
                .eq(CxrUserOrder::getPayStatus, PayStatusEnums.WAIT_PAY.getValue())
                .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.DELETED.getValue()));
    }

    @Override
    public void logicDeleteNotPayCxrUserOrder() {
        // 逻辑删除2天前未删除的订单
        cxrUserOrderMapper.update(null,
            new LambdaUpdateWrapper<CxrUserOrder>()
                .le(CxrUserOrder::getCreateTime, LocalDate.now().plusDays(-2))
                .ge(CxrUserOrder::getCreateTime, LocalDate.now().plusDays(-5))
                .eq(CxrUserOrder::getPayStatus, PayStatusEnums.WAIT_PAY.getValue())
                .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .set(CxrUserOrder::getDeleteStatus, DeleteStatus.DELETED.getValue())
        );
    }

    @Override
    public void huiBoCustomeOrder(LocalDateTime localDateTime) {
        log.info("自动生成订单同步新客户 执行时间 {} , 执行事务xid {}", localDateTime, RootContext.getXID());
        // 执行时间为第二天,来推第一天订单中的新客户的信息
        LocalDateTime endtime = LocalDateTime.now();
        log.info("打印时间的生成{}", endtime);
        LocalDateTime startDate = endtime.plusDays(-1L);
        PageUtiil<CxrUserOrder> cxrUserOrders = new PageUtiil<>(50L);
        cxrUserOrders.pageCompletableFuture((Page<CxrUserOrder> page) -> {
            return CompletableFuture.supplyAsync(() -> {
                Page<CxrUserOrder> cxrUserOrderPage = cxrUserOrderMapper.queryCustomerOrder(startDate, endtime, page);
                return cxrUserOrderPage;
            }, threadPoolTaskExecutor).exceptionally(new Function<Throwable, Page<CxrUserOrder>>() {

                @Override
                public Page<CxrUserOrder> apply(Throwable throwable) {
                    log.error("查询分页数据发生异常", throwable);
                    return null;
                }
            });
        });

        cxrUserOrders
            .businessCompletableFuture((List<CxrUserOrder> listVos) -> {
                return CompletableFuture.runAsync(() -> {
                    if (CollUtil.isNotEmpty(listVos)) {
                        executorhuiBoCustomeOrder(listVos);
                    }
                }, threadPoolTaskExecutor).exceptionally(new Function<Throwable, Void>() {
                    @Override
                    public Void apply(Throwable throwable) {
                        log.error("huiBoCustomeOrder-处理数据发生异常:" + throwable.toString(), throwable);
                        return null;
                    }
                });
            });
        cxrUserOrders.execute();
    }

    public void syncPayInfo(Long id, boolean sc) {
        log.info("同步客户中心，订单id={}", id);
        CxrUserOrder order = cxrUserOrderService.getById(id);
        if (order == null) {
            log.info("同步客户中心，客户id={},查询数据为空", id);
            return;
        }
        if (!OrderTypeEnums.getPayOrderTypeValues().contains(order.getOrderType())) {
            log.info("同步客户中心，客户id={},不同步，订单类型：{}", id, order.getOrderType());
            return;
        }
        YDSCustomerDTO customerInfoDTO = new YDSCustomerDTO();
        Long customerId = order.getCustomerId();
        customerInfoDTO.setId(customerId);
        customerInfoDTO.setPayAmount(order.getAmount());
        if (PayStatusEnums.PAY_SUCCEEDED.getValue() == order.getPayStatus()) {
            customerInfoDTO.setPayTime(DateUtil.toLocalDateTime(order.getPayTime()));
        } else if (OrderAuditStatusEnums.AUDITED.getValue() == order.getAuditStatus()) {
            customerInfoDTO.setPayTime(DateUtil.toLocalDateTime(order.getAuditTime()));
        } else {
            log.info("同步客户中心，客户id={},订单未支付", id);
            return;
        }

        if (CustomerSourceType.XCX.getValue().equals(order.getCreateByType())) {
            log.info("同步客户中心，客户id={},小程序订单不在这边同步", id);
            return;
        }

        customerInfoDTO.setOrderNo(order.getOrderNo());
        mqUtil.sendSyncMessage(CxrCustomerConstant.YDS_CUSTOMER_TOPIC, CxrCustomerConstant.YDS_CUSTOMER_PAY_TAG,
            JSONUtil.toJsonStr(customerInfoDTO), id.toString());
        if (sc) {
            mqUtil.sendSyncMessage(CxrCustomerConstant.CUSTOMER_TOPIC, CxrCustomerConstant.YDS_CUSTOMER_SYNC_TAG,
                customerId.toString());
        }
    }

    public void syncOrder(LocalDateTime date) {
        List<CxrUserOrder> list;
        long maxId = 0;
        do {
            list = cxrUserOrderService.lambdaQuery().select(CxrUserOrder::getId)
                .ge(CxrUserOrder::getId, maxId)
                .ge(CxrUserOrder::getCreateTime, date)
                .in(CxrUserOrder::getOrderType, OrderTypeEnums.getPayOrderTypeValues())
                .and(w -> w.eq(CxrUserOrder::getAuditStatus, OrderAuditStatusEnums.AUDITED.getValue()).or()
                    .eq(CxrUserOrder::getPayStatus, PayStatusEnums.PAY_SUCCEEDED.getValue()))
                .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .orderByAsc(CxrUserOrder::getId)
                .last("limit 1000")
                .list();
            if (CollectionUtil.isNotEmpty(list)) {
                list.forEach(order -> syncPayInfo(order.getId(), false));
                maxId = list.get(list.size() - 1).getId();
            }
        } while (CollectionUtil.isNotEmpty(list));
    }

/*
    public void syncXcxOrder(LocalDate date) {
        List<CxrXcxRetailOrder> cxrXcxRetailOrders;
        long maxId = 0;
        do {
            cxrXcxRetailOrders = cxrXcxRetailOrderMapper.selectList(new LambdaQueryWrapper<CxrXcxRetailOrder>().ge(CxrXcxRetailOrder::getId, maxId)
                .ge(CxrXcxRetailOrder::getCreateTime, date).orderByAsc(CxrXcxRetailOrder::getId).last("limit 1000"));
            if (CollectionUtil.isNotEmpty(cxrXcxRetailOrders)) {
                cxrXcxRetailOrders.forEach(order -> {
                    YDSCustomerDTO customerInfoDTO = new YDSCustomerDTO();
                    customerInfoDTO.setId(order.getCustomerId());
                    customerInfoDTO.setPayAmount(order.getOrderTotalPrice());
                    customerInfoDTO.setPayTime(DateUtil.toLocalDateTime(order.getPayTime()));
                    customerInfoDTO.setOrderNo(order.getOrderNo());
                    SendResult sendResult = mqUtil.sendSyncMessage(CxrCustomerConstant.YDS_CUSTOMER_TOPIC, CxrCustomerConstant.YDS_CUSTOMER_PAY_TAG, JSONUtil.toJsonStr(customerInfoDTO));
                });
                maxId = cxrXcxRetailOrders.get(cxrXcxRetailOrders.size() - 1).getId();
            }
        } while (CollectionUtil.isNotEmpty(cxrXcxRetailOrders));
    }
    public void syncXcxOrder(Long id){
        log.info("同步客户中心，订单id={}", id);
        CxrXcxRetailOrder order = cxrXcxRetailOrderMapper.selectById(id);
        if (order == null){
            log.info("同步客户中心，客户id={},查询数据为空", id);
            return;
        }
        YDSCustomerDTO customerInfoDTO = new YDSCustomerDTO();
        customerInfoDTO.setId(order.getCustomerId());
        customerInfoDTO.setPayAmount(order.getOrderTotalPrice());
        customerInfoDTO.setPayTime(DateUtil.toLocalDateTime(order.getPayTime()));
        customerInfoDTO.setOrderNo(order.getOrderNo());
        SendResult sendResult = mqUtil.sendSyncMessage(CxrCustomerConstant.YDS_CUSTOMER_TOPIC, CxrCustomerConstant.YDS_CUSTOMER_PAY_TAG, JSONUtil.toJsonStr(customerInfoDTO));
    }
*/

    private void executorhuiBoCustomeOrder(List<CxrUserOrder> listVos) {
        //1.通过当天的新订单新增的客户 依据客户表的id 查询客户地址表里面的站点id
        log.info("打印数据listVos{}", listVos);
        if (CollUtil.isNotEmpty(listVos)) {
            List<Long> cxrCustomerIds = listVos.stream().map(CxrUserOrder::getCustomerId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(cxrCustomerIds)) {
                List<CxrCustomer> cxrCustomerList = remoteCustomerService.queryByIds(cxrCustomerIds);
                List<Long> ids = cxrCustomerList.stream().map(CxrCustomer::getId).collect(Collectors.toList());
                Map<Long, CxrCustomer> customerMap = cxrCustomerList.stream()
                    .collect(Collectors.toMap(CxrCustomer::getId, Function.identity(), (v1, v2) -> v2));
                if (CollUtil.isNotEmpty(cxrCustomerList)) {
                    List<CxrCustomerAddress> cxrCustomerAddressList = remoteCustomerAddressService
                        .selectHuiBoCustomerId(
                            ids);
                    Map<Long, CxrCustomerAddress> addressMap = cxrCustomerAddressList.stream().collect(
                        Collectors.toMap(CxrCustomerAddress::getCxrCustomerId, Function.identity(), (v1, v2) -> v2));
                    for (CxrUserOrder cxrUserOrder : listVos) {
                        ////过滤没有手机号码的
                        //1.添加的客户是没有绑定unionId
                        //2.添加的客户有绑定unionId 同步手机号+openid+unionId
                        if (ObjectUtil.isNotEmpty(cxrUserOrder.getCustomerPhone())) {
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            Map<String, Object> map = new HashMap<>();
                            map.put("mobile", cxrUserOrder.getCustomerPhone());
                            map.put("brandId", "123456789000378189");
                            map.put("bindingTime", sdf.format(new Date()));
                            //用来对接正式环境shopNo(需要获取配送员的站点)
                            CxrCustomer cxrCustomer = customerMap.get(cxrUserOrder.getCustomerId());
                            if (ObjectUtil.isNotEmpty(cxrCustomer)) {
                                //取慧博存在数据库里面的ShopeNo
                                CxrCustomerAddress cxrCustomerAddress = addressMap.get(cxrUserOrder.getCustomerId());
                                if (ObjectUtil.isNotEmpty(cxrCustomerAddress)) {
                                    CxrHuiboShop cxrHuiboShop = cxrHuiboShopService.selecctBySiteId(
                                        cxrCustomerAddress.getCxrSiteId());
                                    if (ObjectUtil.isNotEmpty(cxrHuiboShop)) {
                                        map.put("shopNo", cxrHuiboShop.getShopNo());
                                    }
                                }
                            }
                            if (ObjectUtil.isNotEmpty(cxrCustomer)) {
                                if (ObjectUtil.isNotEmpty(cxrCustomer.getWxUnionId())) {
                                    map.put("unionId", cxrCustomer.getWxUnionId());
                                    map.put("shopNo", "kRbKKJ");
                                }
                            }
                            String json = JSONUtil.toJsonStr(map);
                            long timestamp = System.currentTimeMillis();
                            StringBuffer sb = new StringBuffer();
                            sb
                                .append(huiBoConfig.getAppKey())
                                .append(huiBoConfig.getSellerNick())
                                .append(timestamp)
                                .append(json)
                                .append(huiBoConfig.getSecret());

                            String sign = null;
                            try {
                                sign = HuiBoSignUtil.getSign(sb.toString());
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                            String body = OkHttpUtils.postJsonSync(
                                StrUtil.format("{}?app_key={}&&seller_nick={}&&timestamp={}&&sign={}"
                                    , huiBoConfig.getUoloadCustomerUrl(),
                                    huiBoConfig.getAppKey(),
                                    huiBoConfig.getSellerNick()
                                    , timestamp
                                    , sign), JSONUtil.toJsonStr(map));

                            log.info("body参数{}", body);
                        }
                        // 微信授权了 再调用绑定电商号的接口
                        if (ObjectUtil.isNotEmpty(cxrUserOrder.getCustomerPhone())) {
                            CxrCustomer cxrCustomer = customerMap.get(cxrUserOrder.getCustomerId());
                            if (ObjectUtil.isNotEmpty(cxrCustomer)) {
                                if (ObjectUtil.isNotEmpty(cxrCustomer.getWxUnionId())) {
                                    Map<String, Object> map = new HashMap<>();
                                    map.put("platUserType", HuiBoUserType.PHONE.getValue());
                                    map.put("platUserId", cxrCustomer.getPhone());
                                    map.put("unionId", cxrCustomer.getWxUnionId());
                                    map.put("openId", cxrCustomer.getWxOpenId());
                                    map.put("brandId", "123456789000378189");

                                    long timestamp = System.currentTimeMillis();
                                    String appKey = huiBoConfig.getAppKey();
                                    String sellerNick = huiBoConfig.getSellerNick();
                                    String secret = huiBoConfig.getSecret();

                                    StringBuffer sb = new StringBuffer();
                                    sb
                                        .append(appKey)
                                        .append(sellerNick)
                                        .append(timestamp)
                                        .append(JSONUtil.toJsonStr(map))
                                        .append(secret);

                                    String sign = null;
                                    try {
                                        sign = HuiBoSignUtil.getSign(sb.toString());
                                    } catch (Exception e) {
                                        throw new RuntimeException(e);
                                    }
                                    String body = OkHttpUtils.postJsonSync(
                                        StrUtil.format("{}?app_key={}&&seller_nick={}&&timestamp={}&&sign={}"
                                            , "https://api.jkcrm.cn/cloud/member/bilei/bind",
                                            huiBoConfig.getAppKey(),
                                            huiBoConfig.getSellerNick()
                                            , timestamp
                                            , sign), JSONUtil.toJsonStr(map));

                                    log.info("bodys参数{}", body);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    public void executorUpdataOrderProxyId(List<CxrUserOrder> userOrders) {

        for (CxrUserOrder userOrder : userOrders) {
            List<BusinessAgent> agent = userOrder.getBusinessAgent();
            for (BusinessAgent businessAgent : agent) {
                businessAgent.setProxyId(
                    remoteEmployeeService.selectEmployeeJobNumber(businessAgent.getProxyNo()));
            }
            userOrder.setBusinessAgent(agent);
        }
        cxrUserOrderService.updateBatchById(userOrders);
    }

    public void executorUpdataOrderCustomerInfoList(List<CxrUserOrder> userOrders) {

        for (CxrUserOrder userOrder : userOrders) {

            List<CustomerInfo> customerInfoList = userOrder.getCustomerInfoList();
            BusinessAgent businessAgent = userOrder.getBusinessAgent().get(0);
            if (CollectionUtil.isEmpty(customerInfoList)) {
                List<CustomerInfo> customerInfoLists = new ArrayList<>();
                CustomerInfo customerInfo = new CustomerInfo();
                customerInfo.setDistributionId(businessAgent.getProxyId());
                customerInfo.setDistributionName(businessAgent.getProxyName());
                customerInfo.setSiteName(userOrder.getSiteName());
                customerInfo.setCity(userOrder.getCity());
                customerInfo.setProvice(userOrder.getProvince());
                customerInfo.setAdress(userOrder.getCustomerAdress());
                customerInfo.setArea(userOrder.getArea());
                customerInfo.setPhone(userOrder.getCustomerPhone());
                customerInfo.setName(userOrder.getCustomerName());
                customerInfo.setResidentialQuartersName(userOrder.getBigAreaName());
                customerInfoLists.add(customerInfo);
                userOrder.setCustomerInfoList(customerInfoLists);
            } else {
                for (CustomerInfo customerInfo : customerInfoList) {
                    customerInfo.setDistributionId(businessAgent.getProxyId());
                    customerInfo.setDistributionName(businessAgent.getProxyName());
                }
                userOrder.setCustomerInfoList(customerInfoList);
            }
        }
        cxrUserOrderService.updateBatchById(userOrders);
    }

    @Override
    public IPage<String> selectPagePhone(LocalDate now, Page page) {
        cn.hutool.core.date.StopWatch stopWatch = new cn.hutool.core.date.StopWatch();
        stopWatch.start("应送分页");
        IPage<String> phonePage =
            cxrUserOrderMapper.queryShouldSendNumber(
                now,
                AuditStatusEnums.Audit.code(),
                PayStatusEnums.PAY_SUCCEEDED.getValue(),
                OrderTypeEnums.CHANGE_ORDER.getValue(),
                DeleteStatus.NOT_DELETED.getValue(),
                page);
        stopWatch.stop();
        log.info(stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        return phonePage;
    }

    @Override
    public List<UserOrderDTO> queryOrderByids(List<Long> orderIds) {
        List<CxrUserOrder> orders =
            cxrUserOrderService
                .lambdaQuery()
                .select(
                    CxrUserOrder::getId,
                    CxrUserOrder::getOrderNo,
                    CxrUserOrder::getOrderDate,
                    CxrUserOrder::getCustomerPhone)
                .in(CxrUserOrder::getId, orderIds)
                .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .list();

        return orders.stream()
            .map(
                s -> {
                    UserOrderDTO userOrderDTO = new UserOrderDTO();
                    userOrderDTO.setOrderNo(s.getOrderNo());
                    userOrderDTO.setOrderDate(s.getOrderDate());
                    userOrderDTO.setCustomerPhone(s.getCustomerPhone());
                    userOrderDTO.setId(s.getId());
                    return userOrderDTO;
                })
            .collect(Collectors.toList());
    }

    @Override
    public boolean updateOrderPhone(CustomersVo cxrCustomer) {
        //修改客户退订单里面的手机号码
        List<CxrUserReturnOrder> selectByCustomerId = returnOrderService.selectByCustomerId(cxrCustomer.getId());
        if (CollectionUtil.isNotEmpty(selectByCustomerId)) {
            returnOrderService
                .updateByReturnPhone(cxrCustomer.getPhone(), cxrCustomer.getFolmerPhone(), cxrCustomer.getId()
                    , cxrCustomer.getName());
        }
        // 修改订单的里面客户的信息
        List<CxrUserOrder> cxrUserOrderList = cxrUserOrderService.getByCustomerId(cxrCustomer.getId());
        for (CxrUserOrder cxrUserOrder : cxrUserOrderList) {
            List<CustomerInfo> customerInfoList = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(cxrUserOrder.getCustomerInfoList())) {
                for (CustomerInfo info : cxrUserOrder.getCustomerInfoList()) {
                    info.setPhone(cxrCustomer.getPhone());
                    customerInfoList.add(info);
                }
            }
            boolean flag =
                cxrUserOrderService.update(
                    null,
                    new LambdaUpdateWrapper<CxrUserOrder>()
                        .eq(CxrUserOrder::getId, cxrUserOrder.getId())
                        .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                        .eq(CxrUserOrder::getPerfectStatus, PerfectStatusEnums.PERFECT.getValue())
                        .eq(CxrUserOrder::getCustomerPhone, cxrCustomer.getFolmerPhone())
                        .set(CxrUserOrder::getCustomerPhone, cxrCustomer.getPhone())
                        .set(CxrUserOrder::getCustomerInfoList, JSONUtil.toJsonStr(customerInfoList))
                        .set(CxrUserOrder::getUpdateByName, cxrCustomer.getName())
                        .set(CxrUserOrder::getUpdateBy, cxrCustomer.getId())
                        .set(CxrUserOrder::getUpdateByType, cxrCustomer.getUserType())
                        .set(CxrUserOrder::getUpdateTime, new Date())
                );
            if (!flag) {
                throw new ServiceException("修改订单手机号码失败!");
            }
        }

        //处理转单里面的转如人的手机号码
        List<CxrUserOrder> cxrUserOrders = cxrUserOrderService.getByCustomerPhone(cxrCustomer.getFolmerPhone());
        if (CollectionUtil.isNotEmpty(cxrUserOrders)) {
            for (CxrUserOrder cxrUserOrder : cxrUserOrders) {
                boolean result =
                    cxrUserOrderService.update(
                        null,
                        new LambdaUpdateWrapper<CxrUserOrder>()
                            .eq(CxrUserOrder::getId, cxrUserOrder.getId())
                            .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                            .eq(CxrUserOrder::getPerfectStatus, PerfectStatusEnums.PERFECT.getValue())
                            .eq(CxrUserOrder::getCustomerPhoneSwitch, cxrCustomer.getFolmerPhone())
                            .set(CxrUserOrder::getCustomerPhoneSwitch, cxrCustomer.getPhone())
                            .set(CxrUserOrder::getUpdateByName, cxrCustomer.getName())
                            .set(CxrUserOrder::getUpdateBy, cxrCustomer.getId())
                            .set(CxrUserOrder::getUpdateByType, cxrCustomer.getUserType())
                            .set(CxrUserOrder::getUpdateTime, new Date())
                    );

                if (!result) {
                    throw new ServiceException("修改订单手机号码失败!");
                }
            }
        }
        return true;
    }

    @Override
    public List<CustomerOrderRecentlyDate> selectRecentlyOrderDate(List<Long> cxrCustomerIds) {
        return cxrUserOrderMapper.selectRecentlyOrderDate(cxrCustomerIds);
    }

    @Override
    public CustomerOrderRecentlyDate selectRecentlyOrderDateByCustomerId(Long customerId) {
        return cxrUserOrderMapper.selectRecentlyOrderDateByCustomerId(customerId);
    }


    @Override
    public Map<Long, String> queryorderNoByIds(List<Long> orderIds) {
        List<CxrUserOrder> userOrders = cxrUserOrderService
            .lambdaQuery()
            .in(CxrUserOrder::getId, orderIds)
            .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            .select(CxrUserOrder::getOrderNo, CxrUserOrder::getId)
            .list();
        if (CollectionUtil.isEmpty(userOrders)) {
            return new HashMap<Long, String>();
        }

        return userOrders.stream().collect(Collectors.toMap(CxrUserOrder::getId, CxrUserOrder::getOrderNo));
    }

    @Override
    public List<UserOrderDTO> getCustomerIdByOrder(Long cxrCustomerId) {
        List<CxrUserOrder> list = cxrUserOrderService.list(new LambdaQueryWrapper<CxrUserOrder>()
            .eq(CxrUserOrder::getCustomerId, cxrCustomerId)
            .in(CxrUserOrder::getOrderType, OrderTypeEnums.NEW_ORDER.getValue(),
                OrderTypeEnums.CONTINUE_ORDER.getValue()
                , OrderTypeEnums.INCREASE_ORDER.getValue(),
                OrderTypeEnums.CONTRACT_ORDER.getValue())
            .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
        );
        if (CollUtil.isNotEmpty(list)) {
            return BeanCollectionUtils.copyListProperties(list, UserOrderDTO::new, (a, b) -> {
                b.setOrderQuantity(a.getOrderQuantity().toString());
                b.setFreshMilkGiveQuantity(a.getFreshMilkGiveQuantity().toString());
                b.setLongMilkGiveQuantity(a.getLongMilkGiveQuantity().toString());
            });
        }
        return null;
    }

    @Override
    public Map<Long, Date> queryXcxGiveOrderByCustomerId(List<Long> customerIds, LocalDate localDate) {
        List<CxrUserOrder> userOrders = cxrUserOrderMapper.selectList(new LambdaQueryWrapper<CxrUserOrder>()
            .select(CxrUserOrder::getCustomerId, CxrUserOrder::getOrderDate)
            .in(CxrUserOrder::getCustomerId, customerIds)
            .ge(CxrUserOrder::getOrderDate, localDate.minusMonths(1))
            .lt(CxrUserOrder::getOrderDate, localDate)
            .and(w -> w.eq(CxrUserOrder::getPayStatus, PayStatusEnums.PAY_SUCCEEDED.getValue())
                .or()
                .eq(CxrUserOrder::getAuditStatus, AuditStatusEnums.Audit.code()))
            .eq(CxrUserOrder::getOrderType, OrderTypeEnums.GIVE_ORDER.getValue())
            .eq(CxrUserOrder::getCreateByType, DeviceType.XCX.getDevice())
            .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.not_deleted));
        if (CollectionUtil.isNotEmpty(userOrders)) {
            return userOrders.stream().collect(Collectors.toMap(CxrUserOrder::getCustomerId, CxrUserOrder::getOrderDate,
                (a, b) -> a.after(b) ? a : b));
        }
        return new HashMap<>();
    }

    @Override
    public void orderRefund(long orderId) {
        CxrUserReturnOrder returnOrder = returnOrderService.getByOrderId(orderId);
        Long payOrderId = returnOrder.getOrderId();
        CxrUserOrder order = cxrUserOrderService.getById(payOrderId);
        if (payService.orderRefund(order) == PayConstant.REFUND_SUCCESS) {
            cxrUserOrderMapper.update(null,
                new LambdaUpdateWrapper<CxrUserOrder>().set(CxrUserOrder::getRefundSuccessFlag, true)
                    .eq(CxrUserOrder::getId, returnOrder.getUserOrderId()));

            Long afterSalesId = returnOrder.getAfterSalesId();
            cxrOrderAfterSaleMapper.update(null, new LambdaUpdateWrapper<CxrOrderAfterSale>()
                .set(CxrOrderAfterSale::getSaleStatus, SaleStatus.COMPLETED.getValue())
                .set(CxrOrderAfterSale::getReturnDate, new Date())
                .set(CxrOrderAfterSale::getReturnAmount, order.getAmount())
                .set(CxrOrderAfterSale::getReturnStatus, RefundStatusEnums.REFUND_FINISH.getValue())
                .eq(CxrOrderAfterSale::getId, afterSalesId));

            cxrUserOrderCommonService.updateMallOrderStatus(order.getOrderNo());
        }
    }

    @Override
    public Long returnOrderAdd(Long id) {
        return cxrUserReturnOrderService.returnOrderAdd(id);
    }

    public TikTokReturnOrderDTO tiktokOrderRefund(String orderNo) {
        return cxrUserReturnOrderService.tiktokOrderRefund(orderNo);
    }

    @Override
    public Boolean returnGiveOrderAddAndAudit(Long id) {
        return cxrUserReturnOrderService.returnGiveOrderAddAndAudit(id);
    }

    @Override
    public Boolean returnGiveOrderAddAndAudit(Long id, Boolean refundActive) {
        return cxrUserReturnOrderService.returnGiveOrderAddAndAudit(id, refundActive);
    }

    @Override
    public Long cancelOrderAfterSale(Long afterSaleId) {

        CxrUserReturnOrder afterSale = returnOrderService.getByReturnAfterSale(afterSaleId);

        Long orderId = afterSale.getUserOrderId();
//        CxrUserReturnOrder returnOrder = returnOrderService.getByOrderId(orderId);
//        Long payOrderId = returnOrder.getUserOrderId();
        CxrUserOrder order = cxrUserOrderService.getById(orderId);
        if (NumberUtil.equals(order.getAuditStatus(), AuditStatusEnums.Audit.code())) {
            return null;
        }

        boolean update = cxrUserOrderService.update(null, new LambdaUpdateWrapper<CxrUserOrder>()
            .eq(CxrUserOrder::getId, orderId)
            .set(CxrUserOrder::getDeleteStatus, DeleteStatus.DELETED.getValue())
            .set(CxrUserOrder::getDeleteTime, new Date())
        );

        if (update && ObjectUtil.isNotEmpty(afterSale)) {
            cxrUserReturnOrderMapper.update(null, new LambdaUpdateWrapper<CxrUserReturnOrder>()
                .eq(CxrUserReturnOrder::getId, afterSale.getId())
                .set(CxrUserReturnOrder::getDeleteStatus, DeleteStatus.DELETED.getValue())
                .set(CxrUserReturnOrder::getDeleteTime, new Date())
            );
        }

        return orderId;
    }

    @Override
    public void transferOrderAuditSuccessCustomerMessage(Long id) {
        remoteMilkTransferMessageService.milkTransferResultSendToCustomer(id, MilkTransferMsgType.SUCCESS);
    }


    @Override
    public void transferOrderAuditSuccessEmployeeMessage(Long id) {
        CxrServiceWorkOrder workOrder = cxrServiceWorkOrderMapper.selectById(id);

        //转出站点和销售代理
        Long siteId = workOrder.getSiteId();
        Long employeeId = workOrder.getEmployeeId();
        log.info("转单审核成功,发送消息给，转出，配送员：{}{}{}", id, siteId, employeeId);
//        remoteMilkTransferMessageService.businessMessageAdd(id,siteId,employeeId,BusinessMessageTypeEnum.service_work_order_milk_trans);

        String postType = JSONObject.toJSONString(
            Arrays.asList(PostType.GENERATION_DIRECTOR.getValue(), PostType.DIRECTOR.getValue()));

        CxrEmployee employee = remoteEmployeePostService.getByCxrSiteId(siteId, postType);
        if (employee != null) {
            log.info("转单审核成功,发送消息给，转出，站点主管：{}{}{}", id, siteId, employee.getId());
        }

        //销售代理和主管不是同一人才发送
//        if (employee != null && !employeeId.equals(employee.getId())){
//            remoteMilkTransferMessageService.businessMessageAdd(id,siteId,employee.getId(),BusinessMessageTypeEnum.service_work_order_milk_trans);
//        }

        //转入站点和销售代理
        Long inSiteId = workOrder.getInSiteId();
        Long inCustomerId = workOrder.getInCustomerId();
        CxrCustomerAddress customerAddress = remoteCustomerAddressService.queryCustomerAddressDefault(inCustomerId);
        Long cxrEmployeeId = customerAddress.getCxrEmployeeId();
        log.info("转单审核成功,发送消息给，转入，配送员：{}{}{}", id, inSiteId, cxrEmployeeId);
//        if (!employeeId.equals(cxrEmployeeId)){
//            remoteMilkTransferMessageService.businessMessageAdd(id,inSiteId,cxrEmployeeId,BusinessMessageTypeEnum.service_work_order_milk_trans);
//        }

        employee = remoteEmployeePostService.getByCxrSiteId(inSiteId, postType);
        if (employee != null) {
            log.info("转单审核成功,发送消息给，转入，站点主管：{}{}{}", id, inSiteId, employee.getId());
        }


        //销售代理和主管不是同一人才发送
//        if (employee != null && !cxrEmployeeId.equals(employee.getId())){
//            remoteMilkTransferMessageService.businessMessageAdd(id,inSiteId,employee.getId(),BusinessMessageTypeEnum.service_work_order_milk_trans);
//        }
        remoteMilkTransferMessageService.businessMessageAdd(id, BusinessMessageTypeEnum.service_work_order_milk_trans);

    }

    @Override
    public List<UserReturnOrderVo> getUserReturnOrderByAfterSalesId(Long afterSalesId) {
        List<CxrUserReturnOrder> cxrUserReturnOrders = cxrUserReturnOrderMapper.selectList(
            new LambdaQueryWrapper<CxrUserReturnOrder>()
                .eq(CxrUserReturnOrder::getAfterSalesId, afterSalesId), null);
        List<UserReturnOrderVo> results = Lists.newArrayList();
        if (CollectionUtil.isEmpty(cxrUserReturnOrders)) {
            return results;
        }
        cxrUserReturnOrders.forEach(item -> {
            UserReturnOrderVo userReturnOrderVo = new UserReturnOrderVo();
            BeanUtils.copyProperties(item, userReturnOrderVo);
            results.add(userReturnOrderVo);
        });
        return results;
    }

    @Override
    public UserOrderListVo queryByCustomerId(Long id) {
        UserOrderListVo userOrderListVo = cxrUserOrderService.queryByCustomerId(id);
        return userOrderListVo;
    }

    @Override
    public Boolean orderXcxRetailAfterRefund(Long cxrOrderAfterSaleId) {
        return payService.orderXcxRetailAfterRefund(cxrOrderAfterSaleId);
    }


    @Override
    public Boolean isFristTransferMilk(String outCustomerPhone, String inCustomerPhone) {
        return transferMilkService.isFirstTransfer(outCustomerPhone, inCustomerPhone);
    }

    @Override
    public void queryAndUpdateWxRefundResult() {

        LambdaQueryWrapper<CxrPaymentRefundRecord> queryWrapperCount = Wrappers.lambdaQuery(
            CxrPaymentRefundRecord.class);
        // 查询申请中的微信退款记录
        queryWrapperCount.eq(CxrPaymentRefundRecord::getStatus, RefundStatusEnums.REFUND_IN.getValue()); // 1申请中
        queryWrapperCount.eq(CxrPaymentRefundRecord::getMerchantType, CxrPayConfigType.WX.getName()); // 退款记录类型为 微信

        long count = paymentRefundRecordService.count(queryWrapperCount);
        if (count > 0) {
            Integer pageSize = 50;
            int pageTotal = (int) (count % pageSize == 0 ? count / pageSize : count / pageSize + 1);
            PageQuery pageQuery = new PageQuery();
            pageQuery.setPageSize(pageSize);

            Long lastRecordId = 0L;
            for (int pageNum = 1; pageNum <= pageTotal; pageNum++) {

                LambdaQueryWrapper<CxrPaymentRefundRecord> lambdaQueryWrapper = Wrappers.lambdaQuery(
                    CxrPaymentRefundRecord.class);
                // 查询申请中的微信退款记录
                lambdaQueryWrapper.eq(CxrPaymentRefundRecord::getStatus,
                    RefundStatusEnums.REFUND_IN.getValue()); // 1申请中
                lambdaQueryWrapper.eq(CxrPaymentRefundRecord::getMerchantType,
                    CxrPayConfigType.WX.getName());// 退款记录类型为 微信

                lambdaQueryWrapper.gt(CxrPaymentRefundRecord::getId, lastRecordId);
                lambdaQueryWrapper.orderByAsc(CxrPaymentRefundRecord::getId);

                Page<CxrPaymentRefundRecord> page = paymentRefundRecordService.page(pageQuery.build(false),
                    lambdaQueryWrapper);
                List<CxrPaymentRefundRecord> records = page.getRecords();
                if (CollUtil.isNotEmpty(records)) {
                    List<String> refundNoList = page.getRecords().stream()
                        .filter(r -> StrUtil.isNotBlank(r.getRefundNo()))
                        .map(r -> {
                            String refundNo = r.getRefundNo();
                            return refundNo.substring("RN".length(), refundNo.length() - 6);
                        }).collect(Collectors.toList());

                    if (CollUtil.isNotEmpty(refundNoList)) {
                        mqUtil.sendSyncMessage(OrderConstant.MALL_DISTRIBUTE_ORDER_TOPIC,
                            OrderConstant.WX_MALL_DISTRIBUTION_AFTER_REFUND_RESULT_QUERY_TAG,
                            JSONUtil.toJsonStr(refundNoList));
                    }
                    lastRecordId = records.get(records.size() - 1).getId();
                }

            }

        }

    }

    @Override
    public void updatePaymentRefundRecord(String notifyResult) {
        if (StrUtil.isNotBlank(notifyResult)) {
            payService.refundSuccessDoUpdateInfo(notifyResult);
        }
    }

    @Override
    public Page<UserOrderDTO> queryUserOrderByCustomerId(Long customerId, long size, long current) {
        Page<CxrUserOrder> cxrUserOrderPage = cxrUserOrderService.queryUserOrderByCustomerId(customerId, size, current);
        Page<UserOrderDTO> userOrderDTOPage =
            new Page<>(cxrUserOrderPage.getCurrent(), cxrUserOrderPage.getSize(), cxrUserOrderPage.getTotal());

        List<CxrUserOrder> records = cxrUserOrderPage.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            List<UserOrderDTO> userOrderDTOS = BeanUtil.copyToList(records, UserOrderDTO.class);
            userOrderDTOPage.setRecords(userOrderDTOS);
        }
        return userOrderDTOPage;
    }

    @Override
    public void retrunOrderRefundActive(Long userOrderReturnID) {
        returnOrderRefundFactory.refundHandler(userOrderReturnID);
    }

    @Override
    public void wxReturnOrderRefundCallback(String content) {
        returnOrderRefundFactory.getHandler(PaymentPlatform.WX_PAY).callback(content);
    }

    @Override
    public Map<String, JSONObject> getMainOrderStatusList(Collection<String> giftOrderNoList,
                                                          Integer mainOrder_syncType) {
        Map<String, JSONObject> mainOrderStatusMap = Maps.newHashMap();
        giftOrderNoList.forEach(giftOrderNo -> {
            try {
                Set<String> parentOrderNos = Sets.newHashSet(giftOrderNo.split("-")[0]);
                MallOrderEntity mallOrderEntity = mallOrderMapper.selectOne(new LambdaQueryWrapper<MallOrderEntity>()
                    //                .eq(MallOrderEntity::getShippingStatus, 2)//发货状态 商品配送情况;1:未发货 2:已发货 3:已收货 4:退货
                    .in(MallOrderEntity::getParentId, parentOrderNos)
                    .eq(MallOrderEntity::getSyncType, mainOrder_syncType));//SYNC_TYPE：1.鲜奶同步 2.赠送鲜奶 3.赠送商品 4.零售订单
                if (Objects.nonNull(mallOrderEntity)) {
                    Integer shippingStatus = mallOrderEntity.getShippingStatus();
                    JSONObject mainOrder = new JSONObject();
                    mainOrder.put("shippingStatus", shippingStatus);
                    mainOrder.put("orderNo", mallOrderEntity.getId());
                    mainOrder.put("number", mallOrderEntity.getNumber());
                    mainOrder.put("orderStatus", mallOrderEntity.getOrderStatus());
                    mainOrderStatusMap.put(giftOrderNo, mainOrder);
                }
            } catch (Exception e) {
                log.info("\n礼品发货查询订单异常：{}", e);
            }
        });
        return mainOrderStatusMap;
    }

    @Override
    public void updateChannelCommission(CxrChannelCommissionConfigBo bo) {
        LocalDate firstDay = bo.getCreateTime().toLocalDate().with(TemporalAdjusters.firstDayOfMonth());
        boolean update = cxrUserOrderService.lambdaUpdate()
            .set(CxrUserOrder::getCommissionConfigId, bo.getId())
            .set(CxrUserOrder::getAccountingType, bo.getAccountingType())
            .set(CxrUserOrder::getPromotionCommission, bo.getPromotionCommissionFlag())
            .ge(CxrUserOrder::getOrderDate, firstDay)
            .lt(CxrUserOrder::getOrderDate, firstDay.plusMonths(1))
            .eq(CxrUserOrder::getChannel, bo.getChannel())
            .eq(CxrUserOrder::getProductType, bo.getProductType())
            .update();
        AbstractAssert.isTrue(!update, "修改失败了!!!");
    }

    @Override
    public BigDecimal countValidNewOrder(LocalDate now, Long cxrEmployeeId) {
        List<Short> types = new ArrayList<>();
        types.add(OrderTypeEnums.NEW_ORDER.getValue());
        List<String> strings =
            cxrUserOrderMapper.countValidNewOrder(
                now,
                cxrEmployeeId,
                types);

        BigDecimal total = getBigDecimal(cxrEmployeeId, strings);

        List<BigDecimal> list = new ArrayList<>();
        list.add(total);
        return list.get(0);
    }

    @Override
    public CustomerOrderTotal TotalOrderStatisticsUpgrade(CustomerOrderStatisticsBo bo) {
        return cxrUserOrderMapper.TotalOrderStatisticsUpgrade(bo);
    }

    @Override
    public IPage<CustomerOrderStatisticsVo> orderFixationStatistics(CustomerOrderStatisticsBo bo, Page<Object> build) {
        return cxrUserOrderMapper.orderFixationStatistics(bo, build);
    }

    @Override
    public CustomerOrderTotal TotalOrderFixationStatisticsUpgrade(CustomerOrderStatisticsBo bo) {
        return cxrUserOrderMapper.TotalOrderFixationStatisticsUpgrade(bo);
    }

    @Override
    public List<LocalDateTime> getMirrorImageTime() {
        List<CxrUserOrderMirrorImage> mirrorImages = orderMirrorImageMapper.selectList(new LambdaQueryWrapper<CxrUserOrderMirrorImage>()
            .select(CxrUserOrderMirrorImage::getMirrorImageTime)
            .groupBy(CxrUserOrderMirrorImage::getMirrorImageTime)
        );
        if (CollUtil.isNotEmpty(mirrorImages)) {
            return mirrorImages.stream().map(CxrUserOrderMirrorImage::getMirrorImageTime).distinct().collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public void userOrderMirrorImageJob(LocalDate date) {
        LocalDateTime lastDayOfPreviousMonth = LocalDateTime.now()
            .minusMonths(1)
            .with(TemporalAdjusters.lastDayOfMonth())
            .with(LocalTime.MAX);


        int dayOfMonth = date.getDayOfMonth();
        // 自动导出数据
        CustomerOrderStatisticsBo bo = new CustomerOrderStatisticsBo();
        bo.setHtmlName(StrUtil.format("客户订单统计-{}", LocalDateTime.now()));
        Date lastDayOfPreviousDate = Date.from(lastDayOfPreviousMonth.atZone(ZoneId.systemDefault()).toInstant());
        bo.setEndOrderDate(lastDayOfPreviousDate);
        LocalDateTime currentDate = LocalDateTime.now();

        switch (dayOfMonth) {
            case 3:
                // 导出
                caseWhereExport(bo, date, 1);
                //镜像数据
                orderMirrorImageMapper.orderMirrorImageSave(date.minusMonths(3).withDayOfMonth(1), lastDayOfPreviousMonth, currentDate);
                break;
            case 15:
                // 导出
                caseWhereExport(bo, date, 1);
                //镜像数据
                orderMirrorImageMapper.orderMirrorImageSave(date.minusMonths(4).withDayOfMonth(1), lastDayOfPreviousMonth, currentDate);
                break;
            default:
                log.info("---");
        }

    }

    private void caseWhereExport(CustomerOrderStatisticsBo bo, LocalDate date, Integer dayOfMonth) {
        bo.setStartOrderDate(date.minusMonths(dayOfMonth).withDayOfMonth(1));
        bo.setRetainStatus(RetainStatusEnums.retain.getValue());
        log.info(StrUtil.format("bo{}", JSONUtil.toJsonStr(bo)));
        remoteCustomerOrderStatisticsService.orderStatisticsExport(bo, null);
    }

}
