package com.ruoyi.business.base.dubbo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.business.api.domain.dto.PostsDto;
import com.ruoyi.business.api.domain.dto.SendTimesContentDto;
import com.ruoyi.business.base.api.domain.CxrCustomerAddress;
import com.ruoyi.business.base.api.domain.CxrEmployee;
import com.ruoyi.business.base.api.domain.CxrEmployeePost;
import com.ruoyi.business.base.api.domain.CxrSite;
import com.ruoyi.business.base.api.domain.json.BusinessAgent;
import com.ruoyi.business.base.api.dubbo.RemoteIncreaseOrderRulesMessageService;
import com.ruoyi.business.base.api.enums.OrderTypeEnums;
import com.ruoyi.business.base.customer.mapper.CxrCustomerMapper;
import com.ruoyi.business.base.customerAddress.config.CustomerDeliveryInquiryConfig;
import com.ruoyi.business.base.customerAddress.mapper.CxrCustomerAddressMapper;
import com.ruoyi.business.base.cxrActivity.execute.TaskTimeoutHelper;
import com.ruoyi.business.base.employee.mapper.CxrEmployeeMapper;
import com.ruoyi.business.base.employeePost.mapper.CxrEmployeePostMapper;
import com.ruoyi.business.base.orderAfterSale.domain.CxrUserOrder;
import com.ruoyi.business.base.orderAfterSale.mapper.CxrUserOrderMapper;
import com.ruoyi.business.base.site.mapper.CxrSiteMapper;
import com.ruoyi.common.core.enums.ChargeStatus;
import com.ruoyi.common.core.enums.DeleteStatus;
import com.ruoyi.common.core.enums.OccupationStatus;
import com.ruoyi.common.core.enums.PostType;
import com.ruoyi.common.rocketmq.constant.employee.IncreaseOrderRulesMessageConstant;
import com.ruoyi.common.rocketmq.utils.MqUtil;
import com.ruoyi.core.base.domain.CxrIncreaseOrderRulesMessagePush;
import com.ruoyi.core.base.domain.dto.CxrIncreaseOrderRulesMsgDTO;
import com.ruoyi.core.base.mapper.CxrIncreaseOrderRulesMessagePushMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.*;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteIncreaseOrderRulesMessageServiceImpl implements RemoteIncreaseOrderRulesMessageService {

    @Autowired
    private TaskTimeoutHelper taskTimeoutHelper;

    @Autowired
    private MqUtil mqUtil;

    private final CxrEmployeeMapper employeeMapper;

    private final CxrSiteMapper cxrSiteMapper;

    private final CxrEmployeePostMapper employeePostMapper;

    private final CxrUserOrderMapper cxrUserOrderMapper;

    private final CxrIncreaseOrderRulesMessagePushMapper cxrIncreaseOrderRulesMessagePushMapper;

    private final CustomerDeliveryInquiryConfig customerDeliveryInquiryConfig;

    private final CxrCustomerAddressMapper cxrCustomerAddressMapper;

    private final CxrCustomerMapper cxrCustomerMapper;

    @Override
    public void sampleIncreaseOrderNotification() {
        LocalDate now = LocalDate.now();
        CxrIncreaseOrderRulesMessagePush messagePush = cxrIncreaseOrderRulesMessagePushMapper.selectOne(new LambdaQueryWrapper<CxrIncreaseOrderRulesMessagePush>());
        if (ObjectUtil.isNotEmpty(messagePush)) {
            List<SendTimesContentDto> sendTimes = messagePush.getSendTimes();
            int dayOfMonth = now.getDayOfMonth();
            for (SendTimesContentDto sendTime : sendTimes) {
                log.info("当前时间{} 号---------->配置日期 {} ", dayOfMonth, sendTime.getTimeType());
                Integer timeDay = sendTime.getTimeType();
                // 内容匹配
                String content = sendTime.getContent();
//                if (ObjectUtil.equals(timeDay, dayOfMonth)) {
                LocalTime time = sendTime.getTime();
                long endMilli = milliDate(new Date());
                LocalDateTime dateTime = LocalDateTime.of(now, time);
                long startMilli = dateTime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
                taskTimeoutHelper.setTimeout(StrUtil.format("increase_order_notification_trigger#{}#{}", content, timeDay),
                    startMilli - endMilli,
                    endMilli);
                log.info(StrUtil.format("预警通知任务已创建 预计执行时间为{}", dateTime));
//                }
            }
        } else {
            log.info("未配置预警通知");
        }
    }


    @Override
    public void pushMessage(String content, Integer timeDay) {
        CxrIncreaseOrderRulesMessagePush messagePush = cxrIncreaseOrderRulesMessagePushMapper.selectOne(new LambdaQueryWrapper<CxrIncreaseOrderRulesMessagePush>());
        List<PostsDto> messagePushPosts = messagePush.getPosts();
        LocalDate firstTime = LocalDate.now();
        if (ObjectUtil.isNull(timeDay)) {
            timeDay = 0;
        }
        LocalDate today = firstTime.plusDays(-timeDay);
        List<CxrUserOrder> cxrUserOrders = cxrUserOrderMapper.selectList(new LambdaQueryWrapper<CxrUserOrder>()
            .ge(CxrUserOrder::getOrderDate, today.atStartOfDay())
            .lt(CxrUserOrder::getOrderDate, today.plusDays(1).atStartOfDay())
            .and(wrapper -> {
                wrapper.eq(CxrUserOrder::getOrderType, OrderTypeEnums.NEW_ORDER.getValue())
                    .or()
                    .eq(CxrUserOrder::getContractTypeTag, OrderTypeEnums.NEW_ORDER.getValue());
            })
            .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
        );
        log.info(StrUtil.format("today {}", today));
        log.info(StrUtil.format("cxrUserOrdersSize {}", cxrUserOrders.size()));
        log.info(StrUtil.format("messagePushPosts {}", messagePushPosts.size()));
        if (CollUtil.isNotEmpty(cxrUserOrders)) {

            Set<String> openIds = new HashSet<>();
            Set<CxrIncreaseOrderRulesMsgDTO> dTOS = new HashSet<>();

            for (CxrUserOrder cxrUserOrder : cxrUserOrders) {
                log.info(StrUtil.format("{}号订单{}", today, cxrUserOrder.getId()));
                //默认发给开单人
                List<BusinessAgent> businessAgent = JSONUtil.toList(cxrUserOrder.getBusinessAgent(),
                    BusinessAgent.class);
                List<Long> proxyIds = businessAgent.stream().map(BusinessAgent::getProxyId)
                    .collect(Collectors.toList());
                log.info("proxyIds:{}", proxyIds);

                CxrUserOrder increaseOrder = cxrUserOrderMapper.selectOne(new LambdaQueryWrapper<CxrUserOrder>()
                    .eq(CxrUserOrder::getCustomerId, cxrUserOrder.getCustomerId())
                    .eq(CxrUserOrder::getOrderType, OrderTypeEnums.INCREASE_ORDER.getValue())
                    .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                    .ge(CxrUserOrder::getOrderDate, today.atStartOfDay())
                    .le(CxrUserOrder::getOrderDate, today.plusDays(timeDay).atTime(LocalTime.MAX))
                    .last("limit 1")
                );

                if (ObjectUtil.isNotEmpty(increaseOrder)) {
                    log.info("{}号订单{}已经存在增单", today, cxrUserOrder.getId());
                    continue;
                }

                if ((ObjectUtil.isNotEmpty(cxrUserOrder.getPromotionalOrderFlag()) &&
                    ObjectUtil.equals(cxrUserOrder.getPromotionalOrderFlag(), true) || ObjectUtil.isNotEmpty(cxrUserOrder.getApprenticeOrderFlag()) &&
                    ObjectUtil.equals(cxrUserOrder.getApprenticeOrderFlag(), true))) {
                    List<CxrCustomerAddress> customerAddress = cxrCustomerAddressMapper.selectList(new LambdaQueryWrapper<CxrCustomerAddress>()
                        .select(CxrCustomerAddress::getCxrEmployeeId)
                        .eq(CxrCustomerAddress::getCxrCustomerId, cxrUserOrder.getCustomerId())
                        .eq(CxrCustomerAddress::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                    );
                    if (CollUtil.isNotEmpty(customerAddress)) {
                        List<Long> employeeIds = customerAddress.stream().map(CxrCustomerAddress::getCxrEmployeeId).collect(Collectors.toList());
                        log.info("促销单发给配送员{}", employeeIds);
                        proxyIds = employeeIds;
                    }
                }

                List<CxrEmployee> proxyCxrEmployees = employeeMapper.selectList(
                    new LambdaQueryWrapper<CxrEmployee>()
                        .in(CxrEmployee::getId, proxyIds)
                        .eq(CxrEmployee::getOccupationStatus, OccupationStatus.INDUCTION.getValue())
                        .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                );
                if (CollUtil.isEmpty(proxyCxrEmployees)) {
                    log.info("{}号订单{}没有业绩归属人忽略", today, cxrUserOrder.getId());
                    continue;
                }
                openIds.addAll(proxyCxrEmployees.stream().filter(cxrEmployee -> StrUtil.isNotBlank(cxrEmployee.getWxOpenid())).map(CxrEmployee::getWxOpenid)
                    .collect(Collectors.toList()));
                List<CxrIncreaseOrderRulesMsgDTO> proxyRulesMsgDTOS = proxyCxrEmployees.stream().filter(cxrEmployee -> StrUtil.isNotBlank(cxrEmployee.getWxOpenid())).map(x -> {
                    CxrIncreaseOrderRulesMsgDTO msgDTO = new CxrIncreaseOrderRulesMsgDTO();
                    msgDTO.setOrderId(cxrUserOrder.getId());
                    msgDTO.setContent(content);
                    msgDTO.setCustomerPhone(cxrUserOrder.getCustomerPhone());
                    msgDTO.setCustomerName(cxrUserOrder.getCustomerName());
                    if (ObjectUtil.isEmpty(msgDTO.getCustomerName())) {
                        msgDTO.setCustomerName(cxrCustomerMapper.getById(cxrUserOrder.getCustomerId()).getName());
                    }

                    msgDTO.setCustomerAddress(cxrUserOrder.getProvince() + cxrUserOrder.getCity() + cxrUserOrder.getArea() + cxrUserOrder.getCustomerAdress());
                    String customerAddress = msgDTO.getCustomerAddress().length() > 15 ? msgDTO.getCustomerAddress().substring(0, 15) + "..." : msgDTO.getCustomerAddress();
                    msgDTO.setCustomerAddress(customerAddress);


                    msgDTO.setOpenId(x.getWxOpenid());
                    return msgDTO;
                }).collect(Collectors.toList());
                dTOS.addAll(proxyRulesMsgDTOS);

                Set<Long> siteIds = proxyCxrEmployees.stream().map(CxrEmployee::getCxrSiteId)
                    .collect(Collectors.toSet());
                if (CollUtil.isEmpty(siteIds)) {
                    log.info("{}号订单{}没有业绩归属站点忽略", today, cxrUserOrder.getId());
                    continue;
                }
                List<CxrSite> cxrSites = cxrSiteMapper.selectBatchIds(siteIds);
                if (StrUtil.isNotBlank(customerDeliveryInquiryConfig.getIncreaseRegionIds())) {
                    List<String> includeRegionIds = Arrays.asList(customerDeliveryInquiryConfig.getIncreaseRegionIds().split(","));
                    cxrSites =
                        cxrSites.stream().filter(cxrSite -> includeRegionIds.contains(cxrSite.getCxrRootRegionId().toString())).collect(Collectors.toList());
                }
                if (CollUtil.isEmpty(cxrSites)) {
                    log.info("{}号订单{}没有业绩归属站点忽略", today, cxrUserOrder.getId());
                    continue;
                }
                Set<Long> reginIds = cxrSites.stream().map(CxrSite::getCxrRootRegionId).collect(Collectors.toSet());

                //职位判断
                for (PostsDto messagePushPost : messagePushPosts) {

                    String postId = messagePushPost.getPostId();
                    log.info(StrUtil.format("职位 ' {} ',执行----->", postId));

                    List<CxrEmployeePost> employeePosts = null;
                    if (Integer.valueOf(postId) > Integer.valueOf(PostType.DIRECTOR.getValue())) {
                        employeePosts = getEmployeePosts(reginIds, postId);
                    } else {
                        employeePosts = getEmployeePosts(siteIds, postId);
                    }
                    if (CollUtil.isNotEmpty(employeePosts)) {
                        Set<Long> duplicateEmployeeIds = employeePosts.stream().map(CxrEmployeePost::getCxrEmployeeId)
                            .collect(Collectors.toSet());
                        List<CxrEmployee> cxrEmployees = employeeMapper.selectList(
                            new LambdaQueryWrapper<CxrEmployee>()
                                .in(CxrEmployee::getId, duplicateEmployeeIds)
                                .eq(CxrEmployee::getOccupationStatus, OccupationStatus.INDUCTION.getValue())
                                .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                                .isNotNull(CxrEmployee::getWxOpenid)
                        );
                        if (CollUtil.isEmpty(cxrEmployees)) {
                            log.info("{}号订单{}没有对应职务的代理，忽略", today, cxrUserOrder.getId());
                            continue;
                        }
                        openIds.addAll(cxrEmployees.stream().map(CxrEmployee::getWxOpenid)
                            .collect(Collectors.toList()));
                        List<CxrIncreaseOrderRulesMsgDTO> rulesMsgDTOS = cxrEmployees.stream().map(x -> {
                            CxrIncreaseOrderRulesMsgDTO msgDTO = new CxrIncreaseOrderRulesMsgDTO();
                            msgDTO.setOrderId(cxrUserOrder.getId());
                            msgDTO.setContent(content);
                            msgDTO.setCustomerPhone(cxrUserOrder.getCustomerPhone());
                            msgDTO.setCustomerName(cxrUserOrder.getCustomerName());
                            if (ObjectUtil.isEmpty(msgDTO.getCustomerName())) {
                                msgDTO.setCustomerName(cxrCustomerMapper.getById(cxrUserOrder.getCustomerId()).getName());
                            }
                            msgDTO.setCustomerAddress(cxrUserOrder.getProvince() + cxrUserOrder.getCity() + cxrUserOrder.getArea() + cxrUserOrder.getCustomerAdress());
                            String customerAddress = msgDTO.getCustomerAddress().length() > 15 ? msgDTO.getCustomerAddress().substring(0, 15) + "..." : msgDTO.getCustomerAddress();
                            msgDTO.setCustomerAddress(customerAddress);


                            msgDTO.setOpenId(x.getWxOpenid());
                            return msgDTO;
                        }).collect(Collectors.toList());
                        dTOS.addAll(rulesMsgDTOS);
                    }

                }
            }

            log.info(StrUtil.format("推送人员 ' {} ',", openIds));
            sendMessage(dTOS);
        }

    }


    private long milliDate(Date time) {
        Instant instant = time.toInstant();
        LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        return dateTime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
    }

    private void sendMessage(Set<CxrIncreaseOrderRulesMsgDTO> dTOS) {

        Map<String, CxrIncreaseOrderRulesMsgDTO> dtoMap = dTOS.stream().map(x -> {
            String format = StrUtil.format("{}:{}", x.getOpenId(), x.getOrderId());
            String md5Value = MD5.create().digestHex(format);
            x.setUuid(md5Value);
            return x;
        }).collect(Collectors.toMap(
            CxrIncreaseOrderRulesMsgDTO::getUuid,
            x -> x,
            (existing, replacement) -> existing
        ));

        List<CxrIncreaseOrderRulesMsgDTO> listWithoutDuplicates = new ArrayList<>(dtoMap.values());

        log.info(StrUtil.format("推送人员 ' {} ',dTOS:{},listWithoutDuplicates:{}", dTOS, dTOS.size(), listWithoutDuplicates.size()));

        for (CxrIncreaseOrderRulesMsgDTO dTO : dTOS) {
            String dtoJson = JSONUtil.toJsonStr(dTO);
            log.info("推送人员 openId {} bo:{} ---> 发送end", dTO.getOpenId(), dtoJson);
            try {
                mqUtil.sendSyncMessage(
                    IncreaseOrderRulesMessageConstant.INCREASE_ORDER_MESSAGE_TOPIC,
                    IncreaseOrderRulesMessageConstant.INCREASE_ORDER_MESSAGE_TAG,
                    dtoJson
                );
            } catch (Exception e) {
                log.error("推送消息失败，openId: {}, error: {}", dTO.getOpenId(), e.getMessage(), e);
            }
        }


    }

    private List<CxrEmployeePost> getEmployeePosts(Set<Long> tagIds, String postId) {
        LambdaQueryWrapper<CxrEmployeePost> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CxrEmployeePost::getDeleteStatus, 0).eq(CxrEmployeePost::getChargeStatus,
            ChargeStatus.IN_PROGRESS.getValue());
        if (Integer.valueOf(postId) > Integer.valueOf(PostType.DIRECTOR.getValue())) {
            queryWrapper.in(CxrEmployeePost::getCxrRegionId, tagIds);
        } else {
            queryWrapper.in(CxrEmployeePost::getCxrSiteId, tagIds);
        }
        queryWrapper.apply("cxr_post_id -> '$.value' = {0} ", postId);
        return employeePostMapper.selectList(queryWrapper);
    }

}
