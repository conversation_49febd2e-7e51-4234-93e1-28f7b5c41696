package com.ruoyi.common.rocketmq.constant.customer;

public interface CxrCustomerConstant {

    /**
     * 客户  topic
     */
    String CUSTOMER_TOPIC = "customer_topic";

    /**
     * 客户 分组
     */
    String CUSTOMER_GROUP = "customer_group";


    /**
     * 客户同步
     */
    String YDS_CUSTOMER_SYNC_TAG = "yds_customer_sync_tag";
    String YDS_CUSTOMER_ADDRESS_SYNC_TAG = "yds_customer_address_sync_tag";
    String YDS_CUSTOMER_ADDRESS_BATCH_SYNC_TAG = "yds_customer_address_batch_sync_tag";

    String YDS_DISTRIBUTION_TRANSFER_MIDDLE_TAG = "yds_distribution_transfer_middle_tag";

    /**
     * 客户  topic
     */
    String YDS_CUSTOMER_TOPIC = "yds_customer_topic";

    String YDS_CUSTOMER_PAY_TAG = "yds_customer_pay_tag";

    String YDS_RETAIL_CUSTOMER_PAY_TAG = "yds_retail_customer_pay_tag";

    String CUSTOMER_BLOOM_FILTER_TAG = "customer_bloom_filter_tag";
}
