package com.ruoyi.business.base;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.business.api.domain.dto.ProductStatisticsTotalDTO;
import com.ruoyi.business.base.api.domain.*;
import com.ruoyi.business.base.api.domain.bo.CxrPostId;
import com.ruoyi.business.base.api.domain.external.train.Position;
import com.ruoyi.business.base.api.domain.external.train.TrainResponse;
import com.ruoyi.business.base.api.domain.external.train.User;
import com.ruoyi.business.base.api.domain.json.GoodsInfo;
import com.ruoyi.business.base.api.dubbo.RemoteEmployeeAchievementDetailService;
import com.ruoyi.business.base.api.dubbo.RemoteEmployeeService;
import com.ruoyi.business.base.employee.mapper.CxrEmployeeMapper;
import com.ruoyi.business.base.employee.service.ICxrEmployeeService;
import com.ruoyi.business.base.employeePost.service.ICxrEmployeePostService;
import com.ruoyi.business.base.saleProduct.service.ICxrSaleProductService;
import com.ruoyi.business.base.site.mapper.CxrSiteMapper;
import com.ruoyi.common.core.enums.DeleteStatus;
import com.ruoyi.common.core.enums.PostType;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.OkHttpUtils;
import com.ruoyi.common.core.web.domain.CxrBaseEntity;
import com.ruoyi.common.rocketmq.constant.biz.MqBizConst;
import com.ruoyi.common.rocketmq.utils.MqUtil;
import com.ruoyi.system.api.RemoteDictService;
import com.ruoyi.system.enums.DictTypeEnums;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = RuoYiBusinessBaseApplication.class)
public class ansyEmployeeTest {


    @DubboReference
    private RemoteDictService remoteDictService;
    @Autowired
    private CxrSiteMapper cxrSiteMapper;
    @Autowired
    private CxrEmployeeMapper baseMapper;

    @Autowired
    private ICxrEmployeeService cxrEmployeeService;

    @Autowired
    private MqUtil mqUtil;

    @Autowired
    private ICxrEmployeePostService iCxrEmployeePostService;

    @Autowired
    private RemoteEmployeeService remoteEmployeeService;

    @DubboReference
    private RemoteEmployeeAchievementDetailService remoteEmployeeAchievementDetailService;
    @Test
    public void test1() {
        String requestURI = "https://v4.21tb.com/open/v1/uc/user/syncUsersVer2.html";
        String sigin = sigin("/v1/uc/user/syncUsersVer2");
        String corpCode = "cxr";
        String appKey = "607D91B9F38D4C078DAD6618C157F4F1";

        List<CxrEmployee> employees = baseMapper.selectList(
            new LambdaQueryWrapper<CxrEmployee>().ge(CxrEmployee::getQuitTime, "2023-02-01"));

        System.out.println(employees);

        for (CxrEmployee cxrEmployee : employees) {

            CxrSite cxrSite = cxrSiteMapper.selectById(cxrEmployee.getCxrSiteId());
            List<User> userList = new ArrayList<>();
            User user = new User();
            //工号
            user.setEmployeeCode(cxrEmployee.getJobNumber());
            //企业id     配置中的
            user.setCorpCode(corpCode);
            //员工姓名
            user.setUserName(cxrEmployee.getName());
            String postName = PostType.COMMON_EMPLOYEE.getName();
            List<CxrEmployeePost> cxrEmployeePosts =
                iCxrEmployeePostService.getBaseMapper().selectList(Wrappers.lambdaQuery(CxrEmployeePost.class)
                    .eq(CxrEmployeePost::getCxrEmployeeId, cxrEmployee.getId()));

            if (CollUtil.isNotEmpty(cxrEmployeePosts)) {

                List<CxrPostId> cxrPostIdList =
                    cxrEmployeePosts.stream().map(CxrEmployeePost::getCxrPostId).collect(Collectors.toList());
//            log.info("cxrPostIdList{}",cxrPostIdList);
                cxrPostIdList.sort(Comparator.comparing(CxrPostId::getValue).reversed());
                postName = cxrPostIdList.get(0).getName();
//            log.info("postName{}",postName);
                if (postName.equals("普通职员")) {
                    postName = "销售代理";
                }
                syncPositionsVer(postName);
            }

            user.setPositionCode(postName);

            //手机号
            //user.setLoginName(cxrEmployee.getPhone());
            user.setLoginName(cxrEmployee.getJobNumber());

            user.setAccountStatus("FORBIDDEN");

            //部门编号
//          user.setOrganizeCode(cxrSite.getName());
            user.setOrganizeCode(cxrSite.getArea());
            user.setMobile(cxrEmployee.getPhone());
            user.setRank(convertLevel(cxrEmployee.getEmployeeLevelType()));
            userList.add(user);

            Map<String, String> map = new HashMap<>();
            map.put("users", JSONUtil.toJsonStr(userList));
            map.put("updatePassword", Boolean.FALSE + "");
            map.put("sign_", sigin);//签名
            map.put("appKey_", appKey);//key
            map.put("timestamp_", System.currentTimeMillis() + "");//时间 毫秒值

            //发送 请求
            OkHttpUtils.postMapSync(requestURI, map, new OkHttpUtils.OnOkHttpCallback() {

                @SneakyThrows
                @Override
                public void onFailure(IOException e) {
//                sendUpdateSyncExceptionExecutionToTrain(cxrEmployee);
                    throw new ServiceException("同步人员失败");
                }

                @SneakyThrows
                @Override
                public void onFailure(String msg) {
//                sendUpdateSyncExceptionExecutionToTrain(cxrEmployee);
                    throw new ServiceException("同步人员失败");
                }

                @SneakyThrows
                @Override
                public void onSuccessful(String json) {
                    TrainResponse trainResponse = JSONUtil.toBean(json, TrainResponse.class);
                    if (StrUtil.equals(trainResponse.getStatus(), "OK") && ObjectUtil.equals(trainResponse.getSuccess(),
                        Boolean.TRUE) && NumberUtil.equals(trainResponse.getSuccessCount(), 1)) {
//                    log.info("同步人员完成");
                    } else {
//                    sendUpdateSyncExceptionExecutionToTrain(cxrEmployee);
                        throw new ServiceException("同步人员失败");
                    }
                }
            });
        }

    }


    @Test
    public void test2() {
        CompletableFuture[] futures = new CompletableFuture[10];
        for (int i = 0; i <10 ; i++) {
            futures[i] = CompletableFuture.runAsync(() -> {
                for (int j = 0; j < 100; j++) {
                    cxrEmployeeService.detainEarnestMoney(123L,1624955810881765378L, new BigDecimal("100"));
                }
            });
        }
        CompletableFuture.allOf(futures).join();
    }

    private String convertLevel(String value) {
        String levelLabel = remoteDictService.getDictLabel(DictTypeEnums.EMPLOYEE_LEVEL_TYPE.getValue(), value, "");
        return levelLabel;
    }


    /**
     * 签名
     *
     * @param requestURI
     * @return
     */
    private String sigin(String requestURI) {
        String signText =
            "6F614241B1AE41459D0A3978A389C164" + "|" + requestURI + "|" + "6F614241B1AE41459D0A3978A389C164";
        System.out.println(signText);
        String s = MD5.create().digestHex(signText).toUpperCase();
        System.out.println(s);
        return s;
    }

    /**
     * 同步职位
     *
     * @param postName
     */
    private void syncPositionsVer(String postName) {
        String requestURI = "https://v4.21tb.com/open/v1/uc/position/syncPositionsVer2.html";
        String sigin = sigin("/v1/uc/position/syncPositionsVer2");
        String corpCode = "cxr";
        String appKey = "607D91B9F38D4C078DAD6618C157F4F1";

        List<Position> positionList = new ArrayList<>();
        Position position = new Position();
        position.setPositionCode(postName);
        position.setPositionName(postName);
        position.setCategoryCode(postName);
        position.setCategoryName(postName);
        position.setCorpCode(corpCode);

        positionList.add(position);
        Map<String, String> map = new HashMap<>();
        map.put("positions", JSONUtil.toJsonStr(positionList));
        map.put("sign_", sigin);
        map.put("appKey_", appKey);
        map.put("timestamp_", System.currentTimeMillis() + "");

        OkHttpUtils.postMapSync(requestURI, map, new OkHttpUtils.OnOkHttpCallback() {
            @SneakyThrows
            @Override
            public void onFailure(IOException e) {
                throw new ServiceException("同步岗位失败");
            }

            @SneakyThrows
            @Override
            public void onFailure(String msg) {
                throw new ServiceException("同步岗位失败");
            }

            @SneakyThrows
            @Override
            public void onSuccessful(String json) {
                TrainResponse trainResponse = JSONUtil.toBean(json, TrainResponse.class);
                if (StrUtil.equals(trainResponse.getStatus(), "OK") && ObjectUtil.equals(trainResponse.getSuccess(),
                    Boolean.TRUE) && NumberUtil.equals(trainResponse.getSuccessCount(), 1)) {

                } else {
//                    log.error("同步岗位失败信息:{}",json);
//                    throw new ServiceException("同步岗位失败");
                }
            }
        });


    }


    @Test
    public void demo02() {
        remoteEmployeeService.checkEmployeeAuthStatus();
    }

    @Test
    public void test() {
//        List<CxrEmployee> list = cxrEmployeeService.lambdaQuery().select(CxrEmployee::getId).list();
        LocalDate now = LocalDate.parse("2024-09-01");
//        for (CxrEmployee employee : list) {
            JSONObject entries = new JSONObject();
            entries.set("employeeId",1624955810881765378L).set("date", now);
            mqUtil.sendSyncMessage(MqBizConst.DIVIDE_COMPUTE_TOPIC,MqBizConst.DIVIDE_COMPUTE_ALL_TAG, entries.toString());
//        }
    }
    @Test
    public void test3() {
        List<CxrEmployee> list = cxrEmployeeService.lambdaQuery().select(CxrEmployee::getId).orderByDesc(CxrEmployee::getCreateTime).last("limit 100").list();
        LocalDate now = LocalDate.parse("2024-08-01");
        for (CxrEmployee employee : list) {
            JSONObject entries = new JSONObject();
            entries.set("employeeId",employee.getId()).set("date", now);
            mqUtil.sendSyncMessage(MqBizConst.DIVIDE_COMPUTE_TOPIC,MqBizConst.DIVIDE_COMPUTE_ALL_TAG, entries.toString());
        }
    }

    @Test
    public void test5() {
        JSONObject entries = new JSONObject();
        entries.set("employeeId",1624955810881765378L).set("date", LocalDate.parse("2024-09-01"));
        mqUtil.sendSyncMessage(MqBizConst.DIVIDE_COMPUTE_TOPIC,MqBizConst.DIVIDE_COMPUTE_ALL_TAG, entries.toString());
    }

    @Test
    public void test6() {

        remoteEmployeeAchievementDetailService.dailySiteAchievement(1742845007999057921L);
    }

    @Autowired
    private ICxrSaleProductService iCxrSaleProductService;

    @Test
    public void test7(){


//        [ProductStatisticsTotalDTO(statisticsTotal=-15, productId=1591249630820540419, type=null), ProductStatisticsTotalDTO(statisticsTotal=0, productId=1591251275692351491, type=null), ProductStatisticsTotalDTO(statisticsTotal=0, productId=1591250052880769025, type=null), ProductStatisticsTotalDTO(statisticsTotal=0, productId=1591250438224060417, type=null), ProductStatisticsTotalDTO(statisticsTotal=0, productId=1591252314986356739, type=null)]



//        List<Long> productId = Arrays.asList(1591249630820540419L,1591250052880769025L,1591250438224060417L);
        List<Long> productId = Arrays.asList(1591251275692351491L);
        List<CxrSaleProduct> cxrSaleProducts = iCxrSaleProductService.getBaseMapper().selectList(Wrappers.lambdaQuery(CxrSaleProduct.class).eq(CxrBaseEntity::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));
        Map<Long, CxrSaleProduct> saleProductMap = cxrSaleProducts.stream()
            .collect(Collectors.toMap(CxrSaleProduct::getId, Function.identity(), (v1, v2) -> v2));


        String s = "[{\"statisticsTotal\":28, \"productId\":1591249630820540419},\n" +
            "            {\"statisticsTotal\":0, \"productId\":1591251032938618881},\n" +
            "        {\"statisticsTotal\":6, \"productId\":1591251275692351491},\n" +
            "        {\"statisticsTotal\":5, \"productId\":1591250052880769025},\n" +
            "        {\"statisticsTotal\":80, \"productId\":1591251275692351491},\n" +
            "        {\"statisticsTotal\":17, \"productId\":1591252314986356739},\n" +
            "        {\"statisticsTotal\":223, \"productId\":1591249630820540419},\n" +
            "        {\"statisticsTotal\":71, \"productId\":1591250052880769025},\n" +
            "        {\"statisticsTotal\":57, \"productId\":1591250438224060417},\n" +
            "        { \"statisticsTotal\":8, \"productId\":1591250438224060417},\n" +
            "        {\"statisticsTotal\":1, \"productId\":1591251032938618881},\n" +
            "        {\"statisticsTotal\":84, \"productId\":1591252314986356739}]";
        JSONArray objects = JSONArray.parseArray(s);
        List<ProductStatisticsTotalDTO> productStatisticsTotals = objects.toList(ProductStatisticsTotalDTO.class);

        Map<Long, List<ProductStatisticsTotalDTO>> productStatisticsTotalsMap = productStatisticsTotals.stream().collect(Collectors.groupingBy(ProductStatisticsTotalDTO::getProductId));

        List<ProductStatisticsTotalDTO> temporaryAddAssessmentStatistics = new ArrayList<>();
        ProductStatisticsTotalDTO a2 = new ProductStatisticsTotalDTO();
        a2.setProductId(1591250052880769025L);
        a2.setStatisticsTotal(0L);
        temporaryAddAssessmentStatistics.add(a2);
        Map<Long, List<ProductStatisticsTotalDTO>> temporaryAddAssessmentStatisticsMap = temporaryAddAssessmentStatistics.stream().collect(Collectors.groupingBy(ProductStatisticsTotalDTO::getProductId));

        List<ProductStatisticsTotalDTO> productStatisticsTotalDTOS = new ArrayList<>();
        ProductStatisticsTotalDTO a3 = new ProductStatisticsTotalDTO();
        a3.setProductId(1591251032938618881L);
        a3.setStatisticsTotal(3L);
        productStatisticsTotalDTOS.add(a3);
        Map<Long, List<ProductStatisticsTotalDTO>> productStatisticsTotalDTOSMap = productStatisticsTotalDTOS.stream().collect(Collectors.groupingBy(ProductStatisticsTotalDTO::getProductId));

        List<GoodsInfo> goodsInfos = new ArrayList<>();

        for (Long aLong : productId) {
            GoodsInfo goodsInfo = new GoodsInfo();
            List<ProductStatisticsTotalDTO> productStatisticsTotalDTO = productStatisticsTotalsMap.get(aLong);
            List<ProductStatisticsTotalDTO> productStatisticsTotalDTO1 = temporaryAddAssessmentStatisticsMap.get(
                aLong);
            List<ProductStatisticsTotalDTO> productStatisticsTotalDTO2 = productStatisticsTotalDTOSMap.get(aLong);
            CxrSaleProduct cxrSaleProduct = saleProductMap.get(aLong);
            if (ObjectUtil.isNotEmpty(cxrSaleProduct)) {

                goodsInfo.setProductId(cxrSaleProduct.getId());
                goodsInfo.setProductName(cxrSaleProduct.getName());
                goodsInfo.setProductAlias(cxrSaleProduct.getProductAlias());
                goodsInfo.setQuantity(0L);
                if (CollUtil.isNotEmpty(productStatisticsTotalDTO)) {
                    goodsInfo.setQuantity(goodsInfo.getQuantity() + productStatisticsTotalDTO.stream()
                        .collect(Collectors.summingLong(ProductStatisticsTotalDTO::getStatisticsTotal)));
                }

                if (CollUtil.isNotEmpty(productStatisticsTotalDTO1)) {
                    goodsInfo.setQuantity(goodsInfo.getQuantity() + productStatisticsTotalDTO1.stream()
                        .collect(Collectors.summingLong(ProductStatisticsTotalDTO::getStatisticsTotal)));
                }

                if (CollUtil.isNotEmpty(productStatisticsTotalDTO2)) {
                    goodsInfo.setQuantity(goodsInfo.getQuantity() + productStatisticsTotalDTO2.stream()
                        .collect(Collectors.summingLong(ProductStatisticsTotalDTO::getStatisticsTotal)));
                }
                goodsInfos.add(goodsInfo);
            }
        }
        temporaryAddAssessmentStatisticsMap.forEach((key, value) -> {
            if (!productId.contains(key)) {
                CxrSaleProduct cxrSaleProduct = saleProductMap.get(key);
                if (cxrSaleProduct != null) {
                    GoodsInfo goodsInfo = new GoodsInfo();
                    goodsInfo.setProductId(cxrSaleProduct.getId());
                    goodsInfo.setProductName(cxrSaleProduct.getName());
                    goodsInfo.setProductAlias(cxrSaleProduct.getProductAlias());
                    goodsInfo.setQuantity(0L);
                    goodsInfo.setQuantity(goodsInfo.getQuantity() + value.stream()
                        .collect(Collectors.summingLong(ProductStatisticsTotalDTO::getStatisticsTotal)));
                    goodsInfos.add(goodsInfo);
                }
            }
        });


        Long totalQuantity = goodsInfos.stream().collect(Collectors.summingLong(GoodsInfo::getQuantity));
        CxrFreshMilkAssessment saveCxrFreshMilkAssessment = new CxrFreshMilkAssessment();

        saveCxrFreshMilkAssessment.setTotalQuantity(totalQuantity <= 0l ? 0l : totalQuantity);

        LocalDateTime now = LocalDateTime.now();
        saveCxrFreshMilkAssessment.setAssessmentTime(now);

        for (GoodsInfo goodsInfo : goodsInfos) {
            if (goodsInfo.getQuantity() <= 0l) {
                goodsInfo.setQuantity(0l);
            }
        }

        saveCxrFreshMilkAssessment.setAssessmentProductInfo(goodsInfos);
        System.out.printf(com.alibaba.fastjson.JSONObject.toJSONString(goodsInfos));
    }
}
