package com.ruoyi.business.base.customer.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerStockSumVo implements Serializable {


    @ApiModelProperty("鲜奶订购总数量")
    private Integer orderQuantity = 0;

    private Integer freshMilkGiveQuantity = 0;

    private Integer orderFreshMilkSentQuantity = 0;

    @ApiModelProperty("鲜奶已送数")
    private Integer freshMilkSentQuantity = 0;

    @ApiModelProperty("退款数量")
    private Integer freshMilkReturnQuantity = 0;

    //路条已送盒数
    @ApiModelProperty("路条已送盒数")
    private Integer roadWaySentTotal = 0;

    //客户兑换鲜奶数量
    @ApiModelProperty("客户兑换鲜奶数量")
    private Integer exchangeMilkTotal = 0;

    //转出数量
    @ApiModelProperty("转出数量")
    private Integer transferOutTotal = 0;

    //转入数量
    @ApiModelProperty("转入数量")
    private Integer transferInTotal = 0;

    @ApiModelProperty("未送总数")
    private Integer notSentTotal = 0;
    //剩余数量
    @ApiModelProperty("鲜奶未送盒数")
    private Integer totalSurplusQuantity = 0;

    private Integer lockStock = 0;

}
