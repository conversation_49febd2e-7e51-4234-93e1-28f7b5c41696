package com.ruoyi.common.excel.utils;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.ruoyi.common.core.domain.ExcelReportDTO;
import com.ruoyi.common.core.enums.RetainStatusEnums;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.excel.config.ExcelFileConfig;
import com.ruoyi.common.redis.utils.RedisUtils;
import com.ruoyi.common.rocketmq.constant.excelReport.ExcelReportConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;

import java.io.File;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.io.Writer;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Date;

@Slf4j
public class ExcelMqUtil {

    private static final ExcelFileConfig excelFileConfig = SpringUtil.getBean(ExcelFileConfig.class);

    private static final RocketMQTemplate rocketTemplate = SpringUtil.getBean(RocketMQTemplate.class);

    public static void lockReport(String lockKey) {
        if (RedisUtils.getCacheObject(lockKey) != null) {
            throw new ServiceException("当前报表正在导出，请前往列表页查看");
        }
        RedisUtils.setCacheObject(lockKey, 1, Duration.ofSeconds(excelFileConfig.getWaitTime()));
    }

    public static void unlockReport(String lockKey) {
        RedisUtils.deleteObject(lockKey);
    }


    /**
     * 基础目录前缀为 /cxr_cloud/download  + fileDir
     *
     * @param htmlName     页面名称
     * @param dataTotal    总数据量大小
     * @param createById   创建人Id
     * @param createByType 创建类型
     * @param createByName 创建人名字
     * @return
     */
    public static ExcelReportDTO excelSendRecord(String htmlName, Long dataTotal, Long createById, String createByType, String createByName) {

        if (dataTotal > excelFileConfig.getMaxData()) {
            throw new ServiceException(StrUtil.format("导出数据大于{}条", excelFileConfig.getMaxData()));
        }
        String key = createById + htmlName;


        // 1.创建目录
        String fileDirUniqueMark = IdUtil.fastSimpleUUID();
        String pathDir = StrUtil.format("{}/{}", excelFileConfig.getPrefixPath(), fileDirUniqueMark);
        File file = FileUtil.mkdir(pathDir);
        String absolutePath = file.getAbsolutePath();
        // 发送对象
        Date date = new Date();
        long time = date.getTime();
        String yearMothDay = DateUtil.format(date, DatePattern.PURE_DATE_PATTERN);
        String fileNameSuffix = StrUtil.format("{}{}", yearMothDay, StrUtil.subSufByLength(StrUtil.toString(time), 6));

        ExcelReportDTO excelReportDTo = new ExcelReportDTO();
        excelReportDTo.setLockKey(key);
        excelReportDTo.setHtmlName(htmlName);
        excelReportDTo.setFileDir(absolutePath);
        excelReportDTo.setFileNameSuffix(fileNameSuffix);
        excelReportDTo.setDataTotal(dataTotal);
        excelReportDTo.setCreateById(createById);
        excelReportDTo.setCreateByName(createByName);
        excelReportDTo.setType(0);
        excelReportDTo.setCreateByType(createByType);
        excelReportDTo.setCreateTime(LocalDateTime.now());
        excelReportDTo.setUniqueMark(fileDirUniqueMark);
        try {
            rocketTemplate.syncSendOrderly(StrUtil.format("{}:{}", ExcelReportConstant.EXCEL_REPORT_TOPIC,
                    ExcelReportConstant.EXCEL_REPORT_SAVE), JSONUtil.toJsonStr(excelReportDTo),
                fileDirUniqueMark);
            lockReport(key);
        } catch (Exception e) {
            unlockReport(key);
            log.error("创建excel导出记录发送失败", e);
            throw new ServiceException("点击导出太频繁，请稍后！");
        }
        return excelReportDTo;
    }


    /**
     * 发送增量数据
     *
     * @param fileDirUniqueMark
     * @param incrementData     增量数据
     */
    public static void excelSendIncrementData(String fileDirUniqueMark, long incrementData, Exception exception) {

        ExcelReportDTO excelReportDTo = new ExcelReportDTO();
        excelReportDTo.setUniqueMark(fileDirUniqueMark);
        excelReportDTo.setIncrementData(incrementData);
        if (ObjectUtil.isNotNull(exception)) {

            final Writer result = new StringWriter();
            final PrintWriter printWriter = new PrintWriter(result);
            exception.printStackTrace(printWriter);

            excelReportDTo.setIncrementData(0L);
            excelReportDTo.setErrMessage(result.toString().substring(0, 1024));
            excelReportDTo.setType(2);
        } else {
            excelReportDTo.setType(1);
        }

        try {
            rocketTemplate.syncSendOrderly(StrUtil.format("{}:{}", ExcelReportConstant.EXCEL_REPORT_TOPIC,
                    ExcelReportConstant.EXCEL_REPORT_INCREMENT), JSONUtil.toJsonStr(excelReportDTo),
                fileDirUniqueMark);
        } catch (Exception e) {
            log.error("创建excel增量数据导出记录发送失败", e);
            throw new ServiceException("创建增量数据导出记录失败");
        }
    }


    /**
     * 基础目录前缀为 /cxr_cloud/download  + fileDir
     *
     * @param htmlName     页面名称
     * @param dataTotal    总数据量大小
     * @param createById   创建人Id
     * @param createByType 创建类型
     * @param createByName 创建人名字
     * @return
     */
    public static ExcelReportDTO excelSendRecordPermanent(String htmlName, Long dataTotal, Long createById, String createByType, String createByName) {

        if (dataTotal > excelFileConfig.getMaxData()) {
            throw new ServiceException(StrUtil.format("导出数据大于{}条", excelFileConfig.getMaxData()));
        }
        String key = createById + htmlName;


        // 1.创建目录
        String fileDirUniqueMark = IdUtil.fastSimpleUUID();
        String pathDir = StrUtil.format("{}/{}", excelFileConfig.getPrefixPath(), fileDirUniqueMark);
        File file = FileUtil.mkdir(pathDir);
        String absolutePath = file.getAbsolutePath();
        // 发送对象
        Date date = new Date();
        long time = date.getTime();
        String yearMothDay = DateUtil.format(date, DatePattern.PURE_DATE_PATTERN);
        String fileNameSuffix = StrUtil.format("{}{}", yearMothDay, StrUtil.subSufByLength(StrUtil.toString(time), 6));

        ExcelReportDTO excelReportDTo = new ExcelReportDTO();
        excelReportDTo.setLockKey(key);
        excelReportDTo.setHtmlName(htmlName);
        excelReportDTo.setFileDir(absolutePath);
        excelReportDTo.setFileNameSuffix(fileNameSuffix);
        excelReportDTo.setDataTotal(dataTotal);
        excelReportDTo.setCreateById(createById);
        excelReportDTo.setCreateByName(createByName);
        excelReportDTo.setType(0);
        excelReportDTo.setCreateByType(createByType);
        excelReportDTo.setCreateTime(LocalDateTime.now());
        excelReportDTo.setUniqueMark(fileDirUniqueMark);
        excelReportDTo.setRetainStatus(RetainStatusEnums.retain.getValue());
        try {
            rocketTemplate.syncSendOrderly(StrUtil.format("{}:{}", ExcelReportConstant.EXCEL_REPORT_TOPIC,
                    ExcelReportConstant.EXCEL_REPORT_SAVE), JSONUtil.toJsonStr(excelReportDTo),
                fileDirUniqueMark);
            lockReport(key);
        } catch (Exception e) {
            unlockReport(key);
            log.error("创建excel导出记录发送失败", e);
            throw new ServiceException("点击导出太频繁，请稍后！");
        }
        return excelReportDTo;
    }

}
