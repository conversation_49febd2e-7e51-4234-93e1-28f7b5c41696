package com.ruoyi.business.base.dubbo;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.business.base.api.domain.bo.DistributorOrderBo;
import com.ruoyi.business.base.api.domain.vo.DistributorAppletOrderVo;
import com.ruoyi.business.base.api.dubbo.RemoteOrderService;
import com.ruoyi.business.base.api.model.CustomerMilkRefundOrderVo;
import com.ruoyi.business.base.api.model.CustomerOrderVo;
import com.ruoyi.business.base.retailOrder.service.CxrXcxRetailOrderService;
import com.ruoyi.core.base.mapper.CxrXcxRetailOrderMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@Slf4j
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteOrderServiceImpl implements RemoteOrderService {

    private final CxrXcxRetailOrderMapper xcxRetailOrderMapper;

    private final CxrXcxRetailOrderService CxrXcxRetailOrderService;

    @Override
    public List<CustomerOrderVo> getOrderBy(Collection<Long> customerIds, boolean isQueryCxrUserOrder, Collection<String> goodsIds, String beginTime, String endTime, Set<Integer> payStatusList) {
        return xcxRetailOrderMapper.getOrderBy(customerIds, isQueryCxrUserOrder, goodsIds, beginTime, endTime, payStatusList);
    }

    @Override
    public List<CustomerMilkRefundOrderVo> getMilkRefundOrderBy(Collection<Long> customerIds, String beginTime, String endTime, Set<Integer> orderTypes) {
        return xcxRetailOrderMapper.getMilkRefundOrderBy(customerIds, beginTime, endTime, orderTypes);
    }

    @Override
    public IPage<DistributorAppletOrderVo> distributorOrderPage(DistributorOrderBo bo) {
        return CxrXcxRetailOrderService.distributorOrderPage(bo);
    }

    @Override
    public DistributorAppletOrderVo distributorAppletOrderTotal(DistributorOrderBo bo) {
        return CxrXcxRetailOrderService.distributorAppletOrderTotal(bo);
    }

    @Override
    public void distributorAppletOrderExport(DistributorOrderBo bo) {
        CxrXcxRetailOrderService.distributorAppletOrderExport(bo);
    }
}
