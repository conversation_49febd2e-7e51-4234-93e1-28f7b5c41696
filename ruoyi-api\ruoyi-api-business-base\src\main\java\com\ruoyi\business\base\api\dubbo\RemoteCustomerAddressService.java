package com.ruoyi.business.base.api.dubbo;

import com.ruoyi.business.base.api.domain.*;
import com.ruoyi.business.base.api.domain.bo.CxrAddressBo;
import com.ruoyi.business.base.api.domain.bo.CxrCustomerAddressBo;
import com.ruoyi.business.base.api.domain.dto.CxrCustomerAddressDTO;
import com.ruoyi.business.base.api.domain.json.CustomerAddressMilkDistributionDTO;
import com.ruoyi.business.base.api.domain.json.CustomerAddressMilkDistributionInfo;
import com.ruoyi.business.base.api.domain.vo.CxrAddressHistoryVo;
import com.ruoyi.business.base.api.domain.vo.CxrAddressVo;
import com.ruoyi.business.base.api.model.CustomerDistributioninfoDTO;
import com.ruoyi.common.core.domain.TableDataInfo;

import java.time.LocalDate;
import java.util.List;

public interface RemoteCustomerAddressService {

    List<CxrCustomerAddress> cxrCustomerAddressList(Long customerId);

    List<CxrCustomerAddress> getCustomerAddressList(Long customerId);

    List<CxrCustomerAddressDTO> cxrCustomerAddressDTOList(Long customerId);

    List<CxrCustomerAddress> list(CxrCustomerAddress cxrCustomerAddress);

    TableDataInfo<CxrCustomer> pageWithQuarters(Object page, CxrCustomerAddressBo cxrCustomerAddress);

    /**
     * 查询客户地址信息最早的1条
     *
     * @param customerId
     * @return
     */
    CxrCustomerAddress queryCustomerAddress(Long customerId);

    CxrCustomerAddress queryCustomerAddressDefault(Long customerId);

    CxrCustomerAddress getCustomerAddressById(Long id);

    CxrCustomerAddress queryCustomerDefalutAddress(Long customerId);

    CxrCustomerAddress queryCustomerAddressByPhone(String phone);

    /**
     * 根据 传出员工id 传入员工id 和传入站点id 进行更新地址配送员
     *
     * @param outId
     * @param enterId
     * @param enterSiteId
     * @return
     */
    boolean updateAllEmployeeAdress(Long outId, Long enterId, Long enterSiteId);

    /**
     * 根据 员工id 获取客户地址id
     *
     * @param emId
     * @return
     */
    List<Long> selectByEmployeeId(Long emId);

    Long countByEmployeeId(Long emId);

    List<CxrCustomerAddress> findBySite(List<Long> addressId);

    void updateByid(CxrCustomerAddress address);

    void updateByCustomerId(Long customerId);

    void insertAddressHistory(CxrCustomerAddress cxrCustomerAddress);

    List<CxrAddressVo> getAddressids(CxrAddressBo bo);

    List<CxrAddressVo> checkmilkDistributionDetail(Long id, String del);

    int updateChangeMilk(CxrAddressBo bo);

    int updateAmChangeSendOrStop(CxrAddressBo bo);

    int updatePmChangeSendOrStop(CxrAddressBo bo);

    List<CxrCustomerAddress> queryGetByIds(CxrAddressBo bo);

    List<CxrCustomerAddress> getCustomerAddresses(Long id);

    /**
     * 获取客户配送信息
     *
     * @param phone
     * @return
     */
    List<CustomerDistributioninfoDTO> queryCustomerDistributioninfo(Long customerId);

    List<CxrAddressHistoryVo> getOneHistoryByAddressIds(List<Long> addressIds);

    void insertBatchAddressHistory(List<CxrCustomerAddress> customerAddressList);

    void threadWordUpdate(Integer page, List<CxrCustomerAddress> customerAddressList);

    CxrCustomerAddress selectByAddress(Long id, String adress);

    List<CxrCustomerAddress> selectHuiBoCustomerId(List<Long> ids);

    List<CxrCustomerAddress> queryCustomerAddressByIds(List<Long> addressIds);

    CxrCustomerChangeRecord checkCxrCustomerChangeRecord(Long customerAddressId);

    void deleteCxrCustomerChangeRecord(Long id);

    Boolean saveCxrCustomerChangeRecord(CxrCustomerChangeRecord customerChangeRecord);

    void updateChangeStatus(Long customerAddressId, int i);

    void customerChangeRecord(LocalDate localDate);

    CxrCustomerChangeRecord getLastCustomerChangeRecord(Long id);

    void checkUpdataChangeCustomer(Long id);

    boolean updateCustomerReceiverPhone(CxrAddressBo bo);

    Long selectCustomerSentNumberId(Long cxrCustomerId, String customerAddress);

    CxrCustomerChangeRecord selectCustomerChangeRecord(Long id);

    Long customerRecoedMilkDayDistributionTotal(CxrRoadWay cxrRoadWay);

    int updateAmChangeSendOrStopChangeRecord(CxrAddressBo bo);

    int updatePmChangeSendOrStopChangeCustomer(CxrAddressBo bo);

    boolean updateAddressSite(Long id, Long cxrSiteId);

    int updatebyIdGetLnglat(CxrCustomerAddress address);

    CxrCustomerAddress addGetLngLat(CxrCustomerAddress address);

    CxrEmployee getMatchSiteAndEmployeByAddr(Long siteId, Long customerId, String fullAddress);

    void updateCustomerAddressDelivery(LocalDate date, Long addressId);

    void updateCustomerAddressDelivery(CustomerAddressMilkDistributionInfo distributionInfo, Long addressId);

    CustomerAddressMilkDistributionDTO builderAddressDeliveryInfo(LocalDate startDeliveryDate, Long addressId);

    void updateAddressStock(Long addressId);

    boolean queryAddressExistRoadWay(Long addressId);

    Boolean customerSeparateCchangeAddressSave(CxrCustomerChangeRecord customerChangeRecord);

}
