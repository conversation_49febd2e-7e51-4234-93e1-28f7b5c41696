package com.ruoyi.business.dubbo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.ruoyi.business.api.dubbo.RemoteEmployeeTransfeService;
import com.ruoyi.business.cxrEmployeeDistributionTransfer.domain.bo.TransferMiddleMqBo;
import com.ruoyi.business.cxrEmployeeDistributionTransfer.service.IStaffCxrEmployeeDistributionTransferService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

@Slf4j
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteEmployeeTransfeServiceImpl implements RemoteEmployeeTransfeService {

    private final IStaffCxrEmployeeDistributionTransferService distributionTransferService;

    @Override
    public void distributionTransferMiddle(String jobStr) {
        TransferMiddleMqBo middleMqBo = BeanUtil.copyProperties(JSONUtil.parseObj(jobStr), TransferMiddleMqBo.class);
        distributionTransferService.distributionTransferMiddle(middleMqBo);
    }
}
