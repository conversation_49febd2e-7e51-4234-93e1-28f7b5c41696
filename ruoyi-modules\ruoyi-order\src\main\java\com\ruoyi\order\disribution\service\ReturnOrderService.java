package com.ruoyi.order.disribution.service;

import com.ruoyi.business.base.api.domain.vo.CustomerMilkHistoryVO;
import com.ruoyi.order.common.domain.bo.CxrUserReturnOrderBO;
import com.ruoyi.order.common.entity.CxrUserReturnOrder;
import com.ruoyi.order.disribution.domain.vo.OrderVO;

import java.lang.reflect.InvocationTargetException;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface ReturnOrderService {

    public void add(CxrUserReturnOrder cxrUserReturnOrder);

    public CustomerMilkHistoryVO queryHistory(Long customerId);

    public OrderVO detail(Long orderId) throws InvocationTargetException, IllegalAccessException;

    CxrUserReturnOrder getByOrderId(Long id);

    Map<Long, CxrUserReturnOrder> getRefundSuccessByOrderIds(Collection<Long> id);

    boolean edit(CxrUserReturnOrderBO bo);

    Integer updateReturnPhone(String phone, Long customerId, String folmerPhone);

    List<CxrUserReturnOrder> selectByCustomerId(Long customerId);


    void updateByReturnPhone(String newPhone, String folmerPhone, Long userId, String customerName);

    void updateRefundSuccessFlag(Long id);

    CxrUserReturnOrder getByReturnAfterSale(Long afterSaleId);


    List<CxrUserReturnOrder> queryOrderIds(List<Long> tikTokOrderIds);


    List<CxrUserReturnOrder> queryOrderIdList(Long id);

    CxrUserReturnOrder queryUserOrderId(Long id);

    boolean resubmitOrder(CxrUserReturnOrderBO bo);
}
