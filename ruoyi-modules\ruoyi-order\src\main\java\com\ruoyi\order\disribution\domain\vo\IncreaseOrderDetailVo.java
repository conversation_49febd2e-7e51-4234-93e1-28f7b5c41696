package com.ruoyi.order.disribution.domain.vo;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;


@Data
public class IncreaseOrderDetailVo implements Serializable {

    private String type;
    // 客户姓名
    private String customerName;
    //客户电话
    private String customerPhone;
    // 省市区+地址
    private String customerAddress;
    //备注
    private String remark;
    //订单日期
    private LocalDate orderDate;
    //鲜奶订购数量
    private Integer orderQuantity;
    //金额
    private BigDecimal amount;
    // 销售代理
    private String businessAgents;
    // 客户地址1
    private String customerAddressOne;
    //配送员1
    private String businessAgentsOne;
    // 客户地址2
    private String customerAddressTow;
    //配送员2
    private String businessAgentsTow;

}
