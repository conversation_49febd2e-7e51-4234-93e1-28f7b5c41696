package com.ruoyi.open.api.applet.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ruoyi.business.base.api.domain.AddressHistoryMqDoMain;
import com.ruoyi.business.base.api.domain.AlterRecord;
import com.ruoyi.business.base.api.domain.CxrCustomer;
import com.ruoyi.business.base.api.domain.CxrCustomerAddress;
import com.ruoyi.business.base.api.domain.CxrCustomerAddressMatchRecord;
import com.ruoyi.business.base.api.domain.CxrEmployee;
import com.ruoyi.business.base.api.domain.CxrSite;
import com.ruoyi.business.base.api.domain.bo.CxrCustomerAddressAppletBo;
import com.ruoyi.business.base.api.domain.dto.CustomerAddressUpdateDTO;
import com.ruoyi.business.base.api.domain.json.CustomerAddressMilkDistributionInfo;
import com.ruoyi.business.base.api.domain.json.MilkDistributionInfo;
import com.ruoyi.business.base.api.dubbo.RemoteCustomerAddressService;
import com.ruoyi.business.base.api.dubbo.RemoteCxrSaleProductService;
import com.ruoyi.business.base.api.enums.customer.CustomerAddressUpdateType;
import com.ruoyi.common.core.domain.LoginInfo;
import com.ruoyi.common.core.enums.DeleteStatus;
import com.ruoyi.common.core.enums.DeviceType;
import com.ruoyi.common.core.enums.DistributionStatus;
import com.ruoyi.common.core.enums.SysYesNo;
import com.ruoyi.common.core.enums.UserType;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.addr.Location;
import com.ruoyi.common.core.utils.addr.LocationUtils;
import com.ruoyi.common.core.validator.AbstractAssert;
import com.ruoyi.common.enums.AuditStatusEnums;
import com.ruoyi.common.rocketmq.calculate.CustomerAddressConstant;
import com.ruoyi.common.rocketmq.constant.customer.CxrCustomerConstant;
import com.ruoyi.common.rocketmq.utils.MqUtil;
import com.ruoyi.open.api.applet.common.domain.CxrUserOrder;
import com.ruoyi.open.api.applet.common.domain.vo.CxrCustomerAddressVo;
import com.ruoyi.open.api.applet.common.domain.vo.CxrSiteVo;
import com.ruoyi.open.api.applet.common.event.CustomerAddressMatchRecordEvent;
import com.ruoyi.open.api.applet.common.mapper.CxrCustomerAddressMapper;
import com.ruoyi.open.api.applet.common.mapper.CxrCustomerAddressMatchRecordMapper;
import com.ruoyi.open.api.applet.common.mapper.CxrCustomerMapper;
import com.ruoyi.open.api.applet.common.mapper.CxrEmployeeMapper;
import com.ruoyi.open.api.applet.common.mapper.CxrSiteMapper;
import com.ruoyi.open.api.applet.common.mapper.CxrUserOrderMapper;
import com.ruoyi.open.api.applet.common.mapper.SysAreaMapper;
import com.ruoyi.open.api.applet.mapper.CxrEmployeePostMapper;
import com.ruoyi.open.api.applet.mapper.CxrOrderDetermineTemplateMapper;
import com.ruoyi.open.api.applet.service.AppletCustomerAddressService;
import com.ruoyi.open.api.applet.service.NewOrderMatchManager;
import com.ruoyi.open.api.config.CxrForetasteVersionConfig;
import com.ruoyi.order.api.enums.PayStatusEnums;
import com.ruoyi.system.api.domain.SysArea;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@RefreshScope
@Service
@RequiredArgsConstructor
public class AppletCustomerAddressServiceImpl implements AppletCustomerAddressService {

    private final CxrCustomerAddressMapper cxrCustomerAddressMapper;
    private final CxrCustomerMapper cxrCustomerMapper;
    private final SysAreaMapper sysAreaMapper;
    private final CxrSiteMapper cxrSiteMapper;
    private final CxrEmployeeMapper cxrEmployeeMapper;
    private final CxrEmployeePostMapper cxrEmployeePostMapper;
    private final CxrCustomerAddressMatchRecordMapper cxrCustomerAddressMatchRecordMapper;
    private final CxrUserOrderMapper cxrUserOrderMapper;
    private final CxrOrderDetermineTemplateMapper cxrOrderDetermineTemplateMapper;
    private final NewOrderMatchManager newOrderMatchManager;

    @Autowired
    private CxrForetasteVersionConfig cxrForetasteConfig;
    @DubboReference
    private final RemoteCxrSaleProductService remoteCxrSaleProductService;

    @DubboReference
    private RemoteCustomerAddressService remoteCustomerAddressService;

    @Autowired
    private MqUtil mqUtil;

    /**
     * 默认搜索50公里范围
     */
    @Value("${baiduMap.lnglat.searchRange:50}")
    private BigDecimal searchRange;
    /**
     * 一经度维度默认111.195公里单位 地球半径：111195 计算的结果单位是度，需要乘111195（地球半径6371000*PI/180）是将值转化为米
     */
    @Value("${baiduMap.lnglat.trans_to_m_unit:111.195}")
    private BigDecimal trans_to_m_unit;

    public final ApplicationEventPublisher applicationEventPublisher;

    @Resource
    private LocationUtils locationUtils;

    /*    public CxrCustomerAddress queryCustomerAddress(CxrCustomerAddressAppletBo bo) {
            String orderNo = bo.getOrderNo();
            if (StringUtils.isNotBlank(orderNo)){
                return cxrCustomerAddressMapper.selectOne(Wrappers.<CxrCustomerAddress>lambdaQuery()
                    .eq(CxrCustomerAddress::getOrderNo, orderNo));
            }
            return null;
        }*/
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer addCustomerAddress(CxrCustomerAddressAppletBo bo) {
        CxrCustomerAddress cxrCustomerAddress = new CxrCustomerAddress();
        CxrCustomerAddress customerAddress = Objects.isNull(bo.getCxrCustomerId()) ? null
            : cxrCustomerAddressMapper.selectOne(new LambdaQueryWrapper<CxrCustomerAddress>()
            .eq(CxrCustomerAddress::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            .eq(CxrCustomerAddress::getDefalutAccountAddress, SysYesNo.YES.getValue())
            .eq(CxrCustomerAddress::getCxrCustomerId, bo.getCxrCustomerId()));
        if (ObjectUtil.isEmpty(customerAddress)) {
            cxrCustomerAddress.setDefalutAccountAddress(SysYesNo.YES.getValue());
        } else {
            cxrCustomerAddress.setDefalutAccountAddress(SysYesNo.NO.getValue());
        }

        cxrCustomerAddress.setCxrCustomerId(bo.getCxrCustomerId());
        cxrCustomerAddress.setArea(bo.getArea());
        cxrCustomerAddress.setCity(bo.getCity());
        cxrCustomerAddress.setProvice(bo.getProvice());
        cxrCustomerAddress.setDetailDistributionAddress(bo.getDetailDistributionAddress());
        cxrCustomerAddress.setCxrResidentialQuartersId(bo.getCxrResidentialQuartersId());
        cxrCustomerAddress.setReceiverPhone(bo.getReceiverPhone());
        cxrCustomerAddress.setReceiverName(bo.getReceiverName());
        cxrCustomerAddress.setDeleteStatus(DeleteStatus.NOT_DELETED.getValue());
        cxrCustomerAddress.setChangeStatus(DistributionStatus.NO_SEND.getValue());
        CxrCustomer cxrCustomer = cxrCustomerMapper.selectById(bo.getCxrCustomerId());
        if (ObjectUtil.isNotEmpty(cxrCustomer)) {
            if (cxrCustomer.getName().isEmpty()) {
                cxrCustomerMapper.update(null, new LambdaUpdateWrapper<CxrCustomer>()
                    .eq(CxrCustomer::getId, bo.getCxrCustomerId())
                    .set(CxrCustomer::getName, bo.getReceiverName()));
            }
            log.info("客户Customer{}", cxrCustomer);
            cxrCustomerAddress.setCreateBy(cxrCustomer.getId());
            cxrCustomerAddress.setCreateByName(cxrCustomer.getName());
        }
        cxrCustomerAddress.setCreateTime(new Date());
        String createByType = bo.getCreateByType();
        cxrCustomerAddress.setCreateByType(
            StringUtils.isNotEmpty(createByType) ? createByType : DeviceType.XCX.getDevice());
        cxrCustomerAddress.setSysDeptId(0L);
        cxrCustomerAddress.setSortNum(0L);
        BigDecimal longitude = new BigDecimal(bo.getLongitude()).setScale(6, RoundingMode.HALF_DOWN);
        BigDecimal latitude = new BigDecimal(bo.getLatitude()).setScale(6, RoundingMode.HALF_DOWN);
        cxrCustomerAddress.setLongitude(longitude);
        cxrCustomerAddress.setLatitude(latitude);

//        List<CxrSite> getScreenSites = cxrSiteMapper.selectList(new LambdaQueryWrapper<CxrSite>()
//            .eq(CxrSite::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
//            .between(CxrSite::getLatitudeValue, bo.getLatitude() - 1, bo.getLatitude() + 1)
//            .between(CxrSite::getAccuracyValue, bo.getLongitude() - 1, bo.getLongitude() + 1));
//        if (CollectionUtil.isNotEmpty(getScreenSites)) {
        bo.setBeginLongitude(bo.getLongitude() - 1);
        bo.setEndLongitude(bo.getLongitude() + 1);
        bo.setBeginLatitude(bo.getLatitude() - 1);
        bo.setEndLatitude(bo.getLatitude() + 1);
        CxrSiteVo cxrSite = cxrSiteMapper.getAddressDistance(null, bo);
        if (Objects.nonNull(cxrSite)) {
//            List<Long> siteIds = getScreenSites.stream().map(CxrSite::getId).collect(Collectors.toList());
//            CxrSiteVo cxrSite = cxrSiteMapper.getAddressDistance(siteIds, bo);
            if (ObjectUtil.isNotEmpty(cxrSite)) {
                Double withSiteDistance = cxrSite.getDistance();
                log.info("当前用户位置与最近站点[id={}]的配送距离：{}", cxrSite.getId(), withSiteDistance);
                if (ObjectUtil.isEmpty(cxrSite.getDistributionDistance())) {
                    cxrSite.setDistributionDistance(0);
                }
                bo.setNearSiteId(cxrSite.getId());
                bo.setNearSiteDistance(withSiteDistance * 1000);
//                eventMessage = this.setCustomerBelongSiteAndEmployee(cxrCustomerAddress, cxrSite.getId(), withSiteDistance*1000, longitude, latitude);
//                if (cxrSite.getDistance() <= cxrSite.getDistributionDistance()) {
//                    cxrCustomerAddress.setCxrSiteId(cxrSite.getId());
//                    //依据站点进行匹配配送员
//                    CxrCustomerAddress address = cxrCustomerAddressMapper.queryAddressAdjacentOtherAddress(
//                        cxrSite.getId(),
//                        BigDecimal.valueOf(bo.getLatitude()),
//                        BigDecimal.valueOf(bo.getLongitude()));
//                    if (ObjectUtil.isNotEmpty(address)) {
//                        cxrCustomerAddress.setCxrEmployeeId(address.getCxrEmployeeId());
//                    }
//                } else {
//                    log.info("返回的数据值2");
//                    return 2;
//                }
            } else {
                log.info("新用户[{}]地址匹配不到最近的站点信息", bo.getCxrCustomerId());
                return 2;
            }
        } else {
            log.info("此新用户[{}]地址经纬度[longitude={};latitude={}]查询匹配不到最近的站点信息",
                bo.getCxrCustomerId(), bo.getLongitude(), bo.getLatitude());
            return 2;
        }

        cxrCustomerAddress.setDistributionDistrictName(bo.getDistributionDistrictName());
        SysArea sysArea = sysAreaMapper.selectOne(new LambdaQueryWrapper<SysArea>()
            .eq(SysArea::getProvice, bo.getProvice())
            .eq(SysArea::getCity, bo.getCity())
            .eq(SysArea::getArea, bo.getArea())
            .last("limit 1")
        );
        if (ObjectUtil.isNotEmpty(sysArea)) {
            cxrCustomerAddress.setSysAreaId(sysArea.getId());
        } else {
            cxrCustomerAddress.setSysAreaId(0L);
        }
        cxrCustomerAddress.setAmDistributionStatus(SysYesNo.NO.getValue());
        cxrCustomerAddress.setAmDistributionSuspendStartTime(LocalDate.now());
        cxrCustomerAddress.setPmDistributionSuspendStartTime(LocalDate.now());
        cxrCustomerAddress.setPmDistributionStatus(SysYesNo.NO.getValue());
        CustomerAddressMilkDistributionInfo info = getCustomerAddressMilkDistributionInfo();

        cxrCustomerAddress.setAmDistributionInfo(info);
        cxrCustomerAddress.setPmDistributionInfo(info);
        cxrCustomerAddress.setIsShowAmDistribution(SysYesNo.YES.getValue());
        cxrCustomerAddress.setIsShowPmDistribution(SysYesNo.YES.getValue());

        int count = cxrCustomerAddressMapper.insert(cxrCustomerAddress);
        if (count < 1) {
            throw new ServiceException("新增客户地址失败了!");
        }
        bo.setCustomerAddressId(cxrCustomerAddress.getId());
        //保存历史
        AddressHistoryMqDoMain addressHistoryMqDoMain = new AddressHistoryMqDoMain();
        addressHistoryMqDoMain.setCxrCustomerAddressId(cxrCustomerAddress.getId());
        addressHistoryMqDoMain.setCxrCustomerId(cxrCustomerAddress.getCxrCustomerId());
        AlterRecord alterRecord = new AlterRecord();
        CustomerAddressUpdateDTO customerAddressUpdateDTO = new CustomerAddressUpdateDTO();
        customerAddressUpdateDTO.setUpdateType(CustomerAddressUpdateType.ADD.getValue());
        customerAddressUpdateDTO.setAddress(
            Convert.toStr(cxrCustomerAddress.getProvice(), "") + Convert.toStr(cxrCustomerAddress.getCity(), "") + Convert.toStr(cxrCustomerAddress.getArea(), "")
                + Convert.toStr(cxrCustomerAddress.getDetailDistributionAddress(), ""));
        alterRecord.setCustomerAddressUpdate(customerAddressUpdateDTO);
        addressHistoryMqDoMain.setAlterRecord(alterRecord);
        LoginInfo loginInfo = new LoginInfo();
        loginInfo.setUserId(cxrCustomerAddress.getId());
        loginInfo.setUserName(bo.getReceiverName());
        loginInfo.setUserType(UserType.applet_user);
        addressHistoryMqDoMain.setLoginInfo(loginInfo);
        addressHistoryMqDoMain.setUuid(java.util.UUID.randomUUID().toString());
        mqUtil.sendSyncMessage(CustomerAddressConstant.CUSTOMER_ADDRESS_TOPIC,
            CustomerAddressConstant.CUSTOMER_ADDRESS_HISTORY_TAG, JSONUtil.toJsonStr(addressHistoryMqDoMain));
        return count;
    }


    @Override
    public CxrCustomerAddress addAddress(CxrCustomerAddressAppletBo bo) {
        CxrCustomerAddress cxrCustomerAddress = new CxrCustomerAddress();

        cxrCustomerAddress.setDefalutAccountAddress(SysYesNo.YES.getValue());
        cxrCustomerAddress.setCxrCustomerId(bo.getCxrCustomerId());
        cxrCustomerAddress.setArea(bo.getArea());
        cxrCustomerAddress.setCity(bo.getCity());
        cxrCustomerAddress.setProvice(bo.getProvice());
        cxrCustomerAddress.setDetailDistributionAddress(bo.getDetailDistributionAddress());
        cxrCustomerAddress.setCxrResidentialQuartersId(bo.getCxrResidentialQuartersId());
        cxrCustomerAddress.setReceiverPhone(bo.getReceiverPhone());
        cxrCustomerAddress.setReceiverName(bo.getReceiverName());
        cxrCustomerAddress.setDeleteStatus(DeleteStatus.NOT_DELETED.getValue());
        cxrCustomerAddress.setChangeStatus(DistributionStatus.NO_SEND.getValue());
        CxrCustomer cxrCustomer = cxrCustomerMapper.selectById(bo.getCxrCustomerId());
        if (ObjectUtil.isNotEmpty(cxrCustomer)) {
            log.info("客户Customer{}", cxrCustomer);
            cxrCustomerAddress.setCreateBy(cxrCustomer.getId());
            cxrCustomerAddress.setCreateByName(cxrCustomer.getName());
        }
        cxrCustomerAddress.setCreateTime(new Date());
        String createByType = bo.getCreateByType();
        cxrCustomerAddress.setCreateByType(
            StringUtils.isNotEmpty(createByType) ? createByType : DeviceType.XCX.getDevice());
        cxrCustomerAddress.setSysDeptId(0L);
        cxrCustomerAddress.setSortNum(0L);
        BigDecimal longitude = new BigDecimal(bo.getLongitude()).setScale(6, RoundingMode.HALF_DOWN);
        BigDecimal latitude = new BigDecimal(bo.getLatitude()).setScale(6, RoundingMode.HALF_DOWN);
        cxrCustomerAddress.setLongitude(longitude);
        cxrCustomerAddress.setLatitude(latitude);

        bo.setBeginLongitude(bo.getLongitude() - 1);
        bo.setEndLongitude(bo.getLongitude() + 1);
        bo.setBeginLatitude(bo.getLatitude() - 1);
        bo.setEndLatitude(bo.getLatitude() + 1);
        CxrSiteVo cxrSite = cxrSiteMapper.getAddressDistance(null, bo);
        if (Objects.nonNull(cxrSite)) {
            Double withSiteDistance = cxrSite.getDistance();
            log.info("当前用户位置与最近站点[id={}]的配送距离：{}", cxrSite.getId(), withSiteDistance);
            if (ObjectUtil.isEmpty(cxrSite.getDistributionDistance())) {
                cxrSite.setDistributionDistance(0);
            }
            bo.setNearSiteId(cxrSite.getId());
            bo.setNearSiteDistance(withSiteDistance * 1000);
        } else {
            log.info("此新用户[{}]地址经纬度[longitude={};latitude={}]查询匹配不到最近的站点信息",
                bo.getCxrCustomerId(), bo.getLongitude(), bo.getLatitude());
            throw new ServiceException("新增客户地址失败了,没有可以配送的站点!");
        }

        cxrCustomerAddress.setDistributionDistrictName(bo.getDistributionDistrictName());
        SysArea sysArea = sysAreaMapper.selectOne(new LambdaQueryWrapper<SysArea>()
            .eq(SysArea::getProvice, bo.getProvice())
            .eq(SysArea::getCity, bo.getCity())
            .eq(SysArea::getArea, bo.getArea())
            .last("limit 1")
        );
        if (ObjectUtil.isNotEmpty(sysArea)) {
            cxrCustomerAddress.setSysAreaId(sysArea.getId());
        } else {
            cxrCustomerAddress.setSysAreaId(0L);
        }
        cxrCustomerAddress.setAmDistributionStatus(SysYesNo.NO.getValue());
        cxrCustomerAddress.setAmDistributionSuspendStartTime(LocalDate.now());
        cxrCustomerAddress.setPmDistributionSuspendStartTime(LocalDate.now());
        cxrCustomerAddress.setPmDistributionStatus(SysYesNo.NO.getValue());
        CustomerAddressMilkDistributionInfo info = getCustomerAddressMilkDistributionInfo();

        cxrCustomerAddress.setAmDistributionInfo(info);
        cxrCustomerAddress.setPmDistributionInfo(info);
        cxrCustomerAddress.setIsShowAmDistribution(SysYesNo.YES.getValue());
        cxrCustomerAddress.setIsShowPmDistribution(SysYesNo.YES.getValue());

//        calculateCustomerSiteAndEmployee(cxrCustomerAddress, bo.getNearSiteId(), bo.getNearSiteDistance(), longitude, latitude);
        CxrEmployee empForCreateAddr = newOrderMatchManager.getMatchSiteAndEmpForCreateAddr(
            cxrCustomer.getPhone(),
            cxrCustomerAddress.getProvice() + cxrCustomerAddress.getCity() + cxrCustomerAddress.getArea()
                + cxrCustomerAddress.getDetailDistributionAddress());
        if(empForCreateAddr!=null){
            cxrCustomerAddress.setCxrSiteId(empForCreateAddr.getCxrSiteId());
            cxrCustomerAddress.setCxrEmployeeId(empForCreateAddr.getId());
        }
        AbstractAssert.isNull(cxrCustomerAddress.getCxrEmployeeId(), "新增客户地址找不到配送员!");
        AbstractAssert.isTrue(cxrCustomerAddressMapper.insert(cxrCustomerAddress) == 0, "新增客户地址失败了!");
        if(empForCreateAddr!=null&&empForCreateAddr.getCxrOrderDetermineTemplate()!=null){
            empForCreateAddr.getCxrOrderDetermineTemplate().setSourceId(cxrCustomerAddress.getId());
            cxrOrderDetermineTemplateMapper.updateById(empForCreateAddr.getCxrOrderDetermineTemplate());
        }
        //保存历史
        AddressHistoryMqDoMain addressHistoryMqDoMain = new AddressHistoryMqDoMain();
        addressHistoryMqDoMain.setCxrCustomerAddressId(cxrCustomerAddress.getId());
        addressHistoryMqDoMain.setCxrCustomerId(cxrCustomerAddress.getCxrCustomerId());
        AlterRecord alterRecord = new AlterRecord();
        CustomerAddressUpdateDTO customerAddressUpdateDTO = new CustomerAddressUpdateDTO();
        customerAddressUpdateDTO.setUpdateType(CustomerAddressUpdateType.ADD.getValue());
        customerAddressUpdateDTO.setAddress(
            Convert.toStr(cxrCustomerAddress.getProvice(), "") + Convert.toStr(cxrCustomerAddress.getCity(), "") + Convert.toStr(cxrCustomerAddress.getArea(), "")
                + Convert.toStr(cxrCustomerAddress.getDetailDistributionAddress(), ""));
        alterRecord.setCustomerAddressUpdate(customerAddressUpdateDTO);
        addressHistoryMqDoMain.setAlterRecord(alterRecord);
        LoginInfo loginInfo = new LoginInfo();
        loginInfo.setUserId(bo.getCxrCustomerId());
        loginInfo.setUserName(bo.getReceiverName());
        loginInfo.setUserType(UserType.tiktok_user);
        addressHistoryMqDoMain.setLoginInfo(loginInfo);
        addressHistoryMqDoMain.setUuid(java.util.UUID.randomUUID().toString());
        mqUtil.sendDelayMessage(CustomerAddressConstant.CUSTOMER_ADDRESS_TOPIC,
            CustomerAddressConstant.CUSTOMER_ADDRESS_HISTORY_TAG, JSONUtil.toJsonStr(addressHistoryMqDoMain),10);
        return cxrCustomerAddressMapper.selectById(cxrCustomerAddress.getId());
    }

    Long checkAddressExist(CxrCustomerAddressAppletBo bo) {

        String createByType = bo.getCreateByType();
        if (DeviceType.TIKTOK_APPLET.getDevice().equals(createByType)) {
            Long customerId = bo.getCxrCustomerId();

            List<CxrCustomerAddress> addresses = cxrCustomerAddressMapper.selectList(Wrappers.<CxrCustomerAddress>lambdaQuery()
                .eq(CxrCustomerAddress::getCxrCustomerId, customerId)
                .eq(CxrCustomerAddress::getCreateByType, createByType)
                .eq(CxrCustomerAddress::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));

            String addressStr = StrUtil.format("{} {} {} {}", bo.getProvice(), bo.getCity(), bo.getArea(), bo.getDetailDistributionAddress());
            for (CxrCustomerAddress address : addresses) {
                String format = StrUtil.format("{} {} {} {}", address.getProvice(), address.getCity(), address.getArea(), address.getDetailDistributionAddress());
                if (addressStr.trim().equals(format.trim())) {
                    return address.getId();
                }
            }
        }
        return null;
    }

    @NotNull
    private CustomerAddressMilkDistributionInfo getCustomerAddressMilkDistributionInfo() {
        List<MilkDistributionInfo> milkDistributionInfos = remoteCxrSaleProductService
            .queryListGoodInfoProcuctAllForYXN();

        CustomerAddressMilkDistributionInfo info = new CustomerAddressMilkDistributionInfo();
        info.setMonday(milkDistributionInfos);
        info.setTuesday(milkDistributionInfos);
        info.setWednesday(milkDistributionInfos);
        info.setThursday(milkDistributionInfos);
        info.setFriday(milkDistributionInfos);
        info.setSaturday(milkDistributionInfos);
        return info;
    }
//bak
//    /**
//     * 计算最近站点和最近老客户距离比较，走不同匹配规则获取所属站点和配送员，并更新
//     *
//     * @param cxrCustomerAddress
//     * @param nearnestSiteId     下单用户距离最近的站点ID
//     * @param withSiteDistance   下单用户距离最近的站点距离
//     * @param longitude
//     * @param latitude
//     */
//    @Override
//    public CompletableFuture<Void> calculateUpdateCustomerSiteAndEmployee(CxrCustomerAddress cxrCustomerAddress, Long nearnestSiteId,
//                                                                          Double withSiteDistance, BigDecimal longitude, BigDecimal latitude) {
//        return CompletableFuture.runAsync(new Runnable() {
//            @Override
//            public void run() {
////                BigDecimal lnglatFactor = BigDecimal.ONE;
//                BigDecimal lnglatFactor = searchRange.divide(trans_to_m_unit, 1, RoundingMode.HALF_UP);
//                BigDecimal beginLongitude = longitude.subtract(lnglatFactor).setScale(6, RoundingMode.HALF_DOWN);
//                BigDecimal endLongitude = longitude.add(lnglatFactor).setScale(6, RoundingMode.HALF_DOWN);
//                BigDecimal beginLatitude = latitude.subtract(lnglatFactor).setScale(6, RoundingMode.HALF_DOWN);
//                BigDecimal endLatitude = latitude.add(lnglatFactor).setScale(6, RoundingMode.HALF_DOWN);
//                Long sysAreaId = cxrCustomerAddress.getSysAreaId();
//                String areaName = cxrCustomerAddress.getArea();
//                Long orderCustomerId = cxrCustomerAddress.getCxrCustomerId();
//                String receiverPhone = cxrCustomerAddress.getReceiverPhone();//下单用户手机号
//                if (Objects.isNull(orderCustomerId) && Objects.nonNull(receiverPhone)) {
//                    CxrCustomer cxrCustomer = cxrCustomerMapper.selectOne(
//                        new LambdaQueryWrapper<CxrCustomer>().eq(CxrCustomer::getPhone, receiverPhone));
//                    if (Objects.isNull(cxrCustomer)) {
//                        log.error("\nERP系统cxr_customer表通过手机号[{}]获取不到用户信息", receiverPhone);
//                        return;
//                    }
//                    orderCustomerId = cxrCustomer.getId();
//                    cxrCustomerAddress.setCxrCustomerId(orderCustomerId);
//                }
//                CxrCustomerAddressVo customerAddressVo = cxrCustomerAddressMapper.getDistanceByLngLat(orderCustomerId,
//                    null, beginLongitude, endLongitude, beginLatitude, endLatitude, longitude, latitude);
//                if (Objects.isNull(customerAddressVo)) {
//                    log.error(
//                        "\n当前下单新用户[{}]的位置，搜索不到最近老客户，建议后台人工介入进行匹配此新用户的所属站点和配送员了",
//                        orderCustomerId);
//                    return;
//                }
//                BigDecimal withCustomerDistance = customerAddressVo.getDistance();//单位：米
//                if (withCustomerDistance.compareTo(searchRange.multiply(new BigDecimal(1000))) == 1) {
//                    log.error(
//                        "\n根据下单新用户[{}]位置，搜索不到满足在{}公里范围内的老客户，最近老客户距离：{}公里，超过设置范围了,建议后台人工介入进行匹配此新用户的所属站点和配送员了",
//                        orderCustomerId, searchRange, withCustomerDistance);
//                    return;
//                }
//                Long nearCustomerId = customerAddressVo.getCxrCustomerId();
//                Long nearSiteIdCustomerId = null;
//                boolean belongNearSiteRule = withCustomerDistance.compareTo(new BigDecimal(withSiteDistance)) == 1;
//                if (belongNearSiteRule) { //withCustomerDistance>withSiteDistance
//                    //1.下单新用户与最近站点匹配规则
//                    //查询某个站点和指定范围内，与下单用户最近的那个客户信息
//                    CxrCustomerAddressVo customerAddress = cxrCustomerAddressMapper.getDistanceByLngLat(orderCustomerId,
//                        nearnestSiteId, beginLongitude, endLongitude, beginLatitude, endLatitude, longitude, latitude);
//                    if (Objects.isNull(customerAddress)) {
//                        log.error(
//                            "\n下单新用户[{}]与最近站点匹配规则:最近那个站点下找不到任何一个客户信息了，需后台人工介入进行匹配此新用户的所属站点和配送员了",
//                            orderCustomerId);
//                        return;
//                    }
//                    BigDecimal withSiteNearCustomerDistance = customerAddress.getDistance();
//                    if (withSiteNearCustomerDistance.compareTo(searchRange.multiply(new BigDecimal(1000))) == 1) {
//                        log.error(
//                            "\n下单新用户[{}]匹配的站点[{}]里面，搜索不到满足{}公里范围内的老客户，最近老客户距离：{}公里，超过设置范围了，需后台人工介入进行匹配此新用户的所属站点和配送员了",
//                            orderCustomerId, nearnestSiteId, searchRange, withSiteNearCustomerDistance);
//                        return;
//                    }
//                    nearSiteIdCustomerId = customerAddress.getCxrCustomerId();//指定站点里面离用户最近的客户ID
//                    log.info(
//                        "\n下单新用户与最近站点匹配规则：某个站点里面与下单新用户[{}]距离最近的那个客户【customerId={}】距离是：{}米,最近客户所属站点[{}]",
//                        orderCustomerId, nearSiteIdCustomerId, withSiteNearCustomerDistance, nearnestSiteId);
//                } else {
//                    //2.下单新用户与最近客户匹配规则
//                    nearCustomerId = customerAddressVo.getCxrCustomerId();
//                    log.info(
//                        "\n下单新用户与最近客户匹配规则:匹配的最近客户customerId={};此新用户[{}]与最近客户距离：distance={}米",
//                        nearCustomerId, orderCustomerId, withCustomerDistance);
//                }
//
//                List<CxrCustomerAddressVo> nearestCustomer = cxrCustomerAddressMapper.getCustomerAddressByCustomeId(
//                    belongNearSiteRule ? nearSiteIdCustomerId : nearCustomerId);
//                List<CxrCustomerAddressVo> choiceList = nearestCustomer.stream().filter(
//                    item -> (Objects.nonNull(sysAreaId) && sysAreaId.equals(item.getSysAreaId())) || (
//                        Objects.nonNull(areaName) && areaName.equals(item.getArea()))).collect(Collectors.toList());
//                CxrCustomerAddressVo nearCustomerAddress =
//                    CollectionUtils.isEmpty(choiceList) ? nearestCustomer.stream()
//                        .filter(item -> SysYesNo.YES.getValue().equals(item.getDefalutAccountAddress())).findFirst()
//                        .get() : choiceList.get(0);
//                if (Objects.nonNull(nearCustomerAddress)) {
//                    cxrCustomerAddress.setCxrSiteId(nearCustomerAddress.getCxrSiteId());
//                    cxrCustomerAddress.setCxrEmployeeId(nearCustomerAddress.getCxrEmployeeId());
//                }
//                log.info(
//                    "\n下单新用户地址匹配的站点和配送员信息结果【{}】：\n【下单新用户orderCustomerId】:{}\n【下单新用户所属站点siteId】:{}\n【下单新用户的配送员employeeId】：{}\n【下单新用户地址经纬度】：longitude={};latitude={}\n【设置{}km范围搜索老客户】：加减经纬度因子lnglatFactor={}\n下单新用户【与最近站点距离：{}米】;【与最近那个客户距离：{}米】\n【最近站点ID】：{}\n【最近客户ID】：{}"
//                    , belongNearSiteRule ? "下单新用户与最近站点匹配规则" : "下单新用户与最近客户匹配规则",
//                    orderCustomerId, nearCustomerAddress.getCxrSiteId(), nearCustomerAddress.getCxrEmployeeId(),
//                    longitude, latitude, searchRange, lnglatFactor, withSiteDistance, withCustomerDistance,
//                    nearnestSiteId, belongNearSiteRule ? nearSiteIdCustomerId : nearCustomerId);
//
//                CustomerAddressMatchRecordEvent eventMessage = new CustomerAddressMatchRecordEvent(cxrCustomerAddress,
//                    withCustomerDistance, withSiteDistance, nearnestSiteId, nearCustomerId, nearSiteIdCustomerId, this);
//                eventMessage.setRemark(belongNearSiteRule ? "采用与最近站点匹配" : "采用与最近客户匹配");
//                eventMessage.setCompareDesc(belongNearSiteRule ? "U > T" : "U <= T");
//                CxrCustomerAddress update = new CxrCustomerAddress();
//                update.setId(cxrCustomerAddress.getId());
//                update.setCxrCustomerId(orderCustomerId);
//                update.setCxrSiteId(cxrCustomerAddress.getCxrSiteId());
//                update.setCxrEmployeeId(cxrCustomerAddress.getCxrEmployeeId());
//                cxrCustomerAddressMapper.updateById(update);
//                log.info("update的参数的修改{}", update);
//                addFirstMatchRecord(eventMessage);
//                return;
//            }
//        });
//    }

        @Override
        public CompletableFuture<Void> calculateUpdateCustomerSiteAndEmployee(CxrCustomerAddress cxrCustomerAddress, Long nearnestSiteId,
                                                                          Double withSiteDistance, BigDecimal longitude, BigDecimal latitude) {
            return CompletableFuture.runAsync(new Runnable() {

                @Override
                public void run() {
                    CxrCustomer cxrCustomer = cxrCustomerMapper.selectById(cxrCustomerAddress.getCxrCustomerId());
                    CxrEmployee empForCreateAddr = newOrderMatchManager.getMatchSiteAndEmpForCreateAddr(
                        cxrCustomerAddress.getId(),
                        cxrCustomer.getPhone(),
                        cxrCustomerAddress.getProvice() + cxrCustomerAddress.getCity() + cxrCustomerAddress.getArea()
                            + cxrCustomerAddress.getDetailDistributionAddress());
                    CxrCustomerAddress update = new CxrCustomerAddress();
                    update.setId(cxrCustomerAddress.getId());
                    update.setCxrSiteId(empForCreateAddr.getCxrSiteId());
                    update.setCxrEmployeeId(empForCreateAddr.getId());
                    cxrCustomerAddressMapper.updateById(update);
                }
            });
        }


    public void calculateCustomerSiteAndEmployee(CxrCustomerAddress cxrCustomerAddress, Long nearnestSiteId,
                                                 Double withSiteDistance, BigDecimal longitude, BigDecimal latitude) {
        BigDecimal lnglatFactor = searchRange.divide(trans_to_m_unit, 1, RoundingMode.HALF_UP);
        BigDecimal beginLongitude = longitude.subtract(lnglatFactor).setScale(6, RoundingMode.HALF_DOWN);
        BigDecimal endLongitude = longitude.add(lnglatFactor).setScale(6, RoundingMode.HALF_DOWN);
        BigDecimal beginLatitude = latitude.subtract(lnglatFactor).setScale(6, RoundingMode.HALF_DOWN);
        BigDecimal endLatitude = latitude.add(lnglatFactor).setScale(6, RoundingMode.HALF_DOWN);
        Long sysAreaId = cxrCustomerAddress.getSysAreaId();
        String areaName = cxrCustomerAddress.getArea();
        Long orderCustomerId = cxrCustomerAddress.getCxrCustomerId();

        CxrCustomerAddressVo customerAddressVo = cxrCustomerAddressMapper.getDistanceByLngLat(orderCustomerId,
            null, beginLongitude, endLongitude, beginLatitude, endLatitude, longitude, latitude);
        if (Objects.isNull(customerAddressVo)) {
            log.error(
                "\n当前下单新用户[{}]的位置，搜索不到最近老客户，建议后台人工介入进行匹配此新用户的所属站点和配送员了",
                orderCustomerId);
            return;
        }
        BigDecimal withCustomerDistance = customerAddressVo.getDistance();//单位：米
        if (withCustomerDistance.compareTo(searchRange.multiply(new BigDecimal(1000))) == 1) {
            log.error(
                "\n根据下单新用户[{}]位置，搜索不到满足在{}公里范围内的老客户，最近老客户距离：{}公里，超过设置范围了,建议后台人工介入进行匹配此新用户的所属站点和配送员了",
                orderCustomerId, searchRange, withCustomerDistance);
            return;
        }
        Long nearCustomerId = customerAddressVo.getCxrCustomerId();
        Long nearSiteIdCustomerId = null;
        boolean belongNearSiteRule = withCustomerDistance.compareTo(new BigDecimal(withSiteDistance)) == 1;
        if (belongNearSiteRule) { //withCustomerDistance>withSiteDistance
            //1.下单新用户与最近站点匹配规则
            //查询某个站点和指定范围内，与下单用户最近的那个客户信息
            CxrCustomerAddressVo customerAddress = cxrCustomerAddressMapper.getDistanceByLngLat(orderCustomerId,
                nearnestSiteId, beginLongitude, endLongitude, beginLatitude, endLatitude, longitude, latitude);
            if (Objects.isNull(customerAddress)) {
                log.error(
                    "\n下单新用户[{}]与最近站点匹配规则:最近那个站点下找不到任何一个客户信息了，需后台人工介入进行匹配此新用户的所属站点和配送员了",
                    orderCustomerId);
                return;
            }
            BigDecimal withSiteNearCustomerDistance = customerAddress.getDistance();
            if (withSiteNearCustomerDistance.compareTo(searchRange.multiply(new BigDecimal(1000))) == 1) {
                log.error(
                    "\n下单新用户[{}]匹配的站点[{}]里面，搜索不到满足{}公里范围内的老客户，最近老客户距离：{}公里，超过设置范围了，需后台人工介入进行匹配此新用户的所属站点和配送员了",
                    orderCustomerId, nearnestSiteId, searchRange, withSiteNearCustomerDistance);
                return;
            }
            nearSiteIdCustomerId = customerAddress.getCxrCustomerId();//指定站点里面离用户最近的客户ID
            log.info(
                "\n下单新用户与最近站点匹配规则：某个站点里面与下单新用户[{}]距离最近的那个客户【customerId={}】距离是：{}米,最近客户所属站点[{}]",
                orderCustomerId, nearSiteIdCustomerId, withSiteNearCustomerDistance, nearnestSiteId);
        } else {
            //2.下单新用户与最近客户匹配规则
            nearCustomerId = customerAddressVo.getCxrCustomerId();
            log.info(
                "\n下单新用户与最近客户匹配规则:匹配的最近客户customerId={};此新用户[{}]与最近客户距离：distance={}米",
                nearCustomerId, orderCustomerId, withCustomerDistance);
        }

        List<CxrCustomerAddressVo> nearestCustomer = cxrCustomerAddressMapper.getCustomerAddressByCustomeId(
            belongNearSiteRule ? nearSiteIdCustomerId : nearCustomerId);
        List<CxrCustomerAddressVo> choiceList = nearestCustomer.stream().filter(
            item -> (Objects.nonNull(sysAreaId) && sysAreaId.equals(item.getSysAreaId())) || (
                Objects.nonNull(areaName) && areaName.equals(item.getArea()))).collect(Collectors.toList());
        CxrCustomerAddressVo nearCustomerAddress =
            CollectionUtils.isEmpty(choiceList) ? nearestCustomer.stream()
                .filter(item -> SysYesNo.YES.getValue().equals(item.getDefalutAccountAddress())).findFirst()
                .get() : choiceList.get(0);
        if (Objects.nonNull(nearCustomerAddress)) {
            cxrCustomerAddress.setCxrSiteId(nearCustomerAddress.getCxrSiteId());
            cxrCustomerAddress.setCxrEmployeeId(nearCustomerAddress.getCxrEmployeeId());
        }
        log.info(
            "\n下单新用户地址匹配的站点和配送员信息结果【{}】：\n【下单新用户orderCustomerId】:{}\n【下单新用户所属站点siteId】:{}\n【下单新用户的配送员employeeId】：{}\n【下单新用户地址经纬度】：longitude={};latitude={}\n【设置{}km范围搜索老客户】：加减经纬度因子lnglatFactor={}\n下单新用户【与最近站点距离：{}米】;【与最近那个客户距离：{}米】\n【最近站点ID】：{}\n【最近客户ID】：{}"
            , belongNearSiteRule ? "下单新用户与最近站点匹配规则" : "下单新用户与最近客户匹配规则",
            orderCustomerId, nearCustomerAddress.getCxrSiteId(), nearCustomerAddress.getCxrEmployeeId(),
            longitude, latitude, searchRange, lnglatFactor, withSiteDistance, withCustomerDistance,
            nearnestSiteId, belongNearSiteRule ? nearSiteIdCustomerId : nearCustomerId);

        CustomerAddressMatchRecordEvent eventMessage = new CustomerAddressMatchRecordEvent(cxrCustomerAddress,
            withCustomerDistance, withSiteDistance, nearnestSiteId, nearCustomerId, nearSiteIdCustomerId, this);
        eventMessage.setRemark(belongNearSiteRule ? "采用与最近站点匹配" : "采用与最近客户匹配");
        eventMessage.setCompareDesc(belongNearSiteRule ? "U > T" : "U <= T");
    }

    public void calculateUpdateCustomerSiteAndEmployee(CxrCustomerAddressAppletBo bo) {
        CxrCustomerAddress cxrCustomerAddress = remoteCustomerAddressService.getCustomerAddressById(bo.getCustomerAddressId());
        if (Objects.nonNull(cxrCustomerAddress)) {
            BigDecimal longitude = new BigDecimal(bo.getLongitude()).setScale(6, RoundingMode.HALF_DOWN);
            BigDecimal latitude = new BigDecimal(bo.getLatitude()).setScale(6, RoundingMode.HALF_DOWN);
            calculateUpdateCustomerSiteAndEmployee(cxrCustomerAddress, bo.getNearSiteId(), bo.getNearSiteDistance(), longitude, latitude);
        }
        mqUtil.sendDelayMessageYds(CxrCustomerConstant.CUSTOMER_TOPIC, CxrCustomerConstant.YDS_CUSTOMER_SYNC_TAG, bo.getCxrCustomerId().toString(), new Date(System.currentTimeMillis() + 20000));
    }

    /**
     * 添加新用户地址首次匹配站点和配送员记录
     *
     * @param eventMessage
     */
    @Override
    public void addFirstMatchRecord(CustomerAddressMatchRecordEvent eventMessage) {
        if (Objects.isNull(eventMessage)) {
            return;
        }
        log.info(
            "\n保存新用户地址首次匹配站点和配送员消息参数：\nnearSiteId:{}\norderCustomerId:{}\nnearCustomerId:{}\nnearSiteIdCustomerId:{}\nwithSiteDistance:{}\nwithCustomerDistance:{}",
            eventMessage.getNearSiteId(), eventMessage.getCxrCustomerAddress().getCxrCustomerId(),
            eventMessage.getNearCustomerId(), eventMessage.getNearSiteIdCustomerId(),
            eventMessage.getWithSiteDistance(), eventMessage.getWithCustomerDistance());
        CxrCustomerAddress customerAddress = eventMessage.getCxrCustomerAddress();
        CxrCustomerAddressMatchRecord firstMatchRecord = new CxrCustomerAddressMatchRecord();
        firstMatchRecord.setRemark(eventMessage.getRemark());
        firstMatchRecord.setCompareDesc(eventMessage.getCompareDesc());
        firstMatchRecord.setCxrCustomerId(customerAddress.getCxrCustomerId());
        firstMatchRecord.setFirstMatch(1);
        Date date = new Date();
        firstMatchRecord.setCreateTime(date);
        firstMatchRecord.setUpdateTime(date);
        Long cxrEmployeeId = customerAddress.getCxrEmployeeId();
        CxrEmployee cxrEmployee = cxrEmployeeMapper.selectById(cxrEmployeeId);
        firstMatchRecord.setEmployeeId(cxrEmployeeId);
        firstMatchRecord.setEmployeeName(Objects.isNull(cxrEmployee) ? null : cxrEmployee.getName());
        firstMatchRecord.setLongitude(customerAddress.getLongitude().setScale(6, RoundingMode.HALF_DOWN));
        firstMatchRecord.setLatitude(customerAddress.getLatitude().setScale(6, RoundingMode.HALF_DOWN));
        BigDecimal mi_unit = new BigDecimal(1000);//定义单位：米
        //单位：米
        BigDecimal nearSiteDistance = BigDecimal.valueOf(eventMessage.getWithSiteDistance())
            .setScale(6, RoundingMode.HALF_DOWN);//单位：米
        //判断是否：nearSiteDistance>=1000米
        boolean is_km =
            nearSiteDistance.compareTo(mi_unit) == 1 || nearSiteDistance.compareTo(new BigDecimal(1000)) == 0;
        String nearSiteDistanceStr = is_km ? nearSiteDistance.divide(mi_unit, 1, RoundingMode.HALF_DOWN).toString()
            : nearSiteDistance.setScale(1, RoundingMode.HALF_DOWN).toString();
        firstMatchRecord.setNearSiteDistance(String.join("", nearSiteDistanceStr, is_km ? "km" : "m"));
        firstMatchRecord.setSearchRange(String.join("", searchRange.toString(), "km"));
        Long nearSiteId = eventMessage.getNearSiteId();
        Long siteId = customerAddress.getCxrSiteId();
        List<CxrSite> cxrSites = cxrSiteMapper.selectBatchIds(Lists.newArrayList(nearSiteId, siteId));
        Map<Long, CxrSite> siteMap = CollectionUtils.isEmpty(cxrSites) ? Maps.newHashMap()
            : cxrSites.stream().collect(Collectors.toMap(item -> item.getId(), item -> item));
        CxrSite nearSite = siteMap.get(nearSiteId);
        firstMatchRecord.setNearSiteId(nearSiteId);
        firstMatchRecord.setNearSiteName(Objects.isNull(nearSite) ? null : nearSite.getName());
        CxrSite site = siteMap.get(siteId);
        firstMatchRecord.setSiteId(siteId);
        firstMatchRecord.setSiteName(Objects.isNull(site) ? null : site.getName());
        Long nearCustomerId = eventMessage.getNearCustomerId();
        Long nearSiteIdCustomerId = eventMessage.getNearSiteIdCustomerId();
        List<CxrCustomer> nearCustomers = cxrCustomerMapper.selectBatchIds(
            Lists.newArrayList(nearCustomerId, nearSiteIdCustomerId));
        Map<Long, CxrCustomer> nearCustomersMap = CollectionUtils.isNotEmpty(nearCustomers) ? nearCustomers.stream()
            .collect(Collectors.toMap(item -> item.getId(), item -> item)) : Maps.newHashMap();
        CxrCustomer nearCxrCustomer = nearCustomersMap.get(nearCustomerId);
        firstMatchRecord.setNearCustomerId(nearCustomerId);
        firstMatchRecord.setNearCustomerName(Objects.isNull(nearCxrCustomer) ? null : nearCxrCustomer.getName());
        CxrCustomer nearSiteCustomer =
            Objects.isNull(nearSiteIdCustomerId) ? null : nearCustomersMap.get(nearSiteIdCustomerId);
        firstMatchRecord.setNearSiteCustomerId(nearSiteIdCustomerId);
        firstMatchRecord.setNearSiteCustomerName(Objects.isNull(nearSiteCustomer) ? "--" : nearSiteCustomer.getName());
        CxrCustomerAddress nearCustomerAddress = cxrCustomerAddressMapper.selectOne(
            new LambdaQueryWrapper<CxrCustomerAddress>().eq(CxrCustomerAddress::getCxrCustomerId, nearCustomerId)
                .eq(CxrCustomerAddress::getDefalutAccountAddress, SysYesNo.YES.getValue())
        );
        firstMatchRecord.setNearCustomerAddressId(nearCustomerAddress.getId());
        firstMatchRecord.setNearCustomerAddressInfo(
            Objects.isNull(nearCustomerAddress) ? null : nearCustomerAddress.getDetailDistributionAddress());
        //单位：米
        BigDecimal nearCustomerDistance = eventMessage.getWithCustomerDistance()
            .setScale(1, RoundingMode.HALF_DOWN);//单位：米
        //判断是否：nearCustomerDistance>=1000
        is_km = nearCustomerDistance.compareTo(mi_unit) == 1 || nearCustomerDistance.compareTo(mi_unit) == 0;
        String nearCustomerDistanceStr =
            is_km ? nearCustomerDistance.divide(mi_unit, 1, RoundingMode.HALF_DOWN).toString()
                : nearCustomerDistance.setScale(1, RoundingMode.HALF_DOWN).toString();
        firstMatchRecord.setNearCustomerDistance(String.join("", nearCustomerDistanceStr, is_km ? "km" : "m"));
        CxrCustomerAddressMatchRecord isExistDB = cxrCustomerAddressMatchRecordMapper.selectOne(
            new LambdaQueryWrapper<CxrCustomerAddressMatchRecord>().eq(CxrCustomerAddressMatchRecord::getCxrCustomerId,
                customerAddress.getCxrCustomerId()).eq(CxrCustomerAddressMatchRecord::getFirstMatch, 1));
        if (Objects.isNull(isExistDB)) {
            int count = cxrCustomerAddressMatchRecordMapper.insert(firstMatchRecord);
            log.info("\n新增当前的下单新用户【{}】匹配站点【{}】和配送员【{}】结果：{}", firstMatchRecord.getCxrCustomerId(),
                firstMatchRecord.getSiteId(), firstMatchRecord.getEmployeeId(), count > 0);
        } else {
//            firstMatchRecord.setId(isExistDB.getId());
//            firstMatchRecord.setCreateTime(null);
//            int count = cxrCustomerAddressMatchRecordMapper.updateById(firstMatchRecord);
            firstMatchRecord.setFirstMatch(0);
            firstMatchRecord.setRemark("最近客户匹配[非首次用户修改地址]");
            int count = cxrCustomerAddressMatchRecordMapper.insert(firstMatchRecord);
            log.info("\n发现当前的下单新用户【{}】修改收货地址了，重新匹配站点【{}】和配送员【{}】结果：{}",
                firstMatchRecord.getCxrCustomerId(), firstMatchRecord.getSiteId(), firstMatchRecord.getEmployeeId(),
                count > 0);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateAppCustomerAddress(CxrCustomerAddressAppletBo bo) {
        if (ObjectUtil.isEmpty(bo)) {
            throw new ServiceException("参数异常!");
        }
        //该客户下过鲜奶订单
        List<CxrUserOrder> checkOrder = cxrUserOrderMapper.selectList(new LambdaQueryWrapper<CxrUserOrder>()
            .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            .and(Wrapper -> Wrapper
                .eq(CxrUserOrder::getCustomerId, bo.getCxrCustomerId())
                .or()
                .eq(CxrUserOrder::getCustomerIdSwitch, bo.getCxrCustomerId())
            )
            .and(Wrapper -> Wrapper
                .eq(CxrUserOrder::getPayStatus, PayStatusEnums.PAY_SUCCEEDED.getValue())
                .or()
                .eq(CxrUserOrder::getAuditStatus, AuditStatusEnums.Audit.code())
            )
        );
        if (CollUtil.isNotEmpty(checkOrder)) {
            log.info("地址id[{}]", bo.getCxrCustomerId());
            return 3;
        }

        //判断是否能进行配送
        bo.setBeginLongitude(bo.getLongitude() - 1);
        bo.setEndLongitude(bo.getLongitude() + 1);
        bo.setBeginLatitude(bo.getLatitude() - 1);
        bo.setEndLatitude(bo.getLatitude() + 1);
        CxrSiteVo cxrSite = cxrSiteMapper.getAddressDistance(null, bo);
        if (Objects.nonNull(cxrSite)) {
            if (ObjectUtil.isNotEmpty(cxrSite)) {
                Double withSiteDistance = cxrSite.getDistance();
                log.info("当前用户位置与最近站点[id={}]的配送距离：{}", cxrSite.getId(), withSiteDistance);
                if (ObjectUtil.isEmpty(cxrSite.getDistributionDistance())) {
                    cxrSite.setDistributionDistance(0);
                }
                bo.setNearSiteId(cxrSite.getId());
                bo.setNearSiteDistance(withSiteDistance * 1000);
            } else {
                log.info("新用户[{}]地址匹配不到最近的站点信息", bo.getCxrCustomerId());
                return 2;
            }
        } else {
            log.info("此新用户[{}]地址经纬度[longitude={};latitude={}]查询匹配不到最近的站点信息",
                bo.getCxrCustomerId(), bo.getLongitude(), bo.getLatitude());
            return 2;
        }

        SysArea sysArea = sysAreaMapper.selectOne(new LambdaQueryWrapper<SysArea>()
            .like(SysArea::getProvice, bo.getProvice())
            .like(SysArea::getCity, bo.getCity())
            .like(SysArea::getArea, bo.getArea())
            .last("limit 1")
        );
        if (ObjectUtil.isEmpty(sysArea)) {
            sysArea.setId(0L);
        }

        SysArea area = new SysArea();
        if (ObjectUtil.isNotEmpty(sysArea)) {
            area.setId(sysArea.getId());
        } else {
            area.setId(0L);
        }
        boolean addrChanged = false;
        CxrCustomer cxrCustomer = cxrCustomerMapper.selectById(bo.getCxrCustomerId());
        // 查询旧的客户地址信息
        CxrCustomerAddress oldAddress = cxrCustomerAddressMapper.selectById(bo.getCustomerAddressId());
        Location location = locationUtils.getLocation(bo.getProvice(), bo.getCity(), bo.getArea(),
            bo.getDetailDistributionAddress());
        int count = cxrCustomerAddressMapper.update(null, new LambdaUpdateWrapper<CxrCustomerAddress>()
            .eq(CxrCustomerAddress::getId, bo.getCustomerAddressId())
            .eq(CxrCustomerAddress::getCxrCustomerId, bo.getCxrCustomerId())
            .set(CxrCustomerAddress::getSysAreaId, area.getId())
            .set(CxrCustomerAddress::getLatitude, location.getLat())
            .set(CxrCustomerAddress::getCxrSiteId, cxrSite.getId())
            .set(CxrCustomerAddress::getLongitude, location.getLng())
            .set(CxrCustomerAddress::getArea, bo.getArea())
            .set(CxrCustomerAddress::getCity, bo.getCity())
            .set(CxrCustomerAddress::getProvice, bo.getProvice())
            .set(CxrCustomerAddress::getReceiverPhone, bo.getReceiverPhone())
            .set(CxrCustomerAddress::getReceiverName, bo.getReceiverName())
            .set(CxrCustomerAddress::getDetailDistributionAddress, bo.getDetailDistributionAddress())
            .set(CxrCustomerAddress::getUpdateBy, bo.getCxrCustomerId())
            .set(CxrCustomerAddress::getUpdateByType, DeviceType.XCX.getDevice())
            .set(CxrCustomerAddress::getUpdateTime, new Date())
            .set(CxrCustomerAddress::getUpdateByName, cxrCustomer.getName())
        );

        if (count < 1) {
            throw new ServiceException("修改客户地址失败了!");
        }
        // 比较新旧地址字段
        boolean isProvinceChanged = !Objects.equals(oldAddress.getProvice(), bo.getProvice());
        boolean isCityChanged = !Objects.equals(oldAddress.getCity(), bo.getCity());
        boolean isAreaChanged = !Objects.equals(oldAddress.getArea(), bo.getArea());
        boolean isDetailAddressChanged = !Objects.equals(oldAddress.getDetailDistributionAddress(), bo.getDetailDistributionAddress());
        //比较手机号
        boolean isPhoneChanged = !Objects.equals(oldAddress.getReceiverPhone(), bo.getReceiverPhone());
        AlterRecord alterRecord = null;
        if (isProvinceChanged || isCityChanged || isAreaChanged || isDetailAddressChanged) {
            // 地址有变化
            log.info("地址发生变化：省[{}] -> [{}], 市[{}] -> [{}], 区[{}] -> [{}], 详细地址[{}] -> [{}]",
                oldAddress.getProvice(), bo.getProvice(),
                oldAddress.getCity(), bo.getCity(),
                oldAddress.getArea(), bo.getArea(),
                oldAddress.getDetailDistributionAddress(), bo.getDetailDistributionAddress());
            alterRecord = new AlterRecord();
            alterRecord.setOldAdress(oldAddress.getProvice() + oldAddress.getCity() + oldAddress.getArea() + oldAddress.getDetailDistributionAddress());
        }
        if(isPhoneChanged){
            alterRecord = new AlterRecord();
            alterRecord.setOldPhone(oldAddress.getReceiverPhone());
            alterRecord.setNewPhone(bo.getReceiverPhone());
        }
        if(alterRecord != null){
            //保存历史
            AddressHistoryMqDoMain addressHistoryMqDoMain = new AddressHistoryMqDoMain();
            addressHistoryMqDoMain.setThisAddress(
                Convert.toStr(bo.getProvice(), "") + Convert.toStr(bo.getCity(), "") + Convert.toStr(bo.getArea(), "")
                    + Convert.toStr(bo.getDetailDistributionAddress(), ""));
            addressHistoryMqDoMain.setCxrCustomerAddressId(bo.getCustomerAddressId());
            addressHistoryMqDoMain.setCxrCustomerId(bo.getCxrCustomerId());
            addressHistoryMqDoMain.setAlterRecord(alterRecord);
            LoginInfo loginInfo = new LoginInfo();
            loginInfo.setUserId(bo.getCxrCustomerId());
            loginInfo.setUserName(cxrCustomer.getName());
            loginInfo.setUserType(UserType.applet_user);
            addressHistoryMqDoMain.setLoginInfo(loginInfo);
            addressHistoryMqDoMain.setUuid(UUID.randomUUID().toString());
            mqUtil.sendSyncMessageTransaction(CustomerAddressConstant.CUSTOMER_ADDRESS_TOPIC,
                CustomerAddressConstant.CUSTOMER_ADDRESS_HISTORY_TAG, JSONUtil.toJsonStr(addressHistoryMqDoMain));
        }
        return count;
    }
//bak
//    @Override
//    public CxrEmployee getMatchSiteAndEmployeByAddr(Long customerId, String fullAddress) {
//        if (StringUtils.isBlank(fullAddress)) {
//            return null;
//        }
//        BigDecimal lnglatFactor = searchRange.divide(trans_to_m_unit, 1, RoundingMode.HALF_UP);
//        Location location = locationUtils.getLocation(fullAddress);
//        BigDecimal longitude = location.getLng();
//        BigDecimal latitude = location.getLat();
//        if (ObjectUtils.anyNull(longitude, latitude)) {
//            log.info("\n解析地址失败：{},小程序提交用户customerId:{}", fullAddress, customerId);
//            return null;
//        }
//        BigDecimal beginLongitude = longitude.subtract(lnglatFactor).setScale(6, RoundingMode.HALF_DOWN);
//        BigDecimal endLongitude = longitude.add(lnglatFactor).setScale(6, RoundingMode.HALF_DOWN);
//        BigDecimal beginLatitude = latitude.subtract(lnglatFactor).setScale(6, RoundingMode.HALF_DOWN);
//        BigDecimal endLatitude = latitude.add(lnglatFactor).setScale(6, RoundingMode.HALF_DOWN);
//        CxrCustomerAddressAppletBo bo = new CxrCustomerAddressAppletBo();
//        bo.setBeginLongitude(beginLongitude.doubleValue());
//        bo.setEndLongitude(endLongitude.doubleValue());
//        bo.setBeginLatitude(beginLatitude.doubleValue());
//        bo.setEndLatitude(endLatitude.doubleValue());
//        bo.setLongitude(longitude.doubleValue());
//        bo.setLatitude(latitude.doubleValue());
//        BigDecimal nearSiteDistance = BigDecimal.valueOf(1000L);
//        CxrSiteVo cxrSite = cxrSiteMapper.getAddressDistance(null, bo);
//        if (Objects.nonNull(cxrSite)) {
//            nearSiteDistance = BigDecimal.valueOf(cxrSite.getDistance()).multiply(new BigDecimal(1000));//km
//        }
//        BigDecimal nearCustomerDistance = BigDecimal.valueOf(1000L);
//        //查找50km内最近老客户
//        CxrCustomerAddressVo customerAddressVo = cxrCustomerAddressMapper.getDistanceByLngLat(customerId, null,
//            beginLongitude, endLongitude, beginLatitude, endLatitude, longitude, latitude);
//        if (Objects.nonNull(customerAddressVo)) {
//            nearCustomerDistance = customerAddressVo.getDistance();
//        }
//        log.info(
//            "\n开始试喝客户匹配站点和配送员：\n试喝客户填写地址：{}\n腾讯接口解析地址经纬度：{},{}\n试喝客户地址与站点距离：{}米\n试喝客户地址与最近用户距离：{}米",
//            fullAddress, longitude, latitude, nearSiteDistance, nearCustomerDistance);
//        Long cxrEmployeeId = null;
//        if (nearSiteDistance.compareTo(nearCustomerDistance) == 1) {
//            log.info("\n最近老客户匹配");
//            //最近客户
//            cxrEmployeeId = customerAddressVo.getCxrEmployeeId();
//        } else {
//            log.info("\n最近最近站点匹配");
//            //最近站点
//            CxrCustomerAddressVo nearSiteCustomerAddressVo = cxrCustomerAddressMapper.getDistanceByLngLat(customerId,
//                cxrSite.getId(), beginLongitude, endLongitude, beginLatitude, endLatitude, longitude, latitude);
//            cxrEmployeeId = Objects.nonNull(nearSiteCustomerAddressVo) ? nearSiteCustomerAddressVo.getCxrEmployeeId()
//                : cxrEmployeeId;
//        }
//        if (Objects.isNull(cxrEmployeeId)) {
//            return null;
//        }
//        CxrEmployee cxrEmployee = cxrEmployeeMapper.selectById(cxrEmployeeId);
//
//        //update 2024-6-19 匹配配送员改为匹配站点主管
//        CxrEmployeePost employeeDirector = cxrEmployeePostMapper
//            .selectOne(new LambdaQueryWrapper<CxrEmployeePost>()
//                .eq(CxrEmployeePost::getCxrSiteId, cxrEmployee.getCxrSiteId())
//                .eq(CxrEmployeePost::getChargeStatus, ChargeStatus.IN_PROGRESS.getValue())
//                .apply("cxr_post_id -> '$.value' = {0} ", PostType.DIRECTOR.getValue())
//                .eq(CxrEmployeePost::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
//            );
//        if (employeeDirector != null) {
//            CxrEmployee employee = cxrEmployeeMapper.selectById(employeeDirector.getCxrEmployeeId());
//            employee.setCxrRootRegionId(cxrSite.getCxrRootRegionId());
//            employee.setCxrRootRegionName(cxrSite.getCxrRootRegionName());
//            return employee;
//        } else {
//            //没有匹配到站点主管，只分配站点
//            CxrEmployee cxrEmployee1 = new CxrEmployee();
//            cxrEmployee1.setCxrSiteId(cxrEmployee.getCxrSiteId());
//            cxrEmployee1.setCxrRootRegionId(cxrSite.getCxrRootRegionId());
//            cxrEmployee1.setCxrRootRegionName(cxrSite.getCxrRootRegionName());
//            return cxrEmployee1;
//        }
//
//    }




}
