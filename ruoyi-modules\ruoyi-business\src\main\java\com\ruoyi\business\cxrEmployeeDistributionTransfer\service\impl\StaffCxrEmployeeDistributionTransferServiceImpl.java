package com.ruoyi.business.cxrEmployeeDistributionTransfer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.query.MPJQueryWrapper;
import com.ruoyi.business.api.domain.bo.CxrCustomerStock;
import com.ruoyi.business.api.domain.bo.CxrEmployeeBo;
import com.ruoyi.business.base.api.PlanMilkDataConvertUtil;
import com.ruoyi.business.base.api.domain.*;
import com.ruoyi.business.base.api.domain.bo.CxrPostId;
import com.ruoyi.business.base.api.domain.json.CustomerAddressMilkDistributionInfo;
import com.ruoyi.business.base.api.dubbo.*;
import com.ruoyi.business.base.api.model.LoginEmployee;
import com.ruoyi.business.cxrEmployeeDimissionApply.domain.vo.CxrEmployeeListVo;
import com.ruoyi.business.cxrEmployeeDistributionTransfer.domain.CxrEmployeeDistributionTransfer;
import com.ruoyi.business.cxrEmployeeDistributionTransfer.domain.CxrEmployeeDistributionTransferMiddle;
import com.ruoyi.business.cxrEmployeeDistributionTransfer.domain.bo.*;
import com.ruoyi.business.cxrEmployeeDistributionTransfer.domain.vo.*;
import com.ruoyi.business.cxrEmployeeDistributionTransfer.mapper.CxrEmployeeDistributionTransferMapper;
import com.ruoyi.business.cxrEmployeeDistributionTransfer.mapper.CxrEmployeeDistributionTransferMiddleMapper;
import com.ruoyi.business.cxrEmployeeDistributionTransfer.service.IStaffCxrEmployeeDistributionTransferService;
import com.ruoyi.business.cxrEmployeeDistributionTransfer.utils.BeanCollectionUtils;
import com.ruoyi.business.cxrEmployeeTransferApply.service.IEmployeeTransferPostApplyService;
import com.ruoyi.common.core.enums.DeleteStatus;
import com.ruoyi.common.core.enums.DistributionTransferStatus;
import com.ruoyi.common.core.enums.OccupationStatus;
import com.ruoyi.common.core.enums.PostType;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.enums.AuditStatusEnums;
import com.ruoyi.common.enums.TerminalTypeEnums;
import com.ruoyi.common.mybatis.core.page.PageQuery;
import com.ruoyi.common.mybatis.core.page.PageTableDataInfo;
import com.ruoyi.common.rocketmq.constant.customer.CxrCustomerConstant;
import com.ruoyi.common.rocketmq.utils.MqUtil;
import com.ruoyi.common.satoken.utils.helper.StaffLoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 路条转拨Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-09-17
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class StaffCxrEmployeeDistributionTransferServiceImpl extends
    MPJBaseServiceImpl<CxrEmployeeDistributionTransferMapper, CxrEmployeeDistributionTransfer> implements
    IStaffCxrEmployeeDistributionTransferService {


    private final CxrEmployeeDistributionTransferMapper baseMapper;

    private final CxrEmployeeDistributionTransferMiddleMapper transferMiddleMapper;
    @DubboReference(timeout = 18000)
    private RemoteCustomerAddressService remoteCustomerAddressService;
    @DubboReference
    private RemoteEmployeePostService remoteEmployeePostService;
    @DubboReference
    private RemoteEmployeeService remoteEmployeeService;
    @DubboReference
    private RemoteSiteService remoteSiteService;

    @DubboReference
    private RemoteCxrSiteSaleProductService remoteCxrSiteSaleProductService;

    @DubboReference(timeout = 18000)
    private RemoteCustomerDistributionDetailService remoteCustomerDistributionDetailService;

    @Autowired
    private MqUtil mqUtil;

    @DubboReference
    private RemoteCxrSaleProductService remoteCxrSaleProductService;

    private final IEmployeeTransferPostApplyService employeeTransferPostApplyService;

    public static <T extends Comparable<T>> boolean compares(List<T> a, List<T> b) {
        if (a.size() != b.size()) {
            return false;
        }
        Collections.sort(a);
        Collections.sort(b);
        for (int i = 0; i < a.size(); i++) {
            if (!a.get(i).equals(b.get(i))) {
                return false;
            }
        }
        return true;
    }

    @Override
    public EmployeeDistributionTransferVo detail(Long id) {// 转拨id

        EmployeeDistributionTransferVo distributionTransferVo = baseMapper.selectJoinOne(
            EmployeeDistributionTransferVo.class, new MPJQueryWrapper<CxrEmployeeDistributionTransfer>()
                .select(
                    "t.* , t1.job_number as outJobNumber, t2.job_number  as  enterJobNumber,t1.`name` as outDistributionName,t2.`name`  as  enterDistributionName    ")
                .leftJoin("cxr_employee t1 on t.distribution_id=t1.id ")
                .leftJoin(" cxr_employee t2 on t.shift_distribution_id=t2.id ")
                .eq("t.id", id)
        );

        if (ObjectUtil.isEmpty(distributionTransferVo)) {
            throw new ServiceException("数据异常！");
        }

        List<CxrCustomerAddressVo> customerAddressList = baseMapper.distributionCustomerAddressList(id);

        if (CollectionUtil.isNotEmpty(customerAddressList)) {

            // 查询用户库存
            List<Long> customerIdList =
                customerAddressList.stream().map(CxrCustomerAddress::getCxrCustomerId).collect(Collectors.toList());
            customerIdList.add(-1L);

            List<CxrCustomerStock> cxrCustomerStocks = baseMapper.queryCustomerStock(customerIdList);
            if (CollUtil.isNotEmpty(cxrCustomerStocks)) {
                Map<Long, CxrCustomerStock> customerStockMap = cxrCustomerStocks.stream()
                    .collect(Collectors.toMap(CxrCustomerStock::getCustomerId, Function.identity(),
                        (v1, v2) -> v2));
                for (CxrCustomerAddressVo cxrCustomerAddressVo : customerAddressList) {
                    Long cxrCustomerId = cxrCustomerAddressVo.getCxrCustomerId();
                    CxrCustomerStock cxrCustomerStock = customerStockMap.get(cxrCustomerId);
                    if (ObjectUtil.isNotEmpty(cxrCustomerStock)) {
                        int stock = Convert.toInt(cxrCustomerStock.getStock(), 0);
                        cxrCustomerAddressVo.setCustomerStock(stock);
                    } else {
                        cxrCustomerAddressVo.setCustomerStock(0);
                    }
                }


            }

            List<CxrCustomerAddressVo> isNullQuartersName =
                customerAddressList.stream().filter(a -> a.getResidentialQuartersName() == null)
                    .collect(Collectors.toList());

            List<CxrCustomerAddressVo> isNotNullQuartersName =
                customerAddressList.stream().filter(a -> a.getResidentialQuartersName() != null)
                    .collect(Collectors.toList());

            Map<String, List<CxrCustomerAddressVo>> listMap =
                isNotNullQuartersName.stream()
                    .collect(Collectors.groupingBy(CxrCustomerAddressVo::getResidentialQuartersName));

            List<CustomerAddressDTO> dotList = new ArrayList<>();
            if (isNullQuartersName.size() > 0) {
                listMap.put("0", isNullQuartersName);//0 代表没有小区的客户
            }
            for (String key : listMap.keySet()) {
                CustomerAddressDTO customerAddressDTO = new CustomerAddressDTO();
                customerAddressDTO.setChecks(false);
                customerAddressDTO.setName(key);
                customerAddressDTO.setPaths(listMap.get(key));
                dotList.add(customerAddressDTO);
            }

            distributionTransferVo.setDistributionAddresslList(dotList);
        }

        return distributionTransferVo;
    }

    @Override
    public PageTableDataInfo<CxrEmployeeDistributionTransferListVo> page(
        CxrEmployeeDistributionTransferBo cxrEmployeeDistributionTransferBo, PageQuery pageQuery) {

        if (ObjectUtil.isEmpty(StaffLoginHelper.getLoginUser())) {
            throw new ServiceException("数据异常！");
        }

        if (pageQuery.getPageSize() == null) {
            pageQuery.setPageSize(30);
        }

        Collection<CxrPostId> cxrPostIds = StaffLoginHelper.getLoginUser().getCxrPostIds();

        // 如果是主管进来
//        long count = StaffLoginHelper.getLoginUser().getCxrPostIds().stream()
//            .filter(a ->  !a.getValue().equals("80")).count(); //可以是经理或者主管
        long count = 0;
        for (CxrPostId cxrPostId : cxrPostIds) {
            if (cxrPostId.getValue().equals("108") || cxrPostId.getValue().equals("103")
                || cxrPostId.getValue().equals("107") || cxrPostId.getValue().equals("105") || cxrPostId.getValue()
                .equals("104") || cxrPostId.getValue().equals("106")) {
                count += 1;
            }
        }

        if (count == 0) {
            cxrEmployeeDistributionTransferBo.setEmployeeId(StaffLoginHelper.getLoginUser().getUserId());
        } else {
            List<Long> sites = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(cxrEmployeeDistributionTransferBo) && ObjectUtil.isEmpty(
                cxrEmployeeDistributionTransferBo.getSiteId())) {
                LoginEmployee loginUser = StaffLoginHelper.getLoginUser();
                sites = loginUser.getAuthSites().stream().map(CxrSite::getId).collect(Collectors.toList());
                if (sites.size() == 0) {
                    sites.add(loginUser.getSiteId());
                }
                cxrEmployeeDistributionTransferBo.setSiteIdList(sites);
            }
        }

        //分页未生效
        PageTableDataInfo pageTableDataInfo = new PageTableDataInfo();

        IPage<EmployeeDistributionTransferVo> listVoPageInfo = baseMapper.pageDistributionTransfer(
            cxrEmployeeDistributionTransferBo, pageQuery.build());
//        PageInfo<EmployeeDistributionTransferVo> listVoPageInfo = new PageInfo<>
//            (baseMapper.pageDistributionTransfer(BeanUtil.beanToMap(cxrEmployeeDistributionTransferBo)));

        List<EmployeeDistributionTransferVo> transferVos = listVoPageInfo.getRecords().stream()//筛选出未审核超7天的数据
            .filter(i -> i.getAuditUsable() == true && i.getAuditStatus()
                .equals(AuditStatusEnums.ToAudit.code()) || i.getAuditStatus().equals(AuditStatusEnums.Audit.code())
                || i.getAuditStatus().equals(AuditStatusEnums.Refuse.code()) || i.getAuditStatus()
                .equals(AuditStatusEnums.Cancel.code()))
            .collect(Collectors.toList());

        pageTableDataInfo.setRows(transferVos);
        Integer sums = (int) listVoPageInfo.getTotal() - transferVos.size();//计算相差的值
        pageTableDataInfo.setTotal(listVoPageInfo.getTotal() - sums);
        pageTableDataInfo.setCurr(pageQuery.getPageNum());// 前段要求要放回
        pageTableDataInfo.setSize(pageQuery.getPageSize());// 前段要求要放回
        return pageTableDataInfo;
    }

    @Override
    public PageTableDataInfo<CxrEmployeeDistributionTransferListVo> managePage(
        CxrEmployeeDistributionTransferBo cxrEmployeeDistributionTransferBo, PageQuery pageQuery) {
        if (pageQuery.getPageSize() == null) {
            pageQuery.setPageSize(50);
        }

        // 主管查询出所有站点的员工数据
        if (ObjectUtil.isNotEmpty(cxrEmployeeDistributionTransferBo) && ObjectUtil.isEmpty(
            cxrEmployeeDistributionTransferBo.getSiteId())) {
            List<CxrSite> sites = StaffLoginHelper.getLoginUser().getAuthSites().stream().collect(Collectors.toList());
            cxrEmployeeDistributionTransferBo.setSiteIds(sites);
        }

        //分页未生效
        PageTableDataInfo pageTableDataInfo = new PageTableDataInfo();
        IPage<EmployeeDistributionTransferVo> listVoPageInfo = baseMapper.managePageDistributionTransfer(
            cxrEmployeeDistributionTransferBo, pageQuery.build());
//        PageInfo<EmployeeDistributionTransferVo> listVoPageInfo = new PageInfo<>
//            (baseMapper.managePageDistributionTransfer(BeanUtil.beanToMap(cxrEmployeeDistributionTransferBo)));

        List<EmployeeDistributionTransferVo> transferVos = listVoPageInfo.getRecords().stream()//筛选出未审核超7天的数据
            .filter(i -> i.getAuditUsable() == true && i.getAuditStatus()
                .equals(AuditStatusEnums.ToAudit.code()) || i.getAuditStatus().equals(AuditStatusEnums.Audit.code())
                || i.getAuditStatus().equals(AuditStatusEnums.Refuse.code()) || i.getAuditStatus()
                .equals(AuditStatusEnums.Cancel.code()))
            .collect(Collectors.toList());

        pageTableDataInfo.setRows(transferVos);
        Integer sums = (int) listVoPageInfo.getTotal() - transferVos.size();//计算相差的值
        pageTableDataInfo.setTotal(listVoPageInfo.getTotal() - sums);
        pageTableDataInfo.setCurr(pageQuery.getPageNum());// 前段要求要放回
        pageTableDataInfo.setSize(pageQuery.getPageSize());// 前段要求要放回
        return pageTableDataInfo;
    }

    @Override
    public Boolean remove(Set<Long> idSet) {
        //TODO 做一些业务上的校验,判断是否需要校验
        CxrEmployeeDistributionTransfer cxrEmployeeDistributionTransfer = new CxrEmployeeDistributionTransfer();
        cxrEmployeeDistributionTransfer.setDeleteStatus(DeleteStatus.DELETED.getValue());
        return baseMapper.update(cxrEmployeeDistributionTransfer,
            new LambdaUpdateWrapper<>(CxrEmployeeDistributionTransfer.class).
                in(CxrEmployeeDistributionTransfer::getId, idSet)) > 0;
    }

    @Override
    public com.ruoyi.common.core.domain.PageTableDataInfo<CxrEmployeeListVo> employeeLisPage(
        CxrEmployeeBo cxrEmployeeBo,
        com.ruoyi.common.core.domain.PageQuery pageQuery) {
//        Long deptId = StaffLoginHelper.getDeptId();
        if (pageQuery.getPageSize() == null) {
            pageQuery.setPageSize(10);
        }
        String value = PostType.DIRECTOR.getValue();

        IPage<CxrEmployeeListVo> cxrEmployeeListVoIPage = baseMapper.employeeLisPage(cxrEmployeeBo,
            pageQuery.build(), value, OccupationStatus.WAIT_INDUCTION.getValue(),
            OccupationStatus.NO_INDUCTION.getValue());//分页未生效

        //获取到的数据
        com.ruoyi.common.core.domain.PageTableDataInfo<CxrEmployeeListVo> cxrPageVo = new com.ruoyi.common.core.domain.PageTableDataInfo<>();
        cxrPageVo.setRows(cxrEmployeeListVoIPage.getRecords());
        cxrPageVo.setTotal(cxrEmployeeListVoIPage.getTotal());
        cxrPageVo.setCurr(pageQuery.getPageNum());
        cxrPageVo.setSize(pageQuery.getPageSize());
        return cxrPageVo;
    }


    /**
     * 批量转播
     *
     * @param bo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> allDistributionTransfer(CxrEmployeeDistributionTransferBo bo) {

        employeeTransferPostApplyService.checkEmployeeExistPostTRansferApply(bo.getEnterDistributionId());
        //根据登录人id  查询主管  如果为空  对比站点 不一样就不能提交
        LoginEmployee loginUser = StaffLoginHelper.getLoginUser();
        Long userId = loginUser.getUserId();
        CxrEmployeePost cxrEmployee = remoteEmployeePostService.getStationUpByUserId(userId, bo.getOutSiteId());
        if (cxrEmployee == null) {
            if (!bo.getOutSiteId().equals(bo.getEnterSiteId())) {
                throw new ServiceException("非主管或不是转出站点主管不能选择站点以外的转出人员!");
            }
            if (!bo.getOutDistributionId().equals(userId)) {
                throw new ServiceException("非主管或不是转出站点主管不能选择自己以外的转出人员!");
            }
        }

        //提前创建 存储返回数据    避免二次创建

        Map<String, Object> map = new HashMap<>();

        CxrEmployee enterEmployee = remoteEmployeeService.getById(bo.getEnterDistributionId());
        CxrEmployee outEmployee = remoteEmployeeService.getById(bo.getOutDistributionId());
        CxrSite enterCxrSite = remoteSiteService.queryId(enterEmployee.getCxrSiteId());
        CxrSite outCxrSite = remoteSiteService.queryId(outEmployee.getCxrSiteId());

        //中间表id  不为空 直接查询更新表   和验证中间表  转播表id是否为空  为空才能添加
        //优先判断   中间表id是否为空  因为第二次进入 地址id也不为空
        if (CollectionUtil.isNotEmpty(bo.getMiddle())) {

            //判断 转播表中有没有待审核的地址id
            boolean haveAuit = isHaveAuit(bo);
            if (haveAuit) {
                throw new ServiceException("拥有的客户地址,有待审核中的数据!");
            }
            //根据地址id查询 中间表
            List<CxrEmployeeDistributionTransferMiddle> cxrEmployeeDistributionTransferMiddles =
                transferMiddleMapper.selectMobilIdBymobilId(bo.getMiddle());
            //查看   中间表中的   转播表id是否为空
            boolean b =
                cxrEmployeeDistributionTransferMiddles.stream().anyMatch(s -> s.getDistributionTransferId() == null);
            if (!b) {
                throw new ServiceException("你有地址已提交,请查看!");
            }
            //判断有没有修改排奶
            //为true    证明某个中间表数据  上午或下午 配送信息为空   证明没有修改派奶

            boolean b1 =
                cxrEmployeeDistributionTransferMiddles.stream().allMatch(
                    s -> StringUtils.isNotBlank(s.getPmDistributionInfo()) && StringUtils.isNotBlank(
                        s.getAmDistributionInfo()));
            if (!b1) {
                throw new ServiceException("您有未修改的派奶表");
            }

        } else if (!CollectionUtil.isEmpty(bo.getAddressIds())) { //判断转出地址id 是否为空

            //判断 转播表中有没有待审核的地址id
            boolean haveAuit = isHaveAuit(bo);
            if (haveAuit) {
                throw new ServiceException("拥有的客户地址,有待审核中的数据!");
            }
            //向中间表中 插入数据
            List<CxrEmployeeDistributionTransferMiddle> TransferMiddle = bo.getAddressIds().stream().map(s -> {
                CxrEmployeeDistributionTransferMiddle transferMiddle = new CxrEmployeeDistributionTransferMiddle();
                transferMiddle.setCustomerAddressId(s);
                transferMiddle.setCreateBy(userId);
                transferMiddle.setCreateTime(new Date());
                return transferMiddle;
            }).collect(Collectors.toList());
            transferMiddleMapper.insertBatch(TransferMiddle);

            List<Long> middleIds = TransferMiddle.stream().map(s -> {
                return s.getId();
            }).collect(Collectors.toList());
            bo.setMiddle(middleIds);
            //检查是否包含转出站点商品
            boolean b = isOrNotLike(bo);
            if (!b) {
//                List<Long> mibleids = this.baseMapper.selectmibleIdByAddressId(bo.getAddressIds());

                map.put("status", DistributionTransferStatus.UNLIKE.getValue());
                map.put("middle", middleIds);
                return map;
            }
            //获取 用户地址id
//        List<Long> outAddressId = remoteCustomerAddressService.selectByEmployeeId(bo.getOutDistributionId());

            //更新客户地址表   需要审核 先不更新
//   remoteCustomerAddressService.updateAllEmployeeAdress(bo.getOutDistributionId(),
//            bo.getEnterDistributionId(), bo.getEnterSiteId());

        }

        //向转播表中插入数据
        //转播表默认待审核
        CxrEmployeeDistributionTransfer transfer = new CxrEmployeeDistributionTransfer();
        transfer.setSiteId(outCxrSite.getId());
        transfer.setSiteName(outCxrSite.getName());
        transfer.setShiftSiteName(enterCxrSite.getName());
        transfer.setShiftSiteId(enterCxrSite.getId());
        transfer.setDistributionId(bo.getOutDistributionId());
        transfer.setShiftDistributionId(bo.getEnterDistributionId());
        transfer.setDistributionName(bo.getOutDistributionName());
        transfer.setShiftDistributionName(bo.getEnterDistributionName());
        transfer.setTransferCause(bo.getTransferCause());
        transfer.setApplyDate(new Date());
        transfer.setApplyId(StaffLoginHelper.getLoginUser().getUserId());
        transfer.setBatchType(DistributionTransferStatus.BATCH.getValue());
        transfer.setCommitId(userId);
        this.baseMapper.insert(transfer);

        //更新中间表   将转拨表中的id 更新进去
        this.baseMapper.updateMibleTranId(bo.getMiddle(), transfer.getId());

        map.put("status", DistributionTransferStatus.SUCCESSFUL.getValue());
        return map;
    }

    /**
     * 判断  转入站点商品  是否包含  转出站点的商品
     *
     * @param bo
     * @return 包含  true    不包含 false
     */
    private boolean isOrNotLike(CxrEmployeeDistributionTransferBo bo) {
        //转出的站点的商品id
        List<Long> outSaleIds = remoteCxrSiteSaleProductService.selectSaleBySiteId(bo.getOutSiteId());

        //转入站点 商品id
        List<Long> enterSaleIds = remoteCxrSiteSaleProductService.selectSaleBySiteId(bo.getEnterSiteId());

        // 对比 商品id 在不在 转入站点里面
        boolean b = outSaleIds.stream().allMatch(s -> enterSaleIds.contains(s));

        return b;
    }

    /**
     * 判断转拨表中 是否 拥有 待审核的地址id 与 要转播的地址id冲突
     *
     * @param bo
     * @return 有一样的返回 true     没有返回 false
     */
    private boolean isHaveAuit(CxrEmployeeDistributionTransferBo bo) {
        List<Long> NoOutAddressId = this.baseMapper.selectAllAdressIdByOutEmployeeId(bo.getOutDistributionId(),
            DeleteStatus.NOT_DELETED.getValue(), DistributionTransferStatus.NO_AUDIT.getValue());
        boolean haveAuit = true;
        //查看集合中是否包含    bo中的地址id 数据     有的话返回true
        if (CollectionUtil.isEmpty(bo.getAddressIds())) {
            haveAuit = NoOutAddressId.contains(bo.getAddressId());
        } else {
            haveAuit = bo.getAddressIds().stream().anyMatch(s -> NoOutAddressId.contains(s));
        }
        return haveAuit;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean staffAdd(DistributionTransferAddEditBo distributionTransferBo) {

        if (ObjectUtil.isEmpty(distributionTransferBo.getDistributionId()) || ObjectUtil.isEmpty(
            distributionTransferBo.getShiftDistributionId())) {
            throw new ServiceException("转入人转出人参数异常！");
        }

        if (ObjectUtil.isEmpty(distributionTransferBo.getTransferCause())) {
            throw new ServiceException("请填写转拨原因！");
        }

        if (ObjectUtil.isEmpty(distributionTransferBo.getMiddleIds())
            || distributionTransferBo.getMiddleIds().size() == 0) {
            throw new ServiceException("请选择要转拨的路线！");
        }

        employeeTransferPostApplyService.checkEmployeeExistPostTRansferApply(
            distributionTransferBo.getShiftDistributionId());

        // 点击“提交”，需要请求接口，判断转出站点和转入站点的售卖商品口味是否一致，如果一致，则提交转拨申请成功，
        // 生成一条转拨申请数据；如果不一致，则提示以下信息：点击二次弹窗的确定按钮，则跳转到修改排奶信息页面。

        //修改过了
        List<Integer> collect = transferMiddleMapper.selectList(
            new LambdaQueryWrapper<CxrEmployeeDistributionTransferMiddle>()
                .select(CxrEmployeeDistributionTransferMiddle::getId,
                    CxrEmployeeDistributionTransferMiddle::getUpdateStatus)
                .in(
                    CxrEmployeeDistributionTransferMiddle::getId, distributionTransferBo.getMiddleIds()
                )).stream().map(CxrEmployeeDistributionTransferMiddle::getUpdateStatus).collect(Collectors.toList());

        List<Integer> isTrues = new ArrayList<>();
        for (int i = 1; i <= collect.size(); i++) {
            isTrues.add(1);
        }

        boolean containsAll = compares(collect, isTrues);

        if (!distributionTransferBo.getSiteId().equals(distributionTransferBo.getShiftSiteId())
            && !containsAll) {//check site stock
            if (!checkSiteProduct(distributionTransferBo.getShiftSiteId(), distributionTransferBo.getSiteId())) {
//                throw new ServiceException("转出站点与转入站点鲜奶口味不一致,请先更新已选路线排奶信息再进行转拨!");
                return false;
            }
        }
        LoginEmployee loginUser = StaffLoginHelper.getLoginUser();

        CxrEmployeeDistributionTransfer distributionTransfer = new CxrEmployeeDistributionTransfer();

        BeanUtil.copyProperties(distributionTransferBo, distributionTransfer);
        distributionTransfer.setTransferCause(distributionTransferBo.getTransferCause());
        distributionTransfer.setShiftSiteName(remoteCustomerDistributionDetailService.
            selectOneCxrSite(distributionTransferBo.getShiftSiteId()).getName());
        distributionTransfer.setSiteName(remoteCustomerDistributionDetailService.
            selectOneCxrSite(distributionTransferBo.getSiteId()).getName());
        distributionTransfer.setShiftDistributionName(remoteCustomerDistributionDetailService.
            selectOneCxrEmployee(distributionTransferBo.getShiftDistributionId()).getName());
        distributionTransfer.setDistributionName(remoteCustomerDistributionDetailService.
            selectOneCxrEmployee(distributionTransferBo.getDistributionId()).getName());
        distributionTransfer.setAuditStatus(DistributionTransferStatus.NO_AUDIT.getValue());
        distributionTransfer.setTerminalType(TerminalTypeEnums.customer.getValue());
        distributionTransfer.setCreateByName(loginUser.getUserName());
        distributionTransfer.setApplyDate(new Date());
        distributionTransfer.setApplyId(loginUser.getUserId());
        distributionTransfer.setCreateTime(new Date());
        distributionTransfer.setCreateBy(loginUser.getUserId());
        distributionTransfer.setCommitId(loginUser.getUserId());

        int insert = baseMapper.insert(distributionTransfer);

        //处理中间表
        List<Long> middleIds = distributionTransferBo.getMiddleIds();
        List<CxrEmployeeDistributionTransferMiddle> distributionTransferMiddles = transferMiddleMapper.selectBatchIds(
            middleIds);
        if (CollUtil.isNotEmpty(distributionTransferMiddles)) {
            for (CxrEmployeeDistributionTransferMiddle middle : distributionTransferMiddles) {
                middle.setDistributionTransferId(distributionTransfer.getId());
            }
            transferMiddleMapper.updateBatchById(distributionTransferMiddles);
        }
        // update
        return insert > 0;
    }

    public boolean compare(List<Integer> arr1, List<Integer> arr2) {
        //先判断两个数组长度是否相同
        if (arr1.size() == arr2.size()) {
            for (int i = 0; i < arr1.size(); i++) { //开始遍历
//
                if (arr1.get(i) != arr2.get(i)) { //用!=判断效率更高

                    return false;
                } else {
                    return true;//遍历完所有元素没有不相等的返回true
                }
            }
        }

        return false;   //如果两数组长度不等直接返回false
    }

    @Override
    public Boolean staffedit(DistributionTransferAddEditBo distributionTransferBo) {

        if (ObjectUtil.isEmpty(distributionTransferBo.getId())) {
            throw new ServiceException("参数异常！");
        }

        if (ObjectUtil.isEmpty(distributionTransferBo.getTransferCause())) {
            throw new ServiceException("请填写转拨原因！");
        }

        if (ObjectUtil.isEmpty(distributionTransferBo.getMiddleIds())
            || distributionTransferBo.getMiddleIds().size() == 0) {
            throw new ServiceException("请选择要转拨的路线！");
        }

        // 点击“提交”，需要请求接口，判断转出站点和转入站点的售卖商品口味是否一致，如果一致，则提交转拨申请成功，
        // 生成一条转拨申请数据；如果不一致，则提示以下信息：点击二次弹窗的确定按钮，则跳转到修改排奶信息页面。

        //修改过了
        List<Integer> collects = transferMiddleMapper.selectList(
            new LambdaQueryWrapper<CxrEmployeeDistributionTransferMiddle>()
                .select(CxrEmployeeDistributionTransferMiddle::getId,
                    CxrEmployeeDistributionTransferMiddle::getUpdateStatus)
                .in(
                    CxrEmployeeDistributionTransferMiddle::getId, distributionTransferBo.getMiddleIds()
                )).stream().map(CxrEmployeeDistributionTransferMiddle::getUpdateStatus).collect(Collectors.toList());

        List<Integer> isTrues = new ArrayList<>();
        for (int i = 1; i <= collects.size(); i++) {
            isTrues.add(1);
        }

        boolean containsAll = compares(collects, isTrues);

        if (!distributionTransferBo.getSiteId().equals(distributionTransferBo.getShiftSiteId())
            && !containsAll) {//check site stock
            if (!checkSiteProduct(distributionTransferBo.getShiftSiteId(), distributionTransferBo.getSiteId())) {
//                throw new ServiceException("转出站点与转入站点鲜奶口味不一致,请先更新已选路线排奶信息再进行转拨!");
                return false;
            }
        }
//        if (!distributionTransferBo.getSiteId().equals(distributionTransferBo.getShiftSiteId())) {//check site stock
//            if (!checkSiteProduct(distributionTransferBo.getShiftSiteId(), distributionTransferBo.getSiteId())) {
//                throw new ServiceException("转出站点与转入站点鲜奶口味不一致,请先更新已选路线排奶信息再进行转拨!");
//            }
//        }

        LoginEmployee loginUser = StaffLoginHelper.getLoginUser();

        CxrEmployeeDistributionTransfer transfer = baseMapper.selectById(distributionTransferBo.getId());
        LambdaUpdateWrapper<CxrEmployeeDistributionTransfer> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(CxrEmployeeDistributionTransfer::getUpdateByName, loginUser.getUserName())
            .set(CxrEmployeeDistributionTransfer::getUpdateBy, loginUser.getUserId())
            .set(CxrEmployeeDistributionTransfer::getUpdateStatus, DistributionTransferStatus.ALREADY_UPDATE.getValue())
            .set(CxrEmployeeDistributionTransfer::getTransferCause, distributionTransferBo.getTransferCause())
            .set(CxrEmployeeDistributionTransfer::getSiteId, distributionTransferBo.getSiteId())
            .set(CxrEmployeeDistributionTransfer::getShiftSiteId, distributionTransferBo.getShiftSiteId())
            .set(CxrEmployeeDistributionTransfer::getDistributionId, distributionTransferBo.getDistributionId())
            .set(CxrEmployeeDistributionTransfer::getDistributionName, distributionTransferBo.getDistributionName())
            .set(CxrEmployeeDistributionTransfer::getShiftDistributionId,
                distributionTransferBo.getShiftDistributionId())
            .set(CxrEmployeeDistributionTransfer::getShiftDistributionName,
                distributionTransferBo.getShiftDistributionName())
            .set(CxrEmployeeDistributionTransfer::getUpdateTime, new Date())
            .eq(CxrEmployeeDistributionTransfer::getId, distributionTransferBo.getId())
        ;

        baseMapper.update(null, updateWrapper);

        List<CxrCustomerAddressVo> addressVos = baseMapper.distributionCustomerAddressList(
            distributionTransferBo.getId());

        List<Long> transferMiddleIds = addressVos.stream()
            .map(CxrCustomerAddressVo::getTransferMiddleId).collect(Collectors.toList());
        List<Long> boMiddleIds = distributionTransferBo.getMiddleIds();
        if (!boMiddleIds.containsAll(transferMiddleIds) || transferMiddleIds.size() != boMiddleIds.size()) {

            List<CxrEmployeeDistributionTransferMiddle> distributionTransferMiddles = transferMiddleMapper.selectBatchIds(
                boMiddleIds);

            for (CxrEmployeeDistributionTransferMiddle transferMiddle : distributionTransferMiddles) {
                transferMiddle.setDistributionTransferId(transfer.getId());
                transferMiddle.setUpdateTime(new Date());
                transferMiddle.setUpdateBy(loginUser.getUserId());
            }
            transferMiddleMapper.updateBatchById(distributionTransferMiddles);//对有的数据进行修改
//            transferMiddleMapper.insertBatch(addIds);//对没有的数据进行插入

            List<Long> delIds = new ArrayList<>();
            Map<Long, Long> collect = boMiddleIds.stream().collect(Collectors.toMap(a -> a, a -> a));// 从前段传的
            for (Long transferMiddleId : transferMiddleIds) {// 从数据库查出来的
                if (collect.get(transferMiddleId) == null) {
                    delIds.add(transferMiddleId);
                }
            }
            if (delIds.size() > 0) {
                transferMiddleMapper.deleteBatchIds(delIds);//对已经弃用的数据进行删除
            }
        }
        return true;
    }

    @Override
    public MilkDistributionVo distributionDetail(Long addressId, Long enterSiteId) {

        MilkDistributionVo milkDistributionVo = new MilkDistributionVo();
        CxrCustomerAddress cxrCustomerAddress = remoteCustomerDistributionDetailService.selectOneCustomerAddress(
            addressId);

//
        if (ObjectUtil.isEmpty(cxrCustomerAddress)) {
            throw new ServiceException("未找到该客户排奶信息");
        }

        milkDistributionVo.setCustomerAddressDetail(cxrCustomerAddress.getDetailDistributionAddress());
        milkDistributionVo.setCustomerName(cxrCustomerAddress.getReceiverName());
        milkDistributionVo.setCustomerPhone(cxrCustomerAddress.getReceiverPhone());

        milkDistributionVo.setResidentialQuartersName(
            ObjectUtil.isEmpty(cxrCustomerAddress.getCxrResidentialQuartersId()) ? null :
                remoteCustomerDistributionDetailService.selectResidentialQuartersById(
                    cxrCustomerAddress.getCxrResidentialQuartersId()).getName());
        CxrCustomer cxrCustomer = remoteCustomerDistributionDetailService.selectOneCustomer(
            cxrCustomerAddress.getCxrCustomerId());
        milkDistributionVo.setCustomerAddress(cxrCustomer.getProvice() + cxrCustomer.getCity() + cxrCustomer.getArea());

        List<SaleProductVo> productVos = baseMapper.saleProductBySite(enterSiteId);

        //上午配送  待补充
        if (ObjectUtil.isNotEmpty(cxrCustomerAddress.getAmDistributionInfo())) {
            milkDistributionVo.setAmDistributionInfo(
                combinationJsonProduct(cxrCustomerAddress.getAmDistributionInfo()));

            CustomerAddressMilkDistributionInfoDTO amDistributionInfo = new CustomerAddressMilkDistributionInfoDTO();

            BeanUtils.copyProperties(milkDistributionVo.getAmDistributionInfo(), amDistributionInfo);

            milkDistributionVo.setAmEnterDistributionDTOS(transition(productVos, amDistributionInfo));
        }

        //下午配送 待补充
        if (ObjectUtil.isNotEmpty(cxrCustomerAddress.getPmDistributionInfo())) {
            milkDistributionVo.setPmDistributionInfo(
                combinationJsonProduct(cxrCustomerAddress.getPmDistributionInfo()));
            CustomerAddressMilkDistributionInfoDTO pmDistributionInfo = new CustomerAddressMilkDistributionInfoDTO();
            BeanUtils.copyProperties(milkDistributionVo.getPmDistributionInfo(), pmDistributionInfo);
            milkDistributionVo.setPmEnterDistributionDTOS(transition(productVos, pmDistributionInfo));
        }
        return milkDistributionVo;
    }

    public CustomerAddressMilkDistributionInfoDTO transition(List<SaleProductVo> enterProducts,
                                                             CustomerAddressMilkDistributionInfoDTO amDistributionInfo) {

        List<MilkDistributionDTO> monday = BeanCollectionUtils.copyListProperties
            (enterProducts, MilkDistributionDTO::new, (a, b) -> {
                b.setProductName(a.getProductName());
                b.setProductId(a.getProductId());
                b.setQuantity(0l);
            });  // list object  productName== 新商品

        List<MilkDistributionDTO> tuesday = BeanCollectionUtils.copyListProperties
            (enterProducts, MilkDistributionDTO::new, (a, b) -> {
                b.setProductName(a.getProductName());
                b.setProductId(a.getProductId());
                b.setQuantity(0l);
            });
        List<MilkDistributionDTO> wednesday = BeanCollectionUtils.copyListProperties
            (enterProducts, MilkDistributionDTO::new, (a, b) -> {
                b.setProductName(a.getProductName());
                b.setProductId(a.getProductId());
                b.setQuantity(0l);
            });
        List<MilkDistributionDTO> thursday = BeanCollectionUtils.copyListProperties
            (enterProducts, MilkDistributionDTO::new, (a, b) -> {
                b.setProductName(a.getProductName());
                b.setProductId(a.getProductId());
                b.setQuantity(0l);
            });
        List<MilkDistributionDTO> friday = BeanCollectionUtils.copyListProperties
            (enterProducts, MilkDistributionDTO::new, (a, b) -> {
                b.setProductName(a.getProductName());
                b.setProductId(a.getProductId());
                b.setQuantity(0l);
            });
        List<MilkDistributionDTO> saturday = BeanCollectionUtils.copyListProperties
            (enterProducts, MilkDistributionDTO::new, (a, b) -> {
                b.setProductName(a.getProductName());
                b.setProductId(a.getProductId());
                b.setQuantity(0l);
            });

        Map<String, MilkDistributionDTO> mondayMap
            = amDistributionInfo.getMonday().stream().collect(Collectors.toMap(a -> a.getProductName(), a -> a));
        Map<String, MilkDistributionDTO> tuesdayMap = amDistributionInfo.getTuesday().stream()
            .collect(Collectors.toMap(a -> a.getProductName(), a -> a));
        Map<String, MilkDistributionDTO> wednesdayMap = amDistributionInfo.getWednesday().stream()
            .collect(Collectors.toMap(a -> a.getProductName(), a -> a));
        Map<String, MilkDistributionDTO> fridayMap = amDistributionInfo.getFriday().stream()
            .collect(Collectors.toMap(a -> a.getProductName(), a -> a));
        Map<String, MilkDistributionDTO> thursdayMap = amDistributionInfo.getThursday().stream()
            .collect(Collectors.toMap(a -> a.getProductName(), a -> a));
        Map<String, MilkDistributionDTO> saturdayMap = amDistributionInfo.getSaturday().stream()
            .collect(Collectors.toMap(a -> a.getProductName(), a -> a));
        for (MilkDistributionDTO item : monday) {//提取数量
            if (mondayMap.get(item.getProductName()) != null) {
                item.setQuantity(mondayMap.get(item.getProductName()).getQuantity());
            }
        }
        amDistributionInfo.setMonday(monday);

        for (MilkDistributionDTO item : tuesday) {
            if (tuesdayMap.get(item.getProductName()) != null) {
                item.setQuantity(tuesdayMap.get(item.getProductName()).getQuantity());
            }
        }
        amDistributionInfo.setTuesday(tuesday);

        for (MilkDistributionDTO item : wednesday) {
            if (wednesdayMap.get(item.getProductName()) != null) {
                item.setQuantity(wednesdayMap.get(item.getProductName()).getQuantity());
            }
        }

        amDistributionInfo.setWednesday(wednesday);

        for (MilkDistributionDTO item : thursday) {
            if (thursdayMap.get(item.getProductName()) != null) {
                item.setQuantity(thursdayMap.get(item.getProductName()).getQuantity());
            }
        }
        amDistributionInfo.setThursday(thursday);

        for (MilkDistributionDTO item : friday) {
            if (fridayMap.get(item.getProductName()) != null) {
                item.setQuantity(fridayMap.get(item.getProductName()).getQuantity());
            }
        }

        amDistributionInfo.setFriday(friday);
        for (MilkDistributionDTO item : saturday) {
            if (saturdayMap.get(item.getProductName()) != null) {
                item.setQuantity(saturdayMap.get(item.getProductName()).getQuantity());
            }
        }
        amDistributionInfo.setSaturday(saturday);
        return amDistributionInfo;
    }

    @Override
    public Boolean milkDistributionUpdate(MilkDistributionBo milkDistributionBo) {

        if (CollectionUtil.isEmpty(milkDistributionBo.getAmMilkDistributionList())
            || CollectionUtil.isEmpty(milkDistributionBo.getPmMilkDistributionList())) {
            throw new ServiceException("参数异常！");
        }

        List<CxrSaleProduct> cxrSaleProductList = remoteCxrSaleProductService.queryProcuctNoDeleteStatus();

        CustomerAddressMilkDistributionInfo amDistributionInfo = PlanMilkDataConvertUtil.convetProductIdToDate(
            milkDistributionBo.getAmMilkDistributionList(), cxrSaleProductList);
        CustomerAddressMilkDistributionInfo pmDistributionInfo = PlanMilkDataConvertUtil.convetProductIdToDate(
            milkDistributionBo.getPmMilkDistributionList(), cxrSaleProductList);

        String amDistributionInfoJson = JSONUtil.toJsonStr(amDistributionInfo);
        String pmDistributionInfoJson = JSONUtil.toJsonStr(pmDistributionInfo);

        // 修改
        return transferMiddleMapper.update(null, new LambdaUpdateWrapper<CxrEmployeeDistributionTransferMiddle>()
            .eq(CxrEmployeeDistributionTransferMiddle::getId, milkDistributionBo.getTransferMiddleId())
            .set(CxrEmployeeDistributionTransferMiddle::getAmDistributionInfo, amDistributionInfoJson)
            .set(CxrEmployeeDistributionTransferMiddle::getPmDistributionInfo, pmDistributionInfoJson)
            .set(CxrEmployeeDistributionTransferMiddle::getUpdateStatus,
                DistributionTransferStatus.ALREADY_UPDATE.getValue())) > 0;

    }

    @Override
    public Boolean staffOptionPath(JSONObject jsonObject) {

        List<String> pathIds = (List<String>) jsonObject.get("pathIds"); // addressId

        String transferId = jsonObject.getString("transferId");
        String distributionId = jsonObject.getString("distributionId");

        if (CollUtil.isEmpty(pathIds)) {
            throw new ServiceException("请选择至少一个地址信息");
        }

        List<String> quarters = (List<String>) jsonObject.get("quarters");

        if (CollectionUtil.isNotEmpty(quarters)) {

            List<String> addressId = new ArrayList<>();
            RollOutBo bo = new RollOutBo();
            for (String quarter : quarters) {
                //未知小区 \other 小区
                bo.setQuarter(quarter);
                bo.setDistributionId(Long.valueOf(distributionId));

                IPage page = new Page<>();
                page.setSize(10000L);
                page.setCurrent(1L);
                IPage<CxrCustomerAddressVo> pages = baseMapper.employeePathPage(bo, page);//分页未生效
                List<CxrCustomerAddressVo> records = pages.getRecords();
                if (CollectionUtil.isNotEmpty(records)) {
                    for (CxrCustomerAddressVo record : records) {
                        addressId.add(String.valueOf(record.getId()));
                    }
                }

            }

            if (addressId.size() > 0) {

                pathIds.addAll(addressId);
                List<String> myList = pathIds.stream().distinct().collect(Collectors.toList());
                pathIds = myList;
//                Set set = new HashSet();
//                set.addAll(pathIds);
//                pathIds.addAll(set);
            }

        }

        LoginEmployee loginUser = StaffLoginHelper.getLoginUser();
        LambdaUpdateWrapper<CxrEmployeeDistributionTransferMiddle> wrapper = new LambdaUpdateWrapper<>();

        wrapper.in(CxrEmployeeDistributionTransferMiddle::getCustomerAddressId, pathIds)
            .eq(CxrEmployeeDistributionTransferMiddle::getCreateBy, loginUser.getUserId());

        if (!StringUtils.isEmpty(transferId)) {
            wrapper.eq(CxrEmployeeDistributionTransferMiddle::getDistributionTransferId, transferId);//
        } else {
            wrapper.isNull(CxrEmployeeDistributionTransferMiddle::getDistributionTransferId);
        }

        if (CollectionUtil.isNotEmpty(transferMiddleMapper.selectList(wrapper))) {
            throw new ServiceException("重复添加路条！");
        }
        List<CxrEmployeeDistributionTransferMiddle> transferMiddles = new ArrayList<>();
        for (String addressId : pathIds) {  // 改修改 地址id
            CxrEmployeeDistributionTransferMiddle transferMiddle = new CxrEmployeeDistributionTransferMiddle();
            transferMiddle.setCustomerAddressId(Long.valueOf(addressId));
            if (!StringUtils.isEmpty(transferId)) {// 从详情添加商品 时
                transferMiddle.setDistributionTransferId(Long.valueOf(transferId));
            }
            transferMiddle.setCreateBy(loginUser.getUserId());
            transferMiddle.setCreateTime(new Date());
            transferMiddles.add(transferMiddle);
        }

        return transferMiddleMapper.insertBatch(transferMiddles);
    }

    @Override
    public Object employeePathPage(RollOutBo bo, PageQuery pageQuery) {

        if (ObjectUtil.isEmpty(bo.getDistributionId())) {
            throw new ServiceException("参数异常！");
        }

        if (ObjectUtil.isEmpty(pageQuery.getPageSize())) {// id
            pageQuery.setPageSize(20);
        }

        bo.setCreateId(StaffLoginHelper.getLoginUser().getUserId() + "");

        IPage<CxrCustomerAddressVo> pages = baseMapper.employeePathPage(bo, pageQuery.build());

        if (pages.getRecords().size() == 0) {
            return null;
        }
        List<CxrCustomerAddressVo> pagesList = pages.getRecords();

        if (CollUtil.isNotEmpty(pagesList)) {
            // 查询用户库存
            List<Long> customerIdList =
                pagesList.stream().map(CxrCustomerAddress::getCxrCustomerId).collect(Collectors.toList());
            customerIdList.add(-1L);

            List<CxrCustomerStock> cxrCustomerStocks = baseMapper.queryCustomerStock(customerIdList);
            if (CollUtil.isNotEmpty(cxrCustomerStocks)) {
                Map<Long, CxrCustomerStock> customerStockMap = cxrCustomerStocks.stream()
                    .collect(Collectors.toMap(CxrCustomerStock::getCustomerId, Function.identity(),
                        (v1, v2) -> v2));
                for (CxrCustomerAddressVo cxrCustomerAddressVo : pagesList) {
                    Long cxrCustomerId = cxrCustomerAddressVo.getCxrCustomerId();
                    CxrCustomerStock cxrCustomerStock = customerStockMap.get(cxrCustomerId);
                    if (ObjectUtil.isNotEmpty(cxrCustomerStock)) {
                        int stock = Convert.toInt(cxrCustomerStock.getStock(), 0);
                        cxrCustomerAddressVo.setCustomerStock(stock);
                    } else {
                        cxrCustomerAddressVo.setCustomerStock(0);
                    }
                }
            }
        }

        List<CxrCustomerAddressVo> isNullQuartersName =
            pagesList.stream().filter(a -> a.getResidentialQuartersName() == null).collect(Collectors.toList());

        List<CxrCustomerAddressVo> isNotNullQuartersName =
            pagesList.stream().filter(a -> a.getResidentialQuartersName() != null).collect(Collectors.toList());

        List<CustomerAddressDTO> dotList = new ArrayList<>();
        Map<String, List<CxrCustomerAddressVo>> collect = isNotNullQuartersName.stream()
            .collect(Collectors.groupingBy(CxrCustomerAddressVo::getResidentialQuartersName));

        if (isNullQuartersName.size() > 0) {
            collect.put("0", isNullQuartersName);//0 代表没有小区的客户
        }

        for (String key : collect.keySet()) {
            CustomerAddressDTO customerAddressDTO = new CustomerAddressDTO();
            customerAddressDTO.setChecks(false);
            customerAddressDTO.setName(key);
            customerAddressDTO.setPaths(collect.get(key));
            dotList.add(customerAddressDTO);
        }

        Map<String, Object> data = new HashMap<>();
        data.put("data", dotList);
        data.put("quartersTotal", dotList.size());
        data.put("total", pages.getTotal());
        data.put("curr", pageQuery.getPageNum());
        data.put("size", pageQuery.getPageSize());
        return data;
    }

    @Override
    public Object selectOptionPath(RollOutBo bo) {//要改
        bo.setCreateId(StaffLoginHelper.getLoginUser().getUserId() + "");
        IPage<CxrCustomerAddressVo> page = new Page<>(bo.getPageNum(), 1000l);
        IPage<CxrCustomerAddressVo> pages = null;
        if (CollectionUtil.isNotEmpty(bo.getMiddle())) {
            pages = baseMapper.selectOptionPathByMiddleId(bo, page);
        } else {
            pages = baseMapper.selectOptionPath(bo, page);//分页未生效
        }//todo 详情进来 然后新增商品时

        List<CxrCustomerAddressVo> addressVos = pages.getRecords();

        if (CollUtil.isNotEmpty(addressVos)) {
            // 查询用户库存
            List<Long> customerIdList =
                addressVos.stream().map(CxrCustomerAddress::getCxrCustomerId).collect(Collectors.toList());
            customerIdList.add(-1L);

            List<CxrCustomerStock> cxrCustomerStocks = baseMapper.queryCustomerStock(customerIdList);

            if (CollUtil.isNotEmpty(cxrCustomerStocks)) {
                Map<Long, CxrCustomerStock> customerStockMap = cxrCustomerStocks.stream()
                    .collect(Collectors.toMap(CxrCustomerStock::getCustomerId, Function.identity(),
                        (v1, v2) -> v2));
                for (CxrCustomerAddressVo cxrCustomerAddressVo : addressVos) {
                    Long cxrCustomerId = cxrCustomerAddressVo.getCxrCustomerId();
                    CxrCustomerStock cxrCustomerStock = customerStockMap.get(cxrCustomerId);
                    if (ObjectUtil.isNotEmpty(cxrCustomerStock)) {
                        int stock = Convert.toInt(cxrCustomerStock.getStock(), 0);
                        cxrCustomerAddressVo.setCustomerStock(stock);
                    } else {
                        cxrCustomerAddressVo.setCustomerStock(0);
                    }
                }
            }
        }

        List<CustomerAddressDTO> dotList = new ArrayList<>();

        List<CxrCustomerAddressVo> isNullQuartersName =
            addressVos.stream().filter(a -> a.getResidentialQuartersName() == null).collect(Collectors.toList());

        List<CxrCustomerAddressVo> isNotNullQuartersName =
            addressVos.stream().filter(a -> a.getResidentialQuartersName() != null).collect(Collectors.toList());

        Map<String, List<CxrCustomerAddressVo>> listMap =
            isNotNullQuartersName.stream()
                .collect(Collectors.groupingBy(CxrCustomerAddressVo::getResidentialQuartersName));
        if (isNullQuartersName.size() > 0) {
            listMap.put("0", isNullQuartersName);
        }
        for (String key : listMap.keySet()) {
            CustomerAddressDTO customerAddressDTO = new CustomerAddressDTO();
            customerAddressDTO.setChecks(false);
            customerAddressDTO.setName(key);
            customerAddressDTO.setPaths(listMap.get(key));
            dotList.add(customerAddressDTO);
        }

//        distributionTransferVo.setDistributionAddresslList(listMap);
//
//        return distributionTransferVo;

        Map<String, Object> data = new HashMap<>();
        data.put("data", dotList);
        data.put("total", pages.getTotal());
        data.put("curr", bo.getPageNum());
        data.put("size", bo.getPageSize());
        return data;
    }

    @Override
    public Boolean delOptionPath(List<Long> id) {
        return transferMiddleMapper.deleteBatchIds(id) > 0;
    }

    @Override
    public EmployeeDistributionTransferVo staffApplyDetail(Long id) {
        EmployeeDistributionTransferVo distributionTransferVo = baseMapper.selectJoinOne(
            EmployeeDistributionTransferVo.class, new MPJQueryWrapper<CxrEmployeeDistributionTransfer>()
                .select(
                    "t.* , t1.job_number as outJobNumber, t2.job_number  as  enterJobNumber,t1.`name` as outDistributionName,t2.`name`  as  enterDistributionName    ")
                .leftJoin("cxr_employee t1 on t.distribution_id=t1.id ")
                .leftJoin(" cxr_employee t2 on t.shift_distribution_id=t2.id ")
                .eq("t.id", id)
        );

        if (ObjectUtil.isEmpty(distributionTransferVo)) {
            throw new ServiceException("数据异常！");
        }

        distributionTransferVo.setDistributionAddressNotQuartersList(baseMapper.distributionCustomerAddressList(id));
        return distributionTransferVo;
    }

    @Override
    public Boolean applyPass(Long id) {

        LoginEmployee loginUser = StaffLoginHelper.getLoginUser();

        CxrEmployeeDistributionTransfer distributionTransfer = baseMapper.selectById(id);

        checkTransferAudit(distributionTransfer);

        // 转拨表修改
        int update = baseMapper.update(distributionTransfer, new LambdaUpdateWrapper<CxrEmployeeDistributionTransfer>()
            .eq(CxrEmployeeDistributionTransfer::getId, id)
            .set(CxrEmployeeDistributionTransfer::getAuditStatus, DistributionTransferStatus.ALREADY_AUDIT.getValue())
            .set(CxrEmployeeDistributionTransfer::getAuditTime, new Date())
            .set(CxrEmployeeDistributionTransfer::getAuditBy, loginUser.getUserId())
        );

        if (update == 0) {
            throw new ServiceException("未知错误");
        }

        // 中间表修改 审核人和创建人可能不是同一个人
        List<CxrEmployeeDistributionTransferMiddle> transferMiddles = transferMiddleMapper.selectList(
            new LambdaUpdateWrapper<CxrEmployeeDistributionTransferMiddle>()
                .eq(CxrEmployeeDistributionTransferMiddle::getDistributionTransferId, distributionTransfer.getId())
        );

        if (CollectionUtil.isEmpty(transferMiddles)) {
            throw new ServiceException("没有查到要修改的数据");
        }

        List<List<CxrEmployeeDistributionTransferMiddle>> split = CollectionUtil.split(transferMiddles, 50);
        for (List<CxrEmployeeDistributionTransferMiddle> listVos : split) {

            List<Long> addressIds = listVos.stream()
                .map(CxrEmployeeDistributionTransferMiddle::getCustomerAddressId)
                .collect(Collectors.toList());
            if (CollectionUtil.isEmpty(addressIds)) {
                throw new ServiceException("没有查到转播地址");
            }

            TransferMiddleMqBo bo = new TransferMiddleMqBo();
            bo.setDistributionTransfer(distributionTransfer);
            bo.setAddressIds(addressIds);
            bo.setListVos(listVos);
            mqUtil.sendSyncMessage(CxrCustomerConstant.CUSTOMER_TOPIC, CxrCustomerConstant.YDS_DISTRIBUTION_TRANSFER_MIDDLE_TAG, JSONUtil.toJsonStr(bo));

        }

        return true;
    }

    @Override
    public void distributionTransferMiddle(TransferMiddleMqBo middleMqBo) {
        List<CxrCustomerAddress> cxrCustomerAddresses = remoteCustomerAddressService.queryCustomerAddressByIds(
            middleMqBo.getAddressIds());
        if (CollectionUtil.isEmpty(cxrCustomerAddresses)) {
            throw new ServiceException("没有查到转播地址");
        }
        executorTransferMiddle(middleMqBo.getListVos(), middleMqBo.getDistributionTransfer(), cxrCustomerAddresses);
    }

    private void executorTransferMiddle(List<CxrEmployeeDistributionTransferMiddle> transferMiddles,
                                        CxrEmployeeDistributionTransfer distributionTransfer, List<CxrCustomerAddress> cxrCustomerAddresses) {
        Map<Long, CxrCustomerAddress> addressMap =
            cxrCustomerAddresses.stream()
                .collect(Collectors.toMap(CxrCustomerAddress::getId, Function.identity(), (v1, v2) -> v2));
        List<CxrCustomerAddress> customerAddressList = new ArrayList<>();
        for (CxrEmployeeDistributionTransferMiddle item : transferMiddles) {
            //转拨修改配送员、站点信息
            CxrCustomerAddress cxrCustomerAddress = addressMap.get(item.getCustomerAddressId());
            if (ObjectUtil.isEmpty(cxrCustomerAddress)) {
                continue;
            }
            // 临时排奶表 transfer_middle_id    transfer_middle 表的状态
            if (item.getUpdateStatus() == DistributionTransferStatus.ALREADY_UPDATE.getValue()) {// 修改过排奶信息的数据
                CustomerAddressMilkDistributionInfo addressMilkDistributionInfo
                    = JSONUtil.toBean(item.getAmDistributionInfo(), CustomerAddressMilkDistributionInfo.class);
                CustomerAddressMilkDistributionInfo pmDistributionInfo
                    = JSONUtil.toBean(item.getPmDistributionInfo(), CustomerAddressMilkDistributionInfo.class);
                cxrCustomerAddress.setAmDistributionInfo(addressMilkDistributionInfo);// 已经处理过的json
                cxrCustomerAddress.setPmDistributionInfo(pmDistributionInfo);// 已经处理过的json
            }
            cxrCustomerAddress.setCxrEmployeeId(distributionTransfer.getShiftDistributionId());
            cxrCustomerAddress.setCxrSiteId(distributionTransfer.getShiftSiteId());
            customerAddressList.add(cxrCustomerAddress);
            // 客户地址修改
        }
        if (customerAddressList.size() > 0) {
            remoteCustomerDistributionDetailService.updateBatchAddress(customerAddressList);
        }

    }

    @Override
    public Boolean applyRefuse(Long id) {
        return baseMapper.update(null, new LambdaUpdateWrapper<CxrEmployeeDistributionTransfer>()
            .eq(CxrEmployeeDistributionTransfer::getId, id)
            .set(CxrEmployeeDistributionTransfer::getAuditStatus, DistributionTransferStatus.REFULSE.getValue())
            .set(CxrEmployeeDistributionTransfer::getAuditTime, new Date())
            .set(CxrEmployeeDistributionTransfer::getRefuseDate, new Date())
            .set(CxrEmployeeDistributionTransfer::getAuditBy, StaffLoginHelper.getLoginUser().getUserId())
        ) > 0;
    }

    @Override
    public PageTableDataInfo<CxrSite> getUserSite(String siteName, String state, PageQuery pageQuery) {
        //给前段大哥做特殊处理
        if (StringUtils.isEmpty(siteName) || siteName.equals("null")) {
            siteName = null;
        }
        Collection<Long> authSiteIds = StaffLoginHelper.getLoginUser().getAuthSiteIds();
        List<Long> siteList = null;
        if (CollectionUtil.isNotEmpty(authSiteIds)) {
            siteList = StaffLoginHelper.getLoginUser().getAuthSiteIds().stream().collect(Collectors.toList());
        }

        if (ObjectUtil.isEmpty(pageQuery.getPageSize())) {
            pageQuery.setPageSize(20);
        }
        IPage<CxrSite> cxrEmployeeListVoIPage = baseMapper.getUserSite(siteName, state, siteList,
            pageQuery.build());//分页未生效
        //获取到的数据
        PageTableDataInfo<CxrSite> cxrPageVo = new PageTableDataInfo<>();
        cxrPageVo.setRows(cxrEmployeeListVoIPage.getRecords());
        cxrPageVo.setTotal(cxrEmployeeListVoIPage.getTotal());
        cxrPageVo.setSize(pageQuery.getPageSize());
        cxrPageVo.setCurr(pageQuery.getPageNum());
        return cxrPageVo;
    }

    private boolean checkSiteProduct(Long enterSiteId, Long outSiteId) {

        List<String> outProducts =
            baseMapper.saleProductBySite(outSiteId).stream().map(SaleProductVo::getProductName)
                .collect(Collectors.toList());
        List<String> enterProducts =
            baseMapper.saleProductBySite(enterSiteId).stream().map(SaleProductVo::getProductName)
                .collect(Collectors.toList());
        return outProducts.containsAll(enterProducts);
    }

    private Boolean checkTransferAudit(CxrEmployeeDistributionTransfer distributionTransfer) {

        if (ObjectUtil.isEmpty(distributionTransfer)) {
            throw new ServiceException("参数异常");
        }

        if (distributionTransfer.getAuditStatus() == DistributionTransferStatus.ALREADY_AUDIT.getValue()) {
            throw new ServiceException("该订单已审核请勿重复提交");
        }

        return true;
    }

    /**
     * 审核分页
     *
     * @param bo
     * @return
     */
    @Override
    public PageTableDataInfo<CxrEmployeeDistributionTransferVo> TransferPage(CxrTransferPageBo bo) {
//        LoginEmployee demo=   StaffLoginHelper.getLoginUser();
        List<Long> longList = StaffLoginHelper.getLoginUser().getAuthSiteIds().stream().collect(Collectors.toList());

//        Collection<Long>  i=new ArrayList<>();
        //page分页
        IPage<CxrEmployeeDistributionTransferVo> cxrEmployeeDistributionTransferVoIPage =
            this.baseMapper.TransferPage(bo, bo.build(), longList,
                DeleteStatus.NOT_DELETED.getValue());

        return PageTableDataInfo.build(cxrEmployeeDistributionTransferVoIPage);
    }

    public CustomerAddressMilkDistributionInfoDTO combinationJsonProduct(
        CustomerAddressMilkDistributionInfo distributionInfo) {
        CustomerAddressMilkDistributionInfoDTO customerAddressMilkDistributionInfoDTO = new CustomerAddressMilkDistributionInfoDTO();

        customerAddressMilkDistributionInfoDTO.setMonday(
            BeanCollectionUtils.copyListProperties(distributionInfo.getMonday(), MilkDistributionDTO::new, (a, b) -> {
                b.setProductName(productName(b.getProductId()));
            }));
        customerAddressMilkDistributionInfoDTO.setTuesday(
            BeanCollectionUtils.copyListProperties(distributionInfo.getTuesday(), MilkDistributionDTO::new, (a, b) -> {
                b.setProductName(productName(b.getProductId()));
            }));
        customerAddressMilkDistributionInfoDTO.setWednesday(
            BeanCollectionUtils.copyListProperties(distributionInfo.getWednesday(), MilkDistributionDTO::new,
                (a, b) -> {
                    b.setProductName(productName(b.getProductId()));
                }));
        customerAddressMilkDistributionInfoDTO.setThursday(
            BeanCollectionUtils.copyListProperties(distributionInfo.getThursday(), MilkDistributionDTO::new, (a, b) -> {
                b.setProductName(productName(b.getProductId()));
            }));
        customerAddressMilkDistributionInfoDTO.setFriday(
            BeanCollectionUtils.copyListProperties(distributionInfo.getFriday(), MilkDistributionDTO::new, (a, b) -> {
                b.setProductName(productName(b.getProductId()));
            }));
        customerAddressMilkDistributionInfoDTO.setSaturday(
            BeanCollectionUtils.copyListProperties(distributionInfo.getSaturday(), MilkDistributionDTO::new, (a, b) -> {
                b.setProductName(productName(b.getProductId()));
            }));
        return customerAddressMilkDistributionInfoDTO;
    }

    public String productName(Long productId) {
        CxrSaleProduct cxrSaleProduct = remoteCxrSiteSaleProductService.selectOneSaleProduct(productId);
        if (cxrSaleProduct != null) {
            String name = remoteCxrSiteSaleProductService.selectOneSaleProduct(productId).getName();
            if (StringUtils.isNotBlank(name)) {
                return name;
            }
        }
        return null;
    }

    @Override
    public boolean allEdit(CxrTransferPageBo bo) {
        CxrEmployeeDistributionTransfer employeeDistributionTransfer = this.getById(bo.getId());
        if (employeeDistributionTransfer.getAuditStatus().equals(DistributionTransferStatus.ALREADY_AUDIT.getValue())) {
            throw new ServiceException("转拨审核完成不能修改!");
        }
        //根据登录人id  查询主管  如果为空  对比站点 不一样就不能提交
        LoginEmployee loginUser = StaffLoginHelper.getLoginUser();
        Long userId = loginUser.getUserId();
        CxrEmployeePost cxrEmployee = remoteEmployeePostService.getStationUpByUserId(userId,
            employeeDistributionTransfer.getSiteId());
        if (!employeeDistributionTransfer.getCommitId().equals(userId)) {
            if (cxrEmployee == null) {
                throw new ServiceException("不是转出站点主管,不能修改!");
            }
        }

        //只能修改 转入配送员 还是与前一个同一个站点的
        employeeDistributionTransfer.setShiftDistributionId(bo.getEnterDistributionId());
        employeeDistributionTransfer.setShiftDistributionName(bo.getEnterDistributionName());
        //转拨原因也可以修改
        employeeDistributionTransfer.setTransferCause(bo.getTransferCause());
        employeeDistributionTransfer.setAuditStatus(DistributionTransferStatus.NO_AUDIT.getValue());

        return this.updateById(employeeDistributionTransfer);
    }

    @Override
    public Boolean delBatchMiddleIds() {
        RollOutBo bo = new RollOutBo();
        bo.setCreateId(StaffLoginHelper.getLoginUser().getUserId() + "");
        IPage<CxrCustomerAddressVo> page = new Page<>(1, 1000);
        IPage<CxrCustomerAddressVo> pages = baseMapper.selectOptionPath(bo, page);//分页未生效
        List<Long> collect = pages.getRecords().stream().map(CxrCustomerAddressVo::getTransferMiddleId)
            .collect(Collectors.toList());
        if (collect.size() > 0) {
            return transferMiddleMapper.deleteBatchIds(collect) > 0;
        }
        return true;
    }
}
