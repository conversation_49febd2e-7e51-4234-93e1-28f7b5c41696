package com.ruoyi.business.base.customerAddress.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.business.api.domain.vo.CxrSaleProductVo;
import com.ruoyi.business.base.api.domain.*;
import com.ruoyi.business.base.api.domain.bo.CxrAddressBo;
import com.ruoyi.business.base.api.domain.bo.CxrCustomerAddressAppletBo;
import com.ruoyi.business.base.api.domain.bo.CxrCustomerAddressBo;
import com.ruoyi.business.base.api.domain.dto.CustomerAddressUpdateDTO;
import com.ruoyi.business.base.api.domain.dto.CxrCustomerAddressDTO;
import com.ruoyi.business.base.api.domain.json.CustomerAddressMilkDistributionDTO;
import com.ruoyi.business.base.api.domain.json.CustomerAddressMilkDistributionInfo;
import com.ruoyi.business.base.api.domain.json.MilkDistributionInfo;
import com.ruoyi.business.base.api.domain.vo.CxrAddressVo;
import com.ruoyi.business.base.api.dubbo.RemoteEmployeePostService;
import com.ruoyi.business.base.api.dubbo.RemoteResidentialQuarService;
import com.ruoyi.business.base.api.enums.customer.CustomerAddressUpdateType;
import com.ruoyi.business.base.api.model.LoginEmployee;
import com.ruoyi.business.base.customer.mapper.CxrCustomerMapper;
import com.ruoyi.business.base.customer.service.ICxrCustomerService;
import com.ruoyi.business.base.customerAddress.domain.bo.AddressBo;
import com.ruoyi.business.base.customerAddress.domain.bo.CxrCustomerAddrBo;
import com.ruoyi.business.base.customerAddress.domain.bo.CxrCustomerAddressQueryBo;
import com.ruoyi.business.base.customerAddress.domain.vo.CustomerAddressDetailVo;
import com.ruoyi.business.base.customerAddress.domain.vo.CxrCustomerAddressListVo;
import com.ruoyi.business.base.customerAddress.domain.vo.CxrCustomerAddressQueryVo;
import com.ruoyi.business.base.customerAddress.domain.vo.CxrCustomerAddressVo;
import com.ruoyi.business.base.customerAddress.mapper.CxrCustomerAddressMapper;
import com.ruoyi.business.base.customerAddress.service.ICxrCustomerAddressService;
import com.ruoyi.business.base.cxrCustomerAddressHistory.service.ICxrCustomerAddressHistoryService;
import com.ruoyi.business.base.employee.mapper.CxrEmployeeMapper;
import com.ruoyi.business.base.employeePost.mapper.CxrEmployeePostMapper;
import com.ruoyi.business.base.saleProduct.service.ICxrSaleProductService;
import com.ruoyi.business.base.site.domain.vo.CxrSiteVo;
import com.ruoyi.business.base.site.mapper.CxrSiteMapper;
import com.ruoyi.business.base.siteSaleProduct.service.ICxrSiteSaleProductService;
import com.ruoyi.common.core.config.TxMapConfig;
import com.ruoyi.common.core.domain.TableDataInfo;
import com.ruoyi.common.core.enums.ChargeStatus;
import com.ruoyi.common.core.enums.DeleteStatus;
import com.ruoyi.common.core.enums.PostType;
import com.ruoyi.common.core.enums.SysYesNo;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.addr.Location;
import com.ruoyi.common.core.utils.addr.LocationUtils;
import com.ruoyi.common.mybatis.core.page.PageQuery;
import com.ruoyi.common.mybatis.core.page.PageTableDataInfo;
import com.ruoyi.common.rocketmq.calculate.CustomerAddressConstant;
import com.ruoyi.common.rocketmq.utils.MqUtil;
import com.ruoyi.common.satoken.utils.LoginUtil;
import com.ruoyi.common.satoken.utils.helper.CustomerLoginHelper;
import com.ruoyi.common.satoken.utils.helper.StaffLoginHelper;
import com.ruoyi.system.api.RemoteSysConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 客户地址Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-07-02
 */
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(rollbackFor = Exception.class)
public class CxrCustomerAddressServiceImpl extends ServiceImpl<CxrCustomerAddressMapper, CxrCustomerAddress> implements
    ICxrCustomerAddressService {

    private final ICxrSaleProductService iCxrSaleProductService;
    private final ICxrSiteSaleProductService iCxrSiteSaleProductService;
    private final CxrCustomerAddressMapper baseMapper;
    private final CxrCustomerMapper customerMapper;
    private final ICxrCustomerAddressHistoryService iCxrCustomerAddressHistoryService;
    private final CxrSiteMapper cxrSiteMapper;
    private final CxrEmployeeMapper cxrEmployeeMapper;

    private final CxrEmployeePostMapper cxrEmployeePostMapper;

    @DubboReference
    private RemoteSysConfigService remoteSysConfigService;

    @Autowired
    private TxMapConfig txMapConfig;

    @Autowired
    private MqUtil mqUtil;

    @Autowired
    private LocationUtils locationUtils;
    /**
     * 一经度维度默认111.195公里单位 地球半径：111195 计算的结果单位是度，需要乘111195（地球半径6371000*PI/180）是将值转化为米
     */
    @Value("${baiduMap.lnglat.trans_to_m_unit:111.195}")
    private BigDecimal trans_to_m_unit;
    /**
     * 默认搜索50公里范围
     */
    @Value("${baiduMap.lnglat.searchRange:50}")
    private BigDecimal searchRange;

    private final CxrCustomerAddressMapper cxrCustomerAddressMapper;

    @DubboReference
    private RemoteEmployeePostService remoteEmployeePostService;

    @Lazy
    @Autowired
    private ICxrCustomerService iCxrCustomerService;

    @DubboReference
    private RemoteResidentialQuarService remoteResidentialQuarService;

    @Autowired
    @Qualifier("futureThreadPool")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;


    @Override
    public com.ruoyi.business.base.customerAddress.domain.vo.CxrCustomerAddressVo detail(Long id) {

        return baseMapper.selectVoById(id);
    }


    @Override
    public PageTableDataInfo<CxrCustomerAddressListVo> page(CxrCustomerAddressBo cxrCustomerAddressBo,
                                                            PageQuery pageQuery) {

        LambdaQueryWrapper<CxrCustomerAddress> lambdaQueryWrapper = buildLambdaQueryWrapper(cxrCustomerAddressBo);
        IPage<CxrCustomerAddressListVo> result = baseMapper.selectVoPage(pageQuery.build(), lambdaQueryWrapper,
            CxrCustomerAddressListVo.class);

        return PageTableDataInfo.build(result);
    }


    private final LambdaQueryWrapper<CxrCustomerAddress> buildLambdaQueryWrapper(
        CxrCustomerAddressBo cxrCustomerAddressBo) {

        LambdaQueryWrapper<CxrCustomerAddress> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(cxrCustomerAddressBo.getCxrCustomerId() != null, CxrCustomerAddress::getCxrCustomerId,
            cxrCustomerAddressBo.getCxrCustomerId());
        lambdaQueryWrapper.eq(cxrCustomerAddressBo.getSysDeptId() != null, CxrCustomerAddress::getSysDeptId,
            cxrCustomerAddressBo.getSysDeptId());
        lambdaQueryWrapper.like(StringUtils.isNotBlank(cxrCustomerAddressBo.getReceiverName()),
            CxrCustomerAddress::getReceiverName, cxrCustomerAddressBo.getReceiverName());
        lambdaQueryWrapper.eq(StringUtils.isNotBlank(cxrCustomerAddressBo.getReceiverPhone()),
            CxrCustomerAddress::getReceiverPhone, cxrCustomerAddressBo.getReceiverPhone());
        lambdaQueryWrapper.eq(StringUtils.isNotBlank(cxrCustomerAddressBo.getDetailDistributionAddress()),
            CxrCustomerAddress::getDetailDistributionAddress, cxrCustomerAddressBo.getDetailDistributionAddress());
        lambdaQueryWrapper.eq(cxrCustomerAddressBo.getSysAreaId() != null, CxrCustomerAddress::getSysAreaId,
            cxrCustomerAddressBo.getSysAreaId());
        lambdaQueryWrapper.eq(cxrCustomerAddressBo.getCxrSiteId() != null, CxrCustomerAddress::getCxrSiteId,
            cxrCustomerAddressBo.getCxrSiteId());
        lambdaQueryWrapper.eq(cxrCustomerAddressBo.getCxrEmployeeId() != null, CxrCustomerAddress::getCxrEmployeeId,
            cxrCustomerAddressBo.getCxrEmployeeId());
        lambdaQueryWrapper.eq(StringUtils.isNotBlank(cxrCustomerAddressBo.getIsShowAmDistribution()),
            CxrCustomerAddress::getIsShowAmDistribution, cxrCustomerAddressBo.getIsShowAmDistribution());
        lambdaQueryWrapper.eq(StringUtils.isNotBlank(cxrCustomerAddressBo.getAmDistributionStatus()),
            CxrCustomerAddress::getAmDistributionStatus, cxrCustomerAddressBo.getAmDistributionStatus());
        lambdaQueryWrapper.eq(cxrCustomerAddressBo.getAmDistributionStartDeliveryTime() != null,
            CxrCustomerAddress::getAmDistributionStartDeliveryTime,
            cxrCustomerAddressBo.getAmDistributionStartDeliveryTime());
        lambdaQueryWrapper.eq(cxrCustomerAddressBo.getAmDistributionSuspendStartTime() != null,
            CxrCustomerAddress::getAmDistributionSuspendStartTime,
            cxrCustomerAddressBo.getAmDistributionSuspendStartTime());
        lambdaQueryWrapper.eq(cxrCustomerAddressBo.getAmDistributionSuspendEndTime() != null,
            CxrCustomerAddress::getAmDistributionSuspendEndTime,
            cxrCustomerAddressBo.getAmDistributionSuspendEndTime());
        lambdaQueryWrapper.eq(StringUtils.isNotBlank(cxrCustomerAddressBo.getIsShowPmDistribution()),
            CxrCustomerAddress::getIsShowPmDistribution, cxrCustomerAddressBo.getIsShowPmDistribution());
        lambdaQueryWrapper.eq(StringUtils.isNotBlank(cxrCustomerAddressBo.getPmDistributionStatus()),
            CxrCustomerAddress::getPmDistributionStatus, cxrCustomerAddressBo.getPmDistributionStatus());
        lambdaQueryWrapper.eq(cxrCustomerAddressBo.getPmDistributionStartDeliveryTime() != null,
            CxrCustomerAddress::getPmDistributionStartDeliveryTime,
            cxrCustomerAddressBo.getPmDistributionStartDeliveryTime());
        lambdaQueryWrapper.eq(cxrCustomerAddressBo.getPmDistributionSuspendStartTime() != null,
            CxrCustomerAddress::getPmDistributionSuspendStartTime,
            cxrCustomerAddressBo.getPmDistributionSuspendStartTime());
        lambdaQueryWrapper.eq(cxrCustomerAddressBo.getPmDistributionSuspendEndTime() != null,
            CxrCustomerAddress::getPmDistributionSuspendEndTime,
            cxrCustomerAddressBo.getPmDistributionSuspendEndTime());
        lambdaQueryWrapper.like(StringUtils.isNotBlank(cxrCustomerAddressBo.getCreateByName()),
            CxrCustomerAddress::getCreateByName, cxrCustomerAddressBo.getCreateByName());
        lambdaQueryWrapper.eq(StringUtils.isNotBlank(cxrCustomerAddressBo.getCreateByType()),
            CxrCustomerAddress::getCreateByType, cxrCustomerAddressBo.getCreateByType());
        lambdaQueryWrapper.like(StringUtils.isNotBlank(cxrCustomerAddressBo.getUpdateByName()),
            CxrCustomerAddress::getUpdateByName, cxrCustomerAddressBo.getUpdateByName());
        lambdaQueryWrapper.eq(StringUtils.isNotBlank(cxrCustomerAddressBo.getUpdateByType()),
            CxrCustomerAddress::getUpdateByType, cxrCustomerAddressBo.getUpdateByType());
        lambdaQueryWrapper.eq(cxrCustomerAddressBo.getDeleteBy() != null, CxrCustomerAddress::getDeleteBy,
            cxrCustomerAddressBo.getDeleteBy());
        lambdaQueryWrapper.like(StringUtils.isNotBlank(cxrCustomerAddressBo.getDeleteByName()),
            CxrCustomerAddress::getDeleteByName, cxrCustomerAddressBo.getDeleteByName());
        lambdaQueryWrapper.eq(StringUtils.isNotBlank(cxrCustomerAddressBo.getDeleteByType()),
            CxrCustomerAddress::getDeleteByType, cxrCustomerAddressBo.getDeleteByType());
        lambdaQueryWrapper.eq(cxrCustomerAddressBo.getDeleteTime() != null, CxrCustomerAddress::getDeleteTime,
            cxrCustomerAddressBo.getDeleteTime());
        lambdaQueryWrapper.eq(StringUtils.isNotBlank(cxrCustomerAddressBo.getDeleteStatus()),
            CxrCustomerAddress::getDeleteStatus, cxrCustomerAddressBo.getDeleteStatus());
        lambdaQueryWrapper.eq(cxrCustomerAddressBo.getSortNum() != null, CxrCustomerAddress::getSortNum,
            cxrCustomerAddressBo.getSortNum());
        lambdaQueryWrapper.eq(cxrCustomerAddressBo.getSpareId() != null, CxrCustomerAddress::getSpareId,
            cxrCustomerAddressBo.getSpareId());
        return lambdaQueryWrapper;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(CxrCustomerAddrBo cxrCustomerAddressBo) {
        if (cxrCustomerAddressBo.getCxrCustomerId() == null) {
            throw new ServiceException("请绑定客户");
        }
        CxrCustomer cxrCustomer = customerMapper.selectById(cxrCustomerAddressBo.getCxrCustomerId());
        if (cxrCustomer == null) {
            throw new ServiceException("客户不存在!");
        }

        CxrCustomerAddress cxrCustomerAddress = BeanUtil.toBean(cxrCustomerAddressBo, CxrCustomerAddress.class);
        LocalDate now = LocalDate.now();
        cxrCustomerAddress.setAmDistributionStatus(SysYesNo.NO.getValue());
        cxrCustomerAddress.setAmDistributionSuspendStartTime(now);
        cxrCustomerAddress.setPmDistributionSuspendStartTime(now);
        cxrCustomerAddress.setPmDistributionStatus(SysYesNo.NO.getValue());
        CustomerAddressMilkDistributionInfo info = getCustomerAddressMilkDistributionInfo();

        cxrCustomerAddress.setAmDistributionInfo(info);
        cxrCustomerAddress.setPmDistributionInfo(info);
        cxrCustomerAddress.setIsShowAmDistribution(SysYesNo.YES.getValue());
        cxrCustomerAddress.setIsShowPmDistribution(SysYesNo.YES.getValue());

        addGetLngLat(cxrCustomerAddress);

        Long count = baseMapper.selectCount(new LambdaQueryWrapper<CxrCustomerAddress>()
            .select(CxrCustomerAddress::getId)
            .eq(
                CxrCustomerAddress::getCxrCustomerId, cxrCustomer.getId()
            ).eq(CxrCustomerAddress::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
        );

        if (count == 0l) {
            cxrCustomerAddress.setDefalutAccountAddress(SysYesNo.YES.getValue());
        }
        boolean flag = baseMapper.insert(cxrCustomerAddress) > 0;
        if (flag) {
            //保存历史
            AddressHistoryMqDoMain addressHistoryMqDoMain = new AddressHistoryMqDoMain();
            addressHistoryMqDoMain.setCxrCustomerAddressId(cxrCustomerAddress.getId());
            addressHistoryMqDoMain.setCxrCustomerId(cxrCustomerAddress.getCxrCustomerId());
            AlterRecord alterRecord = new AlterRecord();
            CustomerAddressUpdateDTO customerAddressUpdateDTO = new CustomerAddressUpdateDTO();
            customerAddressUpdateDTO.setUpdateType(CustomerAddressUpdateType.ADD.getValue());
            customerAddressUpdateDTO.setAddress(
                Convert.toStr(cxrCustomerAddress.getProvice(), "") + Convert.toStr(cxrCustomerAddress.getCity(), "") + Convert.toStr(cxrCustomerAddress.getArea(), "")
                    + Convert.toStr(cxrCustomerAddress.getDetailDistributionAddress(), ""));
            alterRecord.setCustomerAddressUpdate(customerAddressUpdateDTO);
            addressHistoryMqDoMain.setAlterRecord(alterRecord);
            addressHistoryMqDoMain.setLoginInfo(LoginUtil.getLoginUser());
            addressHistoryMqDoMain.setUuid(UUID.randomUUID().toString());
            mqUtil.sendSyncMessageTransaction(CustomerAddressConstant.CUSTOMER_ADDRESS_TOPIC,
                CustomerAddressConstant.CUSTOMER_ADDRESS_HISTORY_TAG, JSONUtil.toJsonStr(addressHistoryMqDoMain));
        }
        return flag;
    }

    public CxrCustomerAddress addGetLngLat(CxrCustomerAddress address) {
        StringBuffer buffer = new StringBuffer();
        buffer.append(address.getProvice());
        buffer.append(address.getCity());
        buffer.append(address.getArea());
        buffer.append(address.getDetailDistributionAddress());

        //依据导入到数据来看详细地址里会有# ,#对它的去除
        int index = buffer.indexOf("#");
        while (index != -1) {
            buffer.deleteCharAt(index);
            index = buffer.indexOf("#");
        }

        String baseUrl = txMapConfig.getUrl(); // 基础URL
        String path = "?address=" + buffer.toString(); // 参数
        String query = "&key=" + txMapConfig.getAppKey(); // key值

        String url = baseUrl + path + query; // 构建完整的URL
        OkHttpClient client = new OkHttpClient();
        Request request = new Request.Builder()
            .url(url)
            .build();
        try {
            Response response = client.newCall(request).execute();
            String responseBody = response.body().string();

            JSONObject jsonObject = new JSONObject(responseBody);
            // 在这里对响应数据进行解析，获取需要的地址或坐标信息
            System.out.println(responseBody);

            Object lng = jsonObject.getJSONObject("result").getJSONObject("location").get("lng");
            Object lat = jsonObject.getJSONObject("result").getJSONObject("location").get("lat");
            Object level = jsonObject.getJSONObject("result").get("level");
            BigDecimal decimalValue = new BigDecimal(String.valueOf(lng));
            BigDecimal decimalValues = new BigDecimal(String.valueOf(lat));
            String levels = level.toString();
            address.setLevel(Integer.parseInt(levels));
            address.setLatitude(decimalValues);
            address.setLongitude(decimalValue);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return address;
    }

    @NotNull
    private CustomerAddressMilkDistributionInfo getCustomerAddressMilkDistributionInfo() {
        List<MilkDistributionInfo> milkDistributionInfos = iCxrSaleProductService.queryListGoodInfoProcuctAllForYXN();

        CustomerAddressMilkDistributionInfo info = new CustomerAddressMilkDistributionInfo();
        info.setMonday(milkDistributionInfos);
        info.setTuesday(milkDistributionInfos);
        info.setWednesday(milkDistributionInfos);
        info.setThursday(milkDistributionInfos);
        info.setFriday(milkDistributionInfos);
        info.setSaturday(milkDistributionInfos);
        return info;
    }

//    @Override
//    public Boolean edit(CxrCustomerAddressBo cxrCustomerAddressBo) {
//
//        CxrCustomerAddress cxrCustomerAddress = BeanUtil.toBean(cxrCustomerAddressBo, CxrCustomerAddress.class);
//
//        //TODO 做一些数据校验,如唯一约束
//
//        return baseMapper.updateById(cxrCustomerAddress) > 0;
//    }


    @Override
    public Boolean remove(Set<Long> idSet) {

        //TODO 做一些业务上的校验,判断是否需要校验

        return baseMapper.deleteBatchIds(idSet) > 0;
    }

    @Override
    public TableDataInfo<CxrCustomer> pageWithQuarters(Object page, CxrCustomerAddressBo cxrCustomerAddress) {

        IPage tpage = (IPage) page;
        if (StringUtils.isBlank(cxrCustomerAddress.getReceiverName())) {
            return new TableDataInfo(0L, tpage.getCurrent(), tpage.getSize(), new ArrayList());
        }
        //查询用户表

        IPage<CxrCustomer> iPage;
        if (cxrCustomerAddress.getIsReturnOrder() != null && cxrCustomerAddress.getIsReturnOrder()) {
            LoginEmployee loginUser = StaffLoginHelper.getLoginUser();
            List<PostType> postTypes = Arrays.asList(PostType.DIRECTOR, PostType.GENERATION_DIRECTOR);
            List<CxrEmployeePost> cxrEmployeePosts = remoteEmployeePostService
                .cxrEmployeePostByType(loginUser.getUserId(),
                    postTypes);
            if (CollectionUtil.isEmpty(cxrEmployeePosts)) {
                return new TableDataInfo(0L, tpage.getCurrent(), tpage.getSize(), new ArrayList());
            }
            String siteIds = cxrEmployeePosts.stream().map(p -> p.getCxrSiteId().toString())
                .collect(Collectors.joining(","));
            cxrCustomerAddress.setSiteIds(siteIds);
            iPage = customerMapper.queryPageByPhone(tpage, cxrCustomerAddress);
        } else {
            LambdaQueryWrapper<CxrCustomer> wrapper = new LambdaQueryWrapper();
            wrapper
                .eq(StringUtils.isNotBlank(cxrCustomerAddress.getReceiverName()), CxrCustomer::getPhone,
                    cxrCustomerAddress.getReceiverName());
            iPage = customerMapper.selectPage(tpage, wrapper);
        }

        //填写客户站点地址信息
        List<CxrCustomer> records = iPage.getRecords();
        if (CollectionUtil.isEmpty(records)) {
            return new TableDataInfo(0L, tpage.getCurrent(), tpage.getSize(), new ArrayList());
        }

        List<Long> customerIds = records.stream().map(CxrCustomer::getId).collect(Collectors.toList());
        customerIds.add(-1L);
        //查询地址
        List<CxrCustomerAddress> cxrCustomerAddresses = baseMapper.selectList(
            new LambdaQueryWrapper<CxrCustomerAddress>()
                .in(CxrCustomerAddress::getCxrCustomerId, customerIds)
                .eq(CxrCustomerAddress::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
//                .eq(CxrCustomerAddress::getDefalutAccountAddress, SysYesNo.YES.getValue())
        );
        Map<Long, CxrCustomerAddress> addressMap = null;
        Map<Long, String> siteMap = null;
        Map<Long, List<CxrCustomerAddress>> allAddressesSiteMap = null;
        if (CollectionUtil.isNotEmpty(cxrCustomerAddresses)) {
            addressMap = cxrCustomerAddresses.stream().filter(a -> StrUtil.equals(SysYesNo.YES.getValue(), a.getDefalutAccountAddress()))
                .collect(Collectors.toMap(CxrCustomerAddress::getCxrCustomerId,
                    Function.identity(), (v1, v2) -> v2));
            allAddressesSiteMap = cxrCustomerAddresses.stream()
                .collect(Collectors.groupingBy(CxrCustomerAddress::getCxrCustomerId));
            Set<Long> siteIds = cxrCustomerAddresses.stream().map(CxrCustomerAddress::getCxrSiteId).collect(Collectors.toSet());
            List<CxrSite> cxrSites = cxrSiteMapper.selectBatchIds(siteIds);
            if (CollUtil.isNotEmpty(cxrSites)) {
                siteMap = cxrSites.stream().collect(Collectors.toMap(a -> a.getId(), a -> a.getName(), (v1, v2) -> v1));
            }
        }
        //查询小区
        List<Long> Quarters =
            cxrCustomerAddresses.stream().map(CxrCustomerAddress::getCxrResidentialQuartersId)
                .collect(Collectors.toList());
        Quarters.add(-1L);

        List<CxrResidentialQuarters> cxrResidentialQuarters1 = remoteResidentialQuarService
            .queryResidentialQuartersNameByIds(
                Quarters);
        Map<Long, String> quarterMap = null;
        if (CollectionUtil.isNotEmpty(cxrResidentialQuarters1)) {
            quarterMap = cxrResidentialQuarters1.stream()
                .collect(Collectors.toMap(CxrResidentialQuarters::getId,
                    CxrResidentialQuarters::getName, (v1, v2) -> v2));
        }
        Map<Long, String> finalQuarterMap = quarterMap;

        //整合数据
        Map<Long, CxrCustomerAddress> finalAddressMap = addressMap;
        Map<Long, String> finalSiteMap = siteMap;
        Map<Long, List<CxrCustomerAddress>> finalAllAddressesSiteMap = allAddressesSiteMap;
        records.stream().forEach(s -> {
            s.setDetailDistributionAddress("暂无详细地址");
            CxrCustomerAddress cxrCustomerAddressVo = null;
            if (CollectionUtil.isNotEmpty(finalAddressMap)) {
                cxrCustomerAddressVo = finalAddressMap.get(s.getId());
            }

            if (!ObjectUtil.isEmpty(cxrCustomerAddressVo)) {
                s.setDetailDistributionAddress(cxrCustomerAddressVo.getDetailDistributionAddress());
                s.setSysAreaId(cxrCustomerAddressVo.getSysAreaId());
                if (null != cxrCustomerAddressVo.getCxrResidentialQuartersId()) {
                    //小区信息
                    s.setCxrResidentialQuartersId(cxrCustomerAddressVo.getCxrResidentialQuartersId());
                    if (CollectionUtil.isNotEmpty(finalQuarterMap)) {
                        String quarterName = finalQuarterMap.get(cxrCustomerAddressVo.getCxrResidentialQuartersId());
                        s.setCxrResidentialQuartersName(Convert.toStr(quarterName, ""));
                    }
                }
                //站点信息
                s.setCxrSiteId(cxrCustomerAddressVo.getCxrSiteId());
                if (CollUtil.isNotEmpty(finalSiteMap)) {
                    s.setSiteName(Convert.toStr(finalSiteMap.get(cxrCustomerAddressVo.getCxrSiteId()), ""));
                }
            } else {
                s.setSeason("用户未填写主地址!");
            }

            List<CxrCustomerAddress> addressList = finalAllAddressesSiteMap.get(s.getId());
            if (CollUtil.isNotEmpty(addressList)) {
                List<DeliverySiteDTO> siteDTO = new ArrayList<>();
                Set<Long> siteIdCustomerAddress = addressList.stream().map(CxrCustomerAddress::getCxrSiteId).collect(Collectors.toSet());
                for (Long siteId : siteIdCustomerAddress) {
                    DeliverySiteDTO dto = new DeliverySiteDTO();
                    String s1 = finalSiteMap.get(siteId);
                    dto.setSiteId(siteId);
                    if (ObjectUtil.isNotEmpty(s1)) {
                        dto.setSiteName(s1);
                    }
                    siteDTO.add(dto);
                }
                s.setDeliverySiteDTOS(CollUtil.isNotEmpty(siteDTO) ? siteDTO : null);
            }

        });
        //创建返回对象
        TableDataInfo<CxrCustomer> tableDataInfo = new TableDataInfo();
        tableDataInfo.setPageNum(iPage.getCurrent());
        tableDataInfo.setPageSize(iPage.getSize());
        tableDataInfo.setTotal(iPage.getTotal());
        tableDataInfo.setRows(iPage.getRecords());
        return tableDataInfo;

    }


    @Override
    public List<Long> getListByemId(Long id) {

        return baseMapper.getAddressIdByEmId(id, DeleteStatus.NOT_DELETED.getValue());
    }


    @Override
    public PageTableDataInfo<CxrCustomerAddressVo> listAddPeople(
        CxrCustomerAddressBo cxrCustomerAddressBo, PageQuery pageQuery) {
        if (pageQuery.getPageSize() == null) {
            pageQuery.setPageSize(10);
        }
        PageTableDataInfo pageTableDataInfo = new PageTableDataInfo();
        IPage<CxrCustomerAddressVo> listVoPageInfo = baseMapper
            .listAddPeople(
                cxrCustomerAddressBo, pageQuery.build());
        List<CxrCustomerAddressVo> addressVoList = listVoPageInfo.getRecords();
        if (CollectionUtil.isNotEmpty(addressVoList)) {
            if (ObjectUtil.isNull(cxrCustomerAddressBo.getCxrSiteId()) && ObjectUtil
                .isNull(cxrCustomerAddressBo.getCxrEmployeeId())) {
                List<Long> siteIds = addressVoList.stream().map(CxrCustomerAddressVo::getCxrSiteId)
                    .collect(Collectors.toList());
                List<Long> employeeIds = addressVoList.stream().map(CxrCustomerAddressVo::getCxrEmployeeId)
                    .collect(Collectors.toList());

                List<CxrSite> siteList = cxrSiteMapper.selectList(new LambdaQueryWrapper<CxrSite>()
                    .in(CxrSite::getId, siteIds)
                    .eq(CxrSite::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                    .groupBy(CxrSite::getId));

                List<CxrEmployee> employeeList = cxrEmployeeMapper.selectList(new LambdaQueryWrapper<CxrEmployee>()
                    .in(CxrEmployee::getId, employeeIds)
                    .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                    .groupBy(CxrEmployee::getId));

                Map<Long, CxrSite> map = siteList.stream()
                    .collect(Collectors.toMap(CxrSite::getId, Function.identity(), (v1, v2) -> v2));
                Map<Long, CxrEmployee> employeeMap = employeeList.stream().collect(Collectors.toMap(CxrEmployee::getId,
                    Function.identity(), (v1, v2) -> v2));
                //人员名称 人员编号
                for (CxrCustomerAddressVo s : addressVoList) {
                    CxrSite cxrSite = map.get(s.getCxrSiteId());
                    if (ObjectUtil.isNotEmpty(cxrSite)) {
                        s.setCxrSiteName(cxrSite.getName());
                    }
                    CxrEmployee cxrEmployee = employeeMap.get(s.getCxrEmployeeId());
                    if (ObjectUtil.isNotEmpty(cxrEmployee)) {
                        s.setEmployeeName(cxrEmployee.getName());
                        s.setJobNumber(cxrEmployee.getJobNumber());
                    }
                }

            }
            //人员有值,站点没有值
            if (ObjectUtil.isNotEmpty(cxrCustomerAddressBo.getCxrEmployeeId()) && ObjectUtil
                .isNull(cxrCustomerAddressBo.getCxrSiteId())) {
                List<CxrEmployee> employeeList = cxrEmployeeMapper.selectList(new LambdaQueryWrapper<CxrEmployee>()
                    .in(CxrEmployee::getId, cxrCustomerAddressBo.getCxrEmployeeId())
                    .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                    .groupBy(CxrEmployee::getId));

                List<Long> getCxrSiteId = employeeList.stream().map(CxrEmployee::getCxrSiteId)
                    .collect(Collectors.toList());

                List<CxrSite> siteList = cxrSiteMapper.selectList(new LambdaQueryWrapper<CxrSite>()
                    .in(CxrSite::getId, getCxrSiteId)
                    .eq(CxrSite::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                    .groupBy(CxrSite::getId));

                Map<Long, CxrSite> map = siteList.stream()
                    .collect(Collectors.toMap(CxrSite::getId, Function.identity(), (v1, v2) -> v2));
                Map<Long, CxrEmployee> employeeMap = employeeList.stream().collect(Collectors.toMap(CxrEmployee::getId,
                    Function.identity(), (v1, v2) -> v2));
                for (CxrCustomerAddressVo s : addressVoList) {
                    CxrSite cxrSite = map.get(s.getCxrSiteId());
                    if (ObjectUtil.isNotEmpty(cxrSite)) {
                        s.setCxrSiteName(cxrSite.getName());
                    }
                    CxrEmployee cxrEmployee = employeeMap.get(s.getCxrEmployeeId());
                    if (ObjectUtil.isNotEmpty(cxrEmployee)) {
                        s.setEmployeeName(cxrEmployee.getName());
                        s.setJobNumber(cxrEmployee.getJobNumber());
                    }
                }
            }

            //站点有值,人员没有值
            if (ObjectUtil.isNotEmpty(cxrCustomerAddressBo.getCxrSiteId()) && ObjectUtil
                .isNull(cxrCustomerAddressBo.getCxrEmployeeId())) {

                List<CxrSite> siteList = cxrSiteMapper.selectList(new LambdaQueryWrapper<CxrSite>()
                    .in(CxrSite::getId, cxrCustomerAddressBo.getCxrSiteId())
                    .eq(CxrSite::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                    .groupBy(CxrSite::getId));

                List<Long> employeeIds = addressVoList.stream().map(CxrCustomerAddressVo::getCxrEmployeeId)
                    .collect(Collectors.toList());

                if (CollectionUtil.isNotEmpty(employeeIds)) {
                    List<CxrEmployee> employeeList = cxrEmployeeMapper.selectList(new LambdaQueryWrapper<CxrEmployee>()
                        .in(CxrEmployee::getId, employeeIds)
                        .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                        .eq(CxrEmployee::getCxrSiteId, cxrCustomerAddressBo.getCxrSiteId())
                        .groupBy(CxrEmployee::getId));

                    Map<Long, CxrSite> map = siteList.stream()
                        .collect(Collectors.toMap(CxrSite::getId, Function.identity(), (v1, v2) -> v2));
                    Map<Long, CxrEmployee> employeeMap = employeeList.stream()
                        .collect(Collectors.toMap(CxrEmployee::getId,
                            Function.identity(), (v1, v2) -> v2));

                    //人员名称 人员编号
                    for (CxrCustomerAddressVo s : addressVoList) {
                        CxrSite cxrSite = map.get(s.getCxrSiteId());
                        if (ObjectUtil.isNotEmpty(cxrSite)) {
                            s.setCxrSiteName(cxrSite.getName());
                        }
                        CxrEmployee cxrEmployee = employeeMap.get(s.getCxrEmployeeId());
                        if (ObjectUtil.isNotEmpty(cxrEmployee)) {
                            s.setEmployeeName(cxrEmployee.getName());
                            s.setJobNumber(cxrEmployee.getJobNumber());
                        }
                    }
                }

            }
            //站点有值,人员有值
            if (ObjectUtil.isNotEmpty(cxrCustomerAddressBo.getCxrSiteId()) && ObjectUtil
                .isNotEmpty(cxrCustomerAddressBo.getCxrEmployeeId())) {

                List<CxrSite> siteList = cxrSiteMapper.selectList(new LambdaQueryWrapper<CxrSite>()
                    .in(CxrSite::getId, cxrCustomerAddressBo.getCxrSiteId())
                    .eq(CxrSite::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                    .groupBy(CxrSite::getId));

                List<CxrEmployee> employeeList = cxrEmployeeMapper.selectList(new LambdaQueryWrapper<CxrEmployee>()
                    .in(CxrEmployee::getId, cxrCustomerAddressBo.getCxrEmployeeId())
                    .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                    .groupBy(CxrEmployee::getId));

                Map<Long, CxrSite> map = siteList.stream()
                    .collect(Collectors.toMap(CxrSite::getId, Function.identity(), (v1, v2) -> v2));
                Map<Long, CxrEmployee> employeeMap = employeeList.stream().collect(Collectors.toMap(CxrEmployee::getId,
                    Function.identity(), (v1, v2) -> v2));

                //人员名称 人员编号
                for (CxrCustomerAddressVo s : addressVoList) {
                    CxrSite cxrSite = map.get(s.getCxrSiteId());
                    if (ObjectUtil.isNotEmpty(cxrSite)) {
                        s.setCxrSiteName(cxrSite.getName());
                    }
                    CxrEmployee cxrEmployee = employeeMap.get(s.getCxrEmployeeId());
                    if (ObjectUtil.isNotEmpty(cxrEmployee)) {
                        s.setEmployeeName(cxrEmployee.getName());
                        s.setJobNumber(cxrEmployee.getJobNumber());
                    }
                }

            }
        }

        pageTableDataInfo.setRows(listVoPageInfo.getRecords());
        pageTableDataInfo.setTotal(listVoPageInfo.getTotal());
        pageTableDataInfo.setCurr(pageQuery.getPageNum());
        pageTableDataInfo.setSize(pageQuery.getPageSize());
        return pageTableDataInfo;
    }

    @Override
    public List<CxrCustomerAddress> queryByIds(List<CxrCustomerAddress> addresses) {
        List<Long> ids = addresses.stream().map(s -> {
            return s.getId();
        }).collect(Collectors.toList());

        return this.baseMapper.selectList(new LambdaQueryWrapper<CxrCustomerAddress>()
            .in(CxrCustomerAddress::getId, ids)
            .eq(CxrCustomerAddress::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
        );
    }


    /**
     * 更新配送状态
     *
     * @param
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAddressEnableStatus(LocalDate deliveryTime) {
        LocalDate suspendEndTime = deliveryTime.minusDays(1);
        baseMapper.updateBatcham(suspendEndTime, deliveryTime);
        baseMapper.updateBatchpm(suspendEndTime, deliveryTime);
        log.info("更改与用户起送状态完成");


    }


    @Override
    public List<CxrSaleProductVo> selectProductByAddressId(Long id) {
        //获取地址  拿取站点id
        CxrCustomerAddress cxrCustomerAddress = this.getById(id);
        Long cxrSiteId = cxrCustomerAddress.getCxrSiteId();
        if (cxrSiteId == null) {
            throw new ServiceException("该地址没有配送站点,请检查!");
        }
        //根据站点拿取商品
        List<CxrSaleProductVo> cxrSaleProductVos = iCxrSaleProductService.querySaleProductVoBySiteId(cxrSiteId);
        return cxrSaleProductVos;
    }

    @Override
    public List<CxrAddressVo> getAddressids(CxrAddressBo bo) {
        return baseMapper.getAddressids(bo);
    }

    @Override
    public List<CxrAddressVo> checkmilkDistributionDetail(Long id, String del) {
        return baseMapper.checkmilkDistributionDetail(id, del);
    }

    @Override
    public int updateChangeMilk(CxrAddressBo bo) {
        int i = baseMapper.updateChangeMilk(bo);
        if (i > 0) {
            iCxrCustomerService.updateChangeMilkChangeRecord(bo);
        }
        return i;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCustomerReceiverPhone(CxrAddressBo bo) {

        return baseMapper.updateCustomerReceiverPhone(bo);
    }

    @Override
    public int updateCustomerInfo(List<CxrCustomerAddress> addresses) {
        return baseMapper.updateCustomerAddressMilkInfo(addresses);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateAmChangeSendOrStopChangeRecord(CxrAddressBo bo) {
        return baseMapper.updateAmChangeSendOrStopChangeRecord(bo);
    }

    @Override
    public int updatePmChangeSendOrStopChangeCustomer(CxrAddressBo bo) {
        return baseMapper.updatePmChangeSendOrStopChangeCustomer(bo);
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void getCustomerAddressLoglat(AddressBo bo) {
        Integer limit = 1000;
        Long id = 0L;

        String baseUrl = txMapConfig.getUrl() + "?address="; // 基础URL
        String query = "&key=" + txMapConfig.getAppKey(); // key值
        OkHttpClient client = new OkHttpClient();//长连接
        while (true) {
            List<CxrCustomerAddress> records = baseMapper.getCustomerAddressLoglat(id, limit);
            if (CollUtil.isEmpty(records)) {
                break;
            } else {
                for (CxrCustomerAddress address : records) {
                    StringBuffer buffer = new StringBuffer();
                    buffer.append(address.getProvice());
                    buffer.append(address.getCity());
                    buffer.append(address.getArea());
                    buffer.append(address.getDetailDistributionAddress());

                    int index = buffer.indexOf("#");
                    while (index != -1) {
                        buffer.deleteCharAt(index);
                        index = buffer.indexOf("#");
                    }

                    String path = baseUrl + buffer.toString(); // 参数
                    String url = baseUrl + path + query; // 构建完整的URL
                    Request request = new Request.Builder()
                        .url(url)
                        .build();
                    try {
                        Response response = client.newCall(request).execute();
                        String responseBody = response.body().string();

                        JSONObject jsonObject = new JSONObject(responseBody);
                        // 在这里对响应数据进行解析，获取需要的地址或坐标信息
                        System.out.println(responseBody);

                        Object lng = jsonObject.getJSONObject("result").getJSONObject("location").get("lng");
                        Object lat = jsonObject.getJSONObject("result").getJSONObject("location").get("lat");
                        Object level = jsonObject.getJSONObject("result").get("level");

                        baseMapper.update(null, new LambdaUpdateWrapper<CxrCustomerAddress>()
                            .eq(CxrCustomerAddress::getId, address.getId())
                            .set(CxrCustomerAddress::getLatitude, lat)
                            .set(CxrCustomerAddress::getLongitude, lng)
                            .set(CxrCustomerAddress::getLevel, level));

                        // Thread.sleep(1000);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }

            if (records.size() < limit) {
                break;
            }
            id = records.get(records.size() - 1).getId();
        }
    }

    @Override
    public List<CustomerAddressDetailVo> getByCustomerId() {
        LoginEmployee loginUser = CustomerLoginHelper.getLoginUser();
        List<CustomerAddressDetailVo> getByCustomerId = baseMapper.getByCustomerId(loginUser.getUserId());
        for (CustomerAddressDetailVo customerAddressDetailVo : getByCustomerId) {
            String customerDateil = StringUtils.format("{}{}{}{}", customerAddressDetailVo.getProvice(),
                customerAddressDetailVo.getCity(), customerAddressDetailVo.getArea(),
                customerAddressDetailVo.getDetailDistributionAddress());
            customerAddressDetailVo.setCustomerDateil(customerDateil);
        }
        return getByCustomerId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateAmChangeSendOrStop(CxrAddressBo bo) {
        return baseMapper.updateAmChangeSendOrStop(bo);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updatePmChangeSendOrStop(CxrAddressBo bo) {
        int i = baseMapper.updatePmChangeSendOrStop(bo);
        if (i > 0) {
            baseMapper.updatePmChangeSendOrStopChangeCustomer(bo);
        }
        return i;
    }

    @Override
    public List<CxrCustomerAddress> queryGetByIds(CxrAddressBo bo) {
        return this.baseMapper.selectList(new LambdaQueryWrapper<CxrCustomerAddress>()
            .in(CxrCustomerAddress::getId, bo.getId())
            .eq(CxrCustomerAddress::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
        );
    }

    @Override
    public List<CxrCustomerAddress> getCustomerAddresses(Long id) {
        return baseMapper.selectList(new LambdaQueryWrapper<CxrCustomerAddress>()
            .eq(CxrCustomerAddress::getId, id)
            .eq(CxrCustomerAddress::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
        );
    }

    /**
     * 根据客户id查询
     *
     * @param id
     * @return
     */
    @Override
    public List<CxrCustomerAddress> getCustomerAddressesByCustomerId(Long id) {
        return baseMapper.selectList(new LambdaQueryWrapper<CxrCustomerAddress>()
            .eq(CxrCustomerAddress::getCxrCustomerId, id)
            .eq(CxrCustomerAddress::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
        );
    }

    @Override
    public List<CxrCustomerAddress> getCustomerAddressPhone(String receiverPhone) {
        List<CxrCustomerAddress> cxrCustomerAddress = baseMapper.selectList(
            new LambdaUpdateWrapper<CxrCustomerAddress>()
                .eq(CxrCustomerAddress::getReceiverPhone, receiverPhone)
                .eq(CxrCustomerAddress::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));
        return cxrCustomerAddress;
    }

    @Override
    public List<CxrCustomerAddressDTO> cxrCustomerAddressDTOList(Long customerId) {
        return baseMapper.cxrCustomerAddressDTOList(customerId);
    }

    public int updateByCustomerId(Long customerId) {
        LocalDate deliveryDate = LocalDate.now().plusDays(2);
        log.info("订单修改用户{}起送时间：{}", customerId, deliveryDate);
        return baseMapper.updateByCustomerId(customerId, deliveryDate);
    }

    @Override
    public String queryDetailAddressByCustomerId(Long CustomerId) {

        return baseMapper.queryDetailAddressByCustomerId(CustomerId);
    }

    @Override
    public CxrEmployee getMatchSiteAndEmployeByAddr(Long siteId, Long customerId, String fullAddress) {
        if (StringUtils.isBlank(fullAddress)) {
            return null;
        }
        BigDecimal lnglatFactor = searchRange.divide(trans_to_m_unit, 1, RoundingMode.HALF_UP);
        Location location = locationUtils.getLocation(fullAddress);
        BigDecimal longitude = location.getLng();
        BigDecimal latitude = location.getLat();
        if (ObjectUtils.anyNull(longitude, latitude)) {
            log.info("\n解析地址失败：{},小程序提交用户customerId:{}", fullAddress, customerId);
            return null;
        }
        BigDecimal beginLongitude = longitude.subtract(lnglatFactor).setScale(6, RoundingMode.HALF_DOWN);
        BigDecimal endLongitude = longitude.add(lnglatFactor).setScale(6, RoundingMode.HALF_DOWN);
        BigDecimal beginLatitude = latitude.subtract(lnglatFactor).setScale(6, RoundingMode.HALF_DOWN);
        BigDecimal endLatitude = latitude.add(lnglatFactor).setScale(6, RoundingMode.HALF_DOWN);
        CxrCustomerAddressAppletBo bo = new CxrCustomerAddressAppletBo();
        bo.setBeginLongitude(beginLongitude.doubleValue());
        bo.setEndLongitude(endLongitude.doubleValue());
        bo.setBeginLatitude(beginLatitude.doubleValue());
        bo.setEndLatitude(endLatitude.doubleValue());
        bo.setLongitude(longitude.doubleValue());
        bo.setLatitude(latitude.doubleValue());
        BigDecimal nearSiteDistance = BigDecimal.valueOf(1000L);
        CxrSiteVo cxrSite = cxrSiteMapper.getAddressDistance(null, bo);
        if (Objects.nonNull(cxrSite)) {
            nearSiteDistance = BigDecimal.valueOf(cxrSite.getDistance()).multiply(new BigDecimal(1000));//km
        }
        BigDecimal nearCustomerDistance = BigDecimal.valueOf(1000L);
        CxrCustomerAddressVo customerAddressVo = cxrCustomerAddressMapper.getCustomerDistanceByLngLat(customerId,
            siteId, beginLongitude, endLongitude, beginLatitude, endLatitude, longitude, latitude);
        if (Objects.isNull(customerAddressVo)) {
            customerAddressVo = cxrCustomerAddressMapper.getCustomerDistanceByLngLat(customerId, null, beginLongitude,
                endLongitude, beginLatitude, endLatitude, longitude, latitude);
        }
        if (Objects.nonNull(customerAddressVo)) {
            nearCustomerDistance = customerAddressVo.getDistance();
        }
        log.info(
            "\n开始试喝客户匹配站点和配送员：\n试喝客户填写地址：{}\n腾讯接口解析地址经纬度：{},{}\n试喝客户地址与站点距离：{}米\n试喝客户地址与最近用户距离：{}米",
            fullAddress, longitude, latitude, nearSiteDistance, nearCustomerDistance);
        Long cxrEmployeeId = null;
        if (nearSiteDistance.compareTo(nearCustomerDistance) == 1) {
            log.info("\n最近老客户匹配");
            //最近客户
            cxrEmployeeId = customerAddressVo.getCxrEmployeeId();
        } else {
            log.info("\n最近最近站点匹配");
            //最近站点
            CxrCustomerAddressVo nearSiteCustomerAddressVo = cxrCustomerAddressMapper.getCustomerDistanceByLngLat(
                customerId, cxrSite.getId(), beginLongitude, endLongitude, beginLatitude, endLatitude, longitude,
                latitude);
            cxrEmployeeId = Objects.nonNull(nearSiteCustomerAddressVo) ? nearSiteCustomerAddressVo.getCxrEmployeeId()
                : cxrEmployeeId;
        }
        if (Objects.isNull(cxrEmployeeId)) {
            if (ObjectUtil.isNotEmpty(cxrSite)) {
                CxrEmployee cxrEmployee = new CxrEmployee();
                cxrEmployee.setCxrSiteId(cxrSite.getId());
                cxrEmployee.setCxrRootRegionName(cxrSite.getCxrRootRegionName());
                cxrEmployee.setCxrRootRegionId(cxrSite.getCxrRootRegionId());
                return cxrEmployee;
            } else {
                return null;
            }
        }
        CxrEmployee cxrEmployee = cxrEmployeeMapper.selectById(cxrEmployeeId);
        return cxrEmployee;
    }

    @Override
    public PageTableDataInfo<CxrCustomerAddressQueryVo> customerPageQuery(CxrCustomerAddressQueryBo bo) {
        IPage<CxrCustomerAddressQueryVo> customerPageQuery = baseMapper
            .customerPageQuery(bo, bo.build());
        List<CxrCustomerAddressQueryVo> records = customerPageQuery.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            //取地址里面的客户id进行过滤
            List<Long> customerIds = records.stream().map(CxrCustomerAddressQueryVo::getCxrCustomerId).distinct()
                .collect(
                    Collectors.toList());
            List<CxrCustomer> cxrCustomerList = customerMapper.selectList(new LambdaQueryWrapper<CxrCustomer>()
                .select(CxrCustomer::getPhone, CxrCustomer::getId)
                .eq(CxrCustomer::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .in(CxrCustomer::getId, customerIds)
            );
            Map<Long, CxrCustomer> customerMap = cxrCustomerList.stream().collect(Collectors.toMap(CxrCustomer::getId,
                Function.identity(), (v1, v2) -> v2));

            //查询站点
            List<Long> siteIds = records.stream().map(CxrCustomerAddressQueryVo::getCxrSiteId)
                .collect(Collectors.toList());
            List<CxrSite> siteList = cxrSiteMapper.selectList(new LambdaQueryWrapper<CxrSite>()
                .eq(CxrSite::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .in(CxrSite::getId, siteIds)
            );
            Map<Long, CxrSite> siteMap = siteList.stream().collect(Collectors.toMap(CxrSite::getId, Function.identity(),
                (v1, v2) -> v2));
            //销售代理
            List<Long> employeeIds =
                records.stream().map(CxrCustomerAddressQueryVo::getCxrEmployeeId).collect(Collectors.toList());
            List<CxrEmployee> employeeList = cxrEmployeeMapper.selectList(new LambdaQueryWrapper<CxrEmployee>()
                .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .in(CxrEmployee::getId, employeeIds)
            );
            Map<Long, CxrEmployee> employeeMap = employeeList.stream()
                .collect(Collectors.toMap(CxrEmployee::getId, Function.identity(),
                    (v1, v2) -> v2));
            //主管
            List<CxrEmployeePost> cxrEmployeePostList =
                cxrEmployeePostMapper.selectList(new LambdaQueryWrapper<CxrEmployeePost>()
                    .in(CxrEmployeePost::getCxrSiteId, siteIds)
                    .eq(CxrEmployeePost::getChargeStatus, ChargeStatus.IN_PROGRESS.getValue())
                    .eq(CxrEmployeePost::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                    .apply("JSON_OVERLAPS(JSON_EXTRACT(cxr_post_id,'$.value'),{0})", PostType.DIRECTOR.getValue()));

            Map<Long, CxrEmployeePost> postMap = cxrEmployeePostList.stream()
                .collect(Collectors.toMap(CxrEmployeePost::getCxrSiteId,
                    Function.identity(),
                    (v1, v2) -> v2));

            List<Long> directorIds = cxrEmployeePostList.stream().map(CxrEmployeePost::getCxrEmployeeId).collect(
                Collectors.toList());

            Map<Long, CxrEmployee> employeeDirectorMap = new HashMap<>();
            if (ObjectUtil.isNotEmpty(directorIds)) {
                List<CxrEmployee> employeeDirectorList = cxrEmployeeMapper.selectList(
                    new LambdaQueryWrapper<CxrEmployee>()
                        .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                        .in(CxrEmployee::getId, directorIds)
                );

                employeeDirectorMap = employeeDirectorList.stream()
                    .collect(Collectors.toMap(CxrEmployee::getId, Function.identity(),
                        (v1, v2) -> v2));
            }

            for (CxrCustomerAddressQueryVo vo : records) {
                CxrCustomer cxrCustomer = customerMap.get(vo.getCxrCustomerId());
                if (ObjectUtil.isNotEmpty(cxrCustomer)) {
                    vo.setPhone(cxrCustomer.getPhone());
                }
                CxrSite cxrSite = siteMap.get(vo.getCxrSiteId());
                if (ObjectUtil.isNotEmpty(cxrSite)) {
                    vo.setCxrSiteName(cxrSite.getName());
                    vo.setSiteMark(cxrSite.getSiteMark());
                    vo.setRegionId(cxrSite.getCxrRootRegionId());
                    vo.setRegionName(cxrSite.getCxrRootRegionName());
                    vo.setCurrentDeptId(cxrSite.getCurrentDeptId());
                }
                CxrEmployee cxrEmployee = employeeMap.get(vo.getCxrEmployeeId());
                if (ObjectUtil.isNotEmpty(cxrEmployee)) {
                    vo.setCxrEmployeeName(cxrEmployee.getName());
                    vo.setJobNumber(cxrEmployee.getJobNumber());

                }
                CxrEmployeePost post = postMap.get(vo.getCxrSiteId());
                if (ObjectUtil.isNotEmpty(post)) {
                    vo.setDirectorId(post.getCxrEmployeeId());
                    CxrEmployee employeePost = employeeDirectorMap.get(vo.getDirectorId());
                    if (ObjectUtil.isNotEmpty(employeePost)) {
                        vo.setDirectorName(employeePost.getName());
                    }
                }
            }
        }
        return PageTableDataInfo.build(customerPageQuery);
    }

    @Override
    public void updateCustomerAddressDelivery(LocalDate startDeliveryDate, Long addressId) {

        CustomerAddressMilkDistributionInfo distributionInfo = builderAddressDeliveryInfo(startDeliveryDate, addressId);
        CxrCustomerAddress address = this.getById(addressId);
        mergeInfo(address.getAmDistributionInfo(), distributionInfo);
        this.lambdaUpdate()
            .set(CxrCustomerAddress::getAmDistributionInfo, JSONUtil.toJsonStr(distributionInfo))
            .set(CxrCustomerAddress::getAmDistributionStatus, SysYesNo.YES.getValue())
            .set(CxrCustomerAddress::getAmDistributionStartDeliveryTime, startDeliveryDate)
            .eq(CxrCustomerAddress::getId, addressId)
            .update();
    }

    @Override
    public void updateCustomerAddressDelivery(CustomerAddressMilkDistributionInfo distributionInfo, Long addressId) {

        CxrCustomerAddress address = this.getById(addressId);
        CustomerAddressMilkDistributionInfo amDistributionInfo = address.getAmDistributionInfo();
        subDistribution(amDistributionInfo, distributionInfo);
        this.lambdaUpdate().set(CxrCustomerAddress::getAmDistributionInfo, JSONUtil.toJsonStr(amDistributionInfo)).eq(CxrCustomerAddress::getId, addressId).update();
    }

    @Override
    public CustomerAddressMilkDistributionDTO mergeCustomerAddressDelivery(LocalDate startDeliveryDate, Long addressId) {

        CustomerAddressMilkDistributionDTO customerAddressMilkDistributionDTO = new CustomerAddressMilkDistributionDTO();
        CustomerAddressMilkDistributionInfo distributionInfo = builderAddressDeliveryInfo(startDeliveryDate, addressId);
        customerAddressMilkDistributionDTO.setDistributionInfoStr(JSONUtil.toJsonStr(distributionInfo));
        customerAddressMilkDistributionDTO.setDistributionInfo(distributionInfo);
        CxrCustomerAddress address = this.getById(addressId);
        mergeInfo(address.getAmDistributionInfo(), distributionInfo);
        return customerAddressMilkDistributionDTO;
    }

    public void mergeInfo(CustomerAddressMilkDistributionInfo oldInfo, CustomerAddressMilkDistributionInfo newInfo) {
        mergeList(oldInfo.getMonday(), newInfo.getMonday(), (a) -> newInfo.setMonday(a));
        mergeList(oldInfo.getTuesday(), newInfo.getTuesday(), (a) -> newInfo.setTuesday(a));
        mergeList(oldInfo.getWednesday(), newInfo.getWednesday(), (a) -> newInfo.setWednesday(a));
        mergeList(oldInfo.getThursday(), newInfo.getThursday(), (a) -> newInfo.setThursday(a));
        mergeList(oldInfo.getFriday(), newInfo.getFriday(), (a) -> newInfo.setFriday(a));
        mergeList(oldInfo.getSaturday(), newInfo.getSaturday(), (a) -> newInfo.setSaturday(a));
    }

    public void mergeList(List<MilkDistributionInfo> oldFriday, List<MilkDistributionInfo> newFriday, Consumer<List> consumer) {
        oldFriday.addAll(newFriday);
        LinkedHashMap<Long, MilkDistributionInfo> map = new LinkedHashMap<>();
        for (MilkDistributionInfo milkDistributionInfo : oldFriday) {
            MilkDistributionInfo distributionInfo = map.get(milkDistributionInfo.getProductId());
            if (distributionInfo != null) {
                distributionInfo.setQuantity(distributionInfo.getQuantity() + milkDistributionInfo.getQuantity());
            } else {
                map.put(milkDistributionInfo.getProductId(), milkDistributionInfo);
            }
        }
        consumer.accept(map.values().stream().collect(Collectors.toList()));
    }

    public void subDistribution(CustomerAddressMilkDistributionInfo oldInfo, CustomerAddressMilkDistributionInfo newInfo) {
        subDistribution(oldInfo.getMonday(), newInfo.getMonday());
        subDistribution(oldInfo.getTuesday(), newInfo.getTuesday());
        subDistribution(oldInfo.getWednesday(), newInfo.getWednesday());
        subDistribution(oldInfo.getThursday(), newInfo.getThursday());
        subDistribution(oldInfo.getFriday(), newInfo.getFriday());
        subDistribution(oldInfo.getSaturday(), newInfo.getSaturday());
    }

    public void subDistribution(List<MilkDistributionInfo> oldFriday, List<MilkDistributionInfo> newFriday) {
        Map<Long, MilkDistributionInfo> map = newFriday.stream().collect(Collectors.toMap(MilkDistributionInfo::getProductId, Function.identity(), (a, b) -> a));
        for (MilkDistributionInfo milkDistributionInfo : oldFriday) {
            MilkDistributionInfo distributionInfo = map.get(milkDistributionInfo.getProductId());
            if (distributionInfo != null) {
                long quantity = milkDistributionInfo.getQuantity() - distributionInfo.getQuantity();
                milkDistributionInfo.setQuantity(quantity < 0 ? 0 : quantity);
            }
        }
    }

    @Override
    public CustomerAddressMilkDistributionInfo builderAddressDeliveryInfo(LocalDate startDeliveryDate, Long addressId) {

        CxrCustomerAddress address = this.getById(addressId);
        CustomerAddressMilkDistributionInfo distributionInfo = new CustomerAddressMilkDistributionInfo();

        List<CxrSaleProduct> cxrSaleProducts = iCxrSaleProductService.queryProcuctAllForYXN(address.getCxrSiteId());
        cxrSaleProducts.sort(Comparator.comparing(v -> v.getSpareId() == null ? 0 : v.getSpareId()));

        int size = cxrSaleProducts.size();
        DayOfWeek dayOfWeek = startDeliveryDate.getDayOfWeek();
        for (int i = 0; i < 6; ) {
            switch (dayOfWeek) {
                case MONDAY:
                    distributionInfo.setMonday(Arrays.asList(new MilkDistributionInfo(cxrSaleProducts.get(i % size))));
                    break;
                case TUESDAY:
                    distributionInfo.setTuesday(Arrays.asList(new MilkDistributionInfo(cxrSaleProducts.get(i % size))));
                    break;
                case WEDNESDAY:
                    distributionInfo.setWednesday(Arrays.asList(new MilkDistributionInfo(cxrSaleProducts.get(i % size))));
                    break;
                case THURSDAY:
                    distributionInfo.setThursday(Arrays.asList(new MilkDistributionInfo(cxrSaleProducts.get(i % size))));
                    break;
                case FRIDAY:
                    distributionInfo.setFriday(Arrays.asList(new MilkDistributionInfo(cxrSaleProducts.get(i % size))));
                    break;
                case SATURDAY:
                    distributionInfo.setSaturday(Arrays.asList(new MilkDistributionInfo(cxrSaleProducts.get(i % size))));
                    break;
            }
            if (dayOfWeek != DayOfWeek.SUNDAY) {
                i++;
            }
            dayOfWeek = dayOfWeek.plus(1);
        }

        return distributionInfo;
    }

    @Override
    public int updateStockById(Long addressId, Long qty) {
        return baseMapper.updateStockById(addressId, qty);
    }

    @Override
    public List<DeliverySiteDTO> deliverySiteQuery(Long customerId, String customerPhone) {
        List<CxrCustomerAddress> addressList = this.list(new LambdaQueryWrapper<CxrCustomerAddress>()
            .select(CxrCustomerAddress::getCxrCustomerId, CxrCustomerAddress::getReceiverPhone, CxrCustomerAddress::getCxrSiteId)
            .eq(ObjectUtil.isNotEmpty(customerId), CxrCustomerAddress::getCxrCustomerId, customerId)
            .eq(ObjectUtil.isNotEmpty(customerPhone), CxrCustomerAddress::getReceiverPhone, customerPhone)
            .eq(CxrCustomerAddress::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
        );
        if (CollUtil.isNotEmpty(addressList)) {
            List<DeliverySiteDTO> siteDTO = new ArrayList<>();
            Set<Long> siteIdCustomerAddress = addressList.stream().map(CxrCustomerAddress::getCxrSiteId).collect(Collectors.toSet());
            List<CxrSite> cxrSites = cxrSiteMapper.selectBatchIds(siteIdCustomerAddress);
            Map<Long, String> sitesMap = cxrSites.stream().collect(Collectors.toMap(a -> a.getId(), a -> a.getName(), (v1, v2) -> v1));
            for (Long siteId : siteIdCustomerAddress) {
                DeliverySiteDTO dto = new DeliverySiteDTO();
                String s1 = sitesMap.get(siteId);
                dto.setSiteId(siteId);
                if (ObjectUtil.isNotEmpty(s1)) {
                    dto.setSiteName(s1);
                }
                siteDTO.add(dto);
            }
            return siteDTO;
        }
        return null;
    }
}
