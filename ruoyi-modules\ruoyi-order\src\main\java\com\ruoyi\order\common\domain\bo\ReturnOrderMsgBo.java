package com.ruoyi.order.common.domain.bo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ReturnOrderMsgBo {

    //openId
    private String openId;
    //订单ID
    private Long returnOrderId;
    //客户电话
    private String customerPhone;
    //客户地址
    private String customerAddress;
    // 申请时间
    private Date applyTime;
    // 退款金额
    private BigDecimal returnAmount;

    //配送站点
    private String siteName;
    // 退订数量
    private Integer returnQuantity;

    private Integer eod;
}
