package com.ruoyi.order.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.business.base.api.domain.json.BusinessAgent;
import com.ruoyi.business.base.api.domain.json.ContractOrderCustomerInfo;
import com.ruoyi.business.base.api.domain.json.CustomerInfo;
import com.ruoyi.order.common.typeHandler.TypeHandlerConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 用户订单 镜像表
 *
 * @TableName cxr_user_order_mirror_image
 */
@EqualsAndHashCode(callSuper = false)
@TableName(value = "cxr_user_order_mirror_image", autoResultMap = true)
@Data
public class CxrUserOrderMirrorImage implements Serializable {
    /**
     * 编号
     */
    @TableId
    private Long id;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人名称
     */
    private String updateByName;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除状态(详情见字典)
     */
    private String deleteStatus;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 大区id
     */
    private Long bigAreaId;

    /**
     * 大区名称
     */
    private String bigAreaName;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 站点名称
     */
    private String siteName;

    /**
     * 站点地址
     */
    private String siteAdress;

    /**
     * 站点id
     */
    private Long siteId;

    @ApiModelProperty("业务代理详细信息")
    @TableField(typeHandler = TypeHandlerConstant.BusinessAgentTypeHandler.class)
    private List<BusinessAgent> businessAgent;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户电话;只取第一个录入的数据
     */
    private String customerPhone;

    /**
     * 客户地址; 只取第一个录入的数据
     */
    private String customerAdress;

    /**
     * 转入客户姓名
     */
    private String customerNameSwitch;

    /**
     * 转入客户地址
     */
    private String customerAdressSwitch;

    /**
     * 转入客户手机号
     */
    private String customerPhoneSwitch;

    /**
     * 转入人的客户id
     */
    private Long customerIdSwitch;

    /**
     * 1微信;2 支付宝
     */
    private String paymentSouce;

    /**
     * 订单类型;1、新订单2、增订单3、续订单4、转单5、换单6、退订单7、赠送单
     */
    private Integer orderType;

    /**
     * 0数量续单：用于客户账户没有鲜奶了;并且停奶超过了15天。15天后续单的，显示“超过15天”。
     */
    private Integer zeroQuantityRenewal;

    /**
     * 用于换单 换单的商品的类目  , 订单表的 convert_type
     */
    private String conversionType;

    /**
     * 单据;订单单据号
     */
    private String orderNo;

    /**
     * 商户单号
     */
    private String merchantOrderNo;

    /**
     * 收钱吧单号|富友单号
     */
    private String sqbSn;

    /**
     * 订购数量：鲜奶订购数量
     */
    private Integer orderQuantity;

    /**
     * 鲜奶赠送数量 (如果是退单,次字段用来记录退单鲜奶数)
     */
    private Integer freshMilkGiveQuantity;

    /**
     * 常温奶赠送数量 (如果是退单,次字段用来记录常温奶退掉的数)
     */
    private Integer longMilkGiveQuantity;

    /**
     * 超送数量：根据订单套餐超送规则计算出来的超送数量;祥看原型
     */
    private Integer excessQuantity;

    /**
     * 鲜奶已送数量
     */
    private Integer freshMilkSentQuantity;

    /**
     * 常温奶已送数量(已废弃!!!!!!)
     */
    private Integer longMilkSentQuantity;

    /**
     * 剩余数量 , 也是客户列表里的 总数量 （鲜奶订购数量 - 鲜奶已送数量）
     */
    private Integer surplusQuantity;

    /**
     * 转换数量，订单类型为“转单”的时候才会有值;即转换的鲜奶数量
     */
    private Integer conversionQuantity;

    /**
     * 单价：鲜奶单价
     */
    private BigDecimal unitPrice;

    /**
     * 金额：该笔订单的总金额  ,  如果类型是退订单 则是退订单金额 ，用正数方式
     */
    private BigDecimal amount;

    /**
     * 刷卡金额
     */
    private BigDecimal creditCardAmount;

    /**
     * 订单日期
     */
    private Date orderDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 1 待审核 、 2 已审核 、 3.已拒绝
     */
    private Integer auditStatus;

    /**
     * 审核人
     */
    private Long auditBy;

    /**
     * 审核人名字
     */
    private String auditByName;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 是否促销单;true 是 false 否
     */
    private Integer promotionalOrderFlag;

    /**
     * 是否师徒单;true是 false 否
     */
    private Integer apprenticeOrderFlag;

    /**
     * 终端类型  1 后端 2 配送端
     */
    private Integer terminalType;

    /**
     * 客户信息
     */
    @ApiModelProperty(" 客户信息")
    @TableField(typeHandler = TypeHandlerConstant.CustomerInfoTypeHandler.class)
    private List<CustomerInfo> customerInfoList;

    /**
     * 单据图片
     */
    private String orderImages;

    /**
     * 支付截图
     */
    private String playImages;

    /**
     * 支付状态
     */
    private Integer payStatus;

    /**
     * 完善状态 ( 1 =未完善, 2 =已完善)
     */
    private Integer perfectStatus;

    /**
     * 鲜奶退订数量
     */
    private Integer freshMilkReturnQuantity;

    /**
     * 合订单扩展数据
     */
    @ApiModelProperty(" 合订单客户信息")
    @TableField(typeHandler = TypeHandlerConstant.ContractOrderCustomerInfoTypeHandler.class)
    private List<ContractOrderCustomerInfo> contractOrderExt;

    /**
     * 无用字段后期删除
     */
    private Integer orderStatus;

    /**
     * 支付实际
     */
    private Date payTime;

    /**
     * 合订单总金额
     */
    private BigDecimal contractTotalAmount;

    /**
     * 合订单数量
     */
    private Integer contractTotalOrderQuantity;

    /**
     * 换单商品兑换id
     */
    private Long exchangeProductId;

    /**
     * 换单兑换数量
     */
    private Integer exchangeSum;

    /**
     * 换单鲜奶兑换商品数量
     */
    private Integer milkExchangeSum;

    /**
     * 换单商品名称
     */
    private String exchangeProductName;

    /**
     * 换单兑换商品规格
     */
    private String spec;

    /**
     * 是否是导入单
     */
    private Integer isImport;

    /**
     * 活动赠品数量
     */
    private Integer activityGiveQuantity;

    /**
     * 活动赠品已送数量
     */
    private Integer activitySentQuantity;

    /**
     *
     */
    private Long customerId;

    /**
     * 活动类型
     */
    private String activityType;

    /**
     * 给第三方的订单号
     */
    private String thirdOrderNo;

    /**
     * 是否是新客户
     */
    private String newCustomerFlag;

    /**
     *
     */
    private String outLongMilkOrderNo;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 选择礼品数据
     */
    private String giveGiftList;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 套餐价格
     */
    private BigDecimal setMealPrice;

    /**
     * 套餐数量
     */
    private Integer setMealQty;

    /**
     * 订单赠送数是否参与增订单核算
     */
    private Integer pigcFlag;

    /**
     * 分摊费用
     */
    private BigDecimal apportionMoney;

    /**
     * 订单审核次数
     */
    private Integer auditCount;

    /**
     * third_order_no 生成时间
     */
    private Date thirdOrderNoTime;

    /**
     * 合订单的订单类型标签
     */
    private Integer contractTypeTag;

    /**
     * 退订打款标识
     */
    private Integer refundSuccessFlag;

    /**
     * 创建人编号
     */
    private String createByCode;

    /**
     * 0收钱吧 1管家婆 2富友
     */
    private Integer payPlaformType;

    /**
     * 扫付款二维码次数
     */
    private Integer scanQrTimes;

    /**
     * 合订单的订单类型
     */
    private Integer coType;

    /**
     * 账户类型:1.未打款、2.已打款、3.打款失败
     */
    private Integer paymentStatus;

    /**
     * 打款失败原因
     */
    private String paymentFailureReasons;

    /**
     * 用户主订单id
     */
    private Long payOrderId;

    /**
     * 抖音退款单号
     */
    private String tiktokOrderRefundNo;

    /**
     * 平台折扣金额
     */
    private BigDecimal platformDiscount;

    /**
     * 支付金额
     */
    private BigDecimal payAmount;

    /**
     * 渠道：0.线下，1.小程序，2.抖音
     */
    private Integer channel;

    /**
     * 商品分类
     */
    private String productType;

    /**
     * 商品id
     */
    private String productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 核算类型：1.正常核算 ，2. 抖音推广
     */
    private Integer accountingType;

    /**
     * 渠道推广提成：1.有，0.无
     */
    private Integer promotionCommission;

    /**
     * 配置id
     */
    private Long commissionConfigId;

    /**
     *
     */
    private String productTypeName;

    /**
     * 异常订单状态  1：异常
     */
    private Integer abnormalTag;

    /**
     * 预警时间
     */
    private Date abnormalTime;

    /**
     * 是否是老客户，之前有地址的客户
     */
    private Integer oldCustomer;

    /**
     * 0数量超过15天
     */
    private Integer zero15DayAfter;

    /**
     * 配送站点
     */
    private String deliverySites;

    /**
     * 退款审核不通过原因
     */
    private String refundNoAuditReasons;

    /**
     * 镜像时间
     */
    private LocalDateTime mirrorImageTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
