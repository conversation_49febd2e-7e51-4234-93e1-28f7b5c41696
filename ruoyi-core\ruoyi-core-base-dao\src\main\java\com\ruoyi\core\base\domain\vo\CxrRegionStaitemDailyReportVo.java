package com.ruoyi.core.base.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 区域单数统计明细Vo
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@ExcelIgnoreUnannotated
@ApiModel("区域单数统计明细Vo")
@Data
public class CxrRegionStaitemDailyReportVo implements Serializable {

    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    private Long id;

    /**
     * 统计日期
     */
    @ApiModelProperty("统计日期")
    @ExcelProperty("统计日期")
    private Date reportDate;

    /**
     * 星期
     */
    @ApiModelProperty("星期")
    @ExcelProperty("星期")
    private String weekDay;

    /**
     * 维度类型
     */
    @ApiModelProperty("维度类型")
    private String dimensionType;

    /**
     * 公司ID
     */
    @ApiModelProperty("公司ID")
    private Long companyId;

    /**
     * 公司名称
     */
    @ApiModelProperty("公司名称")
    @ExcelProperty("公司")
    private String companyName;

    /**
     * 大区ID
     */
    @ApiModelProperty("大区ID")
    private Long rootRegionId;

    /**
     * 大区名称
     */
    @ApiModelProperty("大区名称")
    @ExcelProperty("大区")
    private String rootRegionName;

    /**
     * 区域ID
     */
    @ApiModelProperty("区域ID")
    private Long regionId;

    /**
     * 区域名称
     */
    @ApiModelProperty("区域名称")
    @ExcelProperty("区域")
    private String regionName;

    /**
     * 区域编号
     */
    @ApiModelProperty("区域编号")
    @ExcelProperty("区域编号")
    private String regionCode;

    /**
     * 站点ID
     */
    @ApiModelProperty("站点ID")
    private Long siteId;

    /**
     * 站点名称
     */
    @ApiModelProperty("站点名称")
    @ExcelProperty("站点")
    private String siteName;

    /**
     * 站点编号
     */
    @ApiModelProperty("站点编号")
    @ExcelProperty("站点编号")
    private String siteCode;

    /**
     * 代理ID
     */
    @ApiModelProperty("代理ID")
    private Long agentId;

    /**
     * 代理姓名
     */
    @ApiModelProperty("代理姓名")
    @ExcelProperty("销售代理")
    private String agentName;

    /**
     * 代理编号
     */
    @ApiModelProperty("代理编号")
    @ExcelProperty("代理编号")
    private String agentCode;

    /**
     * 代理等级
     */
    @ApiModelProperty("代理等级")
    @ExcelProperty("代理等级")
    private String agentLevel;

    /**
     * 新订单数
     */
    @ApiModelProperty("新订单数")
    @ExcelProperty("新订单数")
    private BigDecimal newOrderCount = BigDecimal.ZERO;

    /**
     * 续订单数
     */
    @ApiModelProperty("续订单数")
    @ExcelProperty("续订单数")
    private BigDecimal continueOrderCount = BigDecimal.ZERO;

    /**
     * 增订单数
     */
    @ApiModelProperty("增订单数")
    @ExcelProperty("增订单数")
    private BigDecimal increaseOrderCount = BigDecimal.ZERO;

    /**
     * 退订单数
     */
    @ApiModelProperty("退订单数")
    @ExcelProperty("退订单数")
    private BigDecimal returnOrderCount = BigDecimal.ZERO;

    /**
     * 总订单数
     */
    @ApiModelProperty("总订单数")
    @ExcelProperty("总订单数")
    private BigDecimal totalOrderCount = BigDecimal.ZERO;

    /**
     * 新订单业绩
     */
    @ApiModelProperty("新订单业绩")
    @ExcelProperty("新订单业绩")
    private BigDecimal newOrderAchievement = BigDecimal.ZERO;

    /**
     * 续订单业绩
     */
    @ApiModelProperty("续订单业绩")
    @ExcelProperty("续订单业绩")
    private BigDecimal continueOrderAchievement = BigDecimal.ZERO;

    /**
     * 增订单业绩
     */
    @ApiModelProperty("增订单业绩")
    @ExcelProperty("增订单业绩")
    private BigDecimal increaseOrderAchievement = BigDecimal.ZERO;

    /**
     * 退订单业绩
     */
    @ApiModelProperty("退订单业绩")
    @ExcelProperty("退订单业绩")
    private BigDecimal returnOrderAchievement = BigDecimal.ZERO;

    /**
     * 总业绩
     */
    @ApiModelProperty("总业绩")
    @ExcelProperty("总业绩")
    private BigDecimal totalAchievement = BigDecimal.ZERO;

    /**
     * 新订单占比(%)
     */
    @ApiModelProperty("新订单占比(%)")
    @ExcelProperty("新订单占比(%)")
    private BigDecimal newOrderRatio = BigDecimal.ZERO;

    /**
     * 续订单占比(%)
     */
    @ApiModelProperty("续订单占比(%)")
    @ExcelProperty("续订单占比(%)")
    private BigDecimal continueOrderRatio = BigDecimal.ZERO;

    /**
     * 增订单占比(%)
     */
    @ApiModelProperty("增订单占比(%)")
    @ExcelProperty("增订单占比(%)")
    private BigDecimal increaseOrderRatio = BigDecimal.ZERO;

    /**
     * 退订单占比(%)
     */
    @ApiModelProperty("退订单占比(%)")
    @ExcelProperty("退订单占比(%)")
    private BigDecimal returnOrderRatio = BigDecimal.ZERO;

    /**
     * 新订单客单价
     */
    @ApiModelProperty("新订单客单价")
    @ExcelProperty("新订单客单价")
    private BigDecimal newOrderUnitPrice = BigDecimal.ZERO;

    /**
     * 续订单客单价
     */
    @ApiModelProperty("续订单客单价")
    @ExcelProperty("续订单客单价")
    private BigDecimal continueOrderUnitPrice = BigDecimal.ZERO;

    /**
     * 增订单客单价
     */
    @ApiModelProperty("增订单客单价")
    @ExcelProperty("增订单客单价")
    private BigDecimal increaseOrderUnitPrice = BigDecimal.ZERO;

    /**
     * 总平均客单价
     */
    @ApiModelProperty("总平均客单价")
    @ExcelProperty("总平均客单价")
    private BigDecimal avgUnitPrice = BigDecimal.ZERO;

    /**
     * 新订单数同比(%)
     */
    @ApiModelProperty("新订单数同比(%)")
    @ExcelProperty("新订单数同比(%)")
    private BigDecimal newOrderCountYoy = BigDecimal.ZERO;

    /**
     * 续订单数同比(%)
     */
    @ApiModelProperty("续订单数同比(%)")
    @ExcelProperty("续订单数同比(%)")
    private BigDecimal continueOrderCountYoy = BigDecimal.ZERO;

    /**
     * 增订单数同比(%)
     */
    @ApiModelProperty("增订单数同比(%)")
    @ExcelProperty("增订单数同比(%)")
    private BigDecimal increaseOrderCountYoy = BigDecimal.ZERO;

    /**
     * 退订单数同比(%)
     */
    @ApiModelProperty("退订单数同比(%)")
    @ExcelProperty("退订单数同比(%)")
    private BigDecimal returnOrderCountYoy = BigDecimal.ZERO;

    /**
     * 总订单数同比(%)
     */
    @ApiModelProperty("总订单数同比(%)")
    @ExcelProperty("总订单数同比(%)")
    private BigDecimal totalOrderCountYoy = BigDecimal.ZERO;

    /**
     * 新订单业绩同比(%)
     */
    @ApiModelProperty("新订单业绩同比(%)")
    @ExcelProperty("新订单业绩同比(%)")
    private BigDecimal newOrderAchievementYoy = BigDecimal.ZERO;

    /**
     * 续订单业绩同比(%)
     */
    @ApiModelProperty("续订单业绩同比(%)")
    @ExcelProperty("续订单业绩同比(%)")
    private BigDecimal continueOrderAchievementYoy = BigDecimal.ZERO;

    /**
     * 增订单业绩同比(%)
     */
    @ApiModelProperty("增订单业绩同比(%)")
    @ExcelProperty("增订单业绩同比(%)")
    private BigDecimal increaseOrderAchievementYoy = BigDecimal.ZERO;

    /**
     * 退订单业绩同比(%)
     */
    @ApiModelProperty("退订单业绩同比(%)")
    @ExcelProperty("退订单业绩同比(%)")
    private BigDecimal returnOrderAchievementYoy = BigDecimal.ZERO;

    /**
     * 总业绩同比(%)
     */
    @ApiModelProperty("总业绩同比(%)")
    @ExcelProperty("总业绩同比(%)")
    private BigDecimal totalAchievementYoy = BigDecimal.ZERO;

    /**
     * 新订单数环比(%)
     */
    @ApiModelProperty("新订单数环比(%)")
    @ExcelProperty("新订单数环比(%)")
    private BigDecimal newOrderCountMom = BigDecimal.ZERO;

    /**
     * 续订单数环比(%)
     */
    @ApiModelProperty("续订单数环比(%)")
    @ExcelProperty("续订单数环比(%)")
    private BigDecimal continueOrderCountMom = BigDecimal.ZERO;

    /**
     * 增订单数环比(%)
     */
    @ApiModelProperty("增订单数环比(%)")
    @ExcelProperty("增订单数环比(%)")
    private BigDecimal increaseOrderCountMom = BigDecimal.ZERO;

    /**
     * 退订单数环比(%)
     */
    @ApiModelProperty("退订单数环比(%)")
    @ExcelProperty("退订单数环比(%)")
    private BigDecimal returnOrderCountMom = BigDecimal.ZERO;

    /**
     * 总订单数环比(%)
     */
    @ApiModelProperty("总订单数环比(%)")
    @ExcelProperty("总订单数环比(%)")
    private BigDecimal totalOrderCountMom = BigDecimal.ZERO;

    /**
     * 新订单业绩环比(%)
     */
    @ApiModelProperty("新订单业绩环比(%)")
    @ExcelProperty("新订单业绩环比(%)")
    private BigDecimal newOrderAchievementMom = BigDecimal.ZERO;

    /**
     * 续订单业绩环比(%)
     */
    @ApiModelProperty("续订单业绩环比(%)")
    @ExcelProperty("续订单业绩环比(%)")
    private BigDecimal continueOrderAchievementMom = BigDecimal.ZERO;

    /**
     * 增订单业绩环比(%)
     */
    @ApiModelProperty("增订单业绩环比(%)")
    @ExcelProperty("增订单业绩环比(%)")
    private BigDecimal increaseOrderAchievementMom = BigDecimal.ZERO;

    /**
     * 退订单业绩环比(%)
     */
    @ApiModelProperty("退订单业绩环比(%)")
    @ExcelProperty("退订单业绩环比(%)")
    private BigDecimal returnOrderAchievementMom = BigDecimal.ZERO;

    /**
     * 总业绩环比(%)
     */
    @ApiModelProperty("总业绩环比(%)")
    @ExcelProperty("总业绩环比(%)")
    private BigDecimal totalAchievementMom = BigDecimal.ZERO;
}
