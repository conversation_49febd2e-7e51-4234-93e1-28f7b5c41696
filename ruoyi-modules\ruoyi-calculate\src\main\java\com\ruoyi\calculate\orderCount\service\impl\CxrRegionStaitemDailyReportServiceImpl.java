package com.ruoyi.calculate.orderCount.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.calculate.api.domain.enums.RegionStatisticsDimensionType;
import com.ruoyi.calculate.orderCount.domain.CxrRegionStaitemDailyReport;
import com.ruoyi.calculate.orderCount.mapper.CxrRegionStaitemDailyReportMapper;
import com.ruoyi.calculate.orderCount.service.CxrRegionStaitemDailyReportService;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.core.base.domain.bo.CxrRegionStaitemDailyReportBo;
import com.ruoyi.core.base.domain.vo.CxrRegionStaitemDailyReportVo;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【cxr_region_staitem_daily_report(区域单数统计明细日报表)】的数据库操作Service实现
* @createDate 2025-07-03 16:06:20
*/
@Slf4j
@Service
public class CxrRegionStaitemDailyReportServiceImpl extends ServiceImpl<CxrRegionStaitemDailyReportMapper, CxrRegionStaitemDailyReport>
    implements CxrRegionStaitemDailyReportService {

    @Override
    public Page<CxrRegionStaitemDailyReportVo> page(CxrRegionStaitemDailyReportBo bo, Page<Object> page) {

        Page<CxrRegionStaitemDailyReportVo> result;

        // 根据维度类型调用不同的查询方法，应用不同的权限控制逻辑
        String dimensionType = bo.getDimensionType();
        if (dimensionType == null || dimensionType.isEmpty()) {
            throw new ServiceException("维度类型不能为空");
        }

        // 使用枚举进行类型安全的判断
        RegionStatisticsDimensionType dimensionEnum = RegionStatisticsDimensionType.fromCode(dimensionType);

        switch (dimensionEnum) {
            case COMPANY:
                result = baseMapper.selectCompanyReportPage(bo, page);
                break;
            case ROOT_REGION:
                result = baseMapper.selectRootRegionReportPage(bo, page);
                break;
            case REGION:
                result = baseMapper.selectRegionReportPage(bo, page);
                break;
            case SITE:
                result = baseMapper.selectSiteReportPage(bo, page);
                break;
            case AGENT:
                result = baseMapper.selectAgentReportPage(bo, page);
                break;
            default:
                // 这里不会执行到，因为fromCode方法会抛出异常
                throw new IllegalArgumentException("不支持的维度类型: " + dimensionType);
        }


        return result;
    }

    @Override
    public CxrRegionStaitemDailyReportVo sum(CxrRegionStaitemDailyReportBo bo) {

        // 1. 根据维度类型查询当期聚合数据
        CxrRegionStaitemDailyReportVo currentSummary = getSummaryByDimensionType(bo);
        if (currentSummary == null) {
            currentSummary = new CxrRegionStaitemDailyReportVo();
        }

        // 2. 构造去年同期查询条件
        CxrRegionStaitemDailyReportBo lastYearBo = buildLastYearQuery(bo);

        // 3. 根据维度类型查询去年同期聚合数据
        CxrRegionStaitemDailyReportVo lastYearSummary = getSummaryByDimensionType(lastYearBo);
        if (lastYearSummary == null) {
            lastYearSummary = new CxrRegionStaitemDailyReportVo();
        }

        // 4. 计算时间段同比
        calculatePeriodYoyComparison(currentSummary, lastYearSummary);

        // 5. 重新计算占比和客单价（基于聚合数据）
        recalculateRatiosAndUnitPrices(currentSummary);

        return currentSummary;
    }

    /**
     * 根据维度类型获取合计数据
     */
    private CxrRegionStaitemDailyReportVo getSummaryByDimensionType(CxrRegionStaitemDailyReportBo bo) {
        String dimensionType = bo.getDimensionType();
        if (dimensionType == null) {
            throw new IllegalArgumentException("维度类型不能为空");
        }
        // 使用枚举进行类型安全的判断
        RegionStatisticsDimensionType dimensionEnum = RegionStatisticsDimensionType.fromCode(dimensionType);
        switch (dimensionEnum) {
            case COMPANY:
                return baseMapper.sumCompanyReport(bo);
            case ROOT_REGION:
                return baseMapper.sumRootRegionReport(bo);
            case REGION:
                return baseMapper.sumRegionReport(bo);
            case SITE:
                return baseMapper.sumSiteReport(bo);
            case AGENT:
                return baseMapper.sumAgentReport(bo);
            default:
                throw new IllegalArgumentException("不支持的维度类型：" + dimensionType);
        }
    }

    /**
     * 构造去年同期查询条件
     * 处理闰年问题：如果当前是2月29日，去年是非闰年，则返回null表示没有对应日期
     */
    private CxrRegionStaitemDailyReportBo buildLastYearQuery(CxrRegionStaitemDailyReportBo bo) {
        CxrRegionStaitemDailyReportBo lastYearBo = BeanUtil.copyProperties(bo, CxrRegionStaitemDailyReportBo.class);

        // 时间往前推一年，处理闰年问题
        if (bo.getStartDate() != null) {
            LocalDate lastYearStartDate = calculateLastYearDate(bo.getStartDate());
            if (lastYearStartDate == null) {
                // 如果去年没有对应的日期（如2月29日在非闰年），返回null
                return null;
            }
            lastYearBo.setStartDate(lastYearStartDate);
        }

        if (bo.getEndDate() != null) {
            LocalDate lastYearEndDate = calculateLastYearDate(bo.getEndDate());
            if (lastYearEndDate == null) {
                // 如果去年没有对应的日期，返回null
                return null;
            }
            lastYearBo.setEndDate(lastYearEndDate);
        }

        return lastYearBo;
    }

    /**
     * 计算去年同期日期，处理闰年问题  取消，怎样都会有问题
     *
     * @param currentDate 当前日期
     * @return 去年同期日期，如果当前是2月29日且去年不是闰年则返回null，否则返回去年同期日期
     */
    private static LocalDate calculateLastYearDate(LocalDate currentDate) {
//        // 检查当前日期是否是2月29日（闰年特有的日期）
//        if (currentDate.getMonthValue() == 2 && currentDate.getDayOfMonth() == 29) {
//            // 当前是2月29日，检查去年是否是闰年
//            int lastYear = currentDate.getYear() - 1;
//            if (!cn.hutool.core.date.DateUtil.isLeapYear(lastYear)) {
//                // 去年不是闰年，没有2月29日，返回null
//                log.warn("当前日期{}是2月29日，但去年{}不是闰年，无对应日期", currentDate, lastYear);
//                return null;
//            }
//        }
        // 其他情况正常减一年
        return currentDate.minusYears(1);
    }

    public static void main(String[] args) {
        LocalDate localDate = calculateLastYearDate(LocalDate.of(2024, 2, 29));
        System.out.println(localDate);
    }

    /**
     * 计算时间段同比
     */
    private void calculatePeriodYoyComparison(CxrRegionStaitemDailyReportVo current, CxrRegionStaitemDailyReportVo lastYear) {
        // 新订单数同比
        current.setNewOrderCountYoy(calculateYoyPercentage(current.getNewOrderCount(), lastYear.getNewOrderCount()));
        // 续订单数同比
        current.setContinueOrderCountYoy(calculateYoyPercentage(current.getContinueOrderCount(), lastYear.getContinueOrderCount()));
        // 增订单数同比
        current.setIncreaseOrderCountYoy(calculateYoyPercentage(current.getIncreaseOrderCount(), lastYear.getIncreaseOrderCount()));
        // 退订单数同比
        current.setReturnOrderCountYoy(calculateYoyPercentage(current.getReturnOrderCount(), lastYear.getReturnOrderCount()));
        // 总订单数同比
        current.setTotalOrderCountYoy(calculateYoyPercentage(current.getTotalOrderCount(), lastYear.getTotalOrderCount()));

        // 新订单业绩同比
        current.setNewOrderAchievementYoy(calculateYoyPercentage(current.getNewOrderAchievement(), lastYear.getNewOrderAchievement()));
        // 续订单业绩同比
        current.setContinueOrderAchievementYoy(calculateYoyPercentage(current.getContinueOrderAchievement(), lastYear.getContinueOrderAchievement()));
        // 增订单业绩同比
        current.setIncreaseOrderAchievementYoy(calculateYoyPercentage(current.getIncreaseOrderAchievement(), lastYear.getIncreaseOrderAchievement()));
        // 退订单业绩同比
        current.setReturnOrderAchievementYoy(calculateYoyPercentage(current.getReturnOrderAchievement(), lastYear.getReturnOrderAchievement()));
        // 总业绩同比
        current.setTotalAchievementYoy(calculateYoyPercentage(current.getTotalAchievement(), lastYear.getTotalAchievement()));
    }

    /**
     * 计算同比百分比
     */
    private BigDecimal calculateYoyPercentage(BigDecimal current, BigDecimal lastYear) {
        if (lastYear == null || lastYear.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return current.subtract(lastYear)
                .divide(lastYear.abs(), 4, RoundingMode.DOWN)
                .multiply(new BigDecimal("100"))
                .setScale(2, RoundingMode.DOWN);
    }

    /**
     * 重新计算占比和客单价
     */
    private void recalculateRatiosAndUnitPrices(CxrRegionStaitemDailyReportVo summary) {
        BigDecimal totalCount = summary.getTotalOrderCount();

        // 重新计算占比
        if (totalCount != null && totalCount.compareTo(BigDecimal.ZERO) > 0) {
            summary.setNewOrderRatio(calculateRatio(summary.getNewOrderCount(), totalCount));
            summary.setContinueOrderRatio(calculateRatio(summary.getContinueOrderCount(), totalCount));
            summary.setIncreaseOrderRatio(calculateRatio(summary.getIncreaseOrderCount(), totalCount));
            summary.setReturnOrderRatio(calculateRatio(summary.getReturnOrderCount(), totalCount));
        }

        // 重新计算客单价
        summary.setNewOrderUnitPrice(calculateUnitPrice(summary.getNewOrderAchievement(), summary.getNewOrderCount()));
        summary.setContinueOrderUnitPrice(calculateUnitPrice(summary.getContinueOrderAchievement(), summary.getContinueOrderCount()));
        summary.setIncreaseOrderUnitPrice(calculateUnitPrice(summary.getIncreaseOrderAchievement(), summary.getIncreaseOrderCount()));
        summary.setAvgUnitPrice(calculateUnitPrice(summary.getTotalAchievement(), summary.getTotalOrderCount()));
    }

    /**
     * 计算占比
     */
    private BigDecimal calculateRatio(BigDecimal part, BigDecimal total) {
        if (part == null || total == null || total.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return part.divide(total, 4, RoundingMode.DOWN)
                .multiply(new BigDecimal("100"))
                .setScale(2, RoundingMode.DOWN);
    }

    /**
     * 计算客单价
     */
    private BigDecimal calculateUnitPrice(BigDecimal achievement, BigDecimal count) {
        if (achievement == null || count == null || count.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return achievement.divide(count, 2, RoundingMode.DOWN);
    }
}




