package com.ruoyi.business.cxrEmployeeDistributionTransfer.domain.bo;

import com.ruoyi.business.cxrEmployeeDistributionTransfer.domain.CxrEmployeeDistributionTransfer;
import com.ruoyi.business.cxrEmployeeDistributionTransfer.domain.CxrEmployeeDistributionTransferMiddle;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TransferMiddleMqBo implements Serializable {

    private List<Long> addressIds;

    private List<CxrEmployeeDistributionTransferMiddle> listVos;

    private CxrEmployeeDistributionTransfer distributionTransfer;

}
