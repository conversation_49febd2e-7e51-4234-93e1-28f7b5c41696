package com.ruoyi.calculate.orderCount.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.calculate.orderCount.emuns.WeekdayMapping;
import com.ruoyi.common.excel.annotation.ExcelEnumValue;
import com.ruoyi.common.excel.convert.ExcelEnumValueConverter;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 区域单数统计明细Excel导出对象 - 代理维度
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@ExcelIgnoreUnannotated
@Data
public class CxrRegionStaitemDailyReportAgentExcel implements Serializable {

    /**
     * 统计日期
     */
    @ExcelProperty("统计日期")
    private Date reportDate;

    /**
     * 星期
     */
    @ExcelProperty(value = "星期", converter = ExcelEnumValueConverter.class)
    @ExcelEnumValue(value = WeekdayMapping.class, methodName = "getChineseWeekday")
    private String weekDay;

    /**
     * 公司名称
     */
    @ExcelProperty("公司")
    private String companyName;

    /**
     * 大区名称
     */
    @ExcelProperty("大区")
    private String rootRegionName;

    /**
     * 区域名称
     */
    @ExcelProperty("区域")
    private String regionName;

    /**
     * 区域编号
     */
    @ExcelProperty("区域编号")
    private String regionCode;

    /**
     * 站点名称
     */
    @ExcelProperty("站点")
    private String siteName;

    /**
     * 站点编号
     */
    @ExcelProperty("站点编号")
    private String siteCode;

    /**
     * 代理姓名
     */
    @ExcelProperty("销售代理")
    private String agentName;

    /**
     * 代理编号
     */
    @ExcelProperty("代理编号")
    private String agentCode;

    /**
     * 代理等级
     */
    @ExcelProperty("代理等级")
    private String agentLevel;

    // ========== 新订单 ==========
    /**
     * 新订单数
     */
    @ExcelProperty("新订单数")
    private BigDecimal newOrderCount = BigDecimal.ZERO;

//    /**
//     * 新订单数环比(%)
//     */
//    @ExcelProperty("新订单数环比(%)")
//    private BigDecimal newOrderCountMom = BigDecimal.ZERO;
//
//    /**
//     * 新订单数同比(%)
//     */
//    @ExcelProperty("新订单数同比(%)")
//    private BigDecimal newOrderCountYoy = BigDecimal.ZERO;

    /**
     * 新订单业绩
     */
    @ExcelProperty("新订单业绩")
    private BigDecimal newOrderAchievement = BigDecimal.ZERO;

//    /**
//     * 新订单业绩环比(%)
//     */
//    @ExcelProperty("新订单业绩环比(%)")
//    private BigDecimal newOrderAchievementMom = BigDecimal.ZERO;
//
//    /**
//     * 新订单业绩同比(%)
//     */
//    @ExcelProperty("新订单业绩同比(%)")
//    private BigDecimal newOrderAchievementYoy = BigDecimal.ZERO;

    /**
     * 新订单占比(%)
     */
    @ExcelProperty("新订单占比(%)")
    private BigDecimal newOrderRatio = BigDecimal.ZERO;

    /**
     * 新订单客单价
     */
    @ExcelProperty("新订单客单价")
    private BigDecimal newOrderUnitPrice = BigDecimal.ZERO;



    // ========== 增订单 ==========
    /**
     * 增订单数
     */
    @ExcelProperty("增订单数")
    private BigDecimal increaseOrderCount = BigDecimal.ZERO;

//    /**
//     * 增订单数环比(%)
//     */
//    @ExcelProperty("增订单数环比(%)")
//    private BigDecimal increaseOrderCountMom = BigDecimal.ZERO;
//
//    /**
//     * 增订单数同比(%)
//     */
//    @ExcelProperty("增订单数同比(%)")
//    private BigDecimal increaseOrderCountYoy = BigDecimal.ZERO;

    /**
     * 增订单业绩
     */
    @ExcelProperty("增订单业绩")
    private BigDecimal increaseOrderAchievement = BigDecimal.ZERO;

//    /**
//     * 增订单业绩环比(%)
//     */
//    @ExcelProperty("增订单业绩环比(%)")
//    private BigDecimal increaseOrderAchievementMom = BigDecimal.ZERO;
//
//    /**
//     * 增订单业绩同比(%)
//     */
//    @ExcelProperty("增订单业绩同比(%)")
//    private BigDecimal increaseOrderAchievementYoy = BigDecimal.ZERO;

    /**
     * 增订单占比(%)
     */
    @ExcelProperty("增订单占比(%)")
    private BigDecimal increaseOrderRatio = BigDecimal.ZERO;

    /**
     * 增订单客单价
     */
    @ExcelProperty("增订单客单价")
    private BigDecimal increaseOrderUnitPrice = BigDecimal.ZERO;

    // ========== 续订单 ==========
    /**
     * 续订单数
     */
    @ExcelProperty("续订单数")
    private BigDecimal continueOrderCount = BigDecimal.ZERO;

//    /**
//     * 续订单数环比(%)
//     */
//    @ExcelProperty("续订单数环比(%)")
//    private BigDecimal continueOrderCountMom = BigDecimal.ZERO;
//
//    /**
//     * 续订单数同比(%)
//     */
//    @ExcelProperty("续订单数同比(%)")
//    private BigDecimal continueOrderCountYoy = BigDecimal.ZERO;

    /**
     * 续订单业绩
     */
    @ExcelProperty("续订单业绩")
    private BigDecimal continueOrderAchievement = BigDecimal.ZERO;
//
//    /**
//     * 续订单业绩环比(%)
//     */
//    @ExcelProperty("续订单业绩环比(%)")
//    private BigDecimal continueOrderAchievementMom = BigDecimal.ZERO;
//
//    /**
//     * 续订单业绩同比(%)
//     */
//    @ExcelProperty("续订单业绩同比(%)")
//    private BigDecimal continueOrderAchievementYoy = BigDecimal.ZERO;

    /**
     * 续订单占比(%)
     */
    @ExcelProperty("续订单占比(%)")
    private BigDecimal continueOrderRatio = BigDecimal.ZERO;

    /**
     * 续订单客单价
     */
    @ExcelProperty("续订单客单价")
    private BigDecimal continueOrderUnitPrice = BigDecimal.ZERO;

    // ========== 退订单 ==========
    /**
     * 退订单数
     */
    @ExcelProperty("退订单数")
    private BigDecimal returnOrderCount = BigDecimal.ZERO;

//    /**
//     * 退订单数环比(%)
//     */
//    @ExcelProperty("退订单数环比(%)")
//    private BigDecimal returnOrderCountMom = BigDecimal.ZERO;
//
//    /**
//     * 退订单数同比(%)
//     */
//    @ExcelProperty("退订单数同比(%)")
//    private BigDecimal returnOrderCountYoy = BigDecimal.ZERO;

    /**
     * 退订单业绩
     */
    @ExcelProperty("退订单业绩")
    private BigDecimal returnOrderAchievement = BigDecimal.ZERO;

//    /**
//     * 退订单业绩环比(%)
//     */
//    @ExcelProperty("退订单业绩环比(%)")
//    private BigDecimal returnOrderAchievementMom = BigDecimal.ZERO;
//
//    /**
//     * 退订单业绩同比(%)
//     */
//    @ExcelProperty("退订单业绩同比(%)")
//    private BigDecimal returnOrderAchievementYoy = BigDecimal.ZERO;

    /**
     * 退订单占比(%)
     */
    @ExcelProperty("退订单占比(%)")
    private BigDecimal returnOrderRatio = BigDecimal.ZERO;

    // 退订单没有客单价，因为是负数

    // ========== 总计 ==========
    /**
     * 总订单数
     */
    @ExcelProperty("总订单数")
    private BigDecimal totalOrderCount = BigDecimal.ZERO;

    /**
     * 总订单数环比(%)
     */
    @ExcelProperty("总订单数环比(%)")
    private BigDecimal totalOrderCountMom = BigDecimal.ZERO;

    /**
     * 总订单数同比(%)
     */
    @ExcelProperty("总订单数同比(%)")
    private BigDecimal totalOrderCountYoy = BigDecimal.ZERO;

    /**
     * 总业绩
     */
    @ExcelProperty("总业绩")
    private BigDecimal totalAchievement = BigDecimal.ZERO;

    /**
     * 总业绩环比(%)
     */
    @ExcelProperty("总业绩环比(%)")
    private BigDecimal totalAchievementMom = BigDecimal.ZERO;

    /**
     * 总业绩同比(%)
     */
    @ExcelProperty("总业绩同比(%)")
    private BigDecimal totalAchievementYoy = BigDecimal.ZERO;

    /**
     * 总平均客单价
     */
    @ExcelProperty("总平均客单价")
    private BigDecimal avgUnitPrice = BigDecimal.ZERO;
}
