package com.ruoyi.mq.consumer.customer;


import com.alibaba.fastjson.JSONArray;
import com.ruoyi.business.api.dubbo.RemoteEmployeeTransfeService;
import com.ruoyi.business.base.api.dubbo.RemoteCustomerService;
import com.ruoyi.common.rocketmq.constant.customer.CxrCustomerConstant;
import com.ruoyi.mq.consumer.base.AbstractIdempotent;
import com.ruoyi.order.api.RemoteOrderService;
import com.ruoyi.system.api.RemoteCxrLoginService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;


@RequiredArgsConstructor
@Component
@Slf4j
@RocketMQMessageListener(topic = CxrCustomerConstant.CUSTOMER_TOPIC, consumerGroup = CxrCustomerConstant.CUSTOMER_GROUP)
public class CxrCustomerMQListener extends AbstractIdempotent {

    @DubboReference
    private RemoteCustomerService remoteCustomerService;

    @DubboReference
    private RemoteOrderService remoteOrderService;

    @DubboReference
    private RemoteCxrLoginService remoteCxrLoginService;
    @DubboReference
    private RemoteEmployeeTransfeService remoteEmployeeTransfeService;


    @Override
    protected void execute(Message msg) {
        String tags = msg.getTags();
        String bodyStr = new String(msg.getBody());
        switch (tags) {
            case CxrCustomerConstant.YDS_CUSTOMER_SYNC_TAG:
                remoteCustomerService.syncCustomer(Long.parseLong(bodyStr));
                break;
            case CxrCustomerConstant.YDS_CUSTOMER_ADDRESS_SYNC_TAG:
                remoteCustomerService.syncCustomerAddress(Long.parseLong(bodyStr));
                break;
            case CxrCustomerConstant.YDS_CUSTOMER_ADDRESS_BATCH_SYNC_TAG:
                remoteCustomerService.syncCustomerAddress(JSONArray.parseArray(bodyStr, Long.class));
                break;
            case CxrCustomerConstant.YDS_CUSTOMER_PAY_TAG:
                remoteOrderService.syncPayInfo(Long.parseLong(bodyStr), true);
                break;
            case CxrCustomerConstant.YDS_RETAIL_CUSTOMER_PAY_TAG:
//                remoteOrderService.syncXcxOrder(Long.parseLong(bodyStr));
                break;
            case CxrCustomerConstant.CUSTOMER_BLOOM_FILTER_TAG:
                remoteCxrLoginService.addCustomerBloomFilterPhone(bodyStr);
                break;
            case CxrCustomerConstant.YDS_DISTRIBUTION_TRANSFER_MIDDLE_TAG:
                remoteEmployeeTransfeService.distributionTransferMiddle(bodyStr);
                break;

        }
    }


    @Override
    protected boolean executeBeforeFilter(Message msg) {
        String tags = msg.getTags();
        if (CxrCustomerConstant.YDS_CUSTOMER_ADDRESS_SYNC_TAG.equals(tags) || CxrCustomerConstant.YDS_CUSTOMER_SYNC_TAG.equals(tags)) {
            return Boolean.FALSE;
        } else {
            return super.executeBeforeFilter(msg);
        }
    }

    protected void executeAfter(Message msg) {
        String tags = msg.getTags();
        if (!CxrCustomerConstant.YDS_CUSTOMER_ADDRESS_SYNC_TAG.equals(tags) && !CxrCustomerConstant.YDS_CUSTOMER_SYNC_TAG.equals(tags)) {
            super.executeAfter(msg);
        }
    }

}
