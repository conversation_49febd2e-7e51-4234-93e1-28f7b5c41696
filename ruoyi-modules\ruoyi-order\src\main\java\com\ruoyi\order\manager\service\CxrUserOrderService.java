package com.ruoyi.order.manager.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.business.base.api.domain.NewOrderDetermineTemplateDto;
import com.ruoyi.business.base.api.domain.json.CustomerInfo;
import com.ruoyi.business.base.api.enums.OrderTypeEnums;
import com.ruoyi.common.core.domain.LoginInfo;
import com.ruoyi.common.order.wap.entity.PayNotifyUrlResponse;
import com.ruoyi.order.api.domain.vo.UserOrderListVo;
import com.ruoyi.order.common.domain.bo.CxrUserOrderQueryBo;
import com.ruoyi.order.common.domain.dto.CompleteInfoDTO;
import com.ruoyi.order.common.domain.response.FuyouResponse;
import com.ruoyi.order.common.domain.vo.LastUserOrderDayInfo;
import com.ruoyi.order.common.domain.vo.LastUserOrderVo;
import com.ruoyi.order.common.entity.CxrUserOrder;
import com.ruoyi.order.disribution.enums.PaymentPlatform;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【cxr_user_order(用户订单)】的数据库操作Service
 * @createDate 2022-08-04 19:53:00
 */
public interface CxrUserOrderService extends IService<CxrUserOrder> {

    /**
     * 分页查询《用户订单》
     *
     * @param cxrUserOrderBo 实例化Bo对象封装的查询参数
     * @param pageQuery      分页参数
     * @return
     */
    Object pageQuery(CxrUserOrderQueryBo cxrUserOrderBo);

    /**
     * 审核接口
     *
     * @param id
     * @return
     */
    boolean audit(Long id, boolean systemFlag, Integer auditType, String refundNoAuditReasons);

    /**
     * 删除订单
     *
     * @param id
     * @return
     */
    boolean deleteOrder(Long id);

    /**
     * 订单支持成功之后 客户处理
     *
     * @param cxrUserOrderId
     */
    public void orderAfterCustomerHandler(Long cxrUserOrderId);

    /**
     * 订单支持成功之后 员工处理
     *
     * @param cxrUserOrderId
     */
    public void orderAfterEmployeeHandler(Long cxrUserOrderId);

    /**
     * 查询最后1条订单
     *
     * @param phone
     * @return
     */
    public CxrUserOrder queryLastOrderInfo(String phone);


    /**
     * 查询第一个订单（新订单/续订单/增订单/合订单）
     *
     * @param phone
     * @param customerId
     * @return
     */
    CxrUserOrder queryFirstOrderInfo(String phone, Long customerId);

    /**
     * 查询第一个订单（新订单/续订单/增订单/合订单/赠送单/转单）
     *
     * @param phone
     * @param customerId
     * @return
     */
    CxrUserOrder queryFirstOrderInfoV2(String phone, Long customerId);


    CxrUserOrder queryLastOrder(String phone, Long orderId);

    CxrUserOrder queryFirstDayOrder(String phone, Long orderId);

    CxrUserOrder queryLastOrderInfoNotContinue(String phone);

    Integer statisticUserOrder(Long siteId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 零续单判断
     *
     * @param phone
     * @return
     */
    boolean zeroContinueFlag(String phone, OrderTypeEnums orderTypeEnums);

    boolean updateCustomerIdByOrderId(Long cxrUserOrderId, Long customerAccountId);

    List<CxrUserOrder> getPhone(String phone);

    void paySucessMessage(
        PaymentPlatform paymentPlatform, String sn, String payWay, String finishTime, Long orderId, String thirdOrderNo,
        Object responseMsg);

    void paySucessMessage(
        PaymentPlatform paymentPlatform, String sn, String payWay, String finishTime,
        PayNotifyUrlResponse payNotifyUrlResponse, CxrUserOrder cxrUserOrder);

    /**
     * 支付成功执行
     */
    void paySucess(String sn, String payWay, String finishTime,
                   CxrUserOrder cxrUserOrder, String thirdOrderNo);


    void fuyouPaySuccess(FuyouResponse fuyouResponse, Boolean reconfirm);

    void export(CxrUserOrderQueryBo cxrUserOrderBo, HttpServletResponse response);

    void paySucessMessage(PaymentPlatform paymentPlatform, String sn, String payWay, String finishTime,
                          CxrUserOrder cxrUserOrder);

    void orderHandler(Long id);

    LastUserOrderVo queryLastTenDayOrder(Long customerId, Long orderId);

    void updateCustomerStock(
        CxrUserOrder cxrUserOrder, CompleteInfoDTO completeInfoDTO, Short orderType);

    void updateCustomerStockDiff(CxrUserOrder cxrUserOrder, CompleteInfoDTO completeInfoDTO);

    List<CxrUserOrder> getByCustomerId(Long customerId);

    Integer getDaySumQty(CxrUserOrder cxrUserOrder);

    CxrUserOrder queryById(Long outLongMilkOrderId);

    Long queryOrderIdByOrderNo(String outLongMilkOrderNo);

    Date queryOrderDateByOrderNo(String outLongMilkOrderNo);

    List<CxrUserOrder> getByCustomerPhone(String folmerPhone);

    boolean reverseAudit(Long id);

    UserOrderListVo queryByCustomerId(Long id);

    boolean del(Long id, LoginInfo loginUser);


    /**
     * 根据客户id分页查询客户订单
     *
     * @param customerId 客户id
     * @param size       每页显示条数
     * @param current    当前页
     * @return
     */
    Page<CxrUserOrder> queryUserOrderByCustomerId(Long customerId, long size, long current);

    CxrUserOrder getByPayOrderId(Long payOrderId);

    boolean updateByAfterSaleOrderStatus(Long orderId);

    void reComputeAchievementOrderFmQty();

    NewOrderDetermineTemplateDto queryOrderDetermineTemplate(Long cxrOrderId);

    void updateTiktokNo(Long id);

    void recalculate(String ids);

    void cleaningRegionHistory(LocalDate startDate, LocalDate endDate);

    List<LastUserOrderDayInfo> queryLastTenDayOrderInfo(CustomerInfo bo, Long customerId);

    CxrUserOrder queryLastPromotionalOrderOrder(String phone, Long orderId);

    Long queryZeroQuantityRenewal(Long customerId, Date orderDate);
}
