package com.ruoyi.business.base.orderAfterSale.domain;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.cxrry.logger.api.annotation.LogModel;
import com.cxrry.logger.api.annotation.LogTag;
import com.ruoyi.common.core.web.domain.CxrBaseEntity;
import com.ruoyi.common.excel.annotation.ExcelDictFormat;
import com.ruoyi.common.excel.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 用户订单对象 cxr_user_order
 *
 * <AUTHOR>
 * @date 2022-08-04
 */
@ApiModel
@ExcelIgnoreUnannotated
@Data
@EqualsAndHashCode(callSuper = false)
@LogModel(value = "客户订单表", tableName = "cxr_user_order")
@TableName(value = "cxr_user_order", autoResultMap = true)
public class CxrUserOrder extends CxrBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @ApiModelProperty("主键 id")
    @TableId(value = "id")
    private Long id;

    /**
     * 公司id
     */
    @LogTag(alias = "公司id")
    @ApiModelProperty("公司id")
    private Long companyId;

    /**
     * 公司名称
     */
    @LogTag(alias = "公司名称")
    @ApiModelProperty("公司名称")
    @ExcelProperty("所属公司")
    private String companyName;

    /**
     * 大区id
     */
    @LogTag(alias = "大区id")
    @ApiModelProperty("大区id")
    private Long bigAreaId;

    /**
     * 大区名称
     */
    @LogTag(alias = "大区名称")
    @ApiModelProperty("大区名称")
    @ExcelProperty("所属大区")
    private String bigAreaName;

    /**
     * 省
     */
    @LogTag(alias = "省")
    @ApiModelProperty("省")
    @ExcelProperty("省")
    private String province;

    /**
     * 市
     */
    @LogTag(alias = "市")
    @ApiModelProperty("市")
    @ExcelProperty("市")
    private String city;

    @TableField(exist = false)
    private String fromProductType;
    /**
     * 区
     */
    @LogTag(alias = "区")
    @ApiModelProperty("区")
    @ExcelProperty("区")
    private String area;

    /**
     * 站点名称
     */
    @LogTag(alias = "站点名称")
    @ApiModelProperty("站点名称")
    @ExcelProperty("站点")
    private String siteName;

    /**
     * 站点地址
     */
    @LogTag(alias = "站点地址")
    @ApiModelProperty("站点地址")
    private String siteAdress;

    /**
     * 站点id
     */
    @LogTag(alias = "站点id")
    @ApiModelProperty("站点id")
    private Long siteId;

    /**
     * 业务代理字符串; 以逗号分割
     */
    @LogTag(alias = "业务代理字符串; 以逗号分割")
    @ApiModelProperty("业务代理详细信息")
    private String businessAgent;

    @TableField(exist = false)
    @ExcelProperty("销售代理")
    private String businessAgentName;

    @TableField(exist = false)
    @ExcelProperty("等级")
    private String businessAgentLevel;

    /**
     * 客户名称
     */
    @LogTag(alias = "客户名称")
    @ApiModelProperty("客户名称;只取第一个录入的数据")
    @ExcelProperty("客户名称")
    private String customerName;

    /**
     * 客户电话;只取第一个录入的数据
     */
    @LogTag(alias = "客户电话;只取第一个录入的数据")
    @ApiModelProperty("客户电话;只取第一个录入的数据")
    @ExcelProperty("客户电话")
    private String customerPhone;

    /**
     * 客户地址; 只取第一个录入的数据
     */
    @LogTag(alias = "客户地址; 只取第一个录入的数据")
    @ApiModelProperty("客户地址; 只取第一个录入的数据")
    @ExcelProperty("客户地址")
    private String customerAdress;

    /**
     * 转入客户姓名
     */
    @LogTag(alias = "转入客户姓名")
    @ApiModelProperty("转入客户姓名")
    @ExcelProperty("转入客户名称")
    private String customerNameSwitch;


    /**
     * 转入客户手机号
     */
    @LogTag(alias = "转入客户手机号")
    @ApiModelProperty("转入客户手机号")
    @ExcelProperty("转入客户电话")
    private String customerPhoneSwitch;

    /**
     * 转入客户地址
     */
    @LogTag(alias = "转入客户地址")
    @ApiModelProperty("转入客户地址")
    @ExcelProperty("转入客户地址")
    private String customerAdressSwitch;


    /**
     * 1微信;2 支付宝
     */
    @LogTag(alias = "支付来源 1微信;2 支付宝")
    @ApiModelProperty("支付来源")
    @ExcelProperty("付款来源")
    private String paymentSouce;

    /**
     * 订单类型;1、新订单2、增订单3、续订单4、转单5、换单6、退订单7、赠送单
     */
    @ApiModelProperty("订单类型")
    @ExcelProperty(value = "订单类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=新订单,1=续订单,2=退订单,3=增订单,4=转单,5=换单,6=赠送单,7=合订单,8=转单-常温奶")
    private Short orderType;

    /**
     * 0数量续单：用于客户账户没有鲜奶了;并且停奶超过了15天。15天后续单的，显示“超过15天”。
     */
    @LogTag(alias = "0数量续单")
    @ApiModelProperty("0数量续单")
    private Boolean zeroQuantityRenewal;


    /**
     * 转换类型，用于订单类型为“换单”的订单，显示客户兑换的商品的类目;，剔除重复的 多个以逗号分割
     */
    @LogTag(alias = "转换类型")
    @ApiModelProperty("转换类型")
    @ExcelProperty("转换类型")
    private String conversionType;

    /**
     * 单据;订单单据号
     */
    @LogTag(alias = "单据")
    @ApiModelProperty("单据")
    @ExcelProperty("单据")
    private String orderNo;

    /**
     * 商户单号
     */
    @LogTag(alias = "商户单号")
    @ApiModelProperty("商户单号")
    @ExcelProperty("商户单号")
    private String merchantOrderNo;

    /**
     * 商户单号
     */
    @LogTag(alias = "收钱吧单号")
    @ApiModelProperty("收钱吧单号")
    private String sqbSn;

    /**
     * 订购数量：鲜奶订购数量
     */
    @LogTag(alias = "订购数量")
    @ApiModelProperty("订购数量")
    @ExcelProperty("订购数量")
    private Integer orderQuantity;

    /**
     * 鲜奶赠送数量
     */
    @LogTag(alias = "鲜奶赠送数量")
    @ApiModelProperty("鲜奶赠送数量")
    @ExcelProperty("鲜奶赠送数量")
    private Integer freshMilkGiveQuantity;

    /**
     * 常温奶赠送数量
     */
    @LogTag(alias = "常温奶赠送数量")
    @ApiModelProperty("常温奶赠送数量")
    @ExcelProperty("常温奶赠送数量")
    private Integer longMilkGiveQuantity;

    /**
     * 超送数量：根据订单套餐超送规则计算出来的超送数量;祥看原型
     */
    @LogTag(alias = "超送数量")
    @ApiModelProperty("超送数量")
    @ExcelProperty("超送数量")
    private Integer excessQuantity;

    /**
     * 鲜奶已送数量
     */
    @LogTag(alias = "鲜奶已送数量")
    @ApiModelProperty("鲜奶已送数量")
    @ExcelProperty("鲜奶已送数量")
    private Integer freshMilkSentQuantity;

    /**
     * 转换数量，订单类型为“转单”的时候才会有值;即转换的鲜奶数量 也可以是常温奶数量
     */
    @LogTag(alias = "转换数量")
    @ApiModelProperty("转换数量")
    @ExcelProperty("转换数量")
    private Integer conversionQuantity;

    /**
     * 常温奶已送数量
     */
    @LogTag(alias = "常温奶已送数量")
    @ApiModelProperty("常温奶已送数量")
    private Integer longMilkSentQuantity;

    @ApiModelProperty(value = "剩余数量")
    private Integer surplusQuantity;

    /**
     * 单价：鲜奶单价
     */
    @LogTag(alias = "单价")
    @ApiModelProperty("单价：鲜奶单价")
    @ExcelProperty("单价")
    private BigDecimal unitPrice;

    /**
     * 金额：该笔订单的总金额
     */
    @LogTag(alias = "金额")
    @ApiModelProperty("金额：该笔订单的总金额")
    @ExcelProperty("金额")
    private BigDecimal amount;

    /**
     * 刷卡金额
     */
    @LogTag(alias = "刷卡金额")
    @ApiModelProperty("刷卡金额")
    private BigDecimal creditCardAmount;

    /**
     * 订单日期
     */
    @LogTag(alias = "订单日期")
    @ApiModelProperty("订单日期")
    @ExcelProperty("订单日期")
    private Date orderDate;

    /**
     * 1 待审核 、 2 已审核 、 3.已拒绝
     */
    @LogTag(alias = "审核状态 1 待审核 、 2 已审核 、 3.已拒绝")
    @ApiModelProperty("1 待审核 、 2 已审核 、 3.已拒绝")
    @ExcelProperty("审核状态")
    private Integer auditStatus;

    /**
     * 审核人
     */
    @ApiModelProperty("审核人")
    @ExcelProperty("审核人")
    private Long auditBy;

    /**
     * 审核人
     */
    @LogTag(alias = "审核人")
    @ApiModelProperty("审核人名字")
    @ExcelProperty("审核人名字")
    private String auditByName;

    /**
     * 审核人
     */
    @ApiModelProperty("审核时间")
    @ExcelProperty("审核时间")
    private Date auditTime;

    /**
     * 是否促销单;true 是 false 否
     */
    @LogTag(alias = "是否促销单 true 是 false 否")
    @ApiModelProperty("是否促销单;true 是 false 否")
    @ExcelProperty("是否促销单")
    private Boolean promotionalOrderFlag;

    /**
     * 是否师徒单;true是 false 否
     */
    @LogTag(alias = "是否师徒单 true是 false 否")
    @ApiModelProperty("是否师徒单;true是 false 否")
    @ExcelProperty("是否师徒单")
    private Boolean apprenticeOrderFlag;

    /**
     * 来源 1 后端 2 配送端
     */
    @ApiModelProperty("源  1 后端 2 配送端")
    @ExcelProperty(value = "终端来源", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=后台录入,2=配送端录入,3=客户端录入,4=小程序")
    @TableField(fill = FieldFill.INSERT)
    private Short terminalType;

    /**
     * 单据图片
     */
    @ApiModelProperty("单据图片")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> orderImages;

    /**
     * 支付截图
     */
    @ApiModelProperty("支付截图")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> playImages;

    /**
     * 客户信息
     */


    /**
     * 客户信息
     */
    @TableField(exist = false)
    private String customerInfos;

    @ApiModelProperty("销售代理")
    @TableField(exist = false)
    private String proxyName;

    @ApiModelProperty("业绩")
    @TableField(exist = false)
    private String performanceMoney;

    @ApiModelProperty(notes = "合订单促销单是否可以编辑")
    @TableField(exist = false)
    private boolean promotionalEditFlag = Boolean.FALSE;

    /**
     * 鲜奶退订数量
     */
    @LogTag(alias = "鲜奶退订数量")
    @ApiModelProperty(" 鲜奶退订信息")
    @ExcelProperty(" 鲜奶退订数量")
    private Integer freshMilkReturnQuantity;

    @LogTag(alias = "支付状态")
    @ApiModelProperty(" 支付状态")
    private Integer payStatus;

    // 完善订单入口
    @ApiModelProperty(name = "订单完善状态")
    private Integer perfectStatus;


    /**
     * 支付时间
     */
    @LogTag(alias = "支付时间")
    @ApiModelProperty("支付时间")
    private Date payTime;

    @LogTag(alias = "合订单总金额")
    @ApiModelProperty("合订单总金额")
    private BigDecimal contractTotalAmount;

    @LogTag(alias = "合订单总订购数量")
    @ApiModelProperty("合订单总订购数量")
    private Integer contractTotalOrderQuantity;

    @LogTag(alias = "备注")
    @ApiModelProperty("remark")
    @ExcelProperty("备注")
    private String remark;

    @TableField(exist = false)
    private String showAuditMark;

    @ApiModelProperty("换单商品兑换id")
    private Long exchangeProductId;

    @LogTag(alias = "换单兑换商品规格")
    @ApiModelProperty("换单兑换商品规格")
    private String spec;

    @LogTag(alias = "兑换数量")
    @ApiModelProperty("换单兑换数量")
    @ExcelProperty("兑换数量")
    private Integer exchangeSum;

    @LogTag(alias = "鲜奶兑换数量")
    @ApiModelProperty("换单鲜奶兑换商品数量")
    @ExcelProperty("鲜奶兑换数量")
    private Integer milkExchangeSum;

    @LogTag(alias = "换单商品名称")
    @ApiModelProperty("换单商品名称")
    private String exchangeProductName;

    @LogTag(alias = "是否导入订单")
    @ApiModelProperty("是否导入订单")
    private Boolean isImport;

    @LogTag(alias = "活动赠品数量")
    @ApiModelProperty("活动赠品数量")
    @ExcelProperty("活动礼品数量")
    private Integer activityGiveQuantity;

    @LogTag(alias = "活动赠品已送数量")
    @ApiModelProperty("活动赠品已送数量")
    private Integer activitySentQuantity;

    @ApiModelProperty("客户id")
    private Long customerId;

    @LogTag(alias = "活动类型")
    @ApiModelProperty("活动类型")
    private String activityType;

    @LogTag(alias = "第三方订单号")
    @ApiModelProperty("第三方订单号")
    private String thirdOrderNo;

    @LogTag(alias = "第三方订单号生成时间")
    @ApiModelProperty("第三方订单号生成时间")
    private LocalDateTime thirdOrderNoTime;

    @LogTag(alias = "是否新客户")
    @ApiModelProperty("是否新客户")
    private String newCustomerFlag;

    @ApiModelProperty("常温奶转出订单id")
    private String outLongMilkOrderNo;

    @ApiModelProperty("活动id")
    private Long activityId;

    @LogTag(alias = "活动名称")
    @ApiModelProperty("活动名称")
    @ExcelProperty("活动名称")
    private String activityName;

    @ApiModelProperty("活动对象")
    private String giveGiftList;

    @ApiModelProperty("套餐价格")
    @ExcelProperty("套餐金额")
    private BigDecimal setMealPrice;

    @LogTag(alias = "套餐数量")
    @ApiModelProperty("套餐数量")
    private Integer setMealQty;

    @LogTag(alias = "分摊费用")
    @ApiModelProperty("分摊费用")
    @ExcelProperty("活动分摊费用")
    private BigDecimal apportionMoney;

    /**
     * 订单赠送数是否参与增订单核算
     */
    @LogTag(alias = "订单赠送数是否参与增订单核算")
    private Boolean pigcFlag;

    /**
     * audit_count
     */
    @LogTag
    private Integer auditCount;

    /**
     * 合订单的订单类型标签
     */
    @LogTag(alias = "合订单的订单类型标签")
    private Short contractTypeTag;


    /**
     * 打款标识
     */
    @LogTag(alias = "打款标识")
    @ApiModelProperty("打款标识")
    @TableField(exist = false)
    private Boolean refundSuccessFlag;

    @ApiModelProperty("区分收钱吧/富友标识")
    private Integer payPlaformType;

    @ApiModelProperty(value = "支付订单id")
    private Long payOrderId;

    @ApiModelProperty(value = "异常订单状态  1：异常")
    private Integer abnormalTag;

    @ApiModelProperty("配送站点")
    private String deliverySites;
    @ApiModelProperty("退款审核不通过原因")
    private String refundNoAuditReasons;

}
