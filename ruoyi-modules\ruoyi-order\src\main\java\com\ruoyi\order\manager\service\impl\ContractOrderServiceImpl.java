package com.ruoyi.order.manager.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.ruoyi.business.base.api.domain.*;
import com.ruoyi.business.base.api.domain.dto.CxrCustomerAddressDTO;
import com.ruoyi.business.base.api.domain.dto.CxrSiteDTO;
import com.ruoyi.business.base.api.domain.json.BusinessAgent;
import com.ruoyi.business.base.api.domain.json.ContractOrderCustomerInfo;
import com.ruoyi.business.base.api.domain.json.CustomerInfo;
import com.ruoyi.business.base.api.domain.vo.CustomerSentNumberVo;
import com.ruoyi.business.base.api.dubbo.*;
import com.ruoyi.business.base.api.enums.OrderTypeEnums;
import com.ruoyi.business.base.api.model.CustomerAddressDistributioninfoDTO;
import com.ruoyi.business.base.api.model.CustomerDistributioninfoDTO;
import com.ruoyi.business.base.api.model.LoginEmployee;
import com.ruoyi.common.core.annotation.HuiBoSynCxrUserOrder;
import com.ruoyi.common.core.enums.DeleteStatus;
import com.ruoyi.common.core.enums.SysYesNo;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.SQLException;
import com.ruoyi.common.core.web.domain.CxrBaseEntity;
import com.ruoyi.common.enums.TerminalTypeEnums;
import com.ruoyi.common.redis.annotation.RedisDistributedLock;
import com.ruoyi.common.rocketmq.calculate.CustomerAddressConstant;
import com.ruoyi.common.rocketmq.utils.MqUtil;
import com.ruoyi.common.satoken.utils.helper.StaffLoginHelper;
import com.ruoyi.order.api.RemoteOrderService;
import com.ruoyi.order.api.enums.OrderAuditStatusEnums;
import com.ruoyi.order.api.enums.PerfectStatusEnums;
import com.ruoyi.order.common.domain.bo.ContractOrderCustomerInfoBO;
import com.ruoyi.order.common.domain.bo.ContractOrderHandlerBO;
import com.ruoyi.order.common.domain.dto.CompleteInfoDTO;
import com.ruoyi.order.common.domain.vo.CxrUserOrderVO;
import com.ruoyi.order.common.entity.CxrUserOrder;
import com.ruoyi.order.common.entity.CxrUserOrderSubscribeMilk;
import com.ruoyi.order.common.mapper.CxrUserOrderSubscribeMilkMapper;
import com.ruoyi.order.common.utils.ExcessQuantityUtil;
import com.ruoyi.order.common.utils.OrderUtil;
import com.ruoyi.order.disribution.service.CustomerService;
import com.ruoyi.order.disribution.service.FastOrderService;
import com.ruoyi.order.manager.service.ContractOrderService;
import com.ruoyi.order.manager.service.CxrUserOrderService;
import com.ruoyi.order.manager.strategy.behavior.AbstractCxrUserOrderBehavior;
import io.seata.spring.annotation.GlobalTransactional;
import io.seata.tm.api.GlobalTransaction;
import io.seata.tm.api.GlobalTransactionContext;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/8/11 11:29
 */
@RequiredArgsConstructor
@Service
@GlobalTransactional(rollbackFor = Exception.class)
public class ContractOrderServiceImpl implements ContractOrderService {

    public static final Logger log = LoggerFactory.getLogger(" contract-order");

    private final ExcessQuantityUtil excessQuantityUtil;
    private final AbstractCxrUserOrderBehavior abstractCxrUserOrderBehavior;
    private final IdentifierGenerator identifierGenerator;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final CxrUserOrderService cxrUserOrderService;

    @Autowired
    @Lazy
    private FastOrderService fastOrderService;
    @DubboReference
    private RemoteEmployeeAchievementDetailService remoteEmployeeAchievementDetailService;

    private final CxrUserOrderSubscribeMilkMapper cxrUserOrderSubscribeMilkMapper;

    @DubboReference
    private RemoteCustomerService remoteCustomerService;

    @DubboReference
    private RemoteEmployeeService employeeService;


    @DubboReference(timeout = 10000)
    private RemoteCustomerAddressService remoteCustomerAddressService;

    @DubboReference
    private RemoteCustomerStockDetailService remoteCustomerStockDetailService;

    @DubboReference
    private RemoteLongMilkStockService remoteLongMilkStockService;

    private final RemoteOrderService remoteOrderService;

    @DubboReference
    private RemoteCxrSaleProductService remoteCxrSaleProductService;

    @DubboReference
    private RemoteSiteService remoteSiteService;

    @Lazy
    private final CustomerService customerService;

    private final MqUtil mqUtil;

    @DubboReference
    private RemoteCustomerDistributionDetailService remoteCustomerDistributionDetailService;

    /**
     * 新增合单
     *
     * @param contractOrderHandlerBO
     * @return
     */
    @Override
    public Long contractOrderAdd(ContractOrderHandlerBO contractOrderHandlerBO) {
        OrderTypeEnums orderTypeEnums = OrderTypeEnums.getType(contractOrderHandlerBO.getOrderType());
        contractOrderHandlerBO.setIsManage(true);
        return abstractCxrUserOrderBehavior.executeOrderAddOrUpdate(
            contractOrderHandlerBO, orderTypeEnums);
    }

    @Override
    public String disributionContractOrderAdd(ContractOrderHandlerBO contractOrderHandlerBO) {
        OrderTypeEnums orderTypeEnums = OrderTypeEnums.getType(contractOrderHandlerBO.getOrderType());
        if (contractOrderHandlerBO.getBusinessAgent().size() > 1) {
            throw new ServiceException("只能一个销售代理录入");
        }
        return abstractCxrUserOrderBehavior.disributionContractOrderAdd(
            contractOrderHandlerBO, orderTypeEnums);
    }

    @Override
    public void verifyFastOrderParam(
        ContractOrderHandlerBO contractOrderHandlerBO, TerminalTypeEnums typeEnums, Integer types) {

        // 订购数
        Integer productCount = contractOrderHandlerBO.getOrderQuantity();
        // 常温奶赠送数
        Integer milkGiveCount = 0;
        Integer freshMilkGiveQuantity = 0;

        List<ContractOrderCustomerInfoBO> customerInfoBOList =
            contractOrderHandlerBO.getContractOrderCustomerInfoBOList();
        for (ContractOrderCustomerInfoBO customerInfoBO : customerInfoBOList) {

            if (customerInfoBO.getFreshMilkGiveQuantity() == null) {
                throw new ServiceException("请输入鲜奶赠送数量");
            }
            if (customerInfoBO.getFreshMilkGiveQuantity() < 0) {
                throw new ServiceException("鲜奶赠送数量不能小于0");
            }

            if (customerInfoBO.getLongMilkGiveQuantity() == null) {
                throw new ServiceException("请输入常温奶赠送数量");
            }
            if (customerInfoBO.getLongMilkGiveQuantity() < 0) {
                throw new ServiceException("常温奶赠送数量不能小于0");
            }

            freshMilkGiveQuantity += customerInfoBO.getFreshMilkGiveQuantity();
            milkGiveCount += customerInfoBO.getLongMilkGiveQuantity();
        }

        if (contractOrderHandlerBO.getSiteId() == null) {
            throw new ServiceException("请选择站点");
        }
        if (contractOrderHandlerBO.getBusinessAgent() == null
            || contractOrderHandlerBO.getBusinessAgent().size() == 0) {
            throw new ServiceException("请选择业务代理");
        }

        if (contractOrderHandlerBO.getOrderQuantity() == null) {
            throw new ServiceException("请输入订购数量");
        }
        if (contractOrderHandlerBO.getOrderQuantity() <= 0) {
            throw new ServiceException("订购数量不能小于或等于0");
        }

        //            if (freshMilkGiveQuantity == null) {
        //                throw new ServiceException("请输入鲜奶赠送数量");
        //            }
        //            if (freshMilkGiveQuantity < 0) {
        //                throw new ServiceException("鲜奶赠送数量不能小于0");
        //            }
        //
        //            if (milkGiveCount == null) {
        //                throw new ServiceException("请输入常温奶赠送数量");
        //            }
        //            if (milkGiveCount < 0) {
        //                throw new ServiceException("常温奶赠送数量不能小于0");
        //            }

        if (contractOrderHandlerBO.getAmount() == null) {
            throw new ServiceException("请输入订购金额");
        }
        if (contractOrderHandlerBO.getAmount().compareTo(BigDecimal.ZERO) == -1) {
            throw new ServiceException("订购金额不能小于0");
        }

        if (types == 1) {
            Integer daySumQty = 0;
            if (contractOrderHandlerBO.getUserOrderId() != null) {
                CxrUserOrder order = cxrUserOrderService.getById(contractOrderHandlerBO.getUserOrderId());
                daySumQty = cxrUserOrderService.getDaySumQty(order);
            }
            // 单价标准：低于100盒单价11元/盒，100盒以上单价10元/盒；
            contractOrderHandlerBO.setUnitPrice(
                OrderUtil.getUnitPrice(contractOrderHandlerBO.getOrderQuantity()));
            excessQuantityUtil.giveCheck(
                daySumQty,
                productCount,
                freshMilkGiveQuantity,
                milkGiveCount,
                ExcessQuantityUtil.KDORDERTYPE,
                contractOrderHandlerBO.getSiteId());
            contractOrderHandlerBO.setAmount(
                OrderUtil.getAmount(contractOrderHandlerBO.getOrderQuantity()));
        } else { // 拆单后的数据

            int index = contractOrderHandlerBO.getOrderNo().indexOf("-");
            String orderNo = contractOrderHandlerBO.getOrderNo().substring(0, index);
            /** 查询到子订单 */
            List<CxrUserOrder> cxrUserOrderList =
                cxrUserOrderService
                    .getBaseMapper()
                    .selectList(
                        new LambdaUpdateWrapper<CxrUserOrder>()
                            .likeRight(CxrUserOrder::getOrderNo, StrUtil.format("{}-", orderNo))
                            .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));
            if (CollectionUtil.isNotEmpty(cxrUserOrderList)) {
                Integer freshMilkGiveQuantity1 = 0;
                Integer longMilkGiveQuantity = 0;
                Integer orderQuantity = 0;
                Integer orderQuantitys = 0;
                for (CxrUserOrder cxrUserOrder : cxrUserOrderList) {
                    freshMilkGiveQuantity1 += cxrUserOrder.getFreshMilkGiveQuantity();
                    longMilkGiveQuantity += cxrUserOrder.getLongMilkGiveQuantity();
                    orderQuantity += cxrUserOrder.getOrderQuantity();
                    orderQuantitys += cxrUserOrder.getOrderQuantity();
                }

                // 减去之前的数量 加上现在的数量去做一个比较
                if (contractOrderHandlerBO.getUserOrderId() != null) {
                    CxrUserOrder order = cxrUserOrderService.getById(contractOrderHandlerBO.getUserOrderId());
                    orderQuantity -= order.getOrderQuantity();
                    longMilkGiveQuantity -= order.getLongMilkGiveQuantity();
                    freshMilkGiveQuantity1 -= order.getFreshMilkGiveQuantity();
                }

                List<CustomerInfo> customerInfoList = contractOrderHandlerBO.getCustomerInfoList();

                for (CustomerInfo customerInfo : customerInfoList) {
                    freshMilkGiveQuantity1 += customerInfo.getFreshMilkGiveQuantity();
                    longMilkGiveQuantity += customerInfo.getLongMilkGiveQuantity();
                }

                productCount += orderQuantity;
                orderQuantity += productCount;
                //                longMilkGiveQuantity +=milkGiveCount;
                //
                //                freshMilkGiveQuantity1
                // +=ObjectUtil.isEmpty(contractOrderHandlerBO.getFreshMilkGiveQuantity())
                //                ? freshMilkGiveQuantity
                // :contractOrderHandlerBO.getFreshMilkGiveQuantity();

                contractOrderHandlerBO.setUnitPrice(OrderUtil.getUnitPrice(productCount));
                Integer excessQuantity = excessQuantityUtil.giveCheck(
                    orderQuantitys,
                    productCount,
                    freshMilkGiveQuantity1,
                    longMilkGiveQuantity,
                    ExcessQuantityUtil.KDORDERTYPE,
                    contractOrderHandlerBO.getSiteId());
                contractOrderHandlerBO.setExcessQuantity(excessQuantity);
                contractOrderHandlerBO.setAmount(
                    OrderUtil.getAmount(contractOrderHandlerBO.getOrderQuantity()));
            }
        }
    }

    /**
     * 编辑合订单
     *
     * @param contractOrderHandlerBO
     * @return
     */
    @HuiBoSynCxrUserOrder(value = "#contractOrderHandlerBO.userOrderId")
    @RedisDistributedLock(value = "'cxr:user:order:update' + #contractOrderHandlerBO.userOrderId")
    @Override
    public boolean updateContractOrder(ContractOrderHandlerBO contractOrderHandlerBO) {
        OrderTypeEnums orderTypeEnums = OrderTypeEnums.getType(contractOrderHandlerBO.getOrderType());
        if (PerfectStatusEnums.NO_PERFECT.getValue()
            == contractOrderHandlerBO.getPerfectStatus().intValue()) {
            completeInformation(contractOrderHandlerBO);
        }
        contractOrderHandlerBO.setIsManage(true);
        return abstractCxrUserOrderBehavior.executeOrderUpdate(contractOrderHandlerBO, orderTypeEnums);
    }

    public void completeInformation(ContractOrderHandlerBO contractOrderHandlerBO) {

        CompleteInfoDTO completeInfoDTO =
            BeanUtil.copyProperties(contractOrderHandlerBO, CompleteInfoDTO.class);

        List<CustomerDistributioninfoDTO> distributioninfoDTOS = new ArrayList<>();
        for (ContractOrderCustomerInfoBO customerInfo :
            contractOrderHandlerBO.getContractOrderCustomerInfoBOList()) {
            CustomerDistributioninfoDTO customerDistributioninfoDTO =
                BeanUtil.copyProperties(customerInfo, CustomerDistributioninfoDTO.class);
            customerDistributioninfoDTO.setCustomerName(customerInfo.getName());
            customerDistributioninfoDTO.setCustomerPhone(customerInfo.getPhone());
            customerDistributioninfoDTO.setAddress(customerInfo.getAdress());
            customerDistributioninfoDTO.setProvince(customerInfo.getProvice());
            customerDistributioninfoDTO.setCheckboxList(new ArrayList<>());
            distributioninfoDTOS.add(customerDistributioninfoDTO);
        }

        completeInfoDTO.setCustomerDistributioninfoList(distributioninfoDTOS);
        completeInfoDTO.setTerminalType(TerminalTypeEnums.manager);
        customerService.completeInformation(completeInfoDTO);
    }

    /**
     * 查询详情
     *
     * @param id
     * @return
     */
    @Override
    public CxrUserOrderVO detail(Long id) {
        CxrUserOrderVO cxrUserOrderVO = abstractCxrUserOrderBehavior.executeOrderDetail(id);

        CxrSite cxrSite = remoteSiteService.queryId(cxrUserOrderVO.getSiteId());
        CxrSiteDTO cxrSiteDTO = BeanUtil.toBean(cxrSite, CxrSiteDTO.class);
        cxrUserOrderVO.setSiteDTO(cxrSiteDTO);

        // 获取客户信息
        List<CustomerInfo> customerInfoList = cxrUserOrderVO.getCustomerInfoList();
        if (CollectionUtil.isNotEmpty(customerInfoList)) {
            CxrUserOrderSubscribeMilk cxrUserOrderSubscribeMilk =
                cxrUserOrderSubscribeMilkMapper.selectOne(
                    Wrappers.lambdaQuery(CxrUserOrderSubscribeMilk.class)
                        .eq(CxrUserOrderSubscribeMilk::getCxrUserOrderId, id));
            if (ObjectUtil.isNotEmpty(cxrUserOrderSubscribeMilk)) {
                List<CustomerDistributioninfoDTO> subscribeInfo =
                    cxrUserOrderSubscribeMilk.getSubscribeInfo();

                if (CollUtil.isNotEmpty(subscribeInfo)) {
                    Map<String, CustomerDistributioninfoDTO> map =
                        subscribeInfo.stream()
                            .collect(
                                Collectors.toMap(
                                    CustomerDistributioninfoDTO::getCustomerPhone,
                                    Function.identity(),
                                    (v1, v2) -> v2));
                    for (CustomerInfo info : customerInfoList) {
                        CustomerDistributioninfoDTO customerDistributioninfoDTO = map.get(info.getPhone());
                        if (ObjectUtil.isNotNull(customerDistributioninfoDTO)) {
                            info.setCustomerDistributioninfoDTO(customerDistributioninfoDTO);
                        }
                    }
                }
            }
        }

        if (cxrUserOrderVO.getOrderType() == OrderTypeEnums.CONTRACT_ORDER.getValue()) {
            List<CustomerInfo> customerInfoLists = cxrUserOrderVO.getCustomerInfoList();
            for (CustomerInfo customerInfo : customerInfoLists) {
                customerInfo.setAmount(cxrUserOrderVO.getAmount());
                customerInfo.setOrderQuantity(cxrUserOrderVO.getOrderQuantity());
                customerInfo.setLongMilkGiveQuantity(cxrUserOrderVO.getLongMilkGiveQuantity());
                customerInfo.setFreshMilkSentQuantity(cxrUserOrderVO.getFreshMilkSentQuantity());
                customerInfo.setFreshMilkGiveQuantity(cxrUserOrderVO.getFreshMilkGiveQuantity());
            }
            cxrUserOrderVO.setCustomerInfoList(customerInfoLists);
        }
        return cxrUserOrderVO;
    }

    /**
     * 编辑合订单
     *
     * @param contractOrderHandlerBO
     * @return
     */
    @HuiBoSynCxrUserOrder(value = "#contractOrderHandlerBO.userOrderId")
    @Override
    @RedisDistributedLock(value = "'cxr:user:order:update' + #contractOrderHandlerBO.userOrderId")
    public boolean disributionUpdateContractOrder(ContractOrderHandlerBO contractOrderHandlerBO) {
        OrderTypeEnums orderTypeEnums = OrderTypeEnums.getType(contractOrderHandlerBO.getOrderType());
        return abstractCxrUserOrderBehavior.executeOrderUpdate(contractOrderHandlerBO, orderTypeEnums);
    }

    /**
     * 合订单拆单 抽取出来
     *
     * @param userOrderId
     */
    @Override
    public List<Long> contractOrderSplit(Long userOrderId) {

        CxrUserOrder cxrUserOrder = cxrUserOrderService.getById(userOrderId);
        // 主订单设置为删除状态
        cxrUserOrderService.update(
            new LambdaUpdateWrapper<CxrUserOrder>()
                .set(CxrBaseEntity::getDeleteStatus, DeleteStatus.DELETED.getValue())
                .eq(CxrUserOrder::getId, cxrUserOrder.getId()));

        List<ContractOrderCustomerInfo> contractOrderExt = cxrUserOrder.getContractOrderExt();

        List<Long> idList = new ArrayList<>();
        List<CxrUserOrder> cxrUserOrderList = new ArrayList<>();

        GlobalTransaction globalTransaction = GlobalTransactionContext.getCurrent();

        BigDecimal unitPrice = OrderUtil.getUnitPrice(cxrUserOrder.getOrderQuantity());
        for (int i = 0; i < contractOrderExt.size(); i++) {
            ContractOrderCustomerInfo item = contractOrderExt.get(i);
            CxrUserOrder returnCxrUserOrder = new CxrUserOrder();
            long id = identifierGenerator.nextId(null).longValue();
            idList.add(id);
            returnCxrUserOrder.setId(id);
            returnCxrUserOrder.setCompanyId(cxrUserOrder.getCompanyId());
            returnCxrUserOrder.setCompanyName(cxrUserOrder.getCompanyName());
            returnCxrUserOrder.setBigAreaId(cxrUserOrder.getBigAreaId());
            returnCxrUserOrder.setBigAreaName(cxrUserOrder.getBigAreaName());
            returnCxrUserOrder.setProvince(cxrUserOrder.getProvince());
            returnCxrUserOrder.setCity(cxrUserOrder.getCity());
            returnCxrUserOrder.setArea(cxrUserOrder.getArea());
            returnCxrUserOrder.setSiteName(cxrUserOrder.getSiteName());
            returnCxrUserOrder.setSiteAdress(cxrUserOrder.getSiteAdress());
            returnCxrUserOrder.setSiteId(cxrUserOrder.getSiteId());
            returnCxrUserOrder.setBusinessAgent(cxrUserOrder.getBusinessAgent());
            returnCxrUserOrder.setCustomerName(item.getName());
            returnCxrUserOrder.setCustomerPhone(item.getPhone());
            returnCxrUserOrder.setCustomerAdress(item.getAdress());
            returnCxrUserOrder.setCustomerNameSwitch(null);
            returnCxrUserOrder.setCustomerAdressSwitch(null);
            returnCxrUserOrder.setCustomerPhoneSwitch(null);
            returnCxrUserOrder.setPaymentSouce(cxrUserOrder.getPaymentSouce());
            returnCxrUserOrder.setOrderType(cxrUserOrder.getOrderType());
            returnCxrUserOrder.setRemark(item.getRemark());

            returnCxrUserOrder.setConversionType(null);
            returnCxrUserOrder.setOrderNo(StrUtil.format("{}-{}", cxrUserOrder.getOrderNo(), (i + 1)));
            returnCxrUserOrder.setMerchantOrderNo(cxrUserOrder.getMerchantOrderNo());
            returnCxrUserOrder.setOrderQuantity(item.getOrderQuantity());
            returnCxrUserOrder.setFreshMilkGiveQuantity(item.getFreshMilkGiveQuantity());
            returnCxrUserOrder.setLongMilkGiveQuantity(item.getLongMilkGiveQuantity());
            //合订单超送数量设置第一张单
            if (i == 0) {
                returnCxrUserOrder.setExcessQuantity(cxrUserOrder.getExcessQuantity());
                returnCxrUserOrder.setContractTypeTag(cxrUserOrder.getContractTypeTag());
            } else {
                String contractTypeTag = StringUtils.join(7, cxrUserOrder.getContractTypeTag());
                if (StringUtils.isNotBlank(contractTypeTag)) {
                    returnCxrUserOrder.setContractTypeTag(Short.parseShort(contractTypeTag));
                }
                returnCxrUserOrder.setExcessQuantity(0);
            }
            returnCxrUserOrder.setCoType(item.getCoType());

            returnCxrUserOrder.setFreshMilkSentQuantity(item.getFreshMilkSentQuantity());
            returnCxrUserOrder.setLongMilkSentQuantity(0);
            returnCxrUserOrder.setSurplusQuantity(
                item.getOrderQuantity() - item.getFreshMilkSentQuantity());
            returnCxrUserOrder.setConversionQuantity(0);
            returnCxrUserOrder.setUnitPrice(unitPrice);
            returnCxrUserOrder.setAmount(NumberUtil.mul(unitPrice, item.getOrderQuantity()));
            returnCxrUserOrder.setCreditCardAmount(null);

            if (NumberUtil.equals(
                cxrUserOrder.getTerminalType(), TerminalTypeEnums.disribution.getValue())) {
                returnCxrUserOrder.setAuditStatus(OrderAuditStatusEnums.NO_AUDIT.getValue());
                returnCxrUserOrder.setOrderDate(cxrUserOrder.getPayTime());
            } else {
                returnCxrUserOrder.setAuditStatus(OrderAuditStatusEnums.AUDITED.getValue());
                returnCxrUserOrder.setAuditTime(cxrUserOrder.getAuditTime());
                returnCxrUserOrder.setAuditByName(cxrUserOrder.getAuditByName());
                returnCxrUserOrder.setAuditBy(cxrUserOrder.getAuditBy());
                returnCxrUserOrder.setOrderDate(cxrUserOrder.getAuditTime());
            }

            returnCxrUserOrder.setPromotionalOrderFlag(cxrUserOrder.getPromotionalOrderFlag());
            returnCxrUserOrder.setApprenticeOrderFlag(cxrUserOrder.getApprenticeOrderFlag());
            returnCxrUserOrder.setTerminalType(cxrUserOrder.getTerminalType());
            returnCxrUserOrder.setOrderImages(cxrUserOrder.getOrderImages());
            returnCxrUserOrder.setPlayImages(cxrUserOrder.getPlayImages());

            returnCxrUserOrder.setFreshMilkReturnQuantity(0);
            returnCxrUserOrder.setPayStatus(cxrUserOrder.getPayStatus());

            List<CustomerInfo> customerInfoList = new ArrayList<>();
            CustomerInfo customerInfo = new CustomerInfo();
            if (StrUtil.isNotBlank(item.getPhone())) {
                boolean bo = remoteCustomerService.existsCustomerAccountAddress(item.getPhone());
                if (bo && NumberUtil.equals(cxrUserOrder.getTerminalType(), TerminalTypeEnums.disribution.getValue())) {
                    returnCxrUserOrder.setPerfectStatus(PerfectStatusEnums.PERFECT.getValue());
                    returnCxrUserOrder.setNewCustomerFlag(SysYesNo.NO.getValue());
                    // 判断是配送端 是 需要补全客户信息
                    CxrCustomerAddressDTO cxrCustomerAddressDTO =
                        remoteCustomerService.queryAdressInfo(item.getPhone());
                    if (ObjectUtil.isNotNull(cxrCustomerAddressDTO)) {
                        customerInfo.setName(cxrCustomerAddressDTO.getReceiverName());
                        customerInfo.setPhone(cxrCustomerAddressDTO.getReceiverPhone());
                        customerInfo.setSysAreaId(cxrCustomerAddressDTO.getSysAreaId());
                        customerInfo.setProvice(cxrCustomerAddressDTO.getProvice());
                        customerInfo.setCity(cxrCustomerAddressDTO.getCity());
                        customerInfo.setArea(cxrCustomerAddressDTO.getArea());
                        customerInfo.setResidentialQuartersId(
                            cxrCustomerAddressDTO.getCxrResidentialQuartersId());
                        customerInfo.setResidentialQuartersName(
                            cxrCustomerAddressDTO.getCxrResidentialQuartersName());
                        customerInfo.setAdress(cxrCustomerAddressDTO.getDetailDistributionAddress());
                        customerInfo.setSiteId(cxrCustomerAddressDTO.getCxrSiteId());
                        customerInfo.setSiteName(cxrCustomerAddressDTO.getCxrSiteName());
                        customerInfo.setDistributionId(cxrCustomerAddressDTO.getCxrEmployeeId());
                        customerInfo.setDistributionName(cxrCustomerAddressDTO.getCxrEmployeeName());

                        returnCxrUserOrder.setCustomerName(cxrCustomerAddressDTO.getReceiverName());
                        returnCxrUserOrder.setCustomerPhone(cxrCustomerAddressDTO.getReceiverPhone());
                        returnCxrUserOrder.setCustomerAdress(
                            cxrCustomerAddressDTO.getDetailDistributionAddress());
                        // 订单
                        CxrCustomer cxrCustomer = remoteCustomerService.queryByPhone(item.getPhone());

                        returnCxrUserOrder.setCustomerId(cxrCustomer.getId());

                        if (NumberUtil.equals(
                            cxrUserOrder.getTerminalType(), TerminalTypeEnums.disribution.getValue())) {
                            List<CustomerDistributioninfoDTO> customerDistributioninfoDTOList =
                                remoteCustomerAddressService.queryCustomerDistributioninfo(cxrCustomer.getId());

                            CxrUserOrderSubscribeMilk cxrUserOrderSubscribeMilk = new CxrUserOrderSubscribeMilk();
                            cxrUserOrderSubscribeMilk.setCxrUserOrderId(returnCxrUserOrder.getId());
                            cxrUserOrderSubscribeMilk.setSubscribeInfo(customerDistributioninfoDTOList);
                            SQLException.isTrueFlag(
                                SqlHelper.retBool(
                                    cxrUserOrderSubscribeMilkMapper.insert(cxrUserOrderSubscribeMilk)),
                                () -> {
                                    throw new ServiceException("创建订单失败!");
                                });
                        }
                    }

                } else {
                    returnCxrUserOrder.setPerfectStatus(PerfectStatusEnums.NO_PERFECT.getValue());
                    returnCxrUserOrder.setNewCustomerFlag(SysYesNo.YES.getValue());
                    customerInfo.setName(item.getName());
                    customerInfo.setPhone(item.getPhone());
                    customerInfo.setSysAreaId(item.getSysAreaId());
                    customerInfo.setProvice(item.getProvice());
                    customerInfo.setCity(item.getCity());
                    customerInfo.setArea(item.getArea());
                    customerInfo.setResidentialQuartersId(item.getResidentialQuartersId());
                    customerInfo.setResidentialQuartersName(item.getResidentialQuartersName());
                    customerInfo.setAdress(item.getAdress());
                    customerInfo.setSiteId(item.getSiteId());
                    customerInfo.setSiteName(item.getSiteName());
                    customerInfo.setDistributionId(item.getDistributionId());
                    customerInfo.setDistributionName(item.getDistributionName());
                }
                returnCxrUserOrder.setOldCustomer(bo);
            } else {
                returnCxrUserOrder.setOldCustomer(Boolean.FALSE);
                returnCxrUserOrder.setPerfectStatus(PerfectStatusEnums.NO_PERFECT.getValue());
                returnCxrUserOrder.setNewCustomerFlag(SysYesNo.YES.getValue());
                customerInfo.setName(item.getName());
                customerInfo.setPhone(item.getPhone());
                customerInfo.setSysAreaId(item.getSysAreaId());
                customerInfo.setProvice(item.getProvice());
                customerInfo.setCity(item.getCity());
                customerInfo.setArea(item.getArea());
                customerInfo.setResidentialQuartersId(item.getResidentialQuartersId());
                customerInfo.setResidentialQuartersName(item.getResidentialQuartersName());
                customerInfo.setAdress(item.getAdress());
                customerInfo.setSiteId(item.getSiteId());
                customerInfo.setSiteName(item.getSiteName());
                customerInfo.setDistributionId(item.getDistributionId());
                customerInfo.setDistributionName(item.getDistributionName());
            }

            /** 特殊处理 如果是终端是管理端都是已经完善的，不需要在完善 */
            if (NumberUtil.equals(cxrUserOrder.getTerminalType(), TerminalTypeEnums.manager.getValue())) {
                returnCxrUserOrder.setPerfectStatus(PerfectStatusEnums.PERFECT.getValue());
            }

            Boolean zeroQuantityRenewal = cxrUserOrderService.zeroContinueFlag(item.getPhone(), null);

            customerInfoList.add(customerInfo);
            returnCxrUserOrder.setCustomerInfoList(customerInfoList);

            returnCxrUserOrder.setZeroQuantityRenewal(zeroQuantityRenewal);
            returnCxrUserOrder.setContractOrderExt(null);
            returnCxrUserOrder.setPayTime(cxrUserOrder.getPayTime());
            returnCxrUserOrder.setContractTotalAmount(cxrUserOrder.getAmount());
            returnCxrUserOrder.setContractTotalOrderQuantity(cxrUserOrder.getOrderQuantity());
            returnCxrUserOrder.setUpdateBy(cxrUserOrder.getCreateBy());
            returnCxrUserOrder.setUpdateByName(cxrUserOrder.getCreateByName());
            returnCxrUserOrder.setUpdateByType(cxrUserOrder.getCreateByType());
            returnCxrUserOrder.setCreateBy(cxrUserOrder.getCreateBy());
            returnCxrUserOrder.setCreateByName(cxrUserOrder.getCreateByName());

            cxrUserOrderList.add(returnCxrUserOrder);
        }

        cxrUserOrderService.saveBatch(cxrUserOrderList);

        return idList;
    }

    @HuiBoSynCxrUserOrder(value = "#completeInfoDTO.userOrderId")
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean followUpdate(ContractOrderHandlerBO completeInfoDTO) {

        CxrUserOrder cxrUserOrder = cxrUserOrderService.getById(completeInfoDTO.getUserOrderId());
        if (ObjectUtil.isNull(cxrUserOrder)) {
            throw new ServiceException("订单不存在");
        }
        LoginEmployee loginUser = StaffLoginHelper.getLoginUser();
        // 所以的子单
        String orderNo = cxrUserOrder.getOrderNo();
        String[] split = orderNo.split("-");
        String parentOrderNo = split[0];
        List<CxrUserOrder> cxrUserOrderList =
            cxrUserOrderService
                .getBaseMapper()
                .selectList(
                    new LambdaUpdateWrapper<CxrUserOrder>()
                        .likeRight(CxrUserOrder::getOrderNo, StrUtil.format("{}-", parentOrderNo)));

        checkOrderRecorder(completeInfoDTO.getApprenticeOrderFlag(), completeInfoDTO.getPromotionalOrderFlag()
            , cxrUserOrderList, completeInfoDTO.getBusinessAgent()
        );

        List<Long> ids = null;
        Map<String, CxrUserOrder> orderMap =
            cxrUserOrderList.stream().collect(Collectors.toMap(a -> a.getOrderNo(), a -> a));
        if (CollectionUtil.isNotEmpty(cxrUserOrderList)) {
            ids = cxrUserOrderList.stream().map(CxrUserOrder::getId).collect(Collectors.toList());
            String orderOne = StrUtil.format("{}-1", parentOrderNo);
            boolean flag =
                cxrUserOrderService.update(
                    null,
                    new LambdaUpdateWrapper<CxrUserOrder>()
                        .set(
                            CollectionUtil.isNotEmpty(completeInfoDTO.getOrderImages()),
                            CxrUserOrder::getOrderImages,
                            JSONUtil.toJsonStr(completeInfoDTO.getOrderImages()))
                        .set(
                            CollectionUtil.isNotEmpty(completeInfoDTO.getPlayImages()),
                            CxrUserOrder::getPlayImages,
                            JSONUtil.toJsonStr(completeInfoDTO.getPlayImages()))
                        .set(
                            CxrUserOrder::getBusinessAgent,
                            JSONUtil.toJsonStr(completeInfoDTO.getBusinessAgent()))
                        .set(
                            CxrUserOrder::getApprenticeOrderFlag,
                            completeInfoDTO.getApprenticeOrderFlag())
                        .set(
                            CxrUserOrder::getPromotionalOrderFlag,
                            completeInfoDTO.getPromotionalOrderFlag())
                        .set(CxrUserOrder::getUpdateBy, loginUser.getUserId())
                        .set(CxrUserOrder::getUpdateTime, new Date())
                        .set(CxrUserOrder::getUpdateByName, loginUser.getUserName())
                        .setSql(String.format("excess_quantity = IF(order_no = '%s',%s,0)", orderOne
                            , completeInfoDTO.getExcessQuantity()))
                        .in(CxrUserOrder::getId, ids));

            if (!flag) {
                throw new ServiceException("合订单其他信息同步失败,请稍后再试");
            }
            CxrUserOrder agedUserOrder = orderMap.get(completeInfoDTO.getOrderNo()); // 可能存在修改手机电话的情况
            if (ObjectUtil.isNotEmpty(agedUserOrder)) {

                // 更新客户库存
                CxrCustomer customerInfo =
                    remoteCustomerService.selectByCustomer(agedUserOrder.getCustomerId());

                if (ObjectUtil.isEmpty(agedUserOrder.getLongMilkGiveQuantity())) {
                    agedUserOrder.setLongMilkGiveQuantity(0);
                }
                if (ObjectUtil.isEmpty(agedUserOrder.getFreshMilkSentQuantity())) {
                    agedUserOrder.setFreshMilkSentQuantity(0);
                }

                if (ObjectUtil.isEmpty(agedUserOrder.getFreshMilkGiveQuantity())) {
                    agedUserOrder.setFreshMilkGiveQuantity(0);
                }
                CustomerInfo info1 = completeInfoDTO.getCustomerInfoList().get(0);
                Integer longMilkGiveQuantity = info1.getLongMilkGiveQuantity();
                Integer freshMilkSentQuantity = info1.getFreshMilkSentQuantity();
                //                    Integer freshMilkQuantity = info1.getFreshMilkQuantity();
                Integer freshMilkGiveQuantity = info1.getFreshMilkGiveQuantity();

                if (ObjectUtil.isEmpty(longMilkGiveQuantity)) {
                    longMilkGiveQuantity = 0;
                }

                if (ObjectUtil.isEmpty(freshMilkGiveQuantity)) {
                    freshMilkGiveQuantity = 0;
                }

                if (ObjectUtil.isEmpty(freshMilkSentQuantity)) {
                    freshMilkSentQuantity = 0;
                }

                //
                // customerInfo.setFreshMilkGiveTotal(customerInfo.getFreshMilkGiveTotal()-agedUserOrder.getFreshMilkGiveQuantity()+freshMilkGiveQuantity);
                //
                // customerInfo.setFreshMilkSentTotal(customerInfo.getFreshMilkSentTotal()-agedUserOrder.getFreshMilkSentQuantity()+freshMilkSentQuantity);
                //                    customerInfo.setLongMilkGiveTotal(customerInfo.getLongMilkGiveTotal()-
                //                      agedUserOrder.getLongMilkGiveQuantity()
                //                        +longMilkGiveQuantity);

                customerInfo.setFreshMilkGiveTotal(freshMilkGiveQuantity);
                customerInfo.setFreshMilkSentTotal(freshMilkSentQuantity);
                customerInfo.setLongMilkGiveTotal(longMilkGiveQuantity);
                // 一共要入库多少  减去之前的
                Integer subtractStock =
                    agedUserOrder.getFreshMilkGiveQuantity()
                        - agedUserOrder.getFreshMilkSentQuantity(); // 老的

                Integer addStock = freshMilkGiveQuantity - freshMilkSentQuantity; // 新的替换
                //
                // customerInfo.setCustomerStock(customerInfo.getCustomerStock()-subtractStock+addStock);

                remoteCustomerService.updateOneCustomer(customerInfo);

                List<CxrCustomerStockDetail> cxrCustomerStockDetailList = new ArrayList<>();
                CxrCustomerStockDetail customerStockDetail = new CxrCustomerStockDetail();
                customerStockDetail.setTerminalType(TerminalTypeEnums.disribution.getValue());
                customerStockDetail.setFreshMilkQuantity(-subtractStock); // 减库存
                customerStockDetail.setRemark(
                    StrUtil.format("合订单编辑客户,时间为={},数量={}", LocalDateTime.now().toString(), subtractStock));
                cxrCustomerStockDetailList.add(customerStockDetail);

                CxrCustomerStockDetail customerStockDetail1 = new CxrCustomerStockDetail();
                customerStockDetail1.setTerminalType(TerminalTypeEnums.disribution.getValue());
                customerStockDetail1.setFreshMilkQuantity(+addStock); // 减库存
                customerStockDetail1.setRemark(
                    StrUtil.format("合订单编辑客户,时间为={},数量={}", LocalDateTime.now().toString(), addStock));
                cxrCustomerStockDetailList.add(customerStockDetail1);

                remoteCustomerStockDetailService.addRecord(
                    customerInfo.getId(), cxrCustomerStockDetailList, customerInfo.getRevision());

                cxrUserOrderService.update(
                    null,
                    new LambdaUpdateWrapper<CxrUserOrder>()
                        .eq(CxrUserOrder::getId, agedUserOrder.getId())
                        .set(CxrUserOrder::getFreshMilkGiveQuantity, freshMilkGiveQuantity)
                        .set(CxrUserOrder::getFreshMilkSentQuantity, freshMilkSentQuantity)
                        .set(CxrUserOrder::getLongMilkGiveQuantity, longMilkGiveQuantity)
                        .set(CxrUserOrder::getRemark, completeInfoDTO.getRemark())
                        .set(CxrUserOrder::getUpdateBy, loginUser.getUserId())
                        .set(CxrUserOrder::getUpdateTime, new Date())
                        .set(CxrUserOrder::getUpdateByName, loginUser.getUserName()));

                List<CustomerInfo> customerInfoList = completeInfoDTO.getCustomerInfoList();
                if (CollectionUtil.isNotEmpty(customerInfoList)) {
                    // 地址排奶修改  完善信息   老客户 没有 如何判断他是新客户还是老客户

                    CustomerAddressDistributioninfoDTO customerAddressDistributioninfoDTO =
                        new CustomerAddressDistributioninfoDTO();
                    customerAddressDistributioninfoDTO.setSiteId(cxrUserOrder.getSiteId());
                    customerAddressDistributioninfoDTO.setUserOrderId(cxrUserOrder.getId());
                    List<CustomerDistributioninfoDTO> customerDistributioninfoDTOS =
                        BeanUtil.copyToList(customerInfoList, CustomerDistributioninfoDTO.class);

                    customerAddressDistributioninfoDTO.setCustomerDistributioninfoList(
                        customerDistributioninfoDTOS);

                    List<Long> addressIds =
                        remoteCustomerDistributionDetailService.editCompleteInformation(
                            customerAddressDistributioninfoDTO, cxrUserOrder.getCustomerId());

                    if (CollectionUtil.isNotEmpty(addressIds)
                        && NumberUtil.equals(addressIds.size(), customerDistributioninfoDTOS.size())) {
                        for (int i = 0; i < customerDistributioninfoDTOS.size(); i++) {
                            CustomerDistributioninfoDTO customerDistributioninfoDTO =
                                customerDistributioninfoDTOS.get(i);
                            Long aLong = addressIds.get(i);
                            customerDistributioninfoDTO.setAddressId(aLong);
                        }
                    }

                    boolean exists =
                        new LambdaQueryChainWrapper<>(cxrUserOrderSubscribeMilkMapper)
                            .eq(CxrUserOrderSubscribeMilk::getCxrUserOrderId, cxrUserOrder.getId())
                            .exists();
                    boolean retBool;
                    if (exists) {
                        retBool =
                            SqlHelper.retBool(
                                cxrUserOrderSubscribeMilkMapper.update(
                                    null,
                                    Wrappers.lambdaUpdate(CxrUserOrderSubscribeMilk.class)
                                        .eq(CxrUserOrderSubscribeMilk::getCxrUserOrderId, cxrUserOrder.getId())
                                        .set(
                                            CxrUserOrderSubscribeMilk::getSubscribeInfo,
                                            JSONUtil.toJsonStr(customerDistributioninfoDTOS))));
                    } else {
                        CxrUserOrderSubscribeMilk cxrUserOrderSubscribeMilk = new CxrUserOrderSubscribeMilk();
                        cxrUserOrderSubscribeMilk.setCxrUserOrderId(cxrUserOrder.getId());
                        cxrUserOrderSubscribeMilk.setSubscribeInfo(customerDistributioninfoDTOS);
                        retBool =
                            SqlHelper.retBool(
                                cxrUserOrderSubscribeMilkMapper.insert(cxrUserOrderSubscribeMilk));
                    }
                    log.info("保存订订单订阅信息 结果={}", retBool);
                }

                // 减去常温奶库存
//                remoteLongMilkStockService.updateLongMilks(agedUserOrder.getId(), longMilkGiveQuantity);
                if (!ObjectUtil.equals(completeInfoDTO.getLongMilkGiveQuantity(), cxrUserOrder.getLongMilkGiveQuantity())) {
                    remoteLongMilkStockService.delLongMilk(cxrUserOrder.getId());
                    remoteOrderService.saveLongMilkStock(cxrUserOrder.getId());
                }

            }
            // 废弃之前的业绩
            if (CollectionUtil.isNotEmpty(ids)) {
                remoteEmployeeAchievementDetailService.achievemenDiscard(ids, "配送端订单修改废弃审核的订单业绩");
                cxrUserOrderService.orderAfterEmployeeHandler(cxrUserOrder.getId());
            }

            if (ObjectUtil.isNotEmpty(completeInfoDTO.getFreshMilkSentQuantity())) {
                int sentNumber = completeInfoDTO.getFreshMilkSentQuantity();
                CustomerSentNumberVo customerSentNumberVo = new CustomerSentNumberVo();
                customerSentNumberVo.setLaoMilkDistributionTotal(Long.valueOf(cxrUserOrder.getFreshMilkSentQuantity()));
                customerSentNumberVo.setMilkDistributionTotal(Long.valueOf(sentNumber));
                customerSentNumberVo.setCxrCustomerId(cxrUserOrder.getCustomerId());
                customerSentNumberVo.setDistributionDate(cxrUserOrder.getOrderDate());
                customerSentNumberVo.setReceiverPhone(cxrUserOrder.getCustomerPhone());
                customerSentNumberVo.setCustomerAddress(cxrUserOrder.getCustomerAdress());
                customerSentNumberVo.setIsEdit(true);
                mqUtil.sendSyncMessage(CustomerAddressConstant.CUSTOMER_ADDRESS_TOPIC,
                    CustomerAddressConstant.CUSTOMER_ORDER_SENT_SUM_CHANGE_TAG,
                    JSONUtil.toJsonStr(customerSentNumberVo));
            }
        }
        return true;
    }


    private void verifyUnusual(CxrUserOrder lastCxrUserOrder, List<BusinessAgent> businessAgent, String phone) {

        List<Long> proxyIds =
            lastCxrUserOrder.getBusinessAgent().stream()
                .map(BusinessAgent::getProxyId)
                .collect(Collectors.toList());
        List<CxrEmployee> employeeList = employeeService.getEmployeeIds(proxyIds);

        // 已经离职人员不校验
        employeeList.removeIf(
            e ->
                e.getQuitTime() != null
                    && !DateUtils.getLocalDateFromDate(e.getQuitTime())
                    .isAfter(LocalDate.now()));
        proxyIds = employeeList.stream().map(CxrEmployee::getId)
            .collect(Collectors.toList());

        for (BusinessAgent agent : businessAgent) {
            if (!proxyIds.contains(agent.getProxyId())) {
                throw new ServiceException(
                    StrUtil.format("客户:{}  只能由 `{}`录入", phone,
                        employeeList.stream()
                            .map(CxrEmployee::getName)
                            .collect(Collectors.joining(","))
                    ));
            }
        }
    }

    @Override
    public void checkOrderRecorder(
        Boolean promotionalOrderFlag, Boolean apprenticeOrderFlag, List<CxrUserOrder> cxrUserOrderList,
        List<BusinessAgent> businessAgent) {
//        if (!apprenticeOrderFlag
//            && !promotionalOrderFlag) {
        for (CxrUserOrder userOrder : cxrUserOrderList) {

            OrderTypeEnums orderTypeEnums = fastOrderService.queryOrderTypeNotInId(
                userOrder.getCustomerPhone(), userOrder.getId());
            if (ObjectUtil.equals(orderTypeEnums.getValue(), OrderTypeEnums.CONTINUE_ORDER.getValue())) {

                Long customerId = userOrder.getCustomerId();
                List<CxrCustomerAddress> customerAddressList = remoteCustomerService.getCustomerAddress(customerId);

                List<Long> proxyIds = customerAddressList.stream().map(CxrCustomerAddress::getCxrEmployeeId)
                    .collect(Collectors.toList());
                List<CxrEmployee> employeeList = employeeService.getEmployeeIds(proxyIds);
                for (BusinessAgent agent : businessAgent) {
                    if (!proxyIds.contains(agent.getProxyId())) {
                        throw new ServiceException(
                            StrUtil.format("客户:{}  只能由 `{}`录入", userOrder.getCustomerPhone(),
                                employeeList.stream()
                                    .map(CxrEmployee::getName)
                                    .collect(Collectors.joining(","))
                            ));
                    }
                }

            } else if (ObjectUtil.equals(orderTypeEnums.getValue(), OrderTypeEnums.INCREASE_ORDER.getValue())) {
                CxrUserOrder lastCxrUserOrder =
                    cxrUserOrderService.queryLastOrder(userOrder.getCustomerPhone(), userOrder.getId());//过滤合订单
                if (ObjectUtil.isEmpty(lastCxrUserOrder)) {
                    log.info(userOrder.getCustomerPhone() + "手机号 未匹配到最后一笔订单"
                        + OrderTypeEnums.INCREASE_ORDER.getDesc());
                    continue;
                }
                verifyUnusual(lastCxrUserOrder, businessAgent, userOrder.getCustomerPhone());
            }
//            }
        }
    }

}
