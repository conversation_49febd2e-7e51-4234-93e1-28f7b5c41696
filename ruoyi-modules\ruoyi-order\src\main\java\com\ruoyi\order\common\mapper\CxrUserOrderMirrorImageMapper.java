package com.ruoyi.order.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.order.common.entity.CxrUserOrderMirrorImage;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @description 针对表【cxr_user_order_mirror_image(用户订单 镜像表)】的数据库操作Mapper
 * @createDate 2025-07-23 16:42:32
 * @Entity generator.domain.CxrUserOrderMirrorImage
 */
public interface CxrUserOrderMirrorImageMapper extends BaseMapper<CxrUserOrderMirrorImage> {


    void orderMirrorImageSave(@Param("startDate") LocalDate startDate, @Param("lastDayOfPreviousMonth") LocalDateTime lastDayOfPreviousMonth,
                              @Param("currentDate") LocalDateTime currentDate);
}
