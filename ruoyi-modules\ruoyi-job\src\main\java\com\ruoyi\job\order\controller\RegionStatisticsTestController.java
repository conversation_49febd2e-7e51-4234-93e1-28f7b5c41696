package com.ruoyi.job.order.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.job.order.service.RegionStatisticsCalculateService;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 区域统计报表测试接口
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/job/region-statistics/test")
public class RegionStatisticsTestController {

    @Autowired
    private RegionStatisticsCalculateService regionStatisticsCalculateService;

    /**
     * 计算指定月份的区域统计报表数据
     *
     * @param month 月份，格式：yyyy-MM，如：2024-01
     * @return 计算结果
     */
    @PostMapping("/calculate/{month}")
    public R<String> calculateMonthData(@PathVariable String month) {
        try {
            log.info("开始计算月份 {} 的区域统计报表数据", month);

            // 解析月份参数
            YearMonth yearMonth = YearMonth.parse(month, DateTimeFormatter.ofPattern("yyyy-MM"));

            // 调用计算方法
            regionStatisticsCalculateService.calculateMonthDataWithTransaction(yearMonth);

            log.info("月份 {} 的区域统计报表数据计算完成", month);
            return R.ok("计算完成", "月份 " + month + " 的区域统计报表数据计算成功");

        } catch (Exception e) {
            log.error("计算月份 {} 的区域统计报表数据失败", month, e);
            return R.fail("计算失败：" + e.getMessage());
        }
    }

    /**
     * 计算指定日份的区域统计报表数据
     *
     * @param month 月份，格式：yyyy-MM，如：2024-01
     * @return 计算结果
     */
    @PostMapping("/calculateDay/{Day}")
    public R<String> calculateDayData(@PathVariable String Day) {
        try {
            log.info("开始计算月份 {} 的区域统计报表数据", Day);

            // 解析月份参数
            LocalDate yearMonth = LocalDate.parse(Day, DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            // 调用计算方法
            regionStatisticsCalculateService.calculateDayData(yearMonth,yearMonth);

            log.info("月份 {} 的区域统计报表数据计算完成", Day);
            return R.ok("计算完成", "月份 " + Day + " 的区域统计报表数据计算成功");

        } catch (Exception e) {
            log.error("计算月份 {} 的区域统计报表数据失败", Day, e);
            return R.fail("计算失败：" + e.getMessage());
        }
    }

    /**
     * 计算当前月份的区域统计报表数据
     *
     * @return 计算结果
     */
    @PostMapping("/calculate/current")
    public R<String> calculateCurrentMonthData() {
        try {
            YearMonth currentMonth = YearMonth.now();
            log.info("开始计算当前月份 {} 的区域统计报表数据", currentMonth);

            // 调用计算方法
            regionStatisticsCalculateService.calculateMonthDataWithTransaction(currentMonth);

            log.info("当前月份 {} 的区域统计报表数据计算完成", currentMonth);
            return R.ok("计算完成", "当前月份 " + currentMonth + " 的区域统计报表数据计算成功");

        } catch (Exception e) {
            log.error("计算当前月份的区域统计报表数据失败", e);
            return R.fail("计算失败：" + e.getMessage());
        }
    }

    /**
     * 计算上个月的区域统计报表数据
     *
     * @return 计算结果
     */
    @PostMapping("/calculate/last-month")
    public R<String> calculateLastMonthData() {
        try {
            YearMonth lastMonth = YearMonth.now().minusMonths(1);
            log.info("开始计算上个月 {} 的区域统计报表数据", lastMonth);

            // 调用计算方法
            regionStatisticsCalculateService.calculateMonthDataWithTransaction(lastMonth);

            log.info("上个月 {} 的区域统计报表数据计算完成", lastMonth);
            return R.ok("计算完成", "上个月 " + lastMonth + " 的区域统计报表数据计算成功");

        } catch (Exception e) {
            log.error("计算上个月的区域统计报表数据失败", e);
            return R.fail("计算失败：" + e.getMessage());
        }
    }

    /**
     * 获取计算状态（简单的健康检查）
     *
     * @return 状态信息
     */
    @GetMapping("/status")
    public R<String> getStatus() {
        return R.ok("服务正常", "区域统计报表计算服务运行正常");
    }
}
