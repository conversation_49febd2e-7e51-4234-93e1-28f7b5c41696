package com.ruoyi.calculate.customerOrderStatistics.service;

import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.order.api.domain.bo.CustomerOrderStatisticsBo;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.List;

public interface CustomerOrderStatisticsService {

    Object orderStatistics(CustomerOrderStatisticsBo bo, PageQuery pageQuery);

    void orderStatisticsExport(CustomerOrderStatisticsBo bo, HttpServletResponse response);

    Object orderFixationStatistics(CustomerOrderStatisticsBo bo, PageQuery pageQuery);

    void orderFixationStatisticsExport(CustomerOrderStatisticsBo bo, HttpServletResponse response);

    List<LocalDateTime> getMirrorImageTime();

}
