CREATE TABLE `cxr_user_order_mirror_image`
(
    `id`                            bigint                                                        DEFAULT NULL COMMENT '编号',
    `create_by_name`                varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
    `create_time`                   datetime   NOT NULL                                           DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by_name`                varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新人名称',
    `update_time`                   datetime                                                      DEFAULT NULL COMMENT '更新时间',
    `delete_status`                 varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '删除状态(详情见字典)',
    `company_id`                    bigint                                                        DEFAULT NULL COMMENT '公司id',
    `company_name`                  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL COMMENT '公司名称',
    `big_area_id`                   bigint                                                        DEFAULT NULL COMMENT '大区id',
    `big_area_name`                 varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL COMMENT '大区名称',
    `province`                      varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL COMMENT '省',
    `city`                          varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL COMMENT '市',
    `area`                          varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL COMMENT '区',
    `site_name`                     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL COMMENT '站点名称',
    `site_adress`                   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL COMMENT '站点地址',
    `site_id`                       bigint                                                        DEFAULT NULL COMMENT '站点id',
    `business_agent`                json                                                          DEFAULT NULL COMMENT '业务代理',
    `customer_name`                 varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '客户名称',
    `customer_phone`                varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL COMMENT '客户电话;只取第一个录入的数据',
    `customer_adress`               varchar(256)                                                  DEFAULT NULL COMMENT '客户地址; 只取第一个录入的数据',
    `customer_name_switch`          varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL COMMENT '转入客户姓名',
    `customer_adress_switch`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL COMMENT '转入客户地址',
    `customer_phone_switch`         varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL COMMENT '转入客户手机号',
    `customer_id_switch`            bigint                                                        DEFAULT NULL COMMENT '转入人的客户id',
    `payment_souce`                 varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci   DEFAULT NULL COMMENT '1微信;2 支付宝',
    `order_type`                    int                                                           DEFAULT NULL COMMENT '订单类型;1、新订单2、增订单3、续订单4、转单5、换单6、退订单7、赠送单',
    `zero_quantity_renewal`         tinyint(1)                                                    DEFAULT '0' COMMENT '0数量续单：用于客户账户没有鲜奶了;并且停奶超过了15天。15天后续单的，显示“超过15天”。',
    `conversion_type`               varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL COMMENT '用于换单 换单的商品的类目  , 订单表的 convert_type',
    `order_no`                      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT '' COMMENT '单据;订单单据号',
    `merchant_order_no`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL COMMENT '商户单号',
    `sqb_sn`                        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT '' COMMENT '收钱吧单号|富友单号',
    `order_quantity`                int                                                           DEFAULT '0' COMMENT '订购数量：鲜奶订购数量',
    `fresh_milk_give_quantity`      int                                                           DEFAULT '0' COMMENT '鲜奶赠送数量 (如果是退单,次字段用来记录退单鲜奶数)',
    `long_milk_give_quantity`       int                                                           DEFAULT '0' COMMENT '常温奶赠送数量 (如果是退单,次字段用来记录常温奶退掉的数)',
    `excess_quantity`               int                                                           DEFAULT '0' COMMENT '超送数量：根据订单套餐超送规则计算出来的超送数量;祥看原型',
    `fresh_milk_sent_quantity`      int                                                           DEFAULT '0' COMMENT '鲜奶已送数量',
    `long_milk_sent_quantity`       int                                                           DEFAULT '0' COMMENT '常温奶已送数量(已废弃!!!!!!)',
    `surplus_quantity`              int                                                           DEFAULT '0' COMMENT '剩余数量 , 也是客户列表里的 总数量 （鲜奶订购数量 - 鲜奶已送数量）',
    `conversion_quantity`           int                                                           DEFAULT NULL COMMENT '转换数量，订单类型为“转单”的时候才会有值;即转换的鲜奶数量',
    `unit_price`                    decimal(11, 2)                                                DEFAULT NULL COMMENT '单价：鲜奶单价',
    `amount`                        decimal(11, 2)                                                DEFAULT NULL COMMENT '金额：该笔订单的总金额  ,  如果类型是退订单 则是退订单金额 ，用正数方式',
    `credit_card_amount`            decimal(11, 2)                                                DEFAULT NULL COMMENT '刷卡金额',
    `order_date`                    datetime                                                      DEFAULT NULL COMMENT '订单日期',
    `remark`                        varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
    `audit_status`                  tinyint                                                       DEFAULT NULL COMMENT '1 待审核 、 2 已审核 、 3.已拒绝',
    `audit_by`                      bigint                                                        DEFAULT NULL COMMENT '审核人',
    `audit_by_name`                 varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT '' COMMENT '审核人名字',
    `audit_time`                    datetime                                                      DEFAULT NULL COMMENT '审核时间',
    `promotional_order_flag`        tinyint(1)                                                    DEFAULT NULL COMMENT '是否促销单;true 是 false 否',
    `apprentice_order_flag`         tinyint(1)                                                    DEFAULT NULL COMMENT '是否师徒单;true是 false 否',
    `terminal_type`                 tinyint                                                       DEFAULT NULL COMMENT '终端类型  1 后端 2 配送端',
    `customer_info_list`            json                                                          DEFAULT NULL COMMENT '客户信息',
    `order_images`                  json                                                          DEFAULT NULL COMMENT '单据图片',
    `play_images`                   json                                                          DEFAULT NULL COMMENT '支付截图',
    `pay_status`                    tinyint    NOT NULL                                           DEFAULT '0' COMMENT '支付状态',
    `perfect_status`                tinyint                                                       DEFAULT '2' COMMENT '完善状态 ( 1 =未完善, 2 =已完善)',
    `fresh_milk_return_quantity`    int                                                           DEFAULT NULL COMMENT '鲜奶退订数量',
    `contract_order_ext`            json                                                          DEFAULT NULL COMMENT '合订单扩展数据',
    `order_status`                  int                                                           DEFAULT NULL COMMENT '无用字段后期删除',
    `pay_time`                      datetime                                                      DEFAULT NULL COMMENT '支付实际',
    `contract_total_amount`         decimal(10, 2)                                                DEFAULT NULL COMMENT '合订单总金额',
    `contract_total_order_quantity` int                                                           DEFAULT NULL COMMENT '合订单数量',
    `exchange_product_id`           bigint                                                        DEFAULT NULL COMMENT '换单商品兑换id',
    `exchange_sum`                  int                                                           DEFAULT NULL COMMENT '换单兑换数量',
    `milk_exchange_sum`             int                                                           DEFAULT NULL COMMENT '换单鲜奶兑换商品数量',
    `exchange_product_name`         varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL COMMENT '换单商品名称',
    `spec`                          json                                                          DEFAULT NULL COMMENT '换单兑换商品规格',
    `is_import`                     tinyint                                                       DEFAULT NULL COMMENT '是否是导入单',
    `activity_give_quantity`        int                                                           DEFAULT '0' COMMENT '活动赠品数量',
    `activity_sent_quantity`        int                                                           DEFAULT '0' COMMENT '活动赠品已送数量',
    `customer_id`                   bigint                                                        DEFAULT '0',
    `activity_type`                 varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL COMMENT '活动类型',
    `third_order_no`                varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL COMMENT '给第三方的订单号',
    `new_customer_flag`             varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci   DEFAULT '' COMMENT '是否是新客户',
    `out_long_milk_order_no`        varchar(50)                                                   DEFAULT NULL,
    `activity_id`                   bigint                                                        DEFAULT '0' COMMENT '活动id',
    `give_gift_list`                json                                                          DEFAULT NULL COMMENT '选择礼品数据',
    `activity_name`                 varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '活动名称',
    `set_meal_price`                decimal(10, 2)                                                DEFAULT NULL COMMENT '套餐价格',
    `set_meal_qty`                  int                                                           DEFAULT '0' COMMENT '套餐数量',
    `pigc_flag`                     tinyint(1) NOT NULL                                           DEFAULT '1' COMMENT '订单赠送数是否参与增订单核算',
    `apportion_money`               decimal(10, 2)                                                DEFAULT NULL COMMENT '分摊费用',
    `audit_count`                   tinyint(1) NOT NULL                                           DEFAULT '0' COMMENT '订单审核次数',
    `third_order_no_time`           datetime                                                      DEFAULT NULL COMMENT 'third_order_no 生成时间',
    `contract_type_tag`             tinyint(1)                                                    DEFAULT '-2' COMMENT '合订单的订单类型标签',
    `refund_success_flag`           tinyint(1) NOT NULL                                           DEFAULT '0' COMMENT '退订打款标识',
    `create_by_code`                varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL COMMENT '创建人编号',
    `pay_plaform_type`              int                                                           DEFAULT '0' COMMENT '0收钱吧 1管家婆 2富友',
    `scan_qr_times`                 int                                                           DEFAULT NULL COMMENT '扫付款二维码次数',
    `co_type`                       tinyint(1)                                                    DEFAULT NULL COMMENT '合订单的订单类型',
    `payment_status`                tinyint                                                       DEFAULT NULL COMMENT '账户类型:1.未打款、2.已打款、3.打款失败',
    `payment_failure_reasons`       varchar(255)                                                  DEFAULT NULL COMMENT '打款失败原因',
    `pay_order_id`                  bigint                                                        DEFAULT NULL COMMENT '用户主订单id',
    `tiktok_order_refund_no`        varchar(64)                                                   DEFAULT NULL COMMENT '抖音退款单号',
    `platform_discount`             decimal(20, 2)                                                DEFAULT NULL COMMENT '平台折扣金额',
    `pay_amount`                    decimal(12, 2)                                                DEFAULT NULL COMMENT '支付金额',
    `channel`                       int        NOT NULL                                           DEFAULT '0' COMMENT '渠道：0.线下，1.小程序，2.抖音',
    `product_type`                  varchar(64)                                                   DEFAULT NULL COMMENT '商品分类',
    `product_id`                    varchar(64)                                                   DEFAULT NULL COMMENT '商品id',
    `product_name`                  varchar(255)                                                  DEFAULT NULL COMMENT '商品名称',
    `accounting_type`               tinyint    NOT NULL                                           DEFAULT '0' COMMENT '核算类型：1.正常核算 ，2. 抖音推广',
    `promotion_commission`          tinyint    NOT NULL                                           DEFAULT '0' COMMENT '渠道推广提成：1.有，0.无',
    `commission_config_id`          bigint     NOT NULL                                           DEFAULT '0' COMMENT '配置id',
    `product_type_name`             varchar(255)                                                  DEFAULT NULL,
    `abnormal_tag`                  int        NOT NULL                                           DEFAULT '0' COMMENT '异常订单状态  1：异常 ',
    `abnormal_time`                 datetime                                                      DEFAULT NULL COMMENT '预警时间',
    `old_customer`                  tinyint(1)                                                    DEFAULT NULL COMMENT '是否是老客户，之前有地址的客户',
    `zero15_day_after`              tinyint(1)                                                    DEFAULT '0' COMMENT '0数量超过15天',
    `delivery_sites`                json                                                          DEFAULT NULL COMMENT '配送站点',
    `refund_no_audit_reasons`       varchar(255)                                                  DEFAULT NULL COMMENT '退款审核不通过原因',
    `mirror_image_time`             datetime                                                      DEFAULT NULL COMMENT '镜像时间',
    KEY `idx_customer_id` (`customer_id`),
    KEY `cxr_user_order_order_date_index_order_type` (`order_date`, `order_type`),
    KEY `idx_order_create_date` (`order_date`, `create_time`),
    KEY `idx_customer_phone_switch_delete_status` (`delete_status`, `customer_phone_switch`),
    KEY `idx_customer_phone` (`customer_phone`),
    KEY `idx_pay_time` (`pay_time`),
    KEY `idx_audit_time` (`audit_time`),
    KEY `unique_order_no` (`order_no`) USING BTREE,
    KEY `idx_image_time_idx` (`mirror_image_time`) USING BTREE,
    KEY `idx_bussiness_agent_proxy_id_list` ((cast(json_extract(`business_agent`, _utf8mb4'$[*].proxyId') as unsigned
                                                   array))),
    KEY `idx_order_date` ((date_format(`order_date`, _utf8mb4'%Y-%m-%d')))
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci
  ROW_FORMAT = DYNAMIC COMMENT ='用户订单 镜像表';


ALTER table
    cxr_report_forms_export
    add
        `retain_status` int NOT NULL DEFAULT '0' COMMENT '保留状态  0过期删除 1保留';
