package com.ruoyi.job.order;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.job.order.service.RegionStatisticsCalculateService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.List;

/**
 * 销售代理报表定时任务
 * 
 * <AUTHOR>
 * @date 2025-07-03
 */
@Component
@Slf4j
public class RegionStatisticsItemJob {

    @Autowired
    private RegionStatisticsCalculateService calculateService;

    /**
     * 定时任务入口
     */
    @XxlJob("regionStatisticsItemJob")
    public void execute() {
        try {
            String jobParam = XxlJobHelper.getJobParam();
            log.info("销售代理报表定时任务开始执行，参数：{}", jobParam);

            List<YearMonth> calculateMonths = determineCalculateMonths(jobParam);

            for (YearMonth month : calculateMonths) {
                // 调用Service处理，确保事务生效
                calculateService.calculateMonthDataWithTransaction(month);
            }

            log.info("销售代理报表定时任务执行完成");
        } catch (Exception e) {
            log.error("销售代理报表定时任务执行失败", e);
            throw e;
        }
    }

    /**
     * 确定需要计算的月份列表
     * @param jobParam 任务参数
     * @return 需要计算的月份列表
     */
    private List<YearMonth> determineCalculateMonths(String jobParam) {
        List<YearMonth> months = new ArrayList<>();

        if (StrUtil.isNotBlank(jobParam)) {
            // 有指定日期参数：计算指定日期所在月份
            LocalDate specifiedDate = LocalDate.parse(jobParam);
            YearMonth specifiedMonth = YearMonth.from(specifiedDate);
            months.add(specifiedMonth);
            log.info("指定日期模式：计算月份 {}", specifiedMonth);
        } else {
            // 无指定日期参数：根据当前日期判断
            LocalDate today = LocalDate.now();
            YearMonth currentMonth = YearMonth.from(today);

            if (today.getDayOfMonth() <= 6) {
                // 当前日期 ≤ 6号：计算当月 + 上个月
                YearMonth lastMonth = currentMonth.minusMonths(1);
                months.add(lastMonth);
                months.add(currentMonth);
                log.info("次月前6天模式：计算月份 {} 和 {}", lastMonth, currentMonth);
            } else {
                // 当前日期 > 7号：只计算当月
                months.add(currentMonth);
                log.info("月中模式：计算月份 {}", currentMonth);
            }
        }

        return months;
    }
}
