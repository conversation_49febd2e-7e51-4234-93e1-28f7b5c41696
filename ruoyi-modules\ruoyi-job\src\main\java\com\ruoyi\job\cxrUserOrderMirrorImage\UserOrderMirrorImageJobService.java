package com.ruoyi.job.cxrUserOrderMirrorImage;

import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.order.api.RemoteOrderService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

@Slf4j
@Service
public class UserOrderMirrorImageJobService {


    @DubboReference(timeout = 360000)
    private RemoteOrderService remoteOrderService;

    @XxlJob("userOrderMirrorImageJob")
    public void userOrderMirrorImageJob() {
        String jobParam = XxlJobHelper.getJobParam();
        // 申请时间
        LocalDate now = LocalDate.now();
        if (StringUtils.isNotBlank(jobParam)) {
            now = LocalDate.parse(jobParam);
        }
        remoteOrderService.userOrderMirrorImageJob(now);
        XxlJobHelper.log("查询它集合{}", "userOrderMirrorImageJob");
    }
}
