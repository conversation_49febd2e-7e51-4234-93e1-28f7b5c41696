<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.job.common.mapper.CxrEmployeeAchievementDetailMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.business.base.api.domain.CxrEmployeeAchievementDetail">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="sysDeptId" column="sys_dept_id" jdbcType="BIGINT"/>
        <result property="revision" column="revision" jdbcType="BIGINT"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="createByName" column="create_by_name" jdbcType="VARCHAR"/>
        <result property="createByType" column="create_by_type" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="updateByName" column="update_by_name" jdbcType="VARCHAR"/>
        <result property="updateByType" column="update_by_type" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleteBy" column="delete_by" jdbcType="BIGINT"/>
        <result property="deleteByName" column="delete_by_name" jdbcType="VARCHAR"/>
        <result property="deleteByType" column="delete_by_type" jdbcType="VARCHAR"/>
        <result property="deleteTime" column="delete_time" jdbcType="TIMESTAMP"/>
        <result property="deleteStatus" column="delete_status" jdbcType="VARCHAR"/>
        <result property="sortNum" column="sort_num" jdbcType="BIGINT"/>
        <result property="employeeId" column="employee_id" jdbcType="BIGINT"/>
        <result property="achievementValue" column="achievement_value" jdbcType="DECIMAL"/>
        <result property="souceId" column="souce_id" jdbcType="BIGINT"/>
        <result property="source" column="source" jdbcType="TINYINT"/>
        <result property="terminalType" column="terminal_type" jdbcType="TINYINT"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="siteId" column="site_id" jdbcType="BIGINT"/>
        <result property="excessQuantity" column="excess_quantity" jdbcType="INTEGER"/>
        <result property="boxs" column="boxs" jdbcType="INTEGER"/>
        <result property="orderFmQty" column="order_fm_qty" jdbcType="DECIMAL"/>
        <result property="orderDate" column="order_date"/>
        <result property="freshMilkGiveQuantity" column="fresh_milk_give_quantity"/>
        <result property="longMilkGiveQuantity" column="long_milk_give_quantity"/>
        <result property="employeeName" column="employee_name"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,sys_dept_id,revision,
        create_by,create_by_name,create_by_type,
        create_time,update_by,update_by_name,
        update_by_type,update_time,delete_by,
        delete_by_name,delete_by_type,delete_time,
        delete_status,sort_num,employee_id,
        achievement_value,souce_id,source,
        terminal_type,remark,site_id,
        excess_quantity,boxs,order_date
    </sql>


    <select id="getMouthPerformance" resultType="com.ruoyi.business.base.api.domain.CxrEmployeeAchievementDetail">
        select sum(achievement_value) as achievementValue , employee_id from cxr_employee_achievement_detail
        <where>
            <if test="ids!=null and ids.size()>0">
                and employee_id in
                <foreach collection="ids" separator="," open="(" close=")" index="i" item="item">
                    #{item}
                </foreach>
            </if>
            and delete_status=0 and DATE_FORMAT(order_date, '%Y-%m') = #{currentYearMonth}
        </where>
            group by employee_id
    </select>

    <!-- 按销售代理聚合查询业绩数据，参考 pageGroupEmp 的实现 -->
    <select id="selectAggregatedByEmployee" resultType="com.ruoyi.job.common.domain.CxrRegionStaitemDailyReport">
        SELECT
            a_detail.employee_id as agentId,
            ANY_VALUE(a_detail.employee_name) as agentName,
            ANY_VALUE(a_detail.site_id) as siteId,
            ANY_VALUE(site.current_dept_id) as companyId,
            ANY_VALUE(site.cxr_root_region_id) as regionId,

            <!-- 参考 pageGroupEmp 的订单类型判断逻辑 -->
            SUM(IF(a_detail.order_type = 0 OR a_detail.order_type = 70, a_detail.order_count, 0)) as newOrderCount,
            SUM(IF(a_detail.order_type = 0 OR a_detail.order_type = 70, a_detail.achievement_value, 0)) as newOrderAchievement,

            SUM(IF(a_detail.order_type = 1 OR a_detail.order_type = 71, a_detail.order_count, 0)) as continueOrderCount,
            SUM(IF(a_detail.order_type = 1 OR a_detail.order_type = 71, a_detail.achievement_value, 0)) as continueOrderAchievement,

            SUM(IF(a_detail.order_type = 3, a_detail.order_count, 0)) as increaseOrderCount,
            SUM(IF(a_detail.order_type = 3, a_detail.achievement_value, 0)) as increaseOrderAchievement,

            SUM(IF(a_detail.order_type = 2, a_detail.order_count, 0)) as returnOrderCount,
            SUM(IF(a_detail.order_type = 2, a_detail.achievement_value, 0)) as returnOrderAchievement,

            <!-- 总计算：新订单+续订单+增订单-退订单 -->
            SUM(IF(a_detail.order_type = 0 OR a_detail.order_type = 70, a_detail.order_count, 0) +
                IF(a_detail.order_type = 1 OR a_detail.order_type = 71, a_detail.order_count, 0) +
                IF(a_detail.order_type = 3, a_detail.order_count, 0) -
                IF(a_detail.order_type = 2, a_detail.order_count, 0)) as totalOrderCount,

            SUM(IF(a_detail.order_type = 0 OR a_detail.order_type = 70, a_detail.achievement_value, 0) +
                IF(a_detail.order_type = 1 OR a_detail.order_type = 71, a_detail.achievement_value, 0) +
                IF(a_detail.order_type = 3, a_detail.achievement_value, 0) +
                IF(a_detail.order_type = 2, a_detail.achievement_value, 0)) as totalAchievement

        FROM cxr_employee_achievement_detail a_detail
        LEFT JOIN cxr_site site ON a_detail.site_id = site.id
        WHERE a_detail.delete_status = '0'
          AND a_detail.order_date = #{date}
        GROUP BY a_detail.employee_id
    </select>

</mapper>
