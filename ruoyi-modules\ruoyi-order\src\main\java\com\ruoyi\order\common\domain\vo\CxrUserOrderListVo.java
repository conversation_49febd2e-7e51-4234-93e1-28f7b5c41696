package com.ruoyi.order.common.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.ruoyi.business.base.api.domain.json.BusinessAgent;
import com.ruoyi.business.base.api.domain.json.ContractOrderCustomerInfo;
import com.ruoyi.business.base.api.domain.json.CustomerInfo;
import com.ruoyi.common.excel.annotation.ExcelDictFormat;
import com.ruoyi.common.excel.convert.ExcelDictConvert;
import com.ruoyi.order.common.typeHandler.TypeHandlerConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/8/8 20:48
 */
@Data
@ExcelIgnoreUnannotated
public class CxrUserOrderListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @ApiModelProperty("主键 id")
    private Long id;

    /**
     * 公司id
     */
    @ExcelIgnore
    @ApiModelProperty("公司id")
    private Long companyId;

    /**
     * 公司名称
     */
    @ApiModelProperty("公司名称")
    @ExcelProperty(value = "所属公司")
    private String companyName;

    /**
     * 大区id
     */
    @ApiModelProperty("大区id")
    @ExcelIgnore
    private Long bigAreaId;

    /**
     * 大区名称
     */
    @ApiModelProperty("大区名称")
    @ExcelProperty(value = "所属大区")
    private String bigAreaName;

    /**
     * 省
     */
    @ApiModelProperty("省")
    @ExcelProperty(value = "省")
    private String province;

    /**
     * 市
     */
    @ApiModelProperty("市")
    @ExcelProperty(value = "市")
    private String city;


    @ExcelIgnore
    private String fromProductType;
    /**
     * 区
     */
    @ApiModelProperty("区")
    @ExcelProperty(value = "区")
    private String area;

    /**
     * 站点名称
     */
    @ApiModelProperty("站点名称")
    @ExcelProperty(value = "站点")
    private String siteName;

    /**
     * 站点编号
     */
    @ApiModelProperty("站点编号")
    @ExcelProperty(value = "站点编号")
    private String siteMark;

    /**
     * 站点地址
     */
    @ApiModelProperty("站点地址")
    @ExcelIgnore
    private String siteAdress;

    /**
     * 站点id
     */
    @ApiModelProperty("站点id")
    @ExcelIgnore
    private Long siteId;

    /**
     * 业务代理字符串; 以逗号分割
     */
    @ApiModelProperty("业务代理详细信息")
    @TableField(typeHandler = TypeHandlerConstant.BusinessAgentTypeHandler.class)
    @ExcelIgnore
    private List<BusinessAgent> businessAgent;

    @ExcelProperty(value = "销售代理")
    public String businessAgentName;

    @ExcelProperty(value = "销售代理")
    public String businessAgentJobNumber;

    @ExcelProperty(value = "等级")
    private String businessAgentLevel;

    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称;只取第一个录入的数据")
    @ExcelProperty(value = "客户名称")
    private String customerName;

    /**
     * 客户电话;只取第一个录入的数据
     */
    @ApiModelProperty("客户电话;只取第一个录入的数据")
    @ExcelProperty(value = "客户电话")
    private String customerPhone;

    /**
     * 客户地址; 只取第一个录入的数据
     */
    @ApiModelProperty("客户地址; 只取第一个录入的数据")
    @ExcelProperty(value = "客户地址")
    private String customerAdress;

    /**
     * 转入客户姓名
     */
    @ApiModelProperty("转入客户姓名")
    @ExcelProperty(value = "转入客户名称")
    private String customerNameSwitch;


    /**
     * 转入客户手机号
     */
    @ApiModelProperty("转入客户手机号")
    @ExcelProperty(value = "转入客户电话")
    private String customerPhoneSwitch;

    /**
     * 转入客户地址
     */
    @ApiModelProperty("转入客户地址")
    @ExcelProperty(value = "转入客户地址")
    private String customerAdressSwitch;


    /**
     * 1微信;2 支付宝
     */
    @ApiModelProperty("支付来源")
    @ExcelProperty(value = "付款来源", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "3=微信,2=支付宝")
    private String paymentSouce;

    @ExcelIgnore
    private Short orderType;

    @ExcelProperty(value = "订单类型")
    private String orderTypeValue;

    /**
     * 0数量续单：用于客户账户没有鲜奶了;并且停奶超过了15天。15天后续单的，显示“超过15天”。
     */
    @ApiModelProperty("0数量续单")
    @ExcelIgnore
    private Boolean zeroQuantityRenewal;

    @ExcelProperty(value = "0数量续单")
    private String zeroQuantityRenewalFlag;

    /**
     * 转换类型，用于订单类型为“换单”的订单，显示客户兑换的商品的类目;，剔除重复的 多个以逗号分割
     */
    @ApiModelProperty("转换类型")
    @ExcelProperty(value = "转换类型")
    private String conversionType;

    /**
     * 单据;订单单据号
     */
    @ApiModelProperty("单据")
    @ExcelProperty(value = "单据")
    private String orderNo;

    /**
     * 商户单号
     */
    @ApiModelProperty("商户单号")
    @ExcelProperty(value = "商户单号")
    private String merchantOrderNo;

    /**
     * 商户单号
     */
    @ApiModelProperty("收钱吧单号")
    @ExcelIgnore
    private String sqbSn;

    @ExcelProperty(value = "总数量")
    private Integer totalQuantity;

    @ExcelProperty(value = "鲜奶历史订购总数(退订单用)")
    private Integer freshMilkHistoryTotal;

    /**
     * 订购数量：鲜奶订购数量
     */
    @ApiModelProperty("订购数量")
    @ExcelProperty(value = "订购数量")
    private Integer orderQuantity;

    /**
     * 鲜奶赠送数量
     */
    @ApiModelProperty("鲜奶赠送数量")
    @ExcelProperty(value = "鲜奶赠送数量")
    private Integer freshMilkGiveQuantity;

    /**
     * 常温奶赠送数量
     */
    @ApiModelProperty("常温奶赠送数量")
    @ExcelProperty(value = "常温奶赠送数量")
    private Integer longMilkGiveQuantity;

    /**
     * 超送数量：根据订单套餐超送规则计算出来的超送数量;祥看原型
     */
    @ApiModelProperty("超送数量")
    @ExcelProperty(value = "超送数量")
    private Integer excessQuantity;

    /**
     * 鲜奶已送数量
     */
    @ApiModelProperty("鲜奶已送数量")
    @ExcelProperty(value = "鲜奶已送数量")
    private Integer freshMilkSentQuantity;

    /**
     * 转换数量，订单类型为“转单”的时候才会有值;即转换的鲜奶数量 也可以是常温奶数量
     */
    @ApiModelProperty("转换数量")
    @ExcelProperty(value = "转换数量")
    private Integer conversionQuantity;

    /**
     * 鲜奶退订数量
     */
    @ApiModelProperty(" 鲜奶退订信息")
    @ExcelProperty(value = " 鲜奶退订数量")
    private Integer freshMilkReturnQuantity;

    /**
     * 常温奶已送数量
     */
    @ApiModelProperty("常温奶已送数量")
    @ExcelIgnore
    private Integer longMilkSentQuantity;

    @ApiModelProperty(value = "剩余数量")
    @ExcelIgnore
    private Integer surplusQuantity;

    @ApiModelProperty("换单兑换数量")
    @ExcelProperty(value = "兑换数量")
    private Integer exchangeSum;

    @ApiModelProperty("换单鲜奶兑换商品数量")
    @ExcelProperty(value = "鲜奶兑换数量")
    private Integer milkExchangeSum;

    /**
     * 单价：鲜奶单价
     */
    @ApiModelProperty("单价：鲜奶单价")
    @ExcelProperty(value = "单价")
    private BigDecimal unitPrice;

    /**
     * 金额：该笔订单的总金额
     */
    @ApiModelProperty("金额：该笔订单的总金额")
    @ExcelProperty(value = "金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "商品名称")
    @ExcelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "商品分类名称")
    @ExcelProperty(value = "商品分类")
    private String productTypeName;

    @ApiModelProperty(value = "核算类型:1.正常核算 ，2. 抖音推广")
    @ExcelProperty(value = "核算类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=正常核算订单,2=抖音推广订单")
    private String accountingType;

    @ApiModelProperty(value = "有无渠道推广奖励：1.有，0.无")
    @ExcelProperty(value = "有无渠道推广奖励", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=无,1=有")
    private Integer promotionCommission;

    /**
     * 支付金额
     */
    @ApiModelProperty("支付金额")
    @ExcelProperty("支付金额")
    private BigDecimal payAmount;

    /**
     * 平台优惠金额
     */
    @ApiModelProperty("平台优惠金额")
    @ExcelProperty("平台优惠金额")
    private BigDecimal platformDiscount;

    /**
     * 刷卡金额
     */
    @ApiModelProperty("刷卡金额")
    @ExcelIgnore
    private BigDecimal creditCardAmount;

    /**
     * 订单日期
     */
    @ApiModelProperty("订单日期")
    @ExcelProperty(value = "订单日期")
    private Date orderDate;

    @ApiModelProperty("活动名称")
    @ExcelProperty(value = "活动名称")
    private String activityName;

    @ApiModelProperty("活动对象")
    @ExcelIgnore
    private String giveGiftList;

    @ApiModelProperty("活动对象")
    @ExcelProperty(value = "活动礼品")
    private String giveGiftNames;

    @ApiModelProperty("活动赠品数量")
    @ExcelIgnore
    private Integer activityGiveQuantity;

    @ApiModelProperty("套餐价格")
    @ExcelProperty(value = "套餐金额")
    private BigDecimal setMealPrice;

    @ApiModelProperty("分摊费用")
    @ExcelProperty(value = "活动分摊费用")
    private BigDecimal apportionMoney;


    @ApiModelProperty("remark")
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 来源 1 后端 2 配送端
     */
    @ApiModelProperty("源  1 后端 2 配送端")
    @ExcelProperty(value = "终端来源", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=后台录入,2=配送端录入,3=客户端录入,4=小程序,7=抖音")
    private Short terminalType;

    @ExcelIgnore
    private Long sysDeptId;

    /**
     * 更新者名称
     */
    @ApiModelProperty(value = "更新人名称")
    @ExcelProperty(value = "修改人")
    private String updateByName;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @ExcelProperty(value = "修改人时间")
    private Date updateTime;

    /**
     * 审核人
     */
    @ApiModelProperty("审核人")
    @ExcelIgnore
    private Long auditBy;

    /**
     * 审核人
     */
    @ApiModelProperty("审核人名字")
    @ExcelProperty(value = "审核人")
    private String auditByName;

    /**
     * 审核人
     */
    @ApiModelProperty("审核时间")
    @ExcelProperty(value = "审核时间")
    private Date auditTime;

    /**
     * 1 待审核 、 2 已审核 、 3.已拒绝
     */
    @ApiModelProperty("1 待审核 、 2 已审核 、 3.已拒绝")
    @ExcelProperty(value = "审核状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=待审核,2=已审核,3=已拒绝")
    private Integer auditStatus;

    /**
     * 是否促销单;true 是 false 否
     */
    @ApiModelProperty("是否促销单;true 是 false 否")
    @ExcelIgnore
    private Boolean promotionalOrderFlag = false;

    /**
     * 是否师徒单;true是 false 否
     */
    @ApiModelProperty("是否师徒单;true是 false 否")
    @ExcelIgnore
    private Boolean apprenticeOrderFlag = false;


    /**
     * 单据图片
     */
    @ApiModelProperty("单据图片")
    @TableField(typeHandler = JacksonTypeHandler.class)
    @ExcelIgnore
    private List<String> orderImages;

    /**
     * 支付截图
     */
    @ApiModelProperty("支付截图")
    @TableField(typeHandler = JacksonTypeHandler.class)
    @ExcelIgnore
    private List<String> playImages;

    /**
     * 客户信息
     */
    @ApiModelProperty(" 客户信息")
    @TableField(typeHandler = TypeHandlerConstant.CustomerInfoTypeHandler.class)
    @ExcelIgnore
    private List<CustomerInfo> customerInfoList;

    /**
     * 客户信息
     */
    @ExcelIgnore
    private String customerInfos;

    @ApiModelProperty("销售代理")
    @ExcelIgnore
    private String proxyName;

    @ApiModelProperty("业绩")
    @ExcelIgnore
    private String performanceMoney;

    @ApiModelProperty(notes = "合订单促销单是否可以编辑")
    @ExcelIgnore
    private boolean promotionalEditFlag = Boolean.FALSE;

    @ApiModelProperty(" 支付状态")
    @ExcelIgnore
    private Integer payStatus;

    // 完善订单入口
    @ApiModelProperty(name = "订单完善状态")
    @ExcelIgnore
    private Integer perfectStatus;

    /**
     * 合订单客户信息
     */
    @ApiModelProperty(" 合订单客户信息")
    @TableField(typeHandler = TypeHandlerConstant.ContractOrderCustomerInfoTypeHandler.class)
    @ExcelIgnore
    private List<ContractOrderCustomerInfo> contractOrderExt;

    /**
     * 支付时间
     */
    @ApiModelProperty("支付时间")
    @ExcelProperty(value = "支付时间")
    private Date payTime;

    @ApiModelProperty("合订单总金额")
    @ExcelIgnore
    private BigDecimal contractTotalAmount;

    @ApiModelProperty("合订单总订购数量")
    @ExcelIgnore
    private Integer contractTotalOrderQuantity;


    @ExcelIgnore
    private String showAuditMark;

    @ApiModelProperty("换单商品兑换id")
    @ExcelIgnore
    private Long exchangeProductId;

    @ApiModelProperty("换单兑换商品规格")
    @ExcelIgnore
    private String spec;


    @ApiModelProperty("换单商品名称")
    @ExcelIgnore
    private String exchangeProductName;

    @ApiModelProperty("是否导入订单")
    @ExcelIgnore
    private Boolean isImport;


    @ApiModelProperty("活动赠品已送数量")
    @ExcelIgnore
    private Integer activitySentQuantity;

    @ApiModelProperty("客户id")
    @ExcelIgnore
    private Long customerId;

    @ApiModelProperty("活动类型")
    @ExcelIgnore
    private String activityType;

    @ApiModelProperty("第三方订单号")
    @ExcelIgnore
    private String thirdOrderNo;

    @ApiModelProperty("是否新客户")
    @ExcelIgnore
    private String newCustomerFlag;

    @ApiModelProperty("常温奶转出订单id")
    @ExcelIgnore
    private String outLongMilkOrderNo;

    @ApiModelProperty("活动id")
    @ExcelIgnore
    private Long activityId;


    @ApiModelProperty("套餐数量")
    @ExcelIgnore
    private Integer setMealQty;

    /**
     * 订单赠送数是否参与增订单核算
     */
    @ExcelIgnore
    private Boolean pigcFlag;

    /**
     * audit_count
     */
    @ExcelIgnore
    private Integer auditCount;


    /**
     * 常温奶已送金额\\n\\n\\n销售代理自己输入
     */
    @ApiModelProperty(value = "常温奶已送金额\\n\\n\\n销售代理自己输入", notes = "", required = true)
    @ExcelIgnore
    private BigDecimal longMilkSentAmount;
    /**
     * 常温奶剩余数量;, 也是客户列表里的 总数量 （鲜奶订购数量 - 鲜奶已送数量）
     */
    @ApiModelProperty(value = "常温奶剩余数量", notes = ", 也是客户列表里的 总数量 （鲜奶订购数量 - 鲜奶已送数量）", required = true)
    @ExcelIgnore
    private Integer longMilkSurplusQuantity;
    /**
     * 客户实际退款的鲜奶盒数
     */
    @ApiModelProperty(value = "客户退款盒数 (客户实际退款的鲜奶盒数(排除了送的))", notes = "", required = true)
    @ExcelIgnore
    private Integer freshMilkRefundQuantity;
    /**
     * 客户实际退订的鲜奶盒数
     */
    @ApiModelProperty(value = "客户实际退订的鲜奶盒数", notes = "", required = true)
    @ExcelIgnore
    private Integer freshMilkCancelQuantity;

    @ApiModelProperty("常温奶剩余数量")
    @ExcelIgnore
    private Integer longMilkRestQuantity;

    @ApiModelProperty("鲜奶退订后剩余数量 (常温奶没有这个)")
    @ExcelIgnore
    private Integer freshMilkCancelRestQuantity;

    /**
     * 客户实际退款的鲜奶盒数
     */
    @ApiModelProperty(value = "客户实际退款的鲜奶盒数", notes = "", required = true)
    @ExcelIgnore
    private Integer longMilkCancelQuantity;
    /**
     * 客户退订后;账户中的剩余鲜奶数量
     */
    @ApiModelProperty(value = "客户退订后", notes = "账户中的剩余鲜奶数量", required = true)
    @ExcelIgnore
    private Integer freshMilkRestQuantity;
    /**
     * 实际退还给客户的金额
     */
    @ApiModelProperty(value = "实际退还给客户的金额", notes = "", required = true)
    @ExcelIgnore
    private BigDecimal refundAmount;
    /**
     * 补销售代理返利
     */
    @ApiModelProperty(value = "补销售代理返利", notes = "", required = true)
    @ExcelIgnore
    private BigDecimal rebates;


    /**
     * 开户银行
     */
    @ApiModelProperty(value = "开户银行", notes = "", required = true)
    @ExcelIgnore
    private String bankName;
    /**
     * 账户卡号
     */
    @ApiModelProperty(value = "账户卡号", notes = "", required = true)
    @ExcelIgnore
    private String bankAccountNumber;
    /**
     * 账户名
     */
    @ApiModelProperty(value = "账户名", notes = "", required = true)
    @ExcelIgnore
    private String bankAccountName;


    /**
     * 订购数量：鲜奶订购数量
     */
    @ApiModelProperty("订购数量")
    @ExcelIgnore
    private Integer returnOrderQuantity;

    /**
     * 鲜奶赠送数量
     */
    @ApiModelProperty("鲜奶赠送数量")
    @ExcelIgnore
    private Integer returnFreshMilkGiveQuantity;


    /**
     * 鲜奶已送数量
     */
    @ApiModelProperty("鲜奶已送数量")
    @ExcelIgnore
    private Integer returnFreshMilkSentQuantity;


    @ApiModelProperty(value = "剩余数量")
    @ExcelIgnore
    private Integer returnSurplusQuantity;

    /**
     * 合订单的订单类型标签
     */
    private Short contractTypeTag;

    @ApiModelProperty("录单人")
    @ExcelProperty(value = "录单人")
    private String createByName;

    @ApiModelProperty("录单人编号")
    @ExcelProperty(value = "录单人编号")
    private String createByCode;


    @ApiModelProperty(value = "配送站点")
    @ExcelProperty(value = "配送站点")
    private String deliverySite;

    @ApiModelProperty(value = "配送站点编号")
    @ExcelProperty(value = "配送站点编号")
    private String deliverySiteNo;


    @ApiModelProperty(value = "配送站点ID")
    @TableField(exist = false)
    private Long deliverySiteId;


    @ApiModelProperty(value = "打款状态:0.未打款、1.退款中,2.已打款、3.打款失败")
    @ExcelProperty(value = "打款状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=未打款,1=退款中,2=已打款,3=打款失败")
    private Integer paymentStatus;

    @ApiModelProperty(value = "打款失败原因")
    @ExcelProperty(value = "打款失败原因")
    private String paymentFailureReasons;

    @ApiModelProperty(value = "异常订单状态  1：异常")
    private Integer abnormalTag;

    private String deliverySites;

    private String refundNoAuditReasons;
}

