package com.ruoyi.job.order.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.business.base.api.domain.CxrEmployee;
import com.ruoyi.business.base.api.domain.CxrEmployeeAchievementDetail;
import com.ruoyi.business.base.api.domain.CxrRegion;
import com.ruoyi.business.base.api.domain.CxrSite;
import com.ruoyi.calculate.api.domain.enums.RegionStatisticsDimensionType;
import com.ruoyi.common.core.enums.DeleteStatus;
import com.ruoyi.job.common.domain.CxrRegionStaitemDailyReport;
import com.ruoyi.job.common.mapper.CxrEmployeeAchievementDetailMapper;
import com.ruoyi.job.common.mapper.CxrEmployeeMapper;
import com.ruoyi.job.common.mapper.CxrRegionMapper;
import com.ruoyi.job.common.mapper.CxrRegionStaitemDailyReportMapper;
import com.ruoyi.job.common.mapper.CxrSiteMapper;
import com.ruoyi.job.common.mapper.SysDeptMapper;
import com.ruoyi.job.common.service.CxrRegionStaitemDailyReportService;
import com.ruoyi.system.api.domain.SysDept;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 销售代理报表计算Service
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Service
@Slf4j
public class RegionStatisticsCalculateService {

    @Autowired
    private CxrRegionStaitemDailyReportService reportService;

    @Autowired
    private CxrRegionStaitemDailyReportMapper reportMapper;

    @Autowired
    private CxrEmployeeAchievementDetailMapper achievementDetailMapper;

    @Autowired
    private CxrEmployeeMapper employeeMapper;

    @Autowired
    private CxrSiteMapper siteMapper;

    @Autowired
    private CxrRegionMapper regionMapper;

    @Autowired
    private SysDeptMapper sysDeptMapper;

    // 公共变量：缓存基础数据
    private Map<Long, CxrSite> siteMap = new HashMap<>();
    private Map<Long, CxrRegion> regionMap = new HashMap<>();
    private Map<Long, SysDept> deptMap = new HashMap<>();  // 包含公司和大区数据

    /**
     * 可以指定日生成
     */
    @DSTransactional
    public void calculateDayData(LocalDate startDate,LocalDate endDate) {
        YearMonth month  = YearMonth.from(startDate);
        try {
            log.info("开始计算的报表数据，日期范围：{} 到 {}",startDate, endDate);

            // 1. 删除该月份的所有数据
            log.info("步骤1：删除月份 {} 的历史数据", month);
            deleteMonthData(startDate, endDate);

            // 预加载基础数据
            log.info("步骤2：预加载基础数据");
            preloadBaseData();

            // 2. 按维度顺序计算数据
            log.info("步骤3：开始计算各维度数据");

            log.info("步骤3.1：计算代理维度数据");
            calculateAgentDimension(startDate, endDate);

            log.info("步骤3.2：计算站点维度数据");
            calculateSiteDimension(startDate, endDate);

            log.info("步骤3.3：计算区域维度数据");
            calculateRegionDimension(startDate, endDate);

            log.info("步骤3.4：计算大区维度数据");
            calculateRootRegionDimension(startDate, endDate);

            log.info("步骤3.5：计算公司维度数据");
            calculateCompanyDimension(startDate, endDate);

            // 3. 计算同环比
            log.info("步骤4：计算同环比数据");
            calculateYoyAndMomComparison(startDate, endDate);

            log.info("月份 {} 的报表数据计算完成，日期范围：{} 到 {}", month, startDate, endDate);
        } catch (Exception e) {
            log.error("计算月份 {} 的报表数据失败，日期范围：{} 到 {}，错误：{}",
                month, startDate, endDate, e.getMessage(), e);
        } finally {
            // 清理缓存数据
            log.info("清理缓存数据");
            clearCacheData();
        }
    }

    /**
     * 在事务中计算月份数据
     * @param month 目标月份
     */
    @DSTransactional
    public void calculateMonthDataWithTransaction(YearMonth month) {
        log.info("开始计算月份 {} 的报表数据", month);

        LocalDate startDate = month.atDay(1);
        LocalDate endDate = month.atEndOfMonth();
        if (endDate.isAfter(LocalDate.now().minusDays(1))) {
            endDate = LocalDate.now().minusDays(1);
        }
        calculateDayData(startDate, endDate);
    }

    /**
     * 删除指定月份的所有报表数据
     * @param month 目标月份
     */
    private void deleteMonthData(LocalDate startDate, LocalDate endDate) {

        LambdaQueryWrapper<CxrRegionStaitemDailyReport> deleteWrapper =
            new LambdaQueryWrapper<CxrRegionStaitemDailyReport>()
                .ge(CxrRegionStaitemDailyReport::getReportDate, startDate)
                .le(CxrRegionStaitemDailyReport::getReportDate, endDate);

        int deletedCount = reportMapper.delete(deleteWrapper);
        log.info("删除日期范围 {} 到 {} 的历史数据，共 {} 条", startDate, endDate, deletedCount);
    }

    /**
     * 预加载基础数据到内存Map中，避免循环查询数据库
     */
    private void preloadBaseData() {
        log.info("开始预加载基础数据");

        try {
            // 1. 加载所有站点数据
            List<CxrSite> sites = siteMapper.selectList(
                new LambdaQueryWrapper<CxrSite>()
                    .eq(CxrSite::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            );
            siteMap = sites.stream().collect(Collectors.toMap(CxrSite::getId, site -> site));
            log.info("加载站点数据完成，共 {} 条", siteMap.size());

            // 2. 加载所有区域数据
            List<CxrRegion> regions = regionMapper.selectList(
                new LambdaQueryWrapper<CxrRegion>()
                    .eq(CxrRegion::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            );
            regionMap = regions.stream().collect(Collectors.toMap(CxrRegion::getId, region -> region));
            log.info("加载区域数据完成，共 {} 条", regionMap.size());

            // 3. 加载所有部门数据（包含公司和大区）
            List<SysDept> depts = sysDeptMapper.selectList(
                new LambdaQueryWrapper<SysDept>()
                    .eq(SysDept::getDelFlag, DeleteStatus.NOT_DELETED.getValue())
            );
            deptMap = depts.stream().collect(Collectors.toMap(SysDept::getDeptId, dept -> dept));
            log.info("加载部门数据完成，共 {} 条", deptMap.size());


            log.info("基础数据预加载完成");
        } catch (Exception e) {
            log.error("预加载基础数据失败", e);
            throw e;
        }
    }

    /**
     * 清理缓存数据
     */
    private void clearCacheData() {
        siteMap.clear();
        regionMap.clear();
        deptMap.clear();
        log.info("缓存数据已清理");
    }

    /**
     * 获取星期英文名称
     * @param date 日期
     * @return 星期英文名称
     */
    private String getWeekDay(LocalDate date) {
        DayOfWeek dayOfWeek = date.getDayOfWeek();
        return dayOfWeek.name(); // 返回英文名称：MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY, SUNDAY
    }

    /**
     * 计算销售代理维度数据
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    private void calculateAgentDimension(LocalDate startDate, LocalDate endDate) {
        log.info("开始计算代理维度数据：{} 到 {}", startDate, endDate);

        // 按天循环计算
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            log.info("开始处理日期：{} 的代理维度数据", date);

            List<CxrRegionStaitemDailyReport> agentReports = buildAgentReports(date);

            if (CollectionUtil.isNotEmpty(agentReports)) {
                reportService.saveBatch(agentReports);
                log.info("保存日期 {} 的代理维度数据，共 {} 条", date, agentReports.size());
            } else {
                log.info("日期 {} 没有代理维度数据", date);
            }
        }

        log.info("代理维度数据计算完成：{} 到 {}", startDate, endDate);
    }

    /**
     * 构建代理维度报表数据
     * @param date 统计日期
     * @return 代理维度报表数据列表
     */
    private List<CxrRegionStaitemDailyReport> buildAgentReports(LocalDate date) {
        log.info("开始构建日期 {} 的代理报表数据", date);

        // 直接按销售代理聚合查询，减少内存占用
        List<CxrRegionStaitemDailyReport> aggregatedReports =
            achievementDetailMapper.selectAggregatedByEmployee(date);

        if (CollectionUtil.isEmpty(aggregatedReports)) {
            log.info("日期 {} 没有业绩数据", date);
            return new ArrayList<>();
        }

        log.info("日期 {} 查询到汇总后的代理业绩数据 {} 条", date, aggregatedReports.size());
        List<Long> employeeIds = aggregatedReports.stream().map(CxrRegionStaitemDailyReport::getAgentId).collect(Collectors.toList());
        List<CxrEmployee> employees = employeeMapper.selectBatchIds(employeeIds);
        Map<Long, CxrEmployee> dayEmployeeMap = employees.stream().collect(Collectors.toMap(CxrEmployee::getId, employee -> employee));
        List<Long> siteIds = aggregatedReports.stream().map(CxrRegionStaitemDailyReport::getSiteId).collect(Collectors.toList());
        List<CxrSite> sites = siteMapper.selectBatchIds(siteIds);
        Map<Long, CxrSite> daySiteMap = sites.stream().collect(Collectors.toMap(CxrSite::getId, site -> site));

        for (int i = aggregatedReports.size() - 1; i >= 0; i--) {
            CxrRegionStaitemDailyReport report = aggregatedReports.get(i);
            // 取第一条记录获取员工ID和站点ID
            Long employeeId = report.getAgentId();
            Long siteId = report.getSiteId();
            String employeeName = report.getAgentName(); // 使用业绩产生时的名字

            // 从批量查询结果中获取员工信息
            CxrEmployee employee =  ObjectUtil.defaultIfNull( dayEmployeeMap.get(employeeId), new CxrEmployee());

            // 从批量查询结果中获取站点信息
            CxrSite site = ObjectUtil.defaultIfNull(daySiteMap.get(siteId), new CxrSite());

            // 从缓存中获取区域信息
            CxrRegion region = ObjectUtil.defaultIfNull( regionMap.get(site.getCxrRootRegionId()),new CxrRegion());

            // 从缓存中获取大区信息
            SysDept regionDept = ObjectUtil.defaultIfNull(deptMap.get(region.getId()), new SysDept());
            SysDept rootRegionDept = ObjectUtil.defaultIfNull(deptMap.get(regionDept.getParentId()), new SysDept());

            // 从缓存中获取公司信息
            SysDept company = ObjectUtil.defaultIfNull(deptMap.get(site.getCurrentDeptId()), new SysDept());

            // 基础信息
            report.setReportDate(java.sql.Date.valueOf(date));
            report.setWeekDay(getWeekDay(date));
            report.setDimensionType(RegionStatisticsDimensionType.AGENT.getCode());

            // 设置公司信息
            report.setCompanyId(site.getCurrentDeptId());
            report.setCompanyName(company != null ? company.getDeptName() : null);

            // 设置大区信息
            report.setRootRegionId(rootRegionDept.getDeptId());
            report.setRootRegionName(rootRegionDept.getDeptName());

            // 设置区域信息
            report.setRegionId(region != null ? region.getId() : null);
            report.setRegionName(region != null ? region.getName() : null);
            report.setRegionCode(region != null ? region.getRegionMark() : null);

            // 设置站点信息
            report.setSiteId(site.getId());
            report.setSiteName(site.getName());
            report.setSiteCode(site.getSiteMark());

            // 设置代理信息
            report.setAgentId(employeeId);
            report.setAgentName(employeeName); // 使用业绩产生时的名字
            report.setAgentCode(employee.getJobNumber());
            report.setAgentLevel(employee.getEmployeeLevelType()); //

            // 重新计算占比和客单价
            calculateRatiosAndUnitPrices(report);

            // 设置系统字段
            report.setCreateTime(new Date());
            report.setUpdateTime(new Date());
            report.setDeleteStatus(DeleteStatus.NOT_DELETED.getValue());
        }
        return aggregatedReports;
    }



    /**
     * 计算占比和客单价
     * @param report 报表对象
     */
    private void calculateRatiosAndUnitPrices(CxrRegionStaitemDailyReport report) {
        BigDecimal totalCount = report.getTotalOrderCount();

        // 计算占比（保留两位小数，去尾法）
        if (totalCount.compareTo(BigDecimal.ZERO) > 0) {
            report.setNewOrderRatio(report.getNewOrderCount().divide(totalCount, 4, RoundingMode.DOWN).multiply(new BigDecimal("100")).setScale(2, RoundingMode.DOWN));
            report.setContinueOrderRatio(report.getContinueOrderCount().divide(totalCount, 4, RoundingMode.DOWN).multiply(new BigDecimal("100")).setScale(2, RoundingMode.DOWN));
            report.setIncreaseOrderRatio(report.getIncreaseOrderCount().divide(totalCount, 4, RoundingMode.DOWN).multiply(new BigDecimal("100")).setScale(2, RoundingMode.DOWN));
            report.setReturnOrderRatio(report.getReturnOrderCount().divide(totalCount, 4, RoundingMode.DOWN).multiply(new BigDecimal("100")).setScale(2, RoundingMode.DOWN));
        } else {
            report.setNewOrderRatio(BigDecimal.ZERO);
            report.setContinueOrderRatio(BigDecimal.ZERO);
            report.setIncreaseOrderRatio(BigDecimal.ZERO);
            report.setReturnOrderRatio(BigDecimal.ZERO);
        }

        // 计算客单价（保留两位小数，去尾法）
        if (report.getNewOrderCount().compareTo(BigDecimal.ZERO) > 0) {
            report.setNewOrderUnitPrice(report.getNewOrderAchievement().divide(report.getNewOrderCount(), 2, RoundingMode.DOWN));
        } else {
            report.setNewOrderUnitPrice(BigDecimal.ZERO);
        }

        if (report.getContinueOrderCount().compareTo(BigDecimal.ZERO) > 0) {
            report.setContinueOrderUnitPrice(report.getContinueOrderAchievement().divide(report.getContinueOrderCount(), 2, RoundingMode.DOWN));
        } else {
            report.setContinueOrderUnitPrice(BigDecimal.ZERO);
        }

        if (report.getIncreaseOrderCount().compareTo(BigDecimal.ZERO) > 0) {
            report.setIncreaseOrderUnitPrice(report.getIncreaseOrderAchievement().divide(report.getIncreaseOrderCount(), 2, RoundingMode.DOWN));
        } else {
            report.setIncreaseOrderUnitPrice(BigDecimal.ZERO);
        }

        if (totalCount.compareTo(BigDecimal.ZERO) > 0) {
            report.setAvgUnitPrice(report.getTotalAchievement().divide(totalCount, 2, RoundingMode.DOWN));
        } else {
            report.setAvgUnitPrice(BigDecimal.ZERO);
        }
    }

    /**
     * 计算站点维度数据（从代理维度聚合）
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    private void calculateSiteDimension(LocalDate startDate, LocalDate endDate) {
        log.info("开始计算站点维度数据：{} 到 {}", startDate, endDate);

        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            // 查询当日的代理维度数据
            List<CxrRegionStaitemDailyReport> agentReports =
                reportService.list(
                    new LambdaQueryWrapper<CxrRegionStaitemDailyReport>()
                        .eq(CxrRegionStaitemDailyReport::getReportDate, date)
                        .eq(CxrRegionStaitemDailyReport::getDimensionType, RegionStatisticsDimensionType.AGENT.getCode())
                );

            if (CollectionUtil.isEmpty(agentReports)) {
                continue;
            }

            // 按站点维度组合键分组聚合：(companyId, rootRegionId, regionId, siteId)
            Map<SiteDimensionKey, List<CxrRegionStaitemDailyReport>> siteGroupMap =
                agentReports.stream()
                    .collect(Collectors.groupingBy(report -> new SiteDimensionKey(
                        report.getCompanyId(),
                        report.getRootRegionId(),
                        report.getRegionId(),
                        report.getSiteId()
                    )));

            List<CxrRegionStaitemDailyReport> siteReports = new ArrayList<>();

            for (Map.Entry<SiteDimensionKey, List<CxrRegionStaitemDailyReport>> entry : siteGroupMap.entrySet()) {
                SiteDimensionKey siteKey = entry.getKey();
                List<CxrRegionStaitemDailyReport> siteAgentReports = entry.getValue();

                CxrRegionStaitemDailyReport siteReport = aggregateToSiteReport(date, siteKey, siteAgentReports);
                if (siteReport != null) {
                    siteReports.add(siteReport);
                }
            }

            if (CollectionUtil.isNotEmpty(siteReports)) {
                reportService.saveBatch(siteReports);
                log.info("保存日期 {} 的站点维度数据，共 {} 条", date, siteReports.size());
            }
        }
    }

    /**
     * 聚合到站点维度报表
     * @param date 统计日期
     * @param siteKey 站点维度组合键
     * @param agentReports 代理报表列表
     * @return 站点维度报表
     */
    private CxrRegionStaitemDailyReport aggregateToSiteReport(LocalDate date, SiteDimensionKey siteKey,
                                                             List<CxrRegionStaitemDailyReport> agentReports) {
        if (CollectionUtil.isEmpty(agentReports)) {
            return null;
        }

        // 取第一条记录的基础信息
        CxrRegionStaitemDailyReport firstReport = agentReports.get(0);

        CxrRegionStaitemDailyReport siteReport = new CxrRegionStaitemDailyReport();

        // 基础信息
        siteReport.setReportDate(java.sql.Date.valueOf(date));
        siteReport.setWeekDay(getWeekDay(date));
        siteReport.setDimensionType(RegionStatisticsDimensionType.SITE.getCode());
        siteReport.setCompanyId(siteKey.getCompanyId());
        siteReport.setCompanyName(firstReport.getCompanyName());
        siteReport.setRootRegionId(siteKey.getRootRegionId());
        siteReport.setRootRegionName(firstReport.getRootRegionName());
        siteReport.setRegionId(siteKey.getRegionId());
        siteReport.setRegionName(firstReport.getRegionName());
        siteReport.setRegionCode(firstReport.getRegionCode());
        siteReport.setSiteId(siteKey.getSiteId());
        siteReport.setSiteName(firstReport.getSiteName());
        siteReport.setSiteCode(firstReport.getSiteCode());
        // 站点维度不设置代理信息
        siteReport.setAgentId(null);
        siteReport.setAgentName(null);
        siteReport.setAgentCode(null);

        // 聚合数据
        aggregateReportsData(siteReport, agentReports);

        // 重新计算占比和客单价
        calculateRatiosAndUnitPrices(siteReport);

        // 设置系统字段
        siteReport.setCreateTime(new Date());
        siteReport.setUpdateTime(new Date());
        siteReport.setDeleteStatus(DeleteStatus.NOT_DELETED.getValue());

        return siteReport;
    }

    /**
     * 聚合多个报表数据
     * @param targetReport 目标报表
     * @param sourceReports 源报表列表
     */
    private void aggregateReportsData(CxrRegionStaitemDailyReport targetReport,
                                     List<CxrRegionStaitemDailyReport> sourceReports) {
        BigDecimal newOrderCount = BigDecimal.ZERO;
        BigDecimal continueOrderCount = BigDecimal.ZERO;
        BigDecimal increaseOrderCount = BigDecimal.ZERO;
        BigDecimal returnOrderCount = BigDecimal.ZERO;

        BigDecimal newOrderAchievement = BigDecimal.ZERO;
        BigDecimal continueOrderAchievement = BigDecimal.ZERO;
        BigDecimal increaseOrderAchievement = BigDecimal.ZERO;
        BigDecimal returnOrderAchievement = BigDecimal.ZERO;

        for (CxrRegionStaitemDailyReport report : sourceReports) {
            newOrderCount = newOrderCount.add(report.getNewOrderCount() != null ? report.getNewOrderCount() : BigDecimal.ZERO);
            continueOrderCount = continueOrderCount.add(report.getContinueOrderCount() != null ? report.getContinueOrderCount() : BigDecimal.ZERO);
            increaseOrderCount = increaseOrderCount.add(report.getIncreaseOrderCount() != null ? report.getIncreaseOrderCount() : BigDecimal.ZERO);
            returnOrderCount = returnOrderCount.add(report.getReturnOrderCount() != null ? report.getReturnOrderCount() : BigDecimal.ZERO);

            newOrderAchievement = newOrderAchievement.add(report.getNewOrderAchievement() != null ? report.getNewOrderAchievement() : BigDecimal.ZERO);
            continueOrderAchievement = continueOrderAchievement.add(report.getContinueOrderAchievement() != null ? report.getContinueOrderAchievement() : BigDecimal.ZERO);
            increaseOrderAchievement = increaseOrderAchievement.add(report.getIncreaseOrderAchievement() != null ? report.getIncreaseOrderAchievement() : BigDecimal.ZERO);
            returnOrderAchievement = returnOrderAchievement.add(report.getReturnOrderAchievement() != null ? report.getReturnOrderAchievement() : BigDecimal.ZERO);
        }

        // 设置订单数量
        targetReport.setNewOrderCount(newOrderCount);
        targetReport.setContinueOrderCount(continueOrderCount);
        targetReport.setIncreaseOrderCount(increaseOrderCount);
        targetReport.setReturnOrderCount(returnOrderCount);
        targetReport.setTotalOrderCount(newOrderCount.add(continueOrderCount).add(increaseOrderCount).subtract(returnOrderCount));

        // 设置业绩金额
        targetReport.setNewOrderAchievement(newOrderAchievement);
        targetReport.setContinueOrderAchievement(continueOrderAchievement);
        targetReport.setIncreaseOrderAchievement(increaseOrderAchievement);
        targetReport.setReturnOrderAchievement(returnOrderAchievement);
        targetReport.setTotalAchievement(newOrderAchievement.add(continueOrderAchievement).add(increaseOrderAchievement).add(returnOrderAchievement));
    }

    /**
     * 计算区域维度数据（从站点维度聚合）
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    private void calculateRegionDimension(LocalDate startDate, LocalDate endDate) {
        log.info("开始计算区域维度数据：{} 到 {}", startDate, endDate);

        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            // 查询当日的站点维度数据
            List<CxrRegionStaitemDailyReport> siteReports =
                reportService.list(
                    new LambdaQueryWrapper<CxrRegionStaitemDailyReport>()
                        .eq(CxrRegionStaitemDailyReport::getReportDate, date)
                        .eq(CxrRegionStaitemDailyReport::getDimensionType, RegionStatisticsDimensionType.SITE.getCode())
                );

            if (CollectionUtil.isEmpty(siteReports)) {
                continue;
            }

            // 按区域维度组合键分组聚合：(companyId, rootRegionId, regionId)
            Map<RegionDimensionKey, List<CxrRegionStaitemDailyReport>> regionGroupMap =
                siteReports.stream()
                    .collect(Collectors.groupingBy(report -> new RegionDimensionKey(
                        report.getCompanyId(),
                        report.getRootRegionId(),
                        report.getRegionId()
                    )));

            List<CxrRegionStaitemDailyReport> regionReports = new ArrayList<>();

            for (Map.Entry<RegionDimensionKey, List<CxrRegionStaitemDailyReport>> entry : regionGroupMap.entrySet()) {
                RegionDimensionKey regionKey = entry.getKey();
                List<CxrRegionStaitemDailyReport> regionSiteReports = entry.getValue();

                CxrRegionStaitemDailyReport regionReport = aggregateToRegionReport(date, regionKey, regionSiteReports);
                if (regionReport != null) {
                    regionReports.add(regionReport);
                }
            }

            if (CollectionUtil.isNotEmpty(regionReports)) {
                reportService.saveBatch(regionReports);
                log.info("保存日期 {} 的区域维度数据，共 {} 条", date, regionReports.size());
            }
        }
    }

    /**
     * 聚合到区域维度报表
     * @param date 统计日期
     * @param regionKey 区域维度组合键
     * @param siteReports 站点报表列表
     * @return 区域维度报表
     */
    private CxrRegionStaitemDailyReport aggregateToRegionReport(LocalDate date, RegionDimensionKey regionKey,
                                                               List<CxrRegionStaitemDailyReport> siteReports) {
        if (CollectionUtil.isEmpty(siteReports)) {
            return null;
        }

        // 取第一条记录的基础信息
        CxrRegionStaitemDailyReport firstReport = siteReports.get(0);

        CxrRegionStaitemDailyReport regionReport = new CxrRegionStaitemDailyReport();

        // 基础信息
        regionReport.setReportDate(java.sql.Date.valueOf(date));
        regionReport.setWeekDay(getWeekDay(date));
        regionReport.setDimensionType(RegionStatisticsDimensionType.REGION.getCode());
        regionReport.setCompanyId(regionKey.getCompanyId());
        regionReport.setCompanyName(firstReport.getCompanyName());
        regionReport.setRootRegionId(regionKey.getRootRegionId());
        regionReport.setRootRegionName(firstReport.getRootRegionName());
        regionReport.setRegionId(regionKey.getRegionId());
        regionReport.setRegionName(firstReport.getRegionName());
        regionReport.setRegionCode(firstReport.getRegionCode());
        // 区域维度不设置站点和代理信息
        regionReport.setSiteId(null);
        regionReport.setSiteName(null);
        regionReport.setSiteCode(null);
        regionReport.setAgentId(null);
        regionReport.setAgentName(null);
        regionReport.setAgentCode(null);

        // 聚合数据
        aggregateReportsData(regionReport, siteReports);

        // 重新计算占比和客单价
        calculateRatiosAndUnitPrices(regionReport);

        // 设置系统字段
        regionReport.setCreateTime(new Date());
        regionReport.setUpdateTime(new Date());
        regionReport.setDeleteStatus(DeleteStatus.NOT_DELETED.getValue());

        return regionReport;
    }

    /**
     * 计算大区维度数据（从区域维度聚合）
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    private void calculateRootRegionDimension(LocalDate startDate, LocalDate endDate) {
        log.info("开始计算大区维度数据：{} 到 {}", startDate, endDate);

        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            // 查询当日的区域维度数据
            List<CxrRegionStaitemDailyReport> regionReports =
                reportService.list(
                    new LambdaQueryWrapper<CxrRegionStaitemDailyReport>()
                        .eq(CxrRegionStaitemDailyReport::getReportDate, date)
                        .eq(CxrRegionStaitemDailyReport::getDimensionType, RegionStatisticsDimensionType.REGION.getCode())
                );

            if (CollectionUtil.isEmpty(regionReports)) {
                continue;
            }

            // 按大区维度组合键分组聚合：(companyId, rootRegionId)
            Map<RootRegionDimensionKey, List<CxrRegionStaitemDailyReport>> rootRegionGroupMap =
                regionReports.stream()
                    .collect(Collectors.groupingBy(report -> new RootRegionDimensionKey(
                        report.getCompanyId(),
                        report.getRootRegionId()
                    )));

            List<CxrRegionStaitemDailyReport> rootRegionReports = new ArrayList<>();

            for (Map.Entry<RootRegionDimensionKey, List<CxrRegionStaitemDailyReport>> entry : rootRegionGroupMap.entrySet()) {
                RootRegionDimensionKey rootRegionKey = entry.getKey();
                List<CxrRegionStaitemDailyReport> rootRegionRegionReports = entry.getValue();

                CxrRegionStaitemDailyReport rootRegionReport = aggregateToRootRegionReport(date, rootRegionKey, rootRegionRegionReports);
                if (rootRegionReport != null) {
                    rootRegionReports.add(rootRegionReport);
                }
            }

            if (CollectionUtil.isNotEmpty(rootRegionReports)) {
                reportService.saveBatch(rootRegionReports);
                log.info("保存日期 {} 的大区维度数据，共 {} 条", date, rootRegionReports.size());
            }
        }
    }

    /**
     * 聚合到大区维度报表
     * @param date 统计日期
     * @param rootRegionKey 大区维度组合键
     * @param regionReports 区域报表列表
     * @return 大区维度报表
     */
    private CxrRegionStaitemDailyReport aggregateToRootRegionReport(LocalDate date, RootRegionDimensionKey rootRegionKey,
                                                                   List<CxrRegionStaitemDailyReport> regionReports) {
        if (CollectionUtil.isEmpty(regionReports)) {
            return null;
        }

        // 取第一条记录的基础信息
        CxrRegionStaitemDailyReport firstReport = regionReports.get(0);

        CxrRegionStaitemDailyReport rootRegionReport = new CxrRegionStaitemDailyReport();

        // 基础信息
        rootRegionReport.setReportDate(java.sql.Date.valueOf(date));
        rootRegionReport.setWeekDay(getWeekDay(date));
        rootRegionReport.setDimensionType(RegionStatisticsDimensionType.ROOT_REGION.getCode());
        rootRegionReport.setCompanyId(rootRegionKey.getCompanyId());
        rootRegionReport.setCompanyName(firstReport.getCompanyName());
        rootRegionReport.setRootRegionId(rootRegionKey.getRootRegionId());
        rootRegionReport.setRootRegionName(firstReport.getRootRegionName());
        // 大区维度不设置区域、站点和代理信息
        rootRegionReport.setRegionId(null);
        rootRegionReport.setRegionName(null);
        rootRegionReport.setRegionCode(null);
        rootRegionReport.setSiteId(null);
        rootRegionReport.setSiteName(null);
        rootRegionReport.setSiteCode(null);
        rootRegionReport.setAgentId(null);
        rootRegionReport.setAgentName(null);
        rootRegionReport.setAgentCode(null);

        // 聚合数据
        aggregateReportsData(rootRegionReport, regionReports);

        // 重新计算占比和客单价
        calculateRatiosAndUnitPrices(rootRegionReport);

        // 设置系统字段
        rootRegionReport.setCreateTime(new Date());
        rootRegionReport.setUpdateTime(new Date());
        rootRegionReport.setDeleteStatus(DeleteStatus.NOT_DELETED.getValue());

        return rootRegionReport;
    }

    /**
     * 计算公司维度数据（从大区维度聚合）
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    private void calculateCompanyDimension(LocalDate startDate, LocalDate endDate) {
        log.info("开始计算公司维度数据：{} 到 {}", startDate, endDate);

        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            // 查询当日的大区维度数据
            List<CxrRegionStaitemDailyReport> rootRegionReports =
                reportService.list(
                    new LambdaQueryWrapper<CxrRegionStaitemDailyReport>()
                        .eq(CxrRegionStaitemDailyReport::getReportDate, date)
                        .eq(CxrRegionStaitemDailyReport::getDimensionType, RegionStatisticsDimensionType.ROOT_REGION.getCode())
                );

            if (CollectionUtil.isEmpty(rootRegionReports)) {
                continue;
            }

            // 按公司分组聚合
            Map<Long, List<CxrRegionStaitemDailyReport>> companyGroupMap =
                rootRegionReports.stream()
                    .collect(Collectors.groupingBy(CxrRegionStaitemDailyReport::getCompanyId));

            List<CxrRegionStaitemDailyReport> companyReports = new ArrayList<>();

            for (Map.Entry<Long, List<CxrRegionStaitemDailyReport>> entry : companyGroupMap.entrySet()) {
                Long companyId = entry.getKey();
                List<CxrRegionStaitemDailyReport> companyRootRegionReports = entry.getValue();

                CxrRegionStaitemDailyReport companyReport = aggregateToCompanyReport(date, companyId, companyRootRegionReports);
                if (companyReport != null) {
                    companyReports.add(companyReport);
                }
            }

            if (CollectionUtil.isNotEmpty(companyReports)) {
                reportService.saveBatch(companyReports);
                log.info("保存日期 {} 的公司维度数据，共 {} 条", date, companyReports.size());
            }
        }
    }

    /**
     * 聚合到公司维度报表
     * @param date 统计日期
     * @param companyId 公司ID
     * @param rootRegionReports 大区报表列表
     * @return 公司维度报表
     */
    private CxrRegionStaitemDailyReport aggregateToCompanyReport(LocalDate date, Long companyId,
                                                                List<CxrRegionStaitemDailyReport> rootRegionReports) {
        if (CollectionUtil.isEmpty(rootRegionReports)) {
            return null;
        }

        // 取第一条记录的基础信息
        CxrRegionStaitemDailyReport firstReport = rootRegionReports.get(0);

        CxrRegionStaitemDailyReport companyReport = new CxrRegionStaitemDailyReport();

        // 基础信息
        companyReport.setReportDate(java.sql.Date.valueOf(date));
        companyReport.setWeekDay(getWeekDay(date));
        companyReport.setDimensionType(RegionStatisticsDimensionType.COMPANY.getCode());
        companyReport.setCompanyId(companyId);
        companyReport.setCompanyName(firstReport.getCompanyName());
        // 公司维度不设置大区、区域、站点和代理信息
        companyReport.setRootRegionId(null);
        companyReport.setRootRegionName(null);
        companyReport.setRegionId(null);
        companyReport.setRegionName(null);
        companyReport.setRegionCode(null);
        companyReport.setSiteId(null);
        companyReport.setSiteName(null);
        companyReport.setSiteCode(null);
        companyReport.setAgentId(null);
        companyReport.setAgentName(null);
        companyReport.setAgentCode(null);

        // 聚合数据
        aggregateReportsData(companyReport, rootRegionReports);

        // 重新计算占比和客单价
        calculateRatiosAndUnitPrices(companyReport);

        // 设置系统字段
        companyReport.setCreateTime(new Date());
        companyReport.setUpdateTime(new Date());
        companyReport.setDeleteStatus(DeleteStatus.NOT_DELETED.getValue());

        return companyReport;
    }

    /**
     * 计算同环比数据（优化版本）
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    private void calculateYoyAndMomComparison(LocalDate startDate, LocalDate endDate) {
        log.info("开始计算日期范围 {} 到 {} 的同环比数据", startDate, endDate);

        // 第一步：预检查并批量补数据
        batchGenerateMissingHistoricalData(startDate, endDate);

        // 第二步：一次性查询所有相关数据到缓存
        Map<String, CxrRegionStaitemDailyReport> reportMap = loadAllReportsToCache(startDate, endDate);

        // 第三步：批量计算同环比
        batchCalculateComparisons(startDate, endDate, reportMap);

        log.info("日期范围 {} 到 {} 的同环比数据计算完成", startDate, endDate);
    }

    /**
     * 计算去年同期日期，处理闰年问题
     *
     * @param currentDate 当前日期
     * @return 去年同期日期，如果当前是2月29日且去年不是闰年则返回null，否则返回去年同期日期
     */
    private static LocalDate calculateLastYearDate(LocalDate currentDate) {
        // 检查当前日期是否是2月29日（闰年特有的日期）
        if (currentDate.getMonthValue() == 2 && currentDate.getDayOfMonth() == 29) {
            // 当前是2月29日，检查去年是否是闰年
            int lastYear = currentDate.getYear() - 1;
            if (!cn.hutool.core.date.DateUtil.isLeapYear(lastYear)) {
                // 去年不是闰年，没有2月29日，返回null
                log.warn("当前日期{}是2月29日，但去年{}不是闰年，无对应日期", currentDate, lastYear);
                throw new RuntimeException(StrUtil.format("当前日期{}是2月29日，但去年{}不是闰年，无对应日期", currentDate, lastYear));
            }
        }
        // 其他情况正常减一年
        return currentDate.minusYears(1);
    }

    /**
     * 计算同比/环比增长率
     * @param currentValue 当前值
     * @param lastValue 对比值
     * @return 增长率（百分比，保留两位小数，去尾法）
     */
    private BigDecimal calculateYoyRate(BigDecimal currentValue, BigDecimal lastValue) {
        if (lastValue == null || lastValue.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        if (currentValue == null) {
            currentValue = BigDecimal.ZERO;
        }

        // 计算增长率：(当前值 - 对比值) / 对比值 * 100
        BigDecimal rate = currentValue.subtract(lastValue)
            .divide(lastValue.abs(), 4, RoundingMode.DOWN)
            .multiply(new BigDecimal("100"))
            .setScale(2, RoundingMode.DOWN);

        return rate;
    }

    /**
     * 设置同比字段为0
     * @param report 报表对象
     */
    private void setZeroYoyValues(CxrRegionStaitemDailyReport report) {
        // 订单数量同比
        report.setNewOrderCountYoy(BigDecimal.ZERO);
        report.setContinueOrderCountYoy(BigDecimal.ZERO);
        report.setIncreaseOrderCountYoy(BigDecimal.ZERO);
        report.setReturnOrderCountYoy(BigDecimal.ZERO);
        report.setTotalOrderCountYoy(BigDecimal.ZERO);

        // 业绩金额同比
        report.setNewOrderAchievementYoy(BigDecimal.ZERO);
        report.setContinueOrderAchievementYoy(BigDecimal.ZERO);
        report.setIncreaseOrderAchievementYoy(BigDecimal.ZERO);
        report.setReturnOrderAchievementYoy(BigDecimal.ZERO);
        report.setTotalAchievementYoy(BigDecimal.ZERO);
    }

    /**
     * 设置环比字段为0
     * @param report 报表对象
     */
    private void setZeroMomValues(CxrRegionStaitemDailyReport report) {
        // 订单数量环比
        report.setNewOrderCountMom(BigDecimal.ZERO);
        report.setContinueOrderCountMom(BigDecimal.ZERO);
        report.setIncreaseOrderCountMom(BigDecimal.ZERO);
        report.setReturnOrderCountMom(BigDecimal.ZERO);
        report.setTotalOrderCountMom(BigDecimal.ZERO);

        // 业绩金额环比
        report.setNewOrderAchievementMom(BigDecimal.ZERO);
        report.setContinueOrderAchievementMom(BigDecimal.ZERO);
        report.setIncreaseOrderAchievementMom(BigDecimal.ZERO);
        report.setReturnOrderAchievementMom(BigDecimal.ZERO);
        report.setTotalAchievementMom(BigDecimal.ZERO);
    }

    /**
     * 批量检查指定日期范围是否有业绩数据
     * @param dates 目标日期集合
     * @return 有业绩数据的日期集合
     */
    private Set<LocalDate> batchCheckAchievementDataExists(Set<LocalDate> dates) {
        if (dates.isEmpty()) {
            return new HashSet<>();
        }

        log.info("批量检查日期范围是否有业绩数据，日期数量：{}", dates.size());

        // 一次性查询所有日期的业绩数据
        List<LocalDate> queryDates = new ArrayList<>(dates);
        List<CxrEmployeeAchievementDetail> achievements = achievementDetailMapper.selectList(
            new LambdaQueryWrapper<CxrEmployeeAchievementDetail>()
                .select(CxrEmployeeAchievementDetail::getOrderDate)
                .in(CxrEmployeeAchievementDetail::getOrderDate, queryDates)
                .eq(CxrEmployeeAchievementDetail::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .groupBy(CxrEmployeeAchievementDetail::getOrderDate)
        );

        // 提取有数据的日期
        Set<LocalDate> datesWithData = achievements.stream()
            .map(CxrEmployeeAchievementDetail::getOrderDate)
            .collect(Collectors.toSet());

        log.info("批量检查完成，输入日期数量：{}，有业绩数据的日期数量：{}", dates.size(), datesWithData.size());

        return datesWithData;
    }

    /**
     * 生成历史数据（不计算同环比）
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    private void generateHistoricalData(LocalDate startDate, LocalDate endDate) {
        log.info("开始生成历史数据：{} 到 {}", startDate, endDate);

        try {
            // 删除该日期区间的所有数据（如果存在）
            reportMapper.delete(
                new LambdaQueryWrapper<CxrRegionStaitemDailyReport>()
                    .ge(CxrRegionStaitemDailyReport::getReportDate, startDate)
                    .le(CxrRegionStaitemDailyReport::getReportDate, endDate)
            );

            // 按维度顺序计算数据（不计算同环比）
            calculateAgentDimension(startDate, endDate);
            calculateSiteDimension(startDate, endDate);
            calculateRegionDimension(startDate, endDate);
            calculateRootRegionDimension(startDate, endDate);
            calculateCompanyDimension(startDate, endDate);

            log.info("历史数据生成完成：{} 到 {}", startDate, endDate);
        } catch (Exception e) {
            log.error("生成历史数据失败：{} 到 {}，错误：{}", startDate, endDate, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 预检查并批量补数据
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    private void batchGenerateMissingHistoricalData(LocalDate startDate, LocalDate endDate) {
        log.info("开始预检查历史数据完整性：{} 到 {}", startDate, endDate);

        // 计算需要检查的历史日期范围
        Set<LocalDate> yoyDates = new HashSet<>();  // 同比日期
        Set<LocalDate> momDates = new HashSet<>();  // 环比日期

        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            yoyDates.add(date.minusYears(1));
            momDates.add(date.minusWeeks(1));
        }

        // 检查同比数据完整性
        Set<LocalDate> missingYoyDates = checkMissingDates(yoyDates);
        if (!missingYoyDates.isEmpty()) {
            log.info("发现缺失的同比数据日期：{}，开始批量补数据", missingYoyDates);
            batchGenerateHistoricalData(missingYoyDates);
        }

        // 检查环比数据完整性
        Set<LocalDate> missingMomDates = checkMissingDates(momDates);
        if (!missingMomDates.isEmpty()) {
            log.info("发现缺失的环比数据日期：{}，开始批量补数据", missingMomDates);
            batchGenerateHistoricalData(missingMomDates);
        }

        log.info("历史数据完整性检查完成");
    }

    /**
     * 检查缺失的日期
     * @param targetDates 目标日期集合
     * @return 缺失的日期集合
     */
    private Set<LocalDate> checkMissingDates(Set<LocalDate> targetDates) {
        if (targetDates.isEmpty()) {
            return new HashSet<>();
        }

        // 查询已存在的日期
        List<Date> queryDates = targetDates.stream()
            .map(date -> Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant()))
            .collect(Collectors.toList());

        List<Date> existingDates = reportMapper.selectList(
            new LambdaQueryWrapper<CxrRegionStaitemDailyReport>()
                .select(CxrRegionStaitemDailyReport::getReportDate)
                .in(CxrRegionStaitemDailyReport::getReportDate, queryDates)
                .groupBy(CxrRegionStaitemDailyReport::getReportDate)
        ).stream().map(CxrRegionStaitemDailyReport::getReportDate).collect(Collectors.toList());

        Set<LocalDate> existingLocalDates = existingDates.stream()
            .map(date -> date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate())
            .collect(Collectors.toSet());

        // 找出缺失的日期
        Set<LocalDate> missingDates = new HashSet<>(targetDates);
        missingDates.removeAll(existingLocalDates);

        // 进一步检查：对于缺失的日期，批量检查是否有业绩数据支撑
        Set<LocalDate> datesWithAchievementData = batchCheckAchievementDataExists(missingDates);

        // 只返回既缺失报表数据又有业绩数据的日期
        Set<LocalDate> finalMissingDates = new HashSet<>(missingDates);
        finalMissingDates.retainAll(datesWithAchievementData);

        return finalMissingDates;
    }

    /**
     * 批量生成历史数据
     * @param dates 日期集合
     */
    private void batchGenerateHistoricalData(Set<LocalDate> dates) {
        if (dates.isEmpty()) {
            return;
        }

        // 找出日期范围
        LocalDate minDate = dates.stream().min(LocalDate::compareTo).get();
        LocalDate maxDate = dates.stream().max(LocalDate::compareTo).get();

        log.info("发现缺失的历史数据，日期范围：{} 到 {}，开始批量补数据", minDate, maxDate);

        // 一次性生成整个区间的数据
        generateHistoricalData(minDate, maxDate);
    }

    /**
     * 一次性加载所有相关数据到缓存
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 报表数据映射
     */
    private Map<String, CxrRegionStaitemDailyReport> loadAllReportsToCache(LocalDate startDate, LocalDate endDate) {
        log.info("开始加载所有相关报表数据到缓存");

        // 计算需要查询的所有日期
        Set<LocalDate> allDates = new HashSet<>();

        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            allDates.add(date);                    // 当前日期
            allDates.add(date.minusYears(1));      // 同比日期
            allDates.add(date.minusWeeks(1));      // 环比日期
        }

        // 一次性查询所有数据
        List<Date> queryDates = allDates.stream()
            .map(date -> Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant()))
            .collect(Collectors.toList());

        List<CxrRegionStaitemDailyReport> allReports = reportService.list(
            new LambdaQueryWrapper<CxrRegionStaitemDailyReport>()
                .in(CxrRegionStaitemDailyReport::getReportDate, queryDates)
        );

        log.info("成功加载 {} 条报表数据到缓存", allReports.size());

        // 构建索引映射
        return allReports.stream().collect(Collectors.toMap(
            this::buildReportKey,
            report -> report,
            (existing, replacement) -> existing
        ));
    }

    /**
     * 构建报表唯一键
     * @param report 报表数据
     * @return 唯一键
     */
    private String buildReportKey(CxrRegionStaitemDailyReport report) {
        LocalDate reportDate = report.getReportDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return String.format("%s_%s_%s_%s_%s_%s_%s",
            reportDate,
            report.getDimensionType(),
            report.getCompanyId(),
            Objects.toString(report.getRootRegionId(), "0"),
            Objects.toString(report.getRegionId(), "0"),
            Objects.toString(report.getSiteId(), "0"),
            Objects.toString(report.getAgentId(), "0")
        );
    }

    /**
     * 为指定日期构建报表键
     * @param report 报表数据
     * @param targetDate 目标日期
     * @return 唯一键
     */
    private String buildReportKeyForDate(CxrRegionStaitemDailyReport report, LocalDate targetDate) {
        return String.format("%s_%s_%s_%s_%s_%s_%s",
            targetDate,
            report.getDimensionType(),
            report.getCompanyId(),
            Objects.toString(report.getRootRegionId(), "0"),
            Objects.toString(report.getRegionId(), "0"),
            Objects.toString(report.getSiteId(), "0"),
            Objects.toString(report.getAgentId(), "0")
        );
    }

    /**
     * 批量计算同环比
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param reportMap 报表数据缓存
     */
    private void batchCalculateComparisons(LocalDate startDate, LocalDate endDate,
                                         Map<String, CxrRegionStaitemDailyReport> reportMap) {
        log.info("开始批量计算同环比数据");

        // 筛选当前时间段的数据
        List<CxrRegionStaitemDailyReport> currentReports = new ArrayList<>();
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            Date queryDate = Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant());

            // 从缓存中获取当前日期的所有数据
            List<CxrRegionStaitemDailyReport> dailyReports = reportMap.values().stream()
                .filter(report -> report.getReportDate().equals(queryDate))
                .collect(Collectors.toList());

            currentReports.addAll(dailyReports);
        }

        if (currentReports.isEmpty()) {
            log.info("当前时间段没有数据，跳过同环比计算");
            return;
        }

        log.info("找到当前时间段数据 {} 条，开始计算同环比", currentReports.size());

        // 分批处理
        int batchSize = 1000;
        List<List<CxrRegionStaitemDailyReport>> batches = new ArrayList<>();
        for (int i = 0; i < currentReports.size(); i += batchSize) {
            int end = Math.min(i + batchSize, currentReports.size());
            batches.add(currentReports.subList(i, end));
        }

        for (int i = 0; i < batches.size(); i++) {
            List<CxrRegionStaitemDailyReport> batch = batches.get(i);
            log.info("处理同环比计算批次 {}/{}，数据量：{}", i + 1, batches.size(), batch.size());

            for (CxrRegionStaitemDailyReport report : batch) {
                LocalDate currentDate = report.getReportDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

                // 计算同比
                LocalDate yoyDate = currentDate.minusYears(1);
                String yoyKey = buildReportKeyForDate(report, yoyDate);
                CxrRegionStaitemDailyReport yoyReport = reportMap.get(yoyKey);

                if (yoyReport != null) {
                    calculateYoyComparison(report, yoyReport);
                } else {
                    setZeroYoyValues(report);
                }

                // 计算环比
                LocalDate momDate = currentDate.minusWeeks(1);
                String momKey = buildReportKeyForDate(report, momDate);
                CxrRegionStaitemDailyReport momReport = reportMap.get(momKey);

                if (momReport != null) {
                    calculateMomComparison(report, momReport);
                } else {
                    setZeroMomValues(report);
                }
            }

            // 批量更新
            reportService.updateBatchById(batch);
            log.info("批次 {}/{} 更新完成", i + 1, batches.size());
        }

        log.info("同环比计算完成");
    }

    /**
     * 查询历史报表数据（通用方法）
     * @param currentReport 当前报表（用于构建查询条件）
     * @param targetDate 目标日期
     * @return 历史报表数据
     */
    private CxrRegionStaitemDailyReport queryHistoricalReport(CxrRegionStaitemDailyReport currentReport, LocalDate targetDate) {
        return reportService.getOne(
            new LambdaQueryWrapper<CxrRegionStaitemDailyReport>()
                .eq(CxrRegionStaitemDailyReport::getReportDate, targetDate)
                .eq(CxrRegionStaitemDailyReport::getDimensionType, currentReport.getDimensionType())
                .eq(CxrRegionStaitemDailyReport::getCompanyId, currentReport.getCompanyId())
                .eq(currentReport.getRootRegionId() != null, CxrRegionStaitemDailyReport::getRootRegionId, currentReport.getRootRegionId())
                .eq(currentReport.getRegionId() != null, CxrRegionStaitemDailyReport::getRegionId, currentReport.getRegionId())
                .eq(currentReport.getSiteId() != null, CxrRegionStaitemDailyReport::getSiteId, currentReport.getSiteId())
                .eq(currentReport.getAgentId() != null, CxrRegionStaitemDailyReport::getAgentId, currentReport.getAgentId())
        );
    }

    /**
     * 计算同比（通用方法，可用于合计功能）
     * @param currentReport 当前报表
     * @param lastYearReport 去年同期报表
     */
    public void calculateYoyComparison(CxrRegionStaitemDailyReport currentReport, CxrRegionStaitemDailyReport lastYearReport) {
        // 计算订单数量同比
        currentReport.setNewOrderCountYoy(calculateYoyRate(currentReport.getNewOrderCount(), lastYearReport.getNewOrderCount()));
        currentReport.setContinueOrderCountYoy(calculateYoyRate(currentReport.getContinueOrderCount(), lastYearReport.getContinueOrderCount()));
        currentReport.setIncreaseOrderCountYoy(calculateYoyRate(currentReport.getIncreaseOrderCount(), lastYearReport.getIncreaseOrderCount()));
        currentReport.setReturnOrderCountYoy(calculateYoyRate(currentReport.getReturnOrderCount(), lastYearReport.getReturnOrderCount()));
        currentReport.setTotalOrderCountYoy(calculateYoyRate(currentReport.getTotalOrderCount(), lastYearReport.getTotalOrderCount()));

        // 计算业绩金额同比
        currentReport.setNewOrderAchievementYoy(calculateYoyRate(currentReport.getNewOrderAchievement(), lastYearReport.getNewOrderAchievement()));
        currentReport.setContinueOrderAchievementYoy(calculateYoyRate(currentReport.getContinueOrderAchievement(), lastYearReport.getContinueOrderAchievement()));
        currentReport.setIncreaseOrderAchievementYoy(calculateYoyRate(currentReport.getIncreaseOrderAchievement(), lastYearReport.getIncreaseOrderAchievement()));
        currentReport.setReturnOrderAchievementYoy(calculateYoyRate(currentReport.getReturnOrderAchievement(), lastYearReport.getReturnOrderAchievement()));
        currentReport.setTotalAchievementYoy(calculateYoyRate(currentReport.getTotalAchievement(), lastYearReport.getTotalAchievement()));
    }

    /**
     * 计算环比（通用方法，可用于合计功能）
     * @param currentReport 当前报表
     * @param lastMonthReport 上期报表
     */
    public void calculateMomComparison(CxrRegionStaitemDailyReport currentReport, CxrRegionStaitemDailyReport lastMonthReport) {
        // 计算订单数量环比
        currentReport.setNewOrderCountMom(calculateYoyRate(currentReport.getNewOrderCount(), lastMonthReport.getNewOrderCount()));
        currentReport.setContinueOrderCountMom(calculateYoyRate(currentReport.getContinueOrderCount(), lastMonthReport.getContinueOrderCount()));
        currentReport.setIncreaseOrderCountMom(calculateYoyRate(currentReport.getIncreaseOrderCount(), lastMonthReport.getIncreaseOrderCount()));
        currentReport.setReturnOrderCountMom(calculateYoyRate(currentReport.getReturnOrderCount(), lastMonthReport.getReturnOrderCount()));
        currentReport.setTotalOrderCountMom(calculateYoyRate(currentReport.getTotalOrderCount(), lastMonthReport.getTotalOrderCount()));

        // 计算业绩金额环比
        currentReport.setNewOrderAchievementMom(calculateYoyRate(currentReport.getNewOrderAchievement(), lastMonthReport.getNewOrderAchievement()));
        currentReport.setContinueOrderAchievementMom(calculateYoyRate(currentReport.getContinueOrderAchievement(), lastMonthReport.getContinueOrderAchievement()));
        currentReport.setIncreaseOrderAchievementMom(calculateYoyRate(currentReport.getIncreaseOrderAchievement(), lastMonthReport.getIncreaseOrderAchievement()));
        currentReport.setReturnOrderAchievementMom(calculateYoyRate(currentReport.getReturnOrderAchievement(), lastMonthReport.getReturnOrderAchievement()));
        currentReport.setTotalAchievementMom(calculateYoyRate(currentReport.getTotalAchievement(), lastMonthReport.getTotalAchievement()));
    }

    /**
     * 获取报表标识符（用于日志）
     * @param report 报表数据
     * @return 标识符字符串
     */
    private String getReportIdentifier(CxrRegionStaitemDailyReport report) {
        StringBuilder identifier = new StringBuilder();
        identifier.append("维度:").append(report.getDimensionType());

        switch (report.getDimensionType()) {
            case "AGENT":
                identifier.append(",代理ID:").append(report.getAgentId())
                         .append(",代理:").append(report.getAgentName());
                break;
            case "SITE":
                identifier.append(",站点ID:").append(report.getSiteId())
                         .append(",站点:").append(report.getSiteName());
                break;
            case "REGION":
                identifier.append(",区域ID:").append(report.getRegionId())
                         .append(",区域:").append(report.getRegionName());
                break;
            case "ROOT_REGION":
                identifier.append(",大区ID:").append(report.getRootRegionId())
                         .append(",大区:").append(report.getRootRegionName());
                break;
            case "COMPANY":
                identifier.append(",公司ID:").append(report.getCompanyId())
                         .append(",公司:").append(report.getCompanyName());
                break;
        }

        return identifier.toString();
    }

    /**
     * 站点维度组合键：(companyId, rootRegionId, regionId, siteId)
     */
    private static class SiteDimensionKey {
        private final Long companyId;
        private final Long rootRegionId;
        private final Long regionId;
        private final Long siteId;

        public SiteDimensionKey(Long companyId, Long rootRegionId, Long regionId, Long siteId) {
            this.companyId = companyId;
            this.rootRegionId = rootRegionId;
            this.regionId = regionId;
            this.siteId = siteId;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            SiteDimensionKey that = (SiteDimensionKey) o;
            return Objects.equals(companyId, that.companyId) &&
                   Objects.equals(rootRegionId, that.rootRegionId) &&
                   Objects.equals(regionId, that.regionId) &&
                   Objects.equals(siteId, that.siteId);
        }

        @Override
        public int hashCode() {
            return Objects.hash(companyId, rootRegionId, regionId, siteId);
        }

        @Override
        public String toString() {
            return String.format("SiteDimensionKey{companyId=%d, rootRegionId=%d, regionId=%d, siteId=%d}",
                companyId, rootRegionId, regionId, siteId);
        }

        // Getters
        public Long getCompanyId() { return companyId; }
        public Long getRootRegionId() { return rootRegionId; }
        public Long getRegionId() { return regionId; }
        public Long getSiteId() { return siteId; }
    }

    /**
     * 区域维度组合键：(companyId, rootRegionId, regionId)
     */
    private static class RegionDimensionKey {
        private final Long companyId;
        private final Long rootRegionId;
        private final Long regionId;

        public RegionDimensionKey(Long companyId, Long rootRegionId, Long regionId) {
            this.companyId = companyId;
            this.rootRegionId = rootRegionId;
            this.regionId = regionId;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            RegionDimensionKey that = (RegionDimensionKey) o;
            return Objects.equals(companyId, that.companyId) &&
                   Objects.equals(rootRegionId, that.rootRegionId) &&
                   Objects.equals(regionId, that.regionId);
        }

        @Override
        public int hashCode() {
            return Objects.hash(companyId, rootRegionId, regionId);
        }

        @Override
        public String toString() {
            return String.format("RegionDimensionKey{companyId=%d, rootRegionId=%d, regionId=%d}",
                companyId, rootRegionId, regionId);
        }

        // Getters
        public Long getCompanyId() { return companyId; }
        public Long getRootRegionId() { return rootRegionId; }
        public Long getRegionId() { return regionId; }
    }

    /**
     * 大区维度组合键：(companyId, rootRegionId)
     */
    private static class RootRegionDimensionKey {
        private final Long companyId;
        private final Long rootRegionId;

        public RootRegionDimensionKey(Long companyId, Long rootRegionId) {
            this.companyId = companyId;
            this.rootRegionId = rootRegionId;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            RootRegionDimensionKey that = (RootRegionDimensionKey) o;
            return Objects.equals(companyId, that.companyId) &&
                   Objects.equals(rootRegionId, that.rootRegionId);
        }

        @Override
        public int hashCode() {
            return Objects.hash(companyId, rootRegionId);
        }

        @Override
        public String toString() {
            return String.format("RootRegionDimensionKey{companyId=%d, rootRegionId=%d}",
                companyId, rootRegionId);
        }

        // Getters
        public Long getCompanyId() { return companyId; }
        public Long getRootRegionId() { return rootRegionId; }
    }

}
