<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.business.base.employeeAchievementDetail.mapper.CxrEmployeeAchievementDetailMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.business.base.api.domain.CxrEmployeeAchievementDetail">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="sysDeptId" column="sys_dept_id" jdbcType="BIGINT"/>
        <result property="revision" column="revision" jdbcType="BIGINT"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="createByName" column="create_by_name" jdbcType="VARCHAR"/>
        <result property="createByType" column="create_by_type" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="updateByName" column="update_by_name" jdbcType="VARCHAR"/>
        <result property="updateByType" column="update_by_type" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleteBy" column="delete_by" jdbcType="BIGINT"/>
        <result property="deleteByName" column="delete_by_name" jdbcType="VARCHAR"/>
        <result property="deleteByType" column="delete_by_type" jdbcType="VARCHAR"/>
        <result property="deleteTime" column="delete_time" jdbcType="TIMESTAMP"/>
        <result property="deleteStatus" column="delete_status" jdbcType="VARCHAR"/>
        <result property="sortNum" column="sort_num" jdbcType="BIGINT"/>
        <result property="employeeId" column="employee_id" jdbcType="BIGINT"/>
        <result property="achievementValue" column="achievement_value" jdbcType="DECIMAL"/>
        <result property="souceId" column="souce_id" jdbcType="BIGINT"/>
        <result property="source" column="source" jdbcType="TINYINT"/>
        <result property="terminalType" column="terminal_type" jdbcType="TINYINT"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="siteId" column="site_id" jdbcType="BIGINT"/>
        <result property="excessQuantity" column="excess_quantity" jdbcType="INTEGER"/>
        <result property="boxs" column="boxs" jdbcType="INTEGER"/>
        <result property="orderFmQty" column="order_fm_qty" jdbcType="DECIMAL"/>
        <result property="orderDate" column="order_date"/>
        <result property="freshMilkGiveQuantity" column="fresh_milk_give_quantity"/>
        <result property="longMilkGiveQuantity" column="long_milk_give_quantity"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,sys_dept_id,revision,
        create_by,create_by_name,create_by_type,
        create_time,update_by,update_by_name,
        update_by_type,update_time,delete_by,
        delete_by_name,delete_by_type,delete_time,
        delete_status,sort_num,employee_id,
        achievement_value,souce_id,source,
        terminal_type,remark,site_id,
        excess_quantity,boxs,order_date
    </sql>
    <select id="sumPerformanceEveryDay"
        resultType="com.ruoyi.business.base.api.domain.dto.DailyPerformanceDto">
        select employee_id as cxrEmployeeId,
        sum(achievement_value) as Performance
        from cxr_employee_achievement_detail
        <where>
            order_date=#{now}
            and delete_status =#{del}
            and employee_id = #{id}
        </where>
        group by cxrEmployeeId;
    </select>


    <select id="sumMonthAchievement" resultType="java.math.BigDecimal">
        select sum(IFNULL(achievement_value, 0)) as achievement
        from cxr_employee_achievement_detail

        WHERE employee_id = #{employeeId}
          AND order_date >= #{start}
          AND order_date &lt; #{end}
          and delete_status = '0'

    </select>

    <select id="monthAchievementList" resultType="com.ruoyi.business.base.api.domain.CxrEmployeeAchievementDetail">
        select
            achievement_value,
            boxs,
            fresh_milk_give_quantity,
            long_milk_give_quantity,
            order_count,
            order_type,
            channel,
            accounting_type,
            promotion_commission
        from cxr_employee_achievement_detail

        WHERE employee_id = #{employeeId}
          AND order_date >= #{start}
          AND order_date &lt; #{end}
          and delete_status = '0'

    </select>

    <select id="sumMonthAchievementAll"
        resultType="com.ruoyi.business.base.api.domain.dto.CxrEmployeeAchievementDetailDTO">
        select sum(IFNULL(achievement_value, 0))        as achievement_value,
               sum(IFNULL(excess_quantity, 0))          as excess_quantity,
               sum(IFNULL(boxs, 0))                     as boxs,
               sum(IFNULL(fresh_milk_sent_quantity, 0)) as fresh_milk_sent_quantity,
               sum(IFNULL(long_milk_give_quantity, 0))  as long_milk_give_quantity,
               sum(IFNULL(fresh_milk_give_quantity, 0)) as fresh_milk_give_quantity,
               sum(IF(order_type = 0, order_count, 0))  as newOrderCount,
               sum(IF(order_type = 1, order_count, 0))  as contdOrderCount,
               sum(IF(order_type = 3, order_count, 0))  as increaseOrderCount
        from cxr_employee_achievement_detail

        WHERE employee_id = #{employeeId}
          AND order_date >= #{start}
          AND order_date &lt; #{end}
          and delete_status = '0'

    </select>

    <select id="employeeMonthAchievementSum"
        resultType="com.ruoyi.business.base.api.domain.CxrEmployeeAchievementDetail">
        select sum(IFNULL(achievement_value, 0))        achievement_value,
               sum(IFNULL(fresh_milk_sent_quantity, 0)) fresh_milk_sent_quantity
        from cxr_employee_achievement_detail

        WHERE employee_id = #{employeeId}
          AND order_date >= #{start}
          AND order_date &lt; #{end}
          and delete_status = '0'

    </select>
    <select id="employeeMonthAchievementSumSite"
        resultType="com.ruoyi.business.base.api.domain.CxrEmployeeAchievementDetail">
        select sum(IFNULL(achievement_value, 0))        achievement_value,
               sum(IFNULL(fresh_milk_sent_quantity, 0)) fresh_milk_sent_quantity
        from cxr_employee_achievement_detail

        WHERE employee_id = #{employeeId}
          AND order_date >= #{start}
          AND order_date &lt; #{end}
          and delete_status = '0'
          <if test="siteId !=null">
              and site_id = #{siteId}
          </if>

    </select>


    <select id="sumSiteMonthAchievement" resultType="java.math.BigDecimal">
        select sum(IFNULL(achievement_value, 0)) as achievement
        from cxr_employee_achievement_detail

        WHERE site_id = #{siteId}
          AND order_date >= #{start}
          AND order_date &lt; #{end}
          and delete_status = '0'

    </select>

    <select id="currentDayAchievementSiteNoGroup" resultType="java.math.BigDecimal">
        select sum(IFNULL(achievement_value, 0)) as achievement
        from cxr_employee_achievement_detail

        WHERE site_id = #{siteId}
          AND order_date >= #{start}
          AND order_date &lt;= #{end}
          and delete_status = '0'
          and (group_id =0 or group_id is null)

    </select>

    <select id="sumSiteMonthAchievementFore" resultType="java.math.BigDecimal">

        SELECT SUM(IFNULL(achievement,0)) FROM (
        <foreach collection="posts" item="post" separator=" UNION ALL ">
            select sum(IFNULL(achievement_value,0)) as achievement
            from cxr_employee_achievement_detail

            WHERE site_id = #{post.cxrSiteId}
            AND order_date >= #{post.fist}
            AND order_date &lt; #{post.last}
            and delete_status = '0'
        </foreach>
        )a
    </select>


    <select id="sumboxsEveryDay" resultType="com.ruoyi.business.base.api.domain.dto.DailyIntergerDto">
        select employee_id as cxrEmployeeId,
        sum(boxs) as box
        ,sum(fresh_milk_give_quantity) as freshMilkQuantity
        ,sum(long_milk_give_quantity) as longMilkNumber
        ,any_value(site_id) as cxrSiteId
        from cxr_employee_achievement_detail
        <where>
            order_date=#{now}
            and delete_status =#{del}
            and employee_id =#{id}
        </where>
        group by cxrEmployeeId;

    </select>
    <select id="sumboxsEveryDayGroupSite" resultType="com.ruoyi.business.base.api.domain.dto.DailyIntergerDto">
        select site_id as cxrSiteId, sum(boxs) as box
        ,sum(fresh_milk_give_quantity) as freshMilkQuantity
        ,sum(long_milk_give_quantity) as longMilkNumber
        ,sum(achievement_value) as money
        from cxr_employee_achievement_detail
        <where>
            order_date=#{now}
            and delete_status =#{del}
        </where>
        group by cxrSiteId;

    </select>
    <select id="sumPerformanceByDayGroupSite"
        resultType="com.ruoyi.business.base.api.domain.dto.DailyIntergerDto">

        select site_id as cxrSiteId, sum(achievement_value) as money
        from cxr_employee_achievement_detail
        <where>
            order_date=#{now}
            and delete_status =#{del}
            <if test="ids !=null and   ids.size()>0">
                and site_id in
                <foreach collection="ids" close=")" open="(" item="i" separator=",">
                    #{i}
                </foreach>
            </if>
        </where>
        group by cxrSiteId;

    </select>

    <select id="selectSaleAchievement" resultType="com.ruoyi.business.base.api.domain.dto.DailyPerformanceDto">
        SELECT e.name cxrEmployeeName,e.job_number,a.achievement_value,a.souce_id,boxs
        ,fresh_milk_sent_quantity,long_milk_give_quantity,fresh_milk_give_quantity
        FROM cxr_employee_achievement_detail a
        LEFT JOIN cxr_employee e ON a.employee_id = e.id
        WHERE a.employee_id = #{employeeId} AND a.delete_status = '0'
        AND a.souce_id in
        <foreach collection="orderIds" item="value" separator="," open="(" close=")">
            #{value}
        </foreach>

    </select>


    <delete id="delBySiteDate">
        DELETE FROM cxr_employee_achievement_site
        WHERE site_id = #{siteId} AND order_date = #{orderDate}
    </delete>
    <insert id="saveBySiteDate">
        REPLACE INTO cxr_employee_achievement_site
        SELECT #{orderDate}, #{siteId}, IFNULL(SUM(achievement_value),0) achievement_value
        FROM cxr_employee_achievement_detail
        WHERE site_id = #{siteId} AND date_format(order_date, '%Y-%m-%d') = #{orderDate} AND delete_status = '0'
    </insert>
    <insert id="saveOrUpdate">
        REPLACE INTO cxr_employee_achievement_site
        SELECT #{bo.orderDate}, #{bo.siteId}, SUM(achievement_value) achievement_value
        FROM cxr_employee_achievement_detail
        WHERE site_id = #{bo.siteId}
          AND date_format(order_date, '%Y-%m-%d') = #{bo.orderDate}
          AND delete_status = '0'
    </insert>
    <insert id="saveOrUpdateAll">
        REPLACE INTO cxr_employee_achievement_site(order_date,site_id,achievement_value)
        select date_format(order_date,'%Y-%m-%d') order_date,site_id ,sum(achievement_value) achievement_value
        from cxr_employee_achievement_detail
        where delete_status = '0'
        <if test="date != null">
            AND order_date >= #{date}
        </if>
        group by order_date,site_id
    </insert>

    <insert id="saveOrUpdateBySiteId">
        REPLACE INTO cxr_employee_achievement_site(order_date, site_id, achievement_value)
        select date_format(order_date, '%Y-%m-%d') order_date, site_id, sum(achievement_value) achievement_value
        from cxr_employee_achievement_detail
        where delete_status = '0'
          AND date_format(order_date, '%Y-%m-%d') = #{date}
          AND site_id = #{siteId}
        group by order_date, site_id
    </insert>

    <select id="sumEmployeeNewOrders" resultType="java.math.BigDecimal">
        select IFNULL(sum(order_count), 0) as orderCount
        from cxr_employee_achievement_detail
        WHERE employee_id = #{employeeId}
          and order_type in (0)
          AND order_date like CONCAT('%', #{time}, '%')
          and delete_status = '0'

    </select>

    <select id="sumEmployeeTotalPerformance" resultType="java.math.BigDecimal">
        select IFNULL(sum(achievement_value), 0) as achievementValue
        from cxr_employee_achievement_detail
        WHERE employee_id = #{employeeId}
          AND order_date like CONCAT('%', #{time}, '%')
          and delete_status = '0'

    </select>

    <select id="sumEmployeeDeliveredOrders" resultType="java.math.BigDecimal">
        select IFNULL(sum(fresh_milk_sent_quantity), 0) as quantity
        from cxr_employee_achievement_detail
        WHERE employee_id = #{proxyId}
          AND order_date like CONCAT('%', #{time}, '%')
          and delete_status = '0'
    </select>

    <update id="batchUpdateOrderFmQty">
        <foreach collection="list" item="item" index="key">
            UPDATE cxr_employee_achievement_detail
            SET order_fm_qty = #{item.orderFmQty}
            WHERE
                delete_status = '0'
                <if test="item.employeeId != null">
                    AND employee_id = #{item.employeeId}
                </if>
                AND souce_id = #{item.souceId};
        </foreach>
    </update>

    <!-- employeeMonthAchievementNoGroup -->

    <select id="employeeMonthAchievementNoGroup" resultType="com.ruoyi.business.base.api.domain.CxrEmployeeAchievementDetail">
        select distinct employee_id,site_id,order_date,employee_name
        from
        cxr_employee_achievement_detail
        where delete_status = '0'
        and order_date >= #{first}
        and order_date &lt; #{nextFirst}
        and (group_id =0 or group_id is null)
    </select>

    <delete id="delByDate">
        <foreach collection="list"  item="item">
            DELETE FROM cxr_employee_achievement_site WHERE order_date = #{item.orderDate} AND site_id = #{item.siteId};
        </foreach>
    </delete>
    <insert id="saveByDate">
        INSERT INTO cxr_employee_achievement_site(order_date, site_id, achievement_value)
        SELECT a.order_date,a.site_id, IFNULL(SUM(achievement_value),0) achievement_value
        FROM JSON_TABLE(#{jsonList},'$[*]' COLUMNS (site_id BIGINT PATH '$.siteId', order_date date PATH '$.orderDate'))a
                 INNER JOIN cxr_employee_achievement_detail b ON a.site_id = b.site_id AND a.order_date = b.order_date AND delete_status = '0'
        GROUP BY a.order_date,a.site_id
    </insert>
</mapper>
