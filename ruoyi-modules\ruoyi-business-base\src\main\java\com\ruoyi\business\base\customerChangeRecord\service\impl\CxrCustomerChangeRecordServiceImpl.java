package com.ruoyi.business.base.customerChangeRecord.service.impl;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.business.base.api.domain.CxrAddressHistory;
import com.ruoyi.business.base.api.domain.CxrCustomerAddress;
import com.ruoyi.business.base.api.domain.CxrCustomerChangeRecord;
import com.ruoyi.business.base.api.domain.CxrSite;
import com.ruoyi.business.base.customerAddress.mapper.CxrCustomerAddressMapper;
import com.ruoyi.business.base.customerChangeRecord.mapper.CxrCustomerChangeRecordMapper;
import com.ruoyi.business.base.customerChangeRecord.service.CxrCustomerChangeRecordService;
import com.ruoyi.business.base.cxrAddressHistory.mapper.CxrAddressHistoryMapper;
import com.ruoyi.business.base.site.mapper.CxrSiteMapper;
import com.ruoyi.common.core.enums.DeleteStatus;
import com.ruoyi.common.core.enums.SysYesNo;
import com.ruoyi.common.core.exception.ServiceException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @description 针对表【cxr_customer_change_record(口味数量起送暂停变更记录表)】的数据库操作Service实现
 * @createDate 2023-03-30 11:28:59
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CxrCustomerChangeRecordServiceImpl extends
    ServiceImpl<CxrCustomerChangeRecordMapper, CxrCustomerChangeRecord>
    implements CxrCustomerChangeRecordService {

    private final CxrCustomerAddressMapper cxrCustomerAddressMapper;

    private final CxrSiteMapper siteMapper;

    private final CxrAddressHistoryMapper addressHistoryMapper;

    @Override
    public Boolean customerSeparateCchangeAddressSave(CxrCustomerChangeRecord customerChangeRecord) {
        CxrCustomerChangeRecord agedObj = checkCxrCustomerChangeRecord(customerChangeRecord.getCustomerAddressId());
        if (ObjectUtil.isNotEmpty(agedObj)) {
            this.removeById(agedObj.getId());
        }

        LocalTime time = LocalTime.now();
        CxrSite cxrSite = siteMapper.getCxrSite(customerChangeRecord.getSiteId());
        // 生效时间  异动时间
        LocalTime endTime = cxrSite.getChangeEndTime();
        LocalDate now = LocalDate.now();
        if (time.isBefore(endTime)) {
            customerChangeRecord.setEffectTime(now.plusDays(cxrSite.getChangeIntervalDays()));
            customerChangeRecord.setEffectType(cxrSite.getChangeIntervalDays() + "");
        } else {
            customerChangeRecord.setEffectTime(now.plusDays(cxrSite.getChangeIntervalDaysAfter()));
            customerChangeRecord.setEffectType(cxrSite.getChangeIntervalDaysAfter() + "");
        }
        customerChangeRecord.setId(null);
        customerChangeRecord.setUpdateTime(null);
        Boolean bol = saveCxrCustomerChangeRecord(customerChangeRecord);
        if (!bol) {
            throw new ServiceException("客户变更记录保存失败");
        }
        log.info("客户变更记录保存成功{}", customerChangeRecord.getId());

        updateChangeStatus(customerChangeRecord.getCustomerAddressId(), 1);
        return true;
    }

    public CxrCustomerChangeRecord checkCxrCustomerChangeRecord(Long customerAddressId) {
        return this.getOne(new LambdaQueryWrapper<CxrCustomerChangeRecord>()
            .eq(CxrCustomerChangeRecord::getCustomerAddressId, customerAddressId)
            .eq(CxrCustomerChangeRecord::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            .like(CxrCustomerChangeRecord::getCreateTime, LocalDate.now())
        );
    }

    public Boolean saveCxrCustomerChangeRecord(CxrCustomerChangeRecord customerChangeRecord) {
        if (StrUtil.isNotEmpty(customerChangeRecord.getAmDistributionStatus())
            && ObjectUtil.equals(customerChangeRecord.getAmDistributionStatus(), SysYesNo.YES.getValue())
            && ObjectUtil.isEmpty(customerChangeRecord.getAmDistributionStartDeliveryTime())) {

            CxrAddressHistory addressHistory = getLastCxrAddressHistory(
                customerChangeRecord.getCustomerAddressId());

            if (ObjectUtil.isNotEmpty(addressHistory)) {
                customerChangeRecord.setAmDistributionStatus(addressHistory.getAmDistributionStatus());
                customerChangeRecord.setAmDistributionStartDeliveryTime(addressHistory.getAmSendDate());
                customerChangeRecord.setAmDistributionSuspendStartTime(addressHistory.getAmStopStartDate());
                customerChangeRecord.setAmDistributionSuspendEndTime(addressHistory.getAmStopEndDate());
                log.info("客户排奶参数丢失...{},{}", customerChangeRecord.getCustomerAddressId(),
                    addressHistory.getAmDistributionStatus());
            } else {
                customerChangeRecord.setAmDistributionStartDeliveryTime(LocalDate.now());
            }

        }

        if (StrUtil.isNotEmpty(customerChangeRecord.getPmDistributionStatus())
            && ObjectUtil.equals(customerChangeRecord.getPmDistributionStatus(), SysYesNo.YES.getValue())
            && ObjectUtil.isEmpty(customerChangeRecord.getPmDistributionStartDeliveryTime())) {

            CxrAddressHistory addressHistory = getLastCxrAddressHistory(
                customerChangeRecord.getCustomerAddressId());
            if (ObjectUtil.isNotEmpty(addressHistory)) {
                customerChangeRecord.setPmDistributionStatus(addressHistory.getPmDistributionStatus());
                customerChangeRecord.setPmDistributionStartDeliveryTime(addressHistory.getPmSendDate());
                customerChangeRecord.setPmDistributionSuspendStartTime(addressHistory.getPmStopStartDate());
                customerChangeRecord.setPmDistributionSuspendEndTime(addressHistory.getPmStopEndDate());
                log.info("客户排奶参数丢失...{},{}", customerChangeRecord.getCustomerAddressId(),
                    addressHistory.getPmDistributionStatus());
            } else {
                customerChangeRecord.setPmDistributionStartDeliveryTime(LocalDate.now());
            }

        }

        return this.save(customerChangeRecord);
    }

    private CxrAddressHistory getLastCxrAddressHistory(Long customerAddressId) {
        CxrAddressHistory historyService = addressHistoryMapper.selectOne(new LambdaQueryWrapper<CxrAddressHistory>()
            .eq(CxrAddressHistory::getCxrCustomerAddressId, customerAddressId)
            .eq(CxrAddressHistory::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            .orderByDesc(CxrAddressHistory::getCreateTime)
            .last("limit 1")
        );
        return historyService;
    }

    public void updateChangeStatus(Long customerAddressId, int i) {
        cxrCustomerAddressMapper.update(null,
            new LambdaUpdateWrapper<CxrCustomerAddress>().eq(CxrCustomerAddress::getId, customerAddressId)
                .set(CxrCustomerAddress::getChangeStatus, i));
    }
}
