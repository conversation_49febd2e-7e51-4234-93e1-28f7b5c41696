package com.ruoyi.business.base.api.dubbo;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.business.base.api.domain.bo.DistributorOrderBo;
import com.ruoyi.business.base.api.domain.vo.DistributorAppletOrderVo;
import com.ruoyi.business.base.api.model.CustomerMilkRefundOrderVo;
import com.ruoyi.business.base.api.model.CustomerOrderVo;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 小程序零售订单和客户订单接口服务
 */
public interface RemoteOrderService {

    /**
     * 通过客户ID在指定时间段来获取零售订单和客户订单
     *
     * @param customerIds
     */
    List<CustomerOrderVo> getOrderBy(Collection<Long> customerIds, boolean isQueryCxrUserOrder, Collection<String> goodsIds, String beginTime, String endTime, Set<Integer> payStatusList);

    List<CustomerMilkRefundOrderVo> getMilkRefundOrderBy(Collection<Long> customerIds, String beginTime, String endTime, Set<Integer> orderTypes);

    IPage<DistributorAppletOrderVo> distributorOrderPage(DistributorOrderBo bo);

    DistributorAppletOrderVo distributorAppletOrderTotal(DistributorOrderBo bo);

    void distributorAppletOrderExport(DistributorOrderBo bo);
}
