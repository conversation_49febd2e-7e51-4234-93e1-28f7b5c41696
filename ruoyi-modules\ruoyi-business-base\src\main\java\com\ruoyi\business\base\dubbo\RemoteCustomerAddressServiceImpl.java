package com.ruoyi.business.base.dubbo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.business.api.domain.vo.CxrSaleProductVo;
import com.ruoyi.business.base.api.PlanMilkDataConvertUtil;
import com.ruoyi.business.base.api.domain.*;
import com.ruoyi.business.base.api.domain.bo.CxrAddressBo;
import com.ruoyi.business.base.api.domain.bo.CxrCustomerAddressBo;
import com.ruoyi.business.base.api.domain.dto.CxrCustomerAddressDTO;
import com.ruoyi.business.base.api.domain.json.CustomerAddressMilkDistributionDTO;
import com.ruoyi.business.base.api.domain.json.CustomerAddressMilkDistributionInfo;
import com.ruoyi.business.base.api.domain.vo.CxrAddressHistoryVo;
import com.ruoyi.business.base.api.domain.vo.CxrAddressVo;
import com.ruoyi.business.base.api.dubbo.RemoteCustomerAddressService;
import com.ruoyi.business.base.api.model.CustomerDistributioninfoDTO;
import com.ruoyi.business.base.customer.service.ICxrCustomerService;
import com.ruoyi.business.base.customerAddress.service.ICxrCustomerAddressService;
import com.ruoyi.business.base.customerChangeRecord.service.CxrCustomerChangeRecordService;
import com.ruoyi.business.base.customerDistributionListRecord.domain.CxrCustomerDistributionListRecord;
import com.ruoyi.business.base.customerDistributionListRecord.service.CxrCustomerDistributionListRecordService;
import com.ruoyi.business.base.cxrAddressHistory.service.CxrAddressHistoryService;
import com.ruoyi.business.base.cxrCustomerAddressHistory.service.ICxrCustomerAddressHistoryService;
import com.ruoyi.business.base.employee.service.ICxrEmployeeService;
import com.ruoyi.business.base.residentialQuarters.service.ICxrResidentialQuartersService;
import com.ruoyi.business.base.roadWay.mapper.CxrRoadWayMapper;
import com.ruoyi.business.base.saleProduct.service.ICxrSaleProductService;
import com.ruoyi.business.base.site.service.ICxrSiteService;
import com.ruoyi.common.core.domain.TableDataInfo;
import com.ruoyi.common.core.enums.DeleteStatus;
import com.ruoyi.common.core.enums.DeviceType;
import com.ruoyi.common.core.enums.SysYesNo;
import com.ruoyi.common.core.web.domain.CxrBaseEntity;
import io.seata.spring.annotation.GlobalTransactional;
import io.seata.tm.api.transaction.Propagation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteCustomerAddressServiceImpl implements RemoteCustomerAddressService {

    private final ICxrCustomerAddressService iCxrCustomerAddressService;

    private final CxrAddressHistoryService cxrAddressHistoryService;

    private final ICxrCustomerAddressHistoryService cxrCustomerAddressHistory;

    private final ICxrCustomerService iCxrCustomerService;

    private final ICxrResidentialQuartersService iCxrResidentialQuartersService;

    private final ICxrSiteService iCxrSiteService;

    private final ICxrEmployeeService iCxrEmployeeService;

    private final ICxrSaleProductService iCxrSaleProductService;

    private final CxrCustomerChangeRecordService cxrCustomerChangeRecordService;

    private final CxrCustomerDistributionListRecordService cxrCustomerDistributionListRecordService;

    private final CxrRoadWayMapper cxrRoadWayMapper;


    @Override
    public List<CxrCustomerAddress> cxrCustomerAddressList(Long customerId) {
        List<CxrCustomerAddress> list = iCxrCustomerAddressService.getBaseMapper()
            .selectList(new LambdaQueryWrapper<CxrCustomerAddress>()
                .eq(CxrCustomerAddress::getCxrCustomerId, customerId));
        return list;
    }

    public List<CxrCustomerAddress> getCustomerAddressList(Long customerId) {
        return iCxrCustomerAddressService.lambdaQuery().eq(CxrCustomerAddress::getCxrCustomerId, customerId)
            .isNotNull(CxrCustomerAddress::getCxrEmployeeId)
            .eq(CxrCustomerAddress::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            .orderByDesc(CxrCustomerAddress::getDefalutAccountAddress)
            .list();
    }

    @Override
    @GlobalTransactional(propagation = Propagation.NOT_SUPPORTED)
    public List<CxrCustomerAddressDTO> cxrCustomerAddressDTOList(Long customerId) {
        return iCxrCustomerAddressService.cxrCustomerAddressDTOList(customerId);
    }

    @Override
    public List<CxrCustomerAddress> list(CxrCustomerAddress cxrCustomerAddress) {
        return iCxrCustomerAddressService.getBaseMapper()
            .selectList(new QueryWrapper<CxrCustomerAddress>(cxrCustomerAddress));
    }

    @Override
    public TableDataInfo<CxrCustomer> pageWithQuarters(Object page, CxrCustomerAddressBo cxrCustomerAddress) {
        return iCxrCustomerAddressService.pageWithQuarters(page, cxrCustomerAddress);
    }

    /**
     * 查询客户地址信息最早的1条
     *
     * @param customerId
     * @return
     */
    @Override
    public CxrCustomerAddress queryCustomerAddress(Long customerId) {
        CxrCustomerAddress cxrCustomerAddress = iCxrCustomerAddressService.getBaseMapper()
            .selectOne(new LambdaQueryWrapper<CxrCustomerAddress>()
                .eq(CxrCustomerAddress::getCxrCustomerId, customerId)
                .eq(CxrBaseEntity::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .orderByAsc(CxrBaseEntity::getCreateTime)
                .last(" limit 1")
            );

        return cxrCustomerAddress;
    }

    /**
     * 查询客户默认地址
     *
     * @param customerId
     * @return
     */
    @Override
    public CxrCustomerAddress queryCustomerAddressDefault(Long customerId) {
        CxrCustomerAddress cxrCustomerAddress = iCxrCustomerAddressService.lambdaQuery()
            .eq(CxrCustomerAddress::getCxrCustomerId, customerId)
            .eq(CxrBaseEntity::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            .eq(CxrCustomerAddress::getDefalutAccountAddress, SysYesNo.YES.getValue()).one();
        return cxrCustomerAddress;
    }

    @Override
    public CxrCustomerAddress getCustomerAddressById(Long id) {
        CxrCustomerAddress cxrCustomerAddress = iCxrCustomerAddressService.getBaseMapper()
            .selectOne(new LambdaQueryWrapper<CxrCustomerAddress>()
                .eq(CxrCustomerAddress::getId, id)
                .eq(CxrBaseEntity::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .orderByAsc(CxrBaseEntity::getCreateTime)
                .last("limit 1")
            );
        return cxrCustomerAddress;
    }


    @Override
    public CxrCustomerAddress queryCustomerDefalutAddress(Long customerId) {
        CxrCustomerAddress cxrCustomerAddress = iCxrCustomerAddressService.getBaseMapper()
            .selectOne(new LambdaQueryWrapper<CxrCustomerAddress>()
                .eq(CxrCustomerAddress::getCxrCustomerId, customerId)
                .eq(CxrBaseEntity::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .last(" ORDER BY defalut_account_address = 'Y' DESC limit 1")
            );
        return cxrCustomerAddress;
    }

    @Override
    public CxrCustomerAddress queryCustomerAddressByPhone(String phone) {
        CxrCustomerAddress cxrCustomerAddress = iCxrCustomerAddressService.getBaseMapper()
            .selectOne(new LambdaQueryWrapper<CxrCustomerAddress>()
                .eq(CxrCustomerAddress::getReceiverPhone, phone)
                .eq(CxrBaseEntity::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .orderByDesc(CxrBaseEntity::getCreateTime)
                .last(" limit 1")
            );

        return cxrCustomerAddress;
    }


    /**
     * 根据 转出用户id  更新站点员工
     *
     * @param outId
     * @param enterId
     * @param enterSiteId
     * @return
     */
    @Override
    public boolean updateAllEmployeeAdress(Long outId, Long enterId, Long enterSiteId) {
        boolean update = iCxrCustomerAddressService.update(new UpdateWrapper<CxrCustomerAddress>()
            .set("cxr_employee_id", enterId)
            .set("cxr_site_id", enterSiteId)
            .eq("cxr_employee_id", outId)
            .eq("delete_status", DeleteStatus.NOT_DELETED.getValue())
        );
        return update;

    }


    /**
     * 根据员工id查询所有名下的用户地址 Id
     *
     * @param emId
     * @return
     */
    @Override
    public List<Long> selectByEmployeeId(Long emId) {

        return iCxrCustomerAddressService.getListByemId(emId);
    }


    @Override
    public Long countByEmployeeId(Long emId) {
        return iCxrCustomerAddressService.count(
            new LambdaQueryWrapper<CxrCustomerAddress>()
                .eq(CxrCustomerAddress::getCxrEmployeeId, emId)
                .eq(CxrCustomerAddress::getDeleteStatus, DeleteStatus.not_deleted)
        );
    }


    @Override
    public List<CxrCustomerAddress> findBySite(List<Long> addressIds) {
        if (CollectionUtil.isNotEmpty(addressIds)) {
            return iCxrCustomerAddressService.list(new LambdaQueryWrapper<CxrCustomerAddress>().
                in(CxrCustomerAddress::getId, addressIds));
        }
        return Collections.EMPTY_LIST;
    }


    @Override
    public void updateByid(CxrCustomerAddress address) {
        iCxrCustomerAddressService.updateById(address);
    }

    public void updateByCustomerId(Long customerId) {
        iCxrCustomerAddressService.updateByCustomerId(customerId);
    }

    @Override
    public void insertAddressHistory(CxrCustomerAddress cxrCustomerAddress) {
        cxrCustomerAddressHistory.insertAddressHistory(cxrCustomerAddress);
    }

    @Override
    public List<CxrAddressVo> getAddressids(CxrAddressBo bo) {
        return iCxrCustomerAddressService.getAddressids(bo);
    }

    @Override
    public List<CxrAddressVo> checkmilkDistributionDetail(Long id, String del) {
        return iCxrCustomerAddressService.checkmilkDistributionDetail(id, del);
    }

    @Override
    public List<CxrCustomerAddress> queryGetByIds(CxrAddressBo bo) {
        return iCxrCustomerAddressService.queryGetByIds(bo);
    }

    @Override
    public List<CxrCustomerAddress> getCustomerAddresses(Long id) {
        return iCxrCustomerAddressService.getCustomerAddresses(id);
    }

    /**
     * 获取客户配送信息
     *
     * @param customerId
     * @return
     */
    @Override
    public List<CustomerDistributioninfoDTO> queryCustomerDistributioninfo(Long customerId) {

        List<CustomerDistributioninfoDTO> returnCustomerDistributioninfoDTO = new ArrayList<>();
        CxrCustomer cxrCustomer = iCxrCustomerService.getById(customerId);
        if (ObjectUtil.isNull(cxrCustomer)) {
            return returnCustomerDistributioninfoDTO;

        }
        List<CxrCustomerAddress> customerAddresses = iCxrCustomerAddressService.getCustomerAddressesByCustomerId(
            cxrCustomer.getId());

        for (CxrCustomerAddress customerAddress : customerAddresses) {
            CustomerDistributioninfoDTO customerDistributioninfoDTO = new CustomerDistributioninfoDTO();

            customerDistributioninfoDTO.setAddressId(customerAddress.getId());
            customerDistributioninfoDTO.setCustomerName(customerAddress.getReceiverName());
            customerDistributioninfoDTO.setCustomerPhone(customerAddress.getReceiverPhone());
            customerDistributioninfoDTO.setSysAreaId(customerAddress.getSysAreaId());
            customerDistributioninfoDTO.setDefalutAccountAddress(customerAddress.getDefalutAccountAddress());

            CxrResidentialQuarters cxrResidentialQuarters = iCxrResidentialQuartersService.getById(
                customerAddress.getCxrResidentialQuartersId());
            if (ObjectUtil.isNotNull(cxrResidentialQuarters)) {
                customerDistributioninfoDTO.setResidentialQuartersId(customerAddress.getCxrResidentialQuartersId());
                customerDistributioninfoDTO.setResidentialQuartersName(cxrResidentialQuarters.getName());
            } else {
                customerDistributioninfoDTO.setResidentialQuartersId(null);
                customerDistributioninfoDTO.setResidentialQuartersName(null);
            }

            customerDistributioninfoDTO.setAddress(customerAddress.getDetailDistributionAddress());
            customerDistributioninfoDTO.setSiteId(customerAddress.getCxrSiteId());
            CxrSite cxrSite = iCxrSiteService.getById(customerAddress.getCxrSiteId());
            customerDistributioninfoDTO.setSiteName(cxrSite.getName());
            customerDistributioninfoDTO.setDistributionId(customerAddress.getCxrEmployeeId());
            CxrEmployee cxrEmployee = iCxrEmployeeService.getById(customerAddress.getCxrEmployeeId());
            customerDistributioninfoDTO.setDistributionName(cxrEmployee.getName());

            customerDistributioninfoDTO.setProvince(customerAddress.getProvice());
            customerDistributioninfoDTO.setCity(customerAddress.getCity());
            customerDistributioninfoDTO.setArea(customerAddress.getArea());

            if (StrUtil.equals(SysYesNo.YES.getValue(), customerAddress.getAmDistributionStatus())) {
                customerDistributioninfoDTO.setAmEnable(Boolean.TRUE);
                customerDistributioninfoDTO.setAmSendDate(customerAddress.getAmDistributionStartDeliveryTime());

                customerDistributioninfoDTO.setAmStopStartDate(null);
                customerDistributioninfoDTO.setAmStopEndDate(null);

            } else {
                customerDistributioninfoDTO.setAmEnable(Boolean.FALSE);
                customerDistributioninfoDTO.setAmSendDate(null);

                customerDistributioninfoDTO.setAmStopStartDate(customerAddress.getAmDistributionSuspendStartTime());
                customerDistributioninfoDTO.setAmStopEndDate(customerAddress.getAmDistributionSuspendEndTime());
            }

            if (StrUtil.equals(SysYesNo.YES.getValue(), customerAddress.getPmDistributionStatus())) {
                customerDistributioninfoDTO.setPmEnable(Boolean.TRUE);
                customerDistributioninfoDTO.setPmSendDate(customerAddress.getPmDistributionStartDeliveryTime());
                customerDistributioninfoDTO.setPmStopStartDate(null);
                customerDistributioninfoDTO.setPmStopEndDate(null);
            } else {
                customerDistributioninfoDTO.setPmEnable(Boolean.FALSE);
                customerDistributioninfoDTO.setPmSendDate(null);
                customerDistributioninfoDTO.setPmStopStartDate(customerAddress.getPmDistributionSuspendStartTime());
                customerDistributioninfoDTO.setPmStopEndDate(customerAddress.getPmDistributionSuspendEndTime());

            }
            List<CustomerDistributioninfoDTO.CheckboxList> checkboxList = new ArrayList<CustomerDistributioninfoDTO.CheckboxList>();

            CustomerDistributioninfoDTO.CheckboxList amCheckBox = new CustomerDistributioninfoDTO.CheckboxList();
            amCheckBox.setName("早上送");
            amCheckBox.setChecked(Boolean.TRUE);
            amCheckBox.setDisabled(Boolean.FALSE);

            CustomerDistributioninfoDTO.CheckboxList pmCheckBox = new CustomerDistributioninfoDTO.CheckboxList();
            pmCheckBox.setName("晚上送");
            pmCheckBox.setChecked(Boolean.TRUE);
            pmCheckBox.setDisabled(Boolean.FALSE);
            checkboxList.add(amCheckBox);
            checkboxList.add(pmCheckBox);

            customerDistributioninfoDTO.setCheckboxList(checkboxList);
            CustomerAddressMilkDistributionInfo amDistributionInfo = customerAddress.getAmDistributionInfo();
            CustomerAddressMilkDistributionInfo pmDistributionInfo = customerAddress.getPmDistributionInfo();

            List<CxrSaleProductVo> cxrSaleProductVos = iCxrSaleProductService.querySaleProductVoBySiteId(
                customerAddress.getCxrSiteId());

            customerDistributioninfoDTO.setAmMilkDistributionList(
                PlanMilkDataConvertUtil.convertDateToProductId(amDistributionInfo, cxrSaleProductVos));
            customerDistributioninfoDTO.setPmMilkDistributionList(
                PlanMilkDataConvertUtil.convertDateToProductId(pmDistributionInfo, cxrSaleProductVos));
            returnCustomerDistributioninfoDTO.add(customerDistributioninfoDTO);
        }

        return returnCustomerDistributioninfoDTO;
    }


    @Override
    public int updateChangeMilk(CxrAddressBo bo) {
        return iCxrCustomerAddressService.updateChangeMilk(bo);
    }

    @Override
    public boolean updateCustomerReceiverPhone(CxrAddressBo bo) {
        return iCxrCustomerAddressService.updateCustomerReceiverPhone(bo);
    }

    @Override
    public CxrCustomerChangeRecord selectCustomerChangeRecord(Long id) {
        CxrCustomerChangeRecord record = cxrCustomerChangeRecordService.getBaseMapper()
            .selectOne(new LambdaQueryWrapper<CxrCustomerChangeRecord>()
                .eq(CxrCustomerChangeRecord::getCustomerAddressId, id)
                .eq(CxrCustomerChangeRecord::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .orderByDesc(CxrCustomerChangeRecord::getId)
                .last("limit 1")
            );
        return record;
    }

    @Override
    public int updateAmChangeSendOrStopChangeRecord(CxrAddressBo bo) {
        return iCxrCustomerAddressService.updateAmChangeSendOrStopChangeRecord(bo);
    }

    @Override
    public int updatePmChangeSendOrStopChangeCustomer(CxrAddressBo bo) {
        return iCxrCustomerAddressService.updatePmChangeSendOrStopChangeCustomer(bo);
    }

    @Override
    public boolean updateAddressSite(Long id, Long cxrSiteId) {
        boolean update = iCxrCustomerAddressService.update(
            new LambdaUpdateWrapper<CxrCustomerAddress>().eq(CxrCustomerAddress::getCxrEmployeeId, id)
                .eq(CxrCustomerAddress::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .set(CxrCustomerAddress::getCxrSiteId, cxrSiteId)
        );
        return update;
    }

    @Override
    public int updatebyIdGetLnglat(CxrCustomerAddress address) {
        return iCxrCustomerAddressService.getBaseMapper().update(null, new LambdaUpdateWrapper<CxrCustomerAddress>()
            .eq(CxrCustomerAddress::getId, address.getId())
            .set(CxrCustomerAddress::getLongitude, address.getLongitude())
            .set(CxrCustomerAddress::getLatitude, address.getLatitude())
            .set(CxrCustomerAddress::getLevel, address.getLevel()));
    }

    @Override
    public CxrCustomerAddress addGetLngLat(CxrCustomerAddress address) {
        return iCxrCustomerAddressService.addGetLngLat(address);
    }

    @Override
    public Long customerRecoedMilkDayDistributionTotal(CxrRoadWay cxrRoadWay) {
        LocalDate now = cxrRoadWay.getDistributionDate();
        int dayOfMonth = now.getDayOfMonth();
        int month = now.getMonth().getValue();
        int year = now.getYear();
        CxrCustomerDistributionListRecord record = cxrCustomerDistributionListRecordService.getBaseMapper()
            .selectOne(new LambdaQueryWrapper<CxrCustomerDistributionListRecord>()
                .eq(CxrCustomerDistributionListRecord::getMonthmark, month)
                .eq(CxrCustomerDistributionListRecord::getYearkmar, year)
                .eq(CxrCustomerDistributionListRecord::getCustomerAddressId, cxrRoadWay.getCxrCustomerAddressId())
            );
        if (ObjectUtil.isEmpty(record)) {
            return 0l;
        }

        return distributionListRecordDaySum(record, dayOfMonth);
    }

    @Override
    public Long selectCustomerSentNumberId(Long cxrCustomerId, String customerAddress) {
        CxrCustomerAddress cxrCustomerAddress = iCxrCustomerAddressService.getBaseMapper()
            .selectOne(new LambdaQueryWrapper<CxrCustomerAddress>()
                .eq(CxrCustomerAddress::getCxrCustomerId, cxrCustomerId)
                .eq(CxrCustomerAddress::getDetailDistributionAddress, customerAddress)
                .eq(CxrCustomerAddress::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .last(" limit 1 ")
            );
        if (ObjectUtil.isNotEmpty(cxrCustomerAddress)) {
            return cxrCustomerAddress.getId();
        }
        CxrCustomerAddress address = iCxrCustomerAddressService.getBaseMapper()
            .selectOne(new LambdaQueryWrapper<CxrCustomerAddress>()
                .eq(CxrCustomerAddress::getCxrCustomerId, cxrCustomerId)
                .eq(CxrCustomerAddress::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .last(" limit 1 ")

            );
        if (ObjectUtil.isNotEmpty(address)) {
            return address.getId();
        }
        return null;
    }

    @Override
    public int updateAmChangeSendOrStop(CxrAddressBo bo) {
        int i = iCxrCustomerAddressService.updateAmChangeSendOrStop(bo);
        if (i > 0) {
            iCxrCustomerAddressService.updateAmChangeSendOrStopChangeRecord(bo);
        }
        return i;
    }


    @Override
    public int updatePmChangeSendOrStop(CxrAddressBo bo) {
        return iCxrCustomerAddressService.updatePmChangeSendOrStop(bo);
    }

    @Override
    public List<CxrAddressHistoryVo> getOneHistoryByAddressIds(List<Long> addressIds) {

        List<CxrAddressHistory> list = new ArrayList<>();

        for (Long s : addressIds) {
            CxrAddressHistory one = cxrAddressHistoryService.getOne(new LambdaQueryWrapper<CxrAddressHistory>()
                .eq(CxrAddressHistory::getCxrCustomerAddressId, s)
                .orderByDesc(CxrAddressHistory::getUpdateTime)
                .last("limit 1")
            );
            list.add(one);
        }

        return BeanUtil.copyToList(list, CxrAddressHistoryVo.class);
    }

    @Override
    public void insertBatchAddressHistory(List<CxrCustomerAddress> customerAddressList) {
        cxrCustomerAddressHistory.insertListAddressHistory(customerAddressList);
    }

    @Override
    public void threadWordUpdate(Integer page, List<CxrCustomerAddress> customerAddressList) {
        cxrCustomerAddressHistory.threadWordUpdate(page, customerAddressList);
    }

    @Override
    public CxrCustomerAddress selectByAddress(Long customerId, String adress) {
        return iCxrCustomerAddressService.getBaseMapper().selectOne(new LambdaQueryWrapper<CxrCustomerAddress>()
            .eq(CxrCustomerAddress::getCxrCustomerId, customerId)
            .like(CxrCustomerAddress::getDetailDistributionAddress, adress)
            .last("limit 1"));
    }

    @Override
    public List<CxrCustomerAddress> selectHuiBoCustomerId(List<Long> ids) {
        List<CxrCustomerAddress> cxrCustomerAddressList = iCxrCustomerAddressService.getBaseMapper()
            .selectList(new LambdaQueryWrapper<CxrCustomerAddress>()
                .eq(CxrCustomerAddress::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .in(CxrCustomerAddress::getCxrCustomerId, ids)
                .eq(CxrCustomerAddress::getDefalutAccountAddress, "Y"));
        return cxrCustomerAddressList;
    }


    @Override
    public List<CxrCustomerAddress> queryCustomerAddressByIds(List<Long> addressIds) {

        return iCxrCustomerAddressService.lambdaQuery()
            .in(CxrCustomerAddress::getId, addressIds)
            .eq(CxrCustomerAddress::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            .list();
    }

    @Override
    public CxrCustomerChangeRecord checkCxrCustomerChangeRecord(Long customerAddressId) {
        return cxrCustomerChangeRecordService.getOne(new LambdaQueryWrapper<CxrCustomerChangeRecord>()
            .eq(CxrCustomerChangeRecord::getCustomerAddressId, customerAddressId)
            .eq(CxrCustomerChangeRecord::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            .like(CxrCustomerChangeRecord::getCreateTime, LocalDate.now())
        );
    }

    @Override
    public void deleteCxrCustomerChangeRecord(Long id) {
        cxrCustomerChangeRecordService.removeById(id);
    }

    @Override
    public Boolean saveCxrCustomerChangeRecord(CxrCustomerChangeRecord customerChangeRecord) {
        if (StrUtil.isNotEmpty(customerChangeRecord.getAmDistributionStatus())
            && ObjectUtil.equals(customerChangeRecord.getAmDistributionStatus(), SysYesNo.YES.getValue())
            && ObjectUtil.isEmpty(customerChangeRecord.getAmDistributionStartDeliveryTime())) {

            CxrAddressHistory addressHistory = getLastCxrAddressHistory(
                customerChangeRecord.getCustomerAddressId());

            if (ObjectUtil.isNotEmpty(addressHistory)) {
                customerChangeRecord.setAmDistributionStatus(addressHistory.getAmDistributionStatus());
                customerChangeRecord.setAmDistributionStartDeliveryTime(addressHistory.getAmSendDate());
                customerChangeRecord.setAmDistributionSuspendStartTime(addressHistory.getAmStopStartDate());
                customerChangeRecord.setAmDistributionSuspendEndTime(addressHistory.getAmStopEndDate());
                log.info("客户排奶参数丢失...{},{}", customerChangeRecord.getCustomerAddressId(),
                    addressHistory.getAmDistributionStatus());
            } else {
                customerChangeRecord.setAmDistributionStartDeliveryTime(LocalDate.now());
            }

        }

        if (StrUtil.isNotEmpty(customerChangeRecord.getPmDistributionStatus())
            && ObjectUtil.equals(customerChangeRecord.getPmDistributionStatus(), SysYesNo.YES.getValue())
            && ObjectUtil.isEmpty(customerChangeRecord.getPmDistributionStartDeliveryTime())) {

            CxrAddressHistory addressHistory = getLastCxrAddressHistory(
                customerChangeRecord.getCustomerAddressId());
            if (ObjectUtil.isNotEmpty(addressHistory)) {
                customerChangeRecord.setPmDistributionStatus(addressHistory.getPmDistributionStatus());
                customerChangeRecord.setPmDistributionStartDeliveryTime(addressHistory.getPmSendDate());
                customerChangeRecord.setPmDistributionSuspendStartTime(addressHistory.getPmStopStartDate());
                customerChangeRecord.setPmDistributionSuspendEndTime(addressHistory.getPmStopEndDate());
                log.info("客户排奶参数丢失...{},{}", customerChangeRecord.getCustomerAddressId(),
                    addressHistory.getPmDistributionStatus());
            } else {
                customerChangeRecord.setPmDistributionStartDeliveryTime(LocalDate.now());
            }

        }

        return cxrCustomerChangeRecordService.save(customerChangeRecord);
    }

    @Override
    public void updateChangeStatus(Long customerAddressId, int i) {
        iCxrCustomerAddressService.update(null,
            new LambdaUpdateWrapper<CxrCustomerAddress>().eq(CxrCustomerAddress::getId, customerAddressId)
                .set(CxrCustomerAddress::getChangeStatus, i));
    }

    @Override
    public void customerChangeRecord(LocalDate localDate) {

        List<CxrCustomerChangeRecord> records = cxrCustomerChangeRecordService.getBaseMapper()
            .selectList(new LambdaQueryWrapper<CxrCustomerChangeRecord>()
                .eq(CxrCustomerChangeRecord::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .eq(CxrCustomerChangeRecord::getEffectTime, localDate).orderByAsc(CxrCustomerChangeRecord::getId)
            );

        log.info(StrUtil.format("customerChangeRecord {}执行", localDate.toString()));
        if (CollectionUtil.isNotEmpty(records)) {
            LocalDate localDateTow = LocalDate.now().plusDays(2);
            List<Long> customerAddressId = records.stream().map(CxrCustomerChangeRecord::getCustomerAddressId)
                .collect(Collectors.toList());
            Map<Long, CxrCustomerChangeRecord> customerChangeRecordMap = cxrCustomerChangeRecordService.getBaseMapper()
                .selectList(new LambdaQueryWrapper<CxrCustomerChangeRecord>()
                    .in(CxrCustomerChangeRecord::getCustomerAddressId, customerAddressId)
                    .eq(CxrCustomerChangeRecord::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                    .eq(CxrCustomerChangeRecord::getEffectTime, localDateTow)
                ).stream().collect(Collectors.toMap(a -> a.getCustomerAddressId(), a -> a));

            List<CxrCustomerAddress> customerAddressList = new ArrayList<>();
            records.stream().forEach(item -> {
                CxrCustomerAddress customerAddress = new CxrCustomerAddress();
                CxrCustomerChangeRecord cxrCustomerChangeRecord = customerChangeRecordMap.get(
                    item.getCustomerAddressId());
                customerAddress.setAmDistributionInfo(item.getAmDistributionInfo());
                customerAddress.setAmDistributionStatus(item.getAmDistributionStatus());
                customerAddress.setAmDistributionStartDeliveryTime(item.getAmDistributionStartDeliveryTime());
                customerAddress.setAmDistributionSuspendStartTime(item.getAmDistributionSuspendStartTime());
                customerAddress.setAmDistributionSuspendEndTime(item.getAmDistributionSuspendEndTime());
                customerAddress.setIsShowAmDistribution(item.getIsShowAmDistribution());

                customerAddress.setPmDistributionInfo(item.getPmDistributionInfo());
                customerAddress.setPmDistributionStatus(item.getPmDistributionStatus());
                customerAddress.setPmDistributionStartDeliveryTime(item.getPmDistributionStartDeliveryTime());
                customerAddress.setPmDistributionSuspendStartTime(item.getPmDistributionSuspendStartTime());
                customerAddress.setPmDistributionSuspendEndTime(item.getPmDistributionSuspendEndTime());
                customerAddress.setIsShowPmDistribution(item.getIsShowPmDistribution());
                customerAddress.setChangeStatus(ObjectUtil.isEmpty(cxrCustomerChangeRecord) ? 0 : 1);
                customerAddress.setId(item.getCustomerAddressId());
                customerAddressList.add(customerAddress);
            });
            if (CollectionUtil.isNotEmpty(records)) {
                List<List<CxrCustomerAddress>> split = CollUtil.split(customerAddressList, 100);
                for (List<CxrCustomerAddress> addresses : split) {
                    iCxrCustomerAddressService.updateCustomerInfo(addresses);
                }
                List<Long> list = records.stream().map(CxrCustomerChangeRecord::getId).collect(Collectors.toList());
                cxrCustomerChangeRecordService.update(null, new LambdaUpdateWrapper<CxrCustomerChangeRecord>()
                    .in(CxrCustomerChangeRecord::getId, list)
                    .set(CxrCustomerChangeRecord::getDeleteStatus, DeleteStatus.DELETED.getValue())
                );
            }
        }


    }

    @Override
    public CxrCustomerChangeRecord getLastCustomerChangeRecord(Long id) {
        return cxrCustomerChangeRecordService.getBaseMapper()
            .selectOne(new LambdaQueryWrapper<CxrCustomerChangeRecord>()
                .eq(CxrCustomerChangeRecord::getCustomerAddressId, id)
                .eq(CxrCustomerChangeRecord::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .orderByDesc(CxrCustomerChangeRecord::getEffectTime).last("limit 1")
            );
    }

    @Override
    public void checkUpdataChangeCustomer(Long id) {
        iCxrCustomerService.checkUpdataChangeCustomer(id);
    }

    public Long distributionListRecordDaySum(CxrCustomerDistributionListRecord distributionListRecord,
                                             int day) {
        switch (day) {
            case 1:
                return distributionListRecord.getD1Sum();
            case 2:
                return distributionListRecord.getD2Sum();
            case 3:
                return distributionListRecord.getD3Sum();
            case 4:
                return distributionListRecord.getD4Sum();
            case 5:
                return distributionListRecord.getD5Sum();

            case 6:
                return distributionListRecord.getD6Sum();

            case 7:
                return distributionListRecord.getD7Sum();

            case 8:
                return distributionListRecord.getD8Sum();

            case 9:
                return distributionListRecord.getD9Sum();

            case 10:
                return distributionListRecord.getD10Sum();

            case 11:
                return distributionListRecord.getD11Sum();

            case 12:
                return distributionListRecord.getD12Sum();

            case 13:
                return distributionListRecord.getD13Sum();

            case 14:
                return distributionListRecord.getD14Sum();

            case 15:
                return distributionListRecord.getD15Sum();

            case 16:
                return distributionListRecord.getD16Sum();

            case 17:
                return distributionListRecord.getD17Sum();

            case 18:
                return distributionListRecord.getD18Sum();

            case 19:
                return distributionListRecord.getD19Sum();

            case 20:
                return distributionListRecord.getD20Sum();

            case 21:
                return distributionListRecord.getD21Sum();

            case 22:
                return distributionListRecord.getD22Sum();

            case 23:
                return distributionListRecord.getD23Sum();

            case 24:
                return distributionListRecord.getD24Sum();

            case 25:
                return distributionListRecord.getD25Sum();

            case 26:
                return distributionListRecord.getD26Sum();

            case 27:
                return distributionListRecord.getD27Sum();

            case 28:
                return distributionListRecord.getD28Sum();

            case 29:
                return distributionListRecord.getD29Sum();

            case 30:
                return distributionListRecord.getD30Sum();

            case 31:
                return distributionListRecord.getD31Sum();

        }
        return 0l;
    }

    @Override
    public CxrEmployee getMatchSiteAndEmployeByAddr(Long siteId, Long customerId, String fullAddress) {
        CxrEmployee cxrEmployee = iCxrCustomerAddressService.getMatchSiteAndEmployeByAddr(siteId, customerId,
            fullAddress);
        return cxrEmployee;
    }

    @Override
    public void updateCustomerAddressDelivery(LocalDate date, Long addressId) {
        iCxrCustomerAddressService.updateCustomerAddressDelivery(date, addressId);
    }

    @Override
    public void updateCustomerAddressDelivery(CustomerAddressMilkDistributionInfo distributionInfo, Long addressId) {
        iCxrCustomerAddressService.updateCustomerAddressDelivery(distributionInfo, addressId);
    }

    @Override
    public CustomerAddressMilkDistributionDTO builderAddressDeliveryInfo(LocalDate startDeliveryDate, Long addressId) {
        return iCxrCustomerAddressService.mergeCustomerAddressDelivery(startDeliveryDate, addressId);
    }

   /* @Override
    public void updateAddressStock(Long addressId) {

        CxrCustomerAddress cxrCustomerAddress = iCxrCustomerAddressService.getById(addressId);
        if (cxrCustomerAddress != null){
            Integer totalQuantity = cxrCustomerAddress.getTotalQuantity();
            Integer addressStock = cxrCustomerAddress.getAddressStock();
            log.info("地址库存{}，totalQuantity={},addressStock={}",addressId,totalQuantity, addressStock);
            if (totalQuantity == null || addressStock == null){
                return;
            }

            if (totalQuantity.intValue() == addressStock.intValue()){
                boolean update = iCxrCustomerAddressService.lambdaUpdate()
                    .set(CxrCustomerAddress::getDeleteStatus,DeleteStatus.DELETED.getValue())
                    .set(CxrCustomerAddress::getDeleteBy, 1L)
                    .set(CxrCustomerAddress::getDeleteByName, "system")
                    .set(CxrCustomerAddress::getDeleteByType, DeviceType.TIKTOK_APPLET.getDevice())
                    .set(CxrCustomerAddress::getDeleteTime, new Date())
                    .eq(CxrCustomerAddress::getId, addressId)
                    .update();
                log.info("更新地址库存 {} 结果：{}", addressId,update);
            }
        }
    }*/

    public void updateAddressStock(Long addressId) {

        CxrCustomerAddress cxrCustomerAddress = iCxrCustomerAddressService.getById(addressId);
        if (cxrCustomerAddress != null) {

            String defaultAccountAddress = cxrCustomerAddress.getDefalutAccountAddress();
            if (SysYesNo.YES.getValue().equals(defaultAccountAddress)) {
                return;
            }

            if (cxrCustomerAddress.getFirstRoadWayId() != null) {
                return;
            }

            boolean update = iCxrCustomerAddressService.lambdaUpdate()
                .set(CxrCustomerAddress::getDeleteStatus, DeleteStatus.DELETED.getValue())
                .set(CxrCustomerAddress::getDeleteBy, 1L)
                .set(CxrCustomerAddress::getDeleteByName, "system")
                .set(CxrCustomerAddress::getDeleteByType, DeviceType.TIKTOK_APPLET.getDevice())
                .set(CxrCustomerAddress::getDeleteTime, new Date())
                .eq(CxrCustomerAddress::getId, addressId)
                .update();
            log.info("更新地址库存 {} 结果：{}", addressId, update);
        }
    }

    public boolean queryAddressExistRoadWay(Long addressId) {
        CxrCustomerAddress cxrCustomerAddress = iCxrCustomerAddressService.getById(addressId);
        if (cxrCustomerAddress != null) {

            if (cxrCustomerAddress.getFirstRoadWayId() != null) {
                return true;
            }

            CxrRoadWay roadWay = cxrRoadWayMapper.selectOne(Wrappers.<CxrRoadWay>lambdaQuery()
                .select(CxrRoadWay::getId)
                .eq(CxrRoadWay::getCxrCustomerAddressId, addressId)
                .eq(CxrRoadWay::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .last("limit 1"));
            if (roadWay != null) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Boolean customerSeparateCchangeAddressSave(CxrCustomerChangeRecord customerChangeRecord) {
        return cxrCustomerChangeRecordService.customerSeparateCchangeAddressSave(customerChangeRecord);
    }

    private CxrAddressHistory getLastCxrAddressHistory(Long customerAddressId) {
        CxrAddressHistory historyService = cxrAddressHistoryService.getOne(new LambdaQueryWrapper<CxrAddressHistory>()
            .eq(CxrAddressHistory::getCxrCustomerAddressId, customerAddressId)
            .eq(CxrAddressHistory::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            .orderByDesc(CxrAddressHistory::getCreateTime)
            .last("limit 1")
        );
        return historyService;
    }

}
