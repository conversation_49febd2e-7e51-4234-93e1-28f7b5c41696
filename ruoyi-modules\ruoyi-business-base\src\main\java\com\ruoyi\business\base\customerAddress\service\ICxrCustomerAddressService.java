package com.ruoyi.business.base.customerAddress.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.business.api.domain.vo.CxrSaleProductVo;
import com.ruoyi.business.base.api.domain.CxrCustomer;
import com.ruoyi.business.base.api.domain.CxrCustomerAddress;
import com.ruoyi.business.base.api.domain.CxrEmployee;
import com.ruoyi.business.base.api.domain.DeliverySiteDTO;
import com.ruoyi.business.base.api.domain.bo.CxrAddressBo;
import com.ruoyi.business.base.api.domain.bo.CxrCustomerAddressBo;
import com.ruoyi.business.base.api.domain.dto.CxrCustomerAddressDTO;
import com.ruoyi.business.base.api.domain.json.CustomerAddressMilkDistributionDTO;
import com.ruoyi.business.base.api.domain.json.CustomerAddressMilkDistributionInfo;
import com.ruoyi.business.base.api.domain.vo.CxrAddressVo;
import com.ruoyi.business.base.customerAddress.domain.bo.AddressBo;
import com.ruoyi.business.base.customerAddress.domain.bo.CxrCustomerAddrBo;
import com.ruoyi.business.base.customerAddress.domain.bo.CxrCustomerAddressQueryBo;
import com.ruoyi.business.base.customerAddress.domain.vo.CustomerAddressDetailVo;
import com.ruoyi.business.base.customerAddress.domain.vo.CxrCustomerAddressListVo;
import com.ruoyi.business.base.customerAddress.domain.vo.CxrCustomerAddressQueryVo;
import com.ruoyi.business.base.customerAddress.domain.vo.CxrCustomerAddressVo;
import com.ruoyi.common.core.domain.TableDataInfo;
import com.ruoyi.common.mybatis.core.page.PageQuery;
import com.ruoyi.common.mybatis.core.page.PageTableDataInfo;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * 客户地址Service接口
 *
 * <AUTHOR>
 * @date 2022-07-02
 */
public interface ICxrCustomerAddressService extends IService<CxrCustomerAddress> {


    /**
     * 分页查询《客户地址》
     *
     * @param cxrCustomerAddressBo 实例化Bo对象封装的查询参数
     * @param pageQuery            分页参数
     * @return
     */
    PageTableDataInfo<CxrCustomerAddressListVo> page(CxrCustomerAddressBo cxrCustomerAddressBo, PageQuery pageQuery);


    /**
     * 详细查询《客户地址》
     *
     * @param id 主键
     * @return
     */
    com.ruoyi.business.base.customerAddress.domain.vo.CxrCustomerAddressVo detail(Long id);


    /**
     * 添加数据《客户地址》
     *
     * @param cxrCustomerAddressBo 实例化Bo对象封装的添加参数
     * @return
     */
    Boolean add(CxrCustomerAddrBo cxrCustomerAddressBo);

//
//    /**
//     * 修改数据《客户地址》
//     *
//     * @param cxrCustomerAddressBo 实例化Bo对象封装的修改参数
//     * @return
//     */
//    Boolean edit(CxrCustomerAddressBo cxrCustomerAddressBo);


    /**
     * 删除数据《客户地址》
     *
     * @param idSet 主键集合
     * @return
     */
    Boolean remove(Set<Long> idSet);


    TableDataInfo<CxrCustomer> pageWithQuarters(Object page, CxrCustomerAddressBo cxrCustomerAddress);

    /**
     * 根据 员工id 获取客户地址id
     *
     * @param id
     * @return
     */
    List<Long> getListByemId(Long id);


    //新增转拨
    PageTableDataInfo<CxrCustomerAddressVo> listAddPeople(
        CxrCustomerAddressBo cxrCustomerAddressBo, PageQuery pageQuery);


    List<CxrCustomerAddress> queryByIds(List<CxrCustomerAddress> addresses);

    /**
     * 更新配送状态
     *
     * @param now
     */
    void updateAddressEnableStatus(LocalDate now);

    /**
     * 根据地址id查询商品
     *
     * @param id
     * @return
     */
    List<CxrSaleProductVo> selectProductByAddressId(Long id);

    //查询用户的所有地址
    List<CxrAddressVo> getAddressids(CxrAddressBo bo);

    List<CxrAddressVo> checkmilkDistributionDetail(Long id, String del);

    int updateChangeMilk(CxrAddressBo bo);

    int updateAmChangeSendOrStop(CxrAddressBo bo);

    int updatePmChangeSendOrStop(CxrAddressBo bo);

    List<CxrCustomerAddress> queryGetByIds(CxrAddressBo bo);

    List<CxrCustomerAddress> getCustomerAddresses(Long id);

    List<CxrCustomerAddress> getCustomerAddressesByCustomerId(Long id);

    List<CxrCustomerAddress> getCustomerAddressPhone(String receiverPhone);

    List<CxrCustomerAddressDTO> cxrCustomerAddressDTOList(Long customerId);

    int updateByCustomerId(Long customerId);

    String queryDetailAddressByCustomerId(Long CustomerId);

    boolean updateCustomerReceiverPhone(CxrAddressBo bo);

    int updateCustomerInfo(List<CxrCustomerAddress> addresses);

    int updateAmChangeSendOrStopChangeRecord(CxrAddressBo bo);

    int updatePmChangeSendOrStopChangeCustomer(CxrAddressBo bo);

    void getCustomerAddressLoglat(AddressBo bo);

    List<CustomerAddressDetailVo> getByCustomerId();

    CxrCustomerAddress addGetLngLat(CxrCustomerAddress address);

    CxrEmployee getMatchSiteAndEmployeByAddr(Long siteId, Long customerId, String fullAddress);

    PageTableDataInfo<CxrCustomerAddressQueryVo> customerPageQuery(CxrCustomerAddressQueryBo bo);

    void updateCustomerAddressDelivery(LocalDate startDeliveryDate, Long addressId);

    void updateCustomerAddressDelivery(CustomerAddressMilkDistributionInfo distributionInfo, Long addressId);

    CustomerAddressMilkDistributionDTO mergeCustomerAddressDelivery(LocalDate startDeliveryDate, Long addressId);

    CustomerAddressMilkDistributionInfo builderAddressDeliveryInfo(LocalDate startDeliveryDate, Long addressId);

    int updateStockById(Long addressId, Long qty);

    List<DeliverySiteDTO> deliverySiteQuery(Long customerId, String customerPhone);
}
