package com.ruoyi.order.manager.strategy.behavior;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.business.base.api.enums.OrderTypeEnums;
import com.ruoyi.common.core.domain.LoginInfo;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.validator.AbstractAssert;
import com.ruoyi.common.rocketmq.constant.message.SendMessageReturnOrderConstant;
import com.ruoyi.common.rocketmq.constant.order.OrderConstant;
import com.ruoyi.common.rocketmq.utils.MqUtil;
import com.ruoyi.common.satoken.utils.LoginUtil;
import com.ruoyi.common.satoken.utils.helper.LoginHelper;
import com.ruoyi.order.api.domain.bo.TransactionRecordMatchingBo;
import com.ruoyi.order.api.enums.OrderAuditStatusEnums;
import com.ruoyi.order.api.enums.OrderTypeHanderEnums;
import com.ruoyi.order.api.enums.PayStatusEnums;
import com.ruoyi.order.common.domain.vo.CxrUserOrderVO;
import com.ruoyi.order.common.entity.CxrUserOrder;
import com.ruoyi.order.manager.service.CxrUserOrderService;
import com.ruoyi.order.manager.strategy.AbstractOrderHandler;
import com.ruoyi.system.api.model.LoginUser;
import io.seata.spring.annotation.GlobalTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

/**
 * Description:
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/8/6 11:16
 */
@GlobalTransactional(rollbackFor = Exception.class)
@Service
public class CxrUserOrderBehaviorImpl extends AbstractCxrUserOrderBehavior {

    @Autowired
    private Map<String, AbstractOrderHandler> convertToUserOrderMap;

    @Autowired
    private MqUtil mqUtil;

    /**
     * 订单添加
     *
     * @param obj
     * @param orderTypeEnums
     * @return
     */
    @Override
    public Long executeOrderAddOrUpdate(Object obj, OrderTypeEnums orderTypeEnums) {

        String beanName = OrderTypeHanderEnums.queryBeanName(orderTypeEnums.getValue());
        AbstractOrderHandler abstractConvertToUserOrder = convertToUserOrderMap.get(beanName);
        // 检查参数
        CxrUserOrder cxrUserOrder = abstractConvertToUserOrder.executeConvert(obj);
        if (ObjectUtil.isNull(cxrUserOrder)) {
            return null;
        }
        if (orderTypeEnums == OrderTypeEnums.RETURN_ORDER) {
            mqUtil.sendSyncMessage(SendMessageReturnOrderConstant.MESSAGE_CENTER_CXR_CUSTOMER_RETURN_ORDER_TOPIC, SendMessageReturnOrderConstant.CUSTOMER_CREATE_RETURN_ORDER_MSG_TAG,
                cxrUserOrder.getId() + "");
            return cxrUserOrder.getId();
        }

        boolean flag = super.orderSaveOrUpdate(cxrUserOrder);
        //目前需求新增不需要处理，之后需要再开放
        //abstractConvertToUserOrder.todoCxrActivity(cxrUserOrder.getId(), obj);
        setMQ(flag, cxrUserOrder);
        return flag ? cxrUserOrder.getId() : null;
    }

    /**
     * 匹配收钱吧流水
     */
    public void setMQ(boolean flag, CxrUserOrder userOrder) {
        String merchantOrderNo = userOrder.getMerchantOrderNo();
        if (flag && StringUtils.isNotBlank(merchantOrderNo)) {
            OrderTypeEnums typeEnums = OrderTypeEnums.getType(userOrder.getOrderType());
            if (OrderTypeEnums.NEW_ORDER == typeEnums
                || OrderTypeEnums.CONTRACT_ORDER == typeEnums) {
                TransactionRecordMatchingBo bo =
                    TransactionRecordMatchingBo.builder()
                        .orderId(userOrder.getId())
                        .merchantOrderNo(merchantOrderNo)
                        .build();
                mqUtil.sendSyncMessage(
                    OrderConstant.ORDER_TOPIC,
                    OrderConstant.ORDER_SQB_TRANSACTION_MATCHING_TAG,
                    JSONObject.toJSONString(bo));
            }
        }
    }

    /**
     * 订单更新 新增续 合(后台端,配送端) 退 -- 公用
     *
     * @param obj
     * @param orderTypeEnums
     * @return
     */
    @Override
    public boolean executeOrderUpdate(Object obj, OrderTypeEnums orderTypeEnums) {
        /** 获取bean 新 增 续 */
        String beanName = OrderTypeHanderEnums.queryBeanName(orderTypeEnums.getValue());
        AbstractOrderHandler abstractConvertToUserOrder = convertToUserOrderMap.get(beanName);
        /** 校验类型 并且校验审核状态 已审核返回代理和订单id */
        CxrUserOrder cxrUserOrder = abstractConvertToUserOrder.executeUpdateOrder(obj);
        return super.orderUpdates(obj, cxrUserOrder);
    }

    /**
     * 订单审核
     *
     * @param obj
     * @param orderTypeEnums
     * @return
     */
    @Override
    public boolean executeOrderAudit(Object obj, OrderTypeEnums orderTypeEnums) {
        String beanName = OrderTypeHanderEnums.queryBeanName(orderTypeEnums.getValue());
        AbstractOrderHandler abstractConvertToUserOrder = convertToUserOrderMap.get(beanName);
        CxrUserOrder cxrUserOrder = abstractConvertToUserOrder.executeUpdateOrder(obj);
        return super.orderUpdate(obj, cxrUserOrder);
    }

    /**
     * 订单审核处理
     *
     * @param id
     */
    @Override
    public Boolean executeCxrUserOrderAudit(Long id, boolean systemFlag, Integer auditType, String refundNoAuditReasons) {

        // 获取订单
        CxrUserOrderService cxrUserOrderService = SpringUtils.getBean(CxrUserOrderService.class);
        CxrUserOrder data = cxrUserOrderService.getById(id);
        AbstractAssert.isTrue(OrderAuditStatusEnums.AUDITED.getValue() == data.getAuditStatus().intValue(), "该订单已经审核过,请勿重复审核");
        /** 设置信息 */
        LoginUser loginUser;
        if (systemFlag) {
            loginUser = new LoginUser();
            loginUser.setUserId(1L);
            loginUser.setUserName("admin");
        } else {
            loginUser = LoginHelper.getLoginUser();
        }
        CxrUserOrder cxrUserOrder = new CxrUserOrder();
        cxrUserOrder.setId(id);
        cxrUserOrder.setAuditBy(loginUser.getUserId());
        cxrUserOrder.setAuditByName(loginUser.getUserName());
        cxrUserOrder.setAuditTime(new Date());
        cxrUserOrder.setAuditCount(data.getAuditCount() + 1);
        cxrUserOrder.setAuditStatus(OrderAuditStatusEnums.AUDITED.getValue());
        if (ObjectUtil.isNotEmpty(auditType) && auditType == 2) {
            cxrUserOrder.setRefundNoAuditReasons(refundNoAuditReasons);
            cxrUserOrder.setAuditStatus(OrderAuditStatusEnums.TURN_DOWN.getValue());
            return cxrUserOrderService.updateById(cxrUserOrder);
        }

        /** 获取bean */
        String beanName = OrderTypeHanderEnums.queryBeanName(data.getOrderType());
        AbstractOrderHandler abstractConvertToUserOrder = convertToUserOrderMap.get(beanName);
        // 用户库存明细
        abstractConvertToUserOrder.executeCxrUserOrderAudit(id);

        if (ObjectUtil.equals(data.getOrderType(), OrderTypeEnums.RETURN_ORDER.getValue())) {
            cxrUserOrder.setOrderDate(cxrUserOrder.getAuditTime());
        } else {
            if (data.getAuditCount().intValue() == 0) {
                // 退订单 审核时间 赋值为订单时间
                cxrUserOrder.setOrderDate(cxrUserOrder.getAuditTime());
            } else {
                cxrUserOrder.setOrderDate(data.getOrderDate());
            }
        }

        return super.orderUpdate(null, cxrUserOrder);
    }

    /**
     * 订单审核处理
     *
     * @param id
     */
    @Override
    public Boolean executeCxrUserOrderAuditSubLock(Long id, Boolean subLock) {
        // 获取订单
        CxrUserOrderService cxrUserOrderService = SpringUtils.getBean(CxrUserOrderService.class);
        CxrUserOrder data = cxrUserOrderService.getById(id);
        if (OrderAuditStatusEnums.AUDITED.getValue() == data.getAuditStatus().intValue()) {
            throw new ServiceException("该订单已经审核过,请勿重复审核");
        }

        AbstractOrderHandler abstractOrderHandler = convertToUserOrderMap.get(
            OrderTypeHanderEnums.queryBeanName(data.getOrderType()));
        abstractOrderHandler.cxrUserOrderAudit(id, subLock);
        Date date = new Date();
        return cxrUserOrderService.lambdaUpdate()
            .set(CxrUserOrder::getAuditStatus, OrderAuditStatusEnums.AUDITED.getValue())
            .set(CxrUserOrder::getAuditBy, 1L)
            .set(CxrUserOrder::getAuditByName, "admin")
            .set(CxrUserOrder::getAuditTime, date)
            .set(CxrUserOrder::getAuditCount, data.getAuditCount() + 1)
            .set(CxrUserOrder::getOrderDate, data.getAuditCount().intValue() == 0 ? date : data.getOrderDate())
            .eq(CxrUserOrder::getId, id)
            .update();
    }

    /**
     * 订单审核处理
     *
     * @param id
     */
    @Override
    public CxrUserOrderVO executeOrderDetail(Long id) {
        CxrUserOrderService cxrUserOrderService = SpringUtils.getBean(CxrUserOrderService.class);
        CxrUserOrder data = cxrUserOrderService.getById(id);

        String beanName = OrderTypeHanderEnums.queryBeanName(data.getOrderType());
        AbstractOrderHandler abstractConvertToUserOrder = convertToUserOrderMap.get(beanName);
        return abstractConvertToUserOrder.executeOrderDetail(id);
    }

    /**
     * 删除订单
     *
     * @param id
     * @return
     */
    @Override
    public boolean deleteOrder(Long id) {
        CxrUserOrderService cxrUserOrderService = SpringUtils.getBean(CxrUserOrderService.class);
        CxrUserOrder data = cxrUserOrderService.getById(id);
        LoginInfo loginUser = LoginUtil.getLoginUser();
        if (OrderAuditStatusEnums.WAIT_AUDIT.getValue()
            == data.getAuditStatus().intValue()) { // 未审核可以删除
            String beanName = OrderTypeHanderEnums.queryBeanName(data.getOrderType());
            AbstractOrderHandler abstractConvertToUserOrder = convertToUserOrderMap.get(beanName);
            if (ObjectUtil.isNotNull(abstractConvertToUserOrder)) {
                // 删除之前执行的逻辑
                abstractConvertToUserOrder.executeDeleteBefore(id);
            }
            //修改售后订单的状态
            CxrUserOrder cxrUserOrder = cxrUserOrderService.getBaseMapper().selectById(id);
            if (ObjectUtil.isNotEmpty(cxrUserOrder) && ObjectUtil.isNotEmpty(cxrUserOrder.getPayOrderId())) {
                cxrUserOrderService.updateByAfterSaleOrderStatus(cxrUserOrder.getId());
            }
            //清空主订单里面的抖音单号
            cxrUserOrderService.updateTiktokNo(id);
            return cxrUserOrderService.del(id, loginUser);
        } else if (PayStatusEnums.WAIT_PAY.getValue() == data.getPayStatus().intValue()) {
            String beanName = OrderTypeHanderEnums.queryBeanName(data.getOrderType());
            AbstractOrderHandler abstractConvertToUserOrder = convertToUserOrderMap.get(beanName);
            if (ObjectUtil.isNotNull(abstractConvertToUserOrder)) {
                // 删除之前执行的逻辑
                abstractConvertToUserOrder.executeDeleteBefore(id);
            }
            return cxrUserOrderService.del(id, loginUser);
        }
        return false;
    }

    @Override
    public String disributionContractOrderAdd(Object obj, OrderTypeEnums orderTypeEnums) {
        String beanName = OrderTypeHanderEnums.queryBeanName(orderTypeEnums.getValue());
        AbstractOrderHandler abstractConvertToUserOrder = convertToUserOrderMap.get(beanName);
        // 检查参数
        CxrUserOrder cxrUserOrder = abstractConvertToUserOrder.executeConvert(obj);
        if (ObjectUtil.isNull(cxrUserOrder)) {
            throw new ServiceException("参数错误");
            //            return false;
        }

        boolean flag = super.orderSaveOrUpdate(cxrUserOrder);
        setMQ(flag, cxrUserOrder);
        return cxrUserOrder.getOrderNo();
    }

    @Override
    public void auditAfterChange(Object obj, OrderTypeEnums orderTypeEnums) {
        String beanName = OrderTypeHanderEnums.queryBeanName(orderTypeEnums.getValue());
        AbstractOrderHandler abstractConvertToUserOrder = convertToUserOrderMap.get(beanName);
        abstractConvertToUserOrder.executeAuditAfterChange(obj);
    }

    @Override
    public boolean disributionUpdate(Object obj, OrderTypeEnums type) {
        String beanName = OrderTypeHanderEnums.queryBeanName(type.getValue());

        AbstractOrderHandler abstractOrderHandler = convertToUserOrderMap.get(beanName);
        CxrUserOrder cxrUserOrder = abstractOrderHandler.disributionOrderUpdate(obj);
        return super.orderUpdates(obj, cxrUserOrder);
    }

    public boolean executeCxrUserOrderReverseAudit(CxrUserOrder order) {

        String beanName = OrderTypeHanderEnums.queryBeanName(order.getOrderType());
        AbstractOrderHandler abstractConvertToUserOrder = convertToUserOrderMap.get(beanName);
        // 用户库存明细
        abstractConvertToUserOrder.executeCxrUserOrderReverseAudit(order);
        return true;
    }
}
