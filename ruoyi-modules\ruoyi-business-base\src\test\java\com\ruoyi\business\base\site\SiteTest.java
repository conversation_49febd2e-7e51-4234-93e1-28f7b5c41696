package com.ruoyi.business.base.site;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.business.base.RuoYiBusinessBaseApplication;
import com.ruoyi.business.base.api.config.RoomBoradAccountsDate;
import com.ruoyi.business.base.api.dubbo.RemoteCxrRoadStatistisService;
import com.ruoyi.business.base.api.dubbo.RemoteCxrRoomBoardAccounts;
import com.ruoyi.business.base.api.dubbo.RemoteSiteService;
import com.ruoyi.business.base.api.utils.huibo.HuiBoSignUtil;
import com.ruoyi.business.base.customer.service.ICxrCustomerService;
import com.ruoyi.business.base.cxrSiteDayInjobEmployeeRecord.service.CxrSiteDayInjobEmployeeRecordService;
import com.ruoyi.business.base.employee.service.ICxrEmployeeService;
import com.ruoyi.business.base.freshMilkAssessment.domain.vo.CxrFreshMilkAssessmentDetailListVO;
import com.ruoyi.business.base.freshMilkAssessment.service.ICxrFreshMilkAssessmentService;
import com.ruoyi.business.base.report.service.ICxrGroupEmployeeAchievementReportService;
import com.ruoyi.common.rocketmq.constant.order.OrderConstant;
import com.ruoyi.common.rocketmq.utils.MqUtil;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <p>Description: </p>
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/10/25 09:52
 **/
@SpringBootTest(classes = RuoYiBusinessBaseApplication.class)
public class SiteTest {

    @DubboReference
    private RemoteSiteService remoteSiteService;

    @DubboReference(timeout = 120000)
    private RemoteCxrRoomBoardAccounts remoteCxrRoomBoardAccounts;
    @Autowired
    private ICxrCustomerService iCxrCustomerService;
    @Autowired
    private ICxrEmployeeService iCxrEmployeeService;
    @Autowired
    private RoomBoradAccountsDate roomBoradAccountsDate;

    @Autowired
    private ICxrFreshMilkAssessmentService freshMilkAssessmentService;

    @Autowired
    private  ICxrGroupEmployeeAchievementReportService employeeAchievementReportService;

    @Autowired
    private MqUtil mqUtil;


    @Autowired
    private RocketMQTemplate rocketMQTemplate;


    @Autowired
    private CxrSiteDayInjobEmployeeRecordService cxrSiteDayInjobEmployeeRecordService;

    @DubboReference
    private RemoteCxrRoadStatistisService remoteCxrRoadStatistisService;

//    huibo:
//    conf:
//    appKey: 272660
//    sellerNick: 广州醇鲜然乳业有限公司
//    brandId: 123456789000378189
//    uoloadCustomerUrl: https://api.jkcrm.cn/cloud/member/regAndBind
//    secret : 879e0b8d9f0d0a80

    private final String huiBoQuery = "https://api.jkcrm.cn/api/customLabel/queryLabelListInfo?app_key"
        + "={}&&seller_nick={}&sign={}&timestamp={}";
    private final String huiBoAppKey = "272660";
    private final String huiBoSellerNick = "广州醇鲜然乳业有限公司";
    private final String huiBoSecret = "879e0b8d9f0d0a80";

    @Test
    public void demo1() throws Exception {
        long time = System.currentTimeMillis();
        Map<String, Object> map = new HashMap<>();
        map.put("app_key", huiBoAppKey);
        map.put("seller_nick", huiBoSellerNick);
        map.put("timestamp", time);

        //参数
        Map<String, Object> params = new HashMap<>();
        params.put("labelId", 4284);
        List list = new ArrayList();
        list.add(6775);
        params.put("labelValue", list);
        params.put("pageNumber", 1);
        params.put("pageSize", 10);

        StringBuffer sb = new StringBuffer();
        sb.append(huiBoAppKey)
            .append(huiBoSellerNick)
            .append(time)
            .append(JSONUtil.toJsonStr(params))
            .append(huiBoSecret);
        String sign = HuiBoSignUtil.getSign(sb.toString());
        map.put("sign", sign);
        String format = StrUtil.format(huiBoQuery, huiBoAppKey, huiBoSellerNick, sign, time);
        HttpResponse execute = HttpRequest.post(format)
            .body(JSONUtil.toJsonStr(params)).execute();
        String body = execute.body();
        System.out.println(body);

    }

    @Test
    public void test8() {
        cxrSiteDayInjobEmployeeRecordService.statisticsDayInjobEmployeeRecord();
        try {
            TimeUnit.SECONDS.sleep(60);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }


    @Test
    public void test2() {
        mqUtil.sendSyncMessage(OrderConstant.ORDER_TOPIC, OrderConstant.LONG_MILK_STOCK_TAG, 1585808358001528834L + "");
    }


    /**
     * 发送线上mq     可以群发
     */
    @Test
    public void test() {

        String[] ls = {};

        List<List<String>> split = CollectionUtil.split(Arrays.asList(ls), 10);

        for (List<String> s : split) {
            Map<String, Object> map = new HashMap<>();

            map.put("key", "key");
            map.put("messageBody", JSONUtil.toJsonStr(s));
            map.put("tag", "order_employees_tag");
            map.put("topic", "order_topic");

            HttpResponse execute = HttpRequest
                .post("http://119.23.226.37:9080/topic/sendTopicMessage.do")
                .body(JSONUtil.toJsonStr(map))
                .execute();
            System.out.println(JSONUtil.toJsonStr(execute.body()));
        }

    }

    @Test
    public void test3() {
        CxrFreshMilkAssessmentDetailListVO detailListVO = freshMilkAssessmentService.staffDetail(1768094975055134726L);
        System.out.println(JSONObject.toJSONString(detailListVO));
    }

    @Test
    public void test4() {
//        employeeAchievementReportService.calculateMembers(LocalDate.parse("2024-07-01"));
        employeeAchievementReportService.calculateMembers2(LocalDate.parse("2024-07-01"));
    }

    @Test
    public void test5() {
        remoteCxrRoadStatistisService.roadStatistis(LocalDate.now());
    }

}
