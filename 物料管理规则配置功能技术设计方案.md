# 物料管理规则配置功能技术设计方案

## 1. 功能描述

### 1.1 业务背景
在现有物料管理功能基础上，增加灵活的规则配置机制，支持不同站点使用不同的物料管理规则，提高系统的灵活性和适应性。

### 1.2 核心功能
- **主规则管理**：定义全局性的物料管理规则，作为默认规则应用于所有站点
- **子规则管理**：针对特定站点的个性化规则，可覆盖主规则的部分配置
- **站点规则配置**：支持多站点选择，灵活配置规则适用范围
- **规则优先级处理**：子规则优先于主规则，实现规则的层次化管理
- **规则动态生效**：支持规则的实时更新和生效

### 1.3 规则类型
- **申请审批规则**：物料申请的审批流程、审批人配置、审批阈值等
- **库存管理规则**：库存预警阈值、最大最小库存限制、盘点周期等
- **成本控制规则**：单价限制、预算控制、成本分摊规则等
- **权限控制规则**：物料使用权限、操作权限、数据可见性等
- **供应商规则**：供应商选择限制、采购渠道控制等

## 2. 系统架构设计

### 2.1 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    物料管理规则配置系统                          │
├─────────────────────────────────────────────────────────────┤
│  表现层 (Presentation Layer)                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐│
│  │  规则配置管理界面  │  │  规则应用界面    │  │  规则监控界面    ││
│  └─────────────────┘  └─────────────────┘  └─────────────────┘│
├─────────────────────────────────────────────────────────────┤
│  业务层 (Business Layer)                                    │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐│
│  │  规则配置服务    │  │  规则引擎服务    │  │  规则应用服务    ││
│  └─────────────────┘  └─────────────────┘  └─────────────────┘│
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐│
│  │  规则缓存服务    │  │  规则验证服务    │  │  规则通知服务    ││
│  └─────────────────┘  └─────────────────┘  └─────────────────┘│
├─────────────────────────────────────────────────────────────┤
│  数据层 (Data Layer)                                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐│
│  │  规则数据存储    │  │  缓存存储        │  │  日志存储        ││
│  └─────────────────┘  └─────────────────┘  └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

### 2.2 模块关系图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   主规则模块     │    │   子规则模块     │    │  站点关联模块    │
│                │    │                │    │                │
│ - 主规则CRUD    │    │ - 子规则CRUD    │    │ - 站点选择      │
│ - 规则模板      │    │ - 规则继承      │    │ - 关联管理      │
│ - 全局配置      │    │ - 覆盖逻辑      │    │ - 生效时间      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   规则引擎模块   │
                    │                │
                    │ - 规则解析      │
                    │ - 规则执行      │
                    │ - 优先级处理    │
                    │ - 结果合并      │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  物料申请模块    │    │  库存管理模块    │    │  成本控制模块    │
│                │    │                │    │                │
│ - 申请流程      │    │ - 库存监控      │    │ - 预算控制      │
│ - 审批规则      │    │ - 预警机制      │    │ - 成本分析      │
│ - 权限控制      │    │ - 盘点规则      │    │ - 价格管理      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 3. 数据库设计

### 3.1 主要数据表结构

#### 3.1.1 物料规则表 (cxr_material_rule)
```sql
CREATE TABLE cxr_material_rule (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    rule_code VARCHAR(50) NOT NULL UNIQUE COMMENT '规则编码',
    rule_name VARCHAR(100) NOT NULL COMMENT '规则名称',
    rule_type VARCHAR(50) NOT NULL COMMENT '规则类型(APPROVAL/INVENTORY/COST/PERMISSION/SUPPLIER)',
    rule_level VARCHAR(20) NOT NULL COMMENT '规则层级(MASTER:主规则/SUB:子规则)',
    parent_rule_id BIGINT COMMENT '父规则ID(子规则关联主规则)',
    rule_description TEXT COMMENT '规则描述',
    rule_config JSON NOT NULL COMMENT '规则配置(JSON格式)',
    status TINYINT DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
    priority INT DEFAULT 0 COMMENT '优先级(数值越大优先级越高)',
    effective_date DATETIME COMMENT '生效时间',
    expire_date DATETIME COMMENT '失效时间',
    create_by BIGINT COMMENT '创建人',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by BIGINT COMMENT '更新人',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    delete_status TINYINT DEFAULT 0 COMMENT '删除状态(0:未删除 1:已删除)',
    INDEX idx_rule_type (rule_type),
    INDEX idx_rule_level (rule_level),
    INDEX idx_parent_rule_id (parent_rule_id),
    FOREIGN KEY (parent_rule_id) REFERENCES cxr_material_rule(id)
) COMMENT '物料规则表';
```

#### 3.1.2 规则站点关联表 (cxr_material_rule_site_relation)
```sql
CREATE TABLE cxr_material_rule_site_relation (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    rule_id BIGINT NOT NULL COMMENT '规则ID',
    site_id BIGINT NOT NULL COMMENT '站点ID',
    effective_date DATETIME COMMENT '生效时间',
    expire_date DATETIME COMMENT '失效时间',
    create_by BIGINT COMMENT '创建人',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by BIGINT COMMENT '更新人',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    delete_status TINYINT DEFAULT 0 COMMENT '删除状态',
    UNIQUE KEY uk_rule_site (rule_id, site_id),
    INDEX idx_site_id (site_id),
    FOREIGN KEY (rule_id) REFERENCES cxr_material_rule(id),
    FOREIGN KEY (site_id) REFERENCES cxr_site(id)
) COMMENT '规则站点关联表';
```

### 3.2 表结构设计说明

#### 3.2.1 统一规则表设计优势
采用单表存储主规则和子规则的设计具有以下优势：

1. **结构简化**：
   - 减少表数量，降低系统复杂度
   - 统一的字段结构，便于维护和扩展
   - 简化JOIN查询，提高查询性能

2. **数据一致性**：
   - 主子规则使用相同的字段结构，保证数据一致性
   - 通过rule_level字段区分规则层级，逻辑清晰
   - parent_rule_id建立父子关系，支持多层级规则

3. **扩展性强**：
   - 可以轻松支持多层级规则结构（如：主规则→区域规则→站点规则）
   - 新增规则类型只需要在rule_type字段中添加枚举值
   - 规则配置使用JSON格式，支持灵活的配置结构

4. **查询优化**：
   - 单表查询避免复杂的JOIN操作
   - 通过索引优化查询性能
   - 支持按站点、规则类型、规则层级等多维度查询

#### 3.2.2 字段设计说明

| 字段名 | 类型 | 说明 | 设计考虑 |
|--------|------|------|----------|
| rule_level | VARCHAR(20) | 规则层级(MASTER/SUB) | 区分主子规则，支持扩展更多层级 |
| parent_rule_id | BIGINT | 父规则ID | 建立规则继承关系，子规则关联主规则 |
| rule_config | JSON | 规则配置 | 灵活的JSON格式，支持复杂配置结构 |
| priority | INT | 优先级 | 同层级规则的优先级排序 |
| effective_date | DATETIME | 生效时间 | 支持规则的定时生效 |
| expire_date | DATETIME | 失效时间 | 支持规则的自动失效 |

#### 3.2.3 索引设计
```sql
-- 规则类型索引（常用查询条件）
INDEX idx_rule_type (rule_type)

-- 规则层级索引（区分主子规则）
INDEX idx_rule_level (rule_level)

-- 父规则ID索引（查询子规则）
INDEX idx_parent_rule_id (parent_rule_id)

-- 复合索引（按类型和层级查询）
INDEX idx_type_level (rule_type, rule_level)

-- 状态索引（查询有效规则）
INDEX idx_status_effective (status, effective_date, expire_date)
```

### 3.3 规则配置JSON结构设计

#### 3.2.1 申请审批规则配置
```json
{
  "approval": {
    "required": true,
    "workflow": {
      "steps": [
        {
          "stepName": "部门审批",
          "approvers": ["DEPT_MANAGER"],
          "threshold": 1000,
          "required": true
        },
        {
          "stepName": "财务审批", 
          "approvers": ["FINANCE_MANAGER"],
          "threshold": 5000,
          "required": false
        }
      ]
    },
    "autoApproval": {
      "enabled": true,
      "conditions": {
        "maxAmount": 500,
        "materialTypes": ["OFFICE_SUPPLIES"]
      }
    }
  }
}
```

#### 3.2.2 库存管理规则配置
```json
{
  "inventory": {
    "stockControl": {
      "minStock": 10,
      "maxStock": 1000,
      "warningThreshold": 20,
      "criticalThreshold": 5
    },
    "stockCheck": {
      "frequency": "MONTHLY",
      "autoCheck": true,
      "tolerance": 0.05
    },
    "replenishment": {
      "autoReplenish": true,
      "replenishPoint": 15,
      "replenishQuantity": 100
    }
  }
}
```

#### 3.2.3 成本控制规则配置
```json
{
  "cost": {
    "priceControl": {
      "maxUnitPrice": 1000,
      "priceVarianceThreshold": 0.1,
      "requireApprovalAbove": 5000
    },
    "budgetControl": {
      "monthlyBudget": 50000,
      "quarterlyBudget": 150000,
      "warningThreshold": 0.8
    },
    "costAllocation": {
      "method": "PROPORTIONAL",
      "departments": ["SALES", "ADMIN", "OPERATION"]
    }
  }
}
```

## 4. 设计思想

### 4.1 核心设计理念

#### 4.1.1 分层规则设计
- **主规则层**：提供全局默认规则，确保系统的一致性和标准化
- **子规则层**：支持个性化定制，满足不同站点的特殊需求
- **优先级机制**：子规则优先于主规则，实现灵活的规则覆盖

#### 4.1.2 配置驱动设计
- **JSON配置**：使用灵活的JSON格式存储规则配置，支持复杂的规则结构
- **模板化配置**：提供规则模板，简化配置过程
- **动态生效**：支持规则的实时更新，无需重启系统

#### 4.1.3 可扩展性设计
- **插件化架构**：支持新规则类型的动态扩展
- **策略模式**：不同规则类型采用不同的处理策略
- **事件驱动**：规则变更通过事件机制通知相关模块

### 4.2 技术架构思想

#### 4.2.1 规则引擎设计思路
```
规则引擎核心流程：
站点请求 → 规则查询 → 规则合并 → 规则执行 → 结果返回

1. 规则查询：根据站点ID和规则类型查询适用规则
   - 优先查询子规则
   - 如无子规则则查询主规则
   - 支持规则继承和覆盖

2. 规则合并：将子规则配置覆盖到主规则配置上
   - 深度合并JSON配置
   - 保持配置结构的完整性
   - 支持部分覆盖和完全覆盖

3. 规则执行：根据合并后的规则配置执行业务逻辑
   - 策略模式处理不同规则类型
   - 责任链模式处理复杂规则
   - 异常处理和回滚机制

4. 结果返回：返回规则执行结果和相关信息
   - 统一的结果格式
   - 详细的错误信息
   - 审计日志记录
```


## 5. 设计方案

### 5.1 规则引擎设计方案

#### 5.1.1 规则引擎核心接口
```java
// 规则引擎主接口
interface MaterialRuleEngine {
    // 执行规则检查
    RuleResult executeRule(RuleContext context);

    // 获取站点适用规则
    MaterialRule getSiteRule(Long siteId, String ruleType);

    // 验证规则配置
    ValidationResult validateRule(String ruleType, String ruleConfig);

    // 预览规则效果
    RulePreviewResult previewRule(RuleContext context, String ruleConfig);
}

// 规则上下文对象
class RuleContext {
    private Long siteId;                    // 站点ID
    private String ruleType;                // 规则类型
    private String businessType;            // 业务类型
    private Map<String, Object> params;     // 业务参数
    private Long operatorId;                // 操作人ID
    private LocalDateTime operateTime;      // 操作时间
}

// 规则执行结果
class RuleResult {
    private boolean success;                // 执行是否成功
    private String resultCode;              // 结果编码
    private String message;                 // 结果消息
    private Map<String, Object> data;       // 结果数据
    private List<String> violations;        // 违规信息列表
    private List<String> warnings;          // 警告信息列表
}
```

#### 5.1.2 规则处理策略设计
```java
// 规则处理策略基础接口
interface RuleProcessor {
    // 是否支持该规则类型
    boolean supports(String ruleType);

    // 处理规则逻辑
    RuleResult process(MaterialRule rule, RuleContext context);

    // 获取处理器优先级
    int getPriority();
}

// 申请审批规则处理器
class ApprovalRuleProcessor implements RuleProcessor {

    public boolean supports(String ruleType) {
        return "APPROVAL".equals(ruleType);
    }

    public RuleResult process(MaterialRule rule, RuleContext context) {
        // 1. 解析审批规则配置
        ApprovalConfig config = parseApprovalConfig(rule.getRuleConfig());

        // 2. 检查是否需要审批
        if (!config.isRequired()) {
            return RuleResult.success("无需审批");
        }

        // 3. 检查自动审批条件
        if (checkAutoApproval(config, context)) {
            return RuleResult.success("自动审批通过");
        }

        // 4. 执行人工审批流程
        return executeApprovalWorkflow(config, context);
    }
}

// 库存管理规则处理器
class InventoryRuleProcessor implements RuleProcessor {

    public boolean supports(String ruleType) {
        return "INVENTORY".equals(ruleType);
    }

    public RuleResult process(MaterialRule rule, RuleContext context) {
        // 1. 解析库存规则配置
        InventoryConfig config = parseInventoryConfig(rule.getRuleConfig());

        // 2. 检查库存限制
        RuleResult stockLimitResult = checkStockLimits(config, context);
        if (!stockLimitResult.isSuccess()) {
            return stockLimitResult;
        }

        // 3. 检查库存预警
        checkStockWarnings(config, context);

        // 4. 触发自动补货
        triggerAutoReplenishment(config, context);

        return RuleResult.success("库存检查通过");
    }
}
```

### 5.2 规则配置管理方案

#### 5.2.1 规则配置服务设计
```java
// 规则配置管理服务接口
interface MaterialRuleConfigService {

    // 规则管理（统一接口，通过rule_level区分主子规则）
    Long createRule(MaterialRuleCreateRequest request);
    void updateRule(Long ruleId, MaterialRuleUpdateRequest request);
    void deleteRule(Long ruleId);
    MaterialRuleVO getRule(Long ruleId);
    PageResult<MaterialRuleVO> queryRules(MaterialRuleQueryRequest request);

    // 主规则专用方法
    List<MaterialRuleVO> getMasterRules(String ruleType);
    MaterialRuleVO getMasterRuleByType(String ruleType);

    // 子规则专用方法
    List<MaterialRuleVO> getSubRulesByParent(Long parentRuleId);
    List<MaterialRuleVO> getSiteRules(Long siteId, String ruleType);

    // 站点规则关联管理
    void bindRuleToSites(RuleBindRequest request);
    void unbindRuleFromSites(RuleUnbindRequest request);
    List<SiteRuleRelationVO> getSiteRuleRelations(Long siteId);
    List<SiteVO> getRuleBoundSites(Long ruleId);

    // 规则模板管理
    List<RuleTemplateVO> getRuleTemplates(String ruleType);
    String generateRuleConfigFromTemplate(Long templateId, Map<String, Object> params);
}
```

#### 5.2.2 规则配置验证方案
```java
// 规则配置验证器接口
interface RuleConfigValidator {
    ValidationResult validate(String ruleType, String ruleConfig);
    List<String> getSupportedRuleTypes();
}

// 申请审批规则验证器
class ApprovalRuleValidator implements RuleConfigValidator {

    public ValidationResult validate(String ruleType, String ruleConfig) {
        ValidationResult result = new ValidationResult();

        try {
            // 1. JSON格式验证
            JsonNode configNode = objectMapper.readTree(ruleConfig);

            // 2. 必填字段验证
            validateRequiredFields(configNode, result);

            // 3. 审批流程验证
            validateApprovalWorkflow(configNode, result);

            // 4. 审批人角色验证
            validateApproverRoles(configNode, result);

            // 5. 阈值合理性验证
            validateThresholds(configNode, result);

        } catch (Exception e) {
            result.addError("规则配置格式错误: " + e.getMessage());
        }

        return result;
    }

    private void validateApprovalWorkflow(JsonNode configNode, ValidationResult result) {
        JsonNode workflowNode = configNode.path("approval").path("workflow");
        if (workflowNode.isMissingNode()) {
            result.addError("缺少审批流程配置");
            return;
        }

        JsonNode stepsNode = workflowNode.path("steps");
        if (!stepsNode.isArray() || stepsNode.size() == 0) {
            result.addError("审批流程至少需要一个步骤");
        }

        // 验证每个审批步骤
        for (JsonNode stepNode : stepsNode) {
            validateApprovalStep(stepNode, result);
        }
    }
}

// 库存管理规则验证器
class InventoryRuleValidator implements RuleConfigValidator {

    public ValidationResult validate(String ruleType, String ruleConfig) {
        ValidationResult result = new ValidationResult();

        try {
            JsonNode configNode = objectMapper.readTree(ruleConfig);

            // 1. 库存控制参数验证
            validateStockControl(configNode, result);

            // 2. 库存检查配置验证
            validateStockCheck(configNode, result);

            // 3. 自动补货配置验证
            validateReplenishment(configNode, result);

        } catch (Exception e) {
            result.addError("规则配置格式错误: " + e.getMessage());
        }

        return result;
    }

    private void validateStockControl(JsonNode configNode, ValidationResult result) {
        JsonNode stockControlNode = configNode.path("inventory").path("stockControl");

        int minStock = stockControlNode.path("minStock").asInt(0);
        int maxStock = stockControlNode.path("maxStock").asInt(0);
        int warningThreshold = stockControlNode.path("warningThreshold").asInt(0);

        if (minStock < 0) {
            result.addError("最小库存不能小于0");
        }

        if (maxStock <= minStock) {
            result.addError("最大库存必须大于最小库存");
        }

        if (warningThreshold <= minStock) {
            result.addError("预警阈值必须大于最小库存");
        }
    }
}
```

### 5.3 规则应用集成方案

#### 5.3.1 物料申请规则集成
```java
// 物料申请服务中的规则集成
@Service
class MaterialApplyService {

    @Autowired
    private MaterialRuleEngine ruleEngine;

    @Autowired
    private MaterialApplyMapper applyMapper;

    @Transactional
    public Long submitApply(MaterialApplyRequest request) {
        // 1. 基础数据验证
        validateApplyRequest(request);

        // 2. 构建规则执行上下文
        RuleContext ruleContext = RuleContext.builder()
            .siteId(request.getSiteId())
            .ruleType("APPROVAL")
            .businessType("MATERIAL_APPLY")
            .params(buildApplyParams(request))
            .operatorId(getCurrentUserId())
            .operateTime(LocalDateTime.now())
            .build();

        // 3. 执行申请审批规则
        RuleResult ruleResult = ruleEngine.executeRule(ruleContext);

        // 4. 根据规则执行结果处理申请
        if (!ruleResult.isSuccess()) {
            throw new BusinessException(ruleResult.getMessage());
        }

        // 5. 创建申请记录
        CxrMaterialApply apply = buildApplyEntity(request);

        // 6. 根据规则结果设置申请状态
        setApplyStatusByRuleResult(apply, ruleResult);

        // 7. 保存申请记录
        applyMapper.insert(apply);

        // 8. 触发后续流程
        triggerApprovalWorkflow(apply, ruleResult);

        return apply.getId();
    }

    private Map<String, Object> buildApplyParams(MaterialApplyRequest request) {
        Map<String, Object> params = new HashMap<>();
        params.put("materialId", request.getMaterialId());
        params.put("quantity", request.getQuantity());
        params.put("totalAmount", request.getTotalAmount());
        params.put("urgentLevel", request.getUrgentLevel());
        params.put("applyReason", request.getApplyReason());
        return params;
    }

    private void setApplyStatusByRuleResult(CxrMaterialApply apply, RuleResult ruleResult) {
        String resultCode = ruleResult.getResultCode();

        switch (resultCode) {
            case "AUTO_APPROVED":
                apply.setApprovalStatus("APPROVED");
                apply.setApprovalTime(LocalDateTime.now());
                break;
            case "NEED_APPROVAL":
                apply.setApprovalStatus("PENDING");
                break;
            case "REJECTED":
                apply.setApprovalStatus("REJECTED");
                break;
            default:
                apply.setApprovalStatus("PENDING");
        }
    }
}
```

#### 5.3.2 库存管理规则集成
```java
// 库存管理服务中的规则集成
@Service
class MaterialStockService {

    @Autowired
    private MaterialRuleEngine ruleEngine;

    @Autowired
    private CxrSiteMaterialStockMapper stockMapper;

    @Transactional
    public void updateStock(StockUpdateRequest request) {
        // 1. 构建规则执行上下文
        RuleContext ruleContext = RuleContext.builder()
            .siteId(request.getSiteId())
            .ruleType("INVENTORY")
            .businessType("STOCK_UPDATE")
            .params(buildStockParams(request))
            .operatorId(getCurrentUserId())
            .build();

        // 2. 执行库存管理规则
        RuleResult ruleResult = ruleEngine.executeRule(ruleContext);

        // 3. 检查规则执行结果
        if (!ruleResult.isSuccess()) {
            throw new BusinessException(ruleResult.getMessage());
        }

        // 4. 执行库存更新
        doUpdateStock(request);

        // 5. 处理规则执行产生的后续动作
        handleRuleActions(ruleResult, request);
    }

    private void handleRuleActions(RuleResult ruleResult, StockUpdateRequest request) {
        Map<String, Object> data = ruleResult.getData();

        // 处理库存预警
        if (data.containsKey("stockWarning")) {
            sendStockWarningNotification(request.getSiteId(),
                request.getMaterialId(), (String) data.get("stockWarning"));
        }

        // 处理自动补货
        if (data.containsKey("autoReplenish")) {
            triggerAutoReplenishment(request.getSiteId(),
                request.getMaterialId(), (Integer) data.get("replenishQuantity"));
        }

        // 处理库存锁定
        if (data.containsKey("stockLock")) {
            lockMaterialStock(request.getSiteId(), request.getMaterialId());
        }
    }
}
```



## 6. 业务流程图

### 6.1 规则配置流程
```
开始 → 选择规则类型 → 创建主规则 → 配置规则参数 → 验证规则配置 → 保存主规则
                                                                    ↓
创建子规则 ← 选择站点 ← 配置站点关联 ← 测试规则效果 ← 启用规则 ← 规则配置完成
    ↓
配置覆盖参数 → 验证子规则配置 → 保存子规则 → 绑定站点 → 生效规则 → 结束
```

### 6.2 规则执行流程
```
业务请求 → 解析请求参数 → 确定站点和规则类型 → 查询适用规则
                                                    ↓
查询数据库 → 合并主子规则  → 执行规则逻辑 → 返回执行结果
```

### 6.3 规则优先级处理流程
```
查询站点规则 → 是否存在子规则? → 是 → 获取子规则配置
                    ↓ 否                      ↓
                获取主规则配置              合并主规则和子规则配置
                    ↓                          ↓
                应用主规则 ← ← ← ← ← ← ← ← 应用合并后规则
                    ↓
                返回规则结果
```

## 7. 技术要点总结

### 7.1 核心技术特性
- **分层规则架构**：主规则提供基础能力，子规则实现个性化定制
- **JSON配置驱动**：灵活的配置格式，支持复杂业务规则
- **策略模式扩展**：支持新规则类型的插件化扩展

