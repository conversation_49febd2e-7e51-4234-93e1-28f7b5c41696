package com.ruoyi.business.base.cxrEmployeeChangeRecord.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.business.base.api.domain.CxrEmployeeChangeRecord;
import com.ruoyi.business.base.api.domain.vo.EmployeePostVo;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 员工变动记录表, 涉及后台站点变动、小组变动、调岗站点变动(CxrEmployeeChangeRecord)表服务接口
 *
 * <AUTHOR>
 * @since 2022-11-13 16:47:50
 */
public interface ICxrEmployeeChangeRecordService extends IService<CxrEmployeeChangeRecord> {


    List<CxrEmployeeChangeRecord> queryTimeRange(Long employeeId, LocalDate start, LocalDate end);

    /**
     * @param employeeId
     * @return
     */
    Boolean queryLatestDataByEmployeeId(Long employeeId, LocalDate executeLocalDate);

    List<EmployeePostVo> queryRirectorList(Long siteId, LocalDate start, LocalDate end);

    CxrEmployeeChangeRecord selectGetGroupName(Long emIds, String mouth, Long cxrSiteId);

    CxrEmployeeChangeRecord selectGetGroupNameByDays(Long cxrEmployeeId, Date startTime, Date endTime, Long cxrSiteId);

    CxrEmployeeChangeRecord selectGetGroupNameByEmpty(Long cxrEmployeeId, Long cxrSiteId);

    List<CxrEmployeeChangeRecord> employeeChangeRecordLists(String mouth, List<Long> collects, Long cxrSiteId);
}
