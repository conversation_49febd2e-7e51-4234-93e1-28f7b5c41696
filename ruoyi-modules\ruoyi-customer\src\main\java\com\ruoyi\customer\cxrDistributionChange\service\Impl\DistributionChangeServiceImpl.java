package com.ruoyi.customer.cxrDistributionChange.service.Impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.ruoyi.business.api.domain.vo.SaleProductListVo;
import com.ruoyi.business.base.api.PlanMilkDataConvertUtil;
import com.ruoyi.business.base.api.domain.*;
import com.ruoyi.business.base.api.domain.bo.CxrAddressBo;
import com.ruoyi.business.base.api.domain.json.CustomerAddressMilkDistributionInfo;
import com.ruoyi.business.base.api.domain.json.MilkDistributionInfo;
import com.ruoyi.business.base.api.domain.vo.CxrAddressHistoryVo;
import com.ruoyi.business.base.api.domain.vo.CxrAddressVo;
import com.ruoyi.business.base.api.dubbo.RemoteCustomerAddressHistoryService;
import com.ruoyi.business.base.api.dubbo.RemoteCustomerAddressService;
import com.ruoyi.business.base.api.dubbo.RemoteCxrSaleProductService;
import com.ruoyi.business.base.api.dubbo.RemoteSiteService;
import com.ruoyi.business.base.api.model.LoginEmployee;
import com.ruoyi.business.base.api.model.MilkDistributionDTO;
import com.ruoyi.common.core.enums.DeleteStatus;
import com.ruoyi.common.core.enums.SysYesNo;
import com.ruoyi.common.core.enums.UpdateAddressRedisKey;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.enums.TerminalTypeEnums;
import com.ruoyi.common.rocketmq.calculate.CustomerAddressConstant;
import com.ruoyi.common.rocketmq.utils.MqUtil;
import com.ruoyi.common.satoken.utils.LoginUtil;
import com.ruoyi.common.satoken.utils.helper.CustomerLoginHelper;
import com.ruoyi.customer.cxrDistributionChange.service.DistributionChangeService;
import com.ruoyi.order.api.RemoteOrderService;
import com.ruoyi.order.api.domain.dto.UserOrderDTO;
import com.ruoyi.system.api.RemoteAreaService;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class DistributionChangeServiceImpl implements DistributionChangeService {

    @DubboReference
    private RemoteCustomerAddressService remoteCustomerAddressService;
    @DubboReference
    private RemoteCustomerAddressHistoryService remoteCustomerAddressHistoryService;
    @Resource(name = "scheduledExecutorService")
    private ScheduledExecutorService scheduledExecutorService;
    @DubboReference
    private RemoteCxrSaleProductService remoteCxrSaleProductService;
    @DubboReference
    private RemoteSiteService remoteSiteService;
    @Autowired
    private MqUtil mqUtil;

    @DubboReference
    private RemoteAreaService remoteAreaService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @DubboReference
    private RemoteOrderService remoteOrderService;


    //查询全部的客户地址
    @Override
    public List<CxrAddressVo> getAddressids(CxrAddressBo bo) {
        Long userId = CustomerLoginHelper.getLoginUser().getUserId();
        bo.setCxrCustomerId(userId);
        List<CxrAddressVo> addressids = remoteCustomerAddressService.getAddressids(bo);

        List<Long> siteIds = addressids.stream().map(CxrAddressVo::getCxrSiteId).collect(Collectors.toList());
        siteIds.add(-1L);//以防万一没有站点id
        List<CxrSite> sites = remoteSiteService.queryIds(siteIds);

        Map<Long, CxrSite> siteMap = null;
        if (!ObjectUtil.isEmpty(sites)) {
            siteMap = sites.stream()
                .collect(Collectors.toMap(CxrSite::getId, Function.identity(), (v1, v2) -> v2));
        }
        for (CxrAddressVo s : addressids) {
            if (!ObjectUtil.isEmpty(siteMap)) {
                if (ObjectUtil.isNotEmpty(s.getCxrSiteId())) {
                    CxrSite cxrSite = siteMap.get(s.getCxrSiteId());
                    if (ObjectUtil.isNotEmpty(cxrSite)) {
                        LocalTime changeEndTime = cxrSite.getChangeEndTime();
                        Long changeIntervalDays = cxrSite.getChangeIntervalDays();
                        Long changeIntervalDaysAfter = cxrSite.getChangeIntervalDaysAfter();
                        if (!ObjectUtil.isNull(changeEndTime) && !ObjectUtil.isNull(changeIntervalDays)
                            && !ObjectUtil.isNull(
                            changeIntervalDaysAfter)) {
                            LocalTime now = LocalTime.now();
                            boolean flag = true;
                            if (now.compareTo(changeEndTime) > -1) {
                                //异动之后
                                flag = false;
                            }
                            if (flag) {
                                //异动前
                                s.setSuspendDate(LocalDate.now().plusDays(changeIntervalDays));
                            } else {
                                //异动后
                                s.setSuspendDate(LocalDate.now().plusDays(changeIntervalDaysAfter));
                            }
                        }
                    }
                }
            }
        }
        List<CxrAddressVo> collect = addressids.stream().sorted(Comparator.comparing(CxrAddressVo::getId))
            .collect(Collectors.toList());
        return collect;
    }

    //进行初始化
    private void newMilkDistributionInfo(CxrAddressVo s, CustomerAddressMilkDistributionInfo pmInfo) {
        List<MilkDistributionInfo> milkDistributionInfos = s.getCxrSaleProducts().stream().map(pr -> {
            MilkDistributionInfo milkDistributionInfo = new MilkDistributionInfo();
            milkDistributionInfo.setProductId(pr.getId());
            milkDistributionInfo.setProductName(pr.getName());
            milkDistributionInfo.setProductAlias(pr.getProductAlias());
            milkDistributionInfo.setQuantity(0L);
            return milkDistributionInfo;
        }).collect(Collectors.toList());
        pmInfo.setFriday(milkDistributionInfos);
        pmInfo.setMonday(milkDistributionInfos);
        pmInfo.setSaturday(milkDistributionInfos);
        pmInfo.setThursday(milkDistributionInfos);
        pmInfo.setTuesday(milkDistributionInfos);
        pmInfo.setWednesday(milkDistributionInfos);
    }

    //依据地址id进行查询
    @Override
    public List<CxrAddressVo> checkmilkDistributionDetail(Long id) {
        List<CxrAddressVo> cxrCustomerAddrVos = remoteCustomerAddressService.checkmilkDistributionDetail(id,
            DeleteStatus.NOT_DELETED.getValue());

        /*拿取站点售卖商品口味*/
        List<Long> siteIds = cxrCustomerAddrVos.stream().map(CxrAddressVo::getCxrSiteId).collect(Collectors.toList());
        Map<Long, List<CxrSaleProduct>> longListMap = remoteCxrSaleProductService.queryProductBySiteIds(siteIds);
        /*汇聚地址id*/
        List<Long> addressIds = cxrCustomerAddrVos.stream().map(CxrAddressVo::getId).collect(Collectors.toList());

        List<CxrSite> cxrSites = remoteSiteService.queryIds(siteIds);
        Map<Long, CxrSite> cxrSitesMap = cxrSites.stream().collect(Collectors.toMap(CxrSite::getId, a -> a));

        /*转换*/
        cxrCustomerAddrVos.stream().forEach(s -> {


            CxrSite site = cxrSitesMap.get(s.getCxrSiteId());
            s.setChangeEndTime(site.getChangeEndTime());
            s.setChangeIntervalDaysAfter(site.getChangeIntervalDaysAfter());
            s.setChangeIntervalDays(site.getChangeIntervalDays());

            CxrCustomerChangeRecord changeRecord = remoteCustomerAddressService.getLastCustomerChangeRecord(
                s.getId());
            if (ObjectUtil.isNotEmpty(changeRecord)) {
                s.setAmDistributionInfo(changeRecord.getAmDistributionInfo());
                s.setAmDistributionStatus(changeRecord.getAmDistributionStatus());
                s.setAmDistributionStartDeliveryTime(changeRecord.getAmDistributionStartDeliveryTime());
                s.setAmDistributionSuspendStartTime(changeRecord.getAmDistributionSuspendStartTime());
                s.setAmDistributionSuspendEndTime(changeRecord.getAmDistributionSuspendEndTime());
                s.setPmDistributionInfo(changeRecord.getPmDistributionInfo());
                s.setPmDistributionStatus(changeRecord.getPmDistributionStatus());
                s.setPmDistributionStartDeliveryTime(changeRecord.getPmDistributionStartDeliveryTime());
                s.setPmDistributionSuspendStartTime(changeRecord.getPmDistributionSuspendStartTime());
                s.setPmDistributionSuspendEndTime(changeRecord.getPmDistributionSuspendEndTime());
                s.setIsShowAmDistribution(ObjectUtil.isEmpty(changeRecord.getIsShowAmDistribution()) ?
                    s.getIsShowAmDistribution() : changeRecord.getIsShowAmDistribution());
                s.setIsShowPmDistribution(ObjectUtil.isEmpty(changeRecord.getIsShowPmDistribution()) ?
                    s.getIsShowPmDistribution() : changeRecord.getIsShowPmDistribution());
            }

            if (SysYesNo.YES.getValue().equals(s.getIsShowAmDistribution())) {
                s.setIsShowAmDistribution("1");
            } else {
                s.setIsShowAmDistribution("2");
            }
            if (SysYesNo.YES.getValue().equals(s.getIsShowPmDistribution())) {
                s.setIsShowPmDistribution("1");
            } else {
                s.setIsShowPmDistribution("2");
            }

            if (SysYesNo.YES.getValue().equals(s.getAmDistributionStatus())) {
                s.setAmDistributionStatus("1");
            } else {
                s.setAmDistributionStatus("2");
            }
            if (SysYesNo.YES.getValue().equals(s.getPmDistributionStatus())) {
                s.setPmDistributionStatus("1");
            } else {
                s.setPmDistributionStatus("2");
            }
            /*校验是否排奶*/
            CustomerAddressMilkDistributionInfo amDistributionInfo = s.getAmDistributionInfo();
            boolean amFlag = checkInfoQuantityIsGeZero(amDistributionInfo);

            CustomerAddressMilkDistributionInfo pmDistributionInfo = s.getPmDistributionInfo();
            boolean pmFlag = checkInfoQuantityIsGeZero(pmDistributionInfo);

            s.setAmWhetherToDisplay(amFlag);
            s.setPmWhetherToDisplay(pmFlag);

            /*根据站点拿取商品*/
            List<CxrSaleProduct> cxrSaleProducts = longListMap.get(s.getCxrSiteId());
            /*设置售卖商品*/
            s.setCxrSaleProductsCustomer(cxrSaleProducts);
            log.info("------------------------------------------------------------------------");
            //null的话设置 空数组
            if (s.getPmDistributionInfo() == null && s.getAmDistributionInfo() == null) {
                CustomerAddressMilkDistributionInfo pmInfo = new CustomerAddressMilkDistributionInfo();
                if (s.getCxrSaleProducts() != null) {
                    newMilkDistributionInfo(s, pmInfo);
                }
                s.setPmDistributionInfo(pmInfo);
                s.setAmDistributionInfo(pmInfo);
            } else if (s.getPmDistributionInfo() == null) {
                CustomerAddressMilkDistributionInfo pmInfo = new CustomerAddressMilkDistributionInfo();
                if (s.getCxrSaleProducts() != null) {
                    newMilkDistributionInfo(s, pmInfo);
                }
                s.setPmDistributionInfo(pmInfo);
            } else if (s.getAmDistributionInfo() == null) {
                CustomerAddressMilkDistributionInfo pmInfo = new CustomerAddressMilkDistributionInfo();
                if (s.getCxrSaleProducts() != null) {
                    newMilkDistributionInfo(s, pmInfo);
                }
                s.setAmDistributionInfo(pmInfo);
            }
        });

        /*拿取历史*/
        List<CxrAddressHistoryVo> oneHistoryByAddressIds = remoteCustomerAddressService.getOneHistoryByAddressIds(
            addressIds);
        Map<Long, CxrAddressHistoryVo> historyVoMap = oneHistoryByAddressIds.stream().collect(
            Collectors.toMap(CxrAddressHistoryVo::getCxrCustomerAddressId, Function.identity(), (v1, v2) -> v2));
        /*排序*/
        List<CxrAddressVo> collect = cxrCustomerAddrVos.stream().sorted(Comparator.comparing(CxrAddressVo::getId))
            .collect(Collectors.toList());
        /*处理历史*/
        for (CxrAddressVo s : collect) {
            CxrAddressHistoryVo cxrAddressHistoryVo = historyVoMap.get(s.getId());
            if (ObjectUtil.isNotEmpty(cxrAddressHistoryVo)) {
                //转换    好判断
                cxrAddressHistoryVo.setAmMilkDistributionInfos(
                    JSONUtil.toList(cxrAddressHistoryVo.getAmMilkDistributionInfo(), MilkDistributionDTO.class));
                cxrAddressHistoryVo.setPmMilkDistributionInfos(
                    JSONUtil.toList(cxrAddressHistoryVo.getPmMilkDistributionInfo(), MilkDistributionDTO.class));
                cxrAddressHistoryVo.setAlterAmTastes(
                    JSONUtil.toList(cxrAddressHistoryVo.getAlterAmTaste(), MilkDistributionDTO.class));
                cxrAddressHistoryVo.setAlterRecords(
                    JSONUtil.toBean(cxrAddressHistoryVo.getAlterRecord(), AlterRecord.class));
                cxrAddressHistoryVo.setAlterPmTastes(
                    JSONUtil.toList(cxrAddressHistoryVo.getAlterPmTaste(), MilkDistributionDTO.class));

                //根据boolean 判断
                boolean amFlag = true;
                boolean pmFlag = true;
                l:
                for (MilkDistributionDTO i : cxrAddressHistoryVo.getAlterAmTastes()) {
                    if (i.isFrdc() || i.isModc() || i.isSadc() || i.isThdc() || i.isTudc() || i.isWedc()) {
                        amFlag = false;
                        break l;
                    }
                }

                a:
                for (MilkDistributionDTO i : cxrAddressHistoryVo.getAlterPmTastes()) {
                    if (i.isFrdc() || i.isModc() || i.isSadc() || i.isThdc() || i.isTudc() || i.isWedc()) {
                        pmFlag = false;
                        break a;
                    }
                }

                //将  没有变化  使用的字段  滞空
                if (amFlag) {
                    cxrAddressHistoryVo.setAlterAmTaste(null);
                    cxrAddressHistoryVo.setAlterAmTastes(null);
                    cxrAddressHistoryVo.setAmMilkDistributionInfo(null);
                    cxrAddressHistoryVo.setAmMilkDistributionInfos(null);
                }
                if (pmFlag) {
                    cxrAddressHistoryVo.setAlterPmTaste(null);
                    cxrAddressHistoryVo.setAlterPmTastes(null);
                    cxrAddressHistoryVo.setPmMilkDistributionInfo(null);
                    cxrAddressHistoryVo.setPmMilkDistributionInfos(null);
                }
            }

            s.setCxrAddressHistoryVo(cxrAddressHistoryVo);

            if (CollUtil.isNotEmpty(s.getCxrSaleProducts())) {
                Collections.sort(s.getCxrSaleProducts(), new Comparator<SaleProductListVo>() {
                    @Override
                    public int compare(SaleProductListVo o1, SaleProductListVo o2) {
                        return o1.getProductAlias().compareTo(o2.getProductAlias());
                    }
                });
            }


        }
        return collect;
    }

    /**
     * 检查排奶  是否排奶 大于0
     *
     * @param addressMilkDistributionInfo
     */
    private boolean checkInfoQuantityIsGeZero(CustomerAddressMilkDistributionInfo addressMilkDistributionInfo) {
        if (ObjectUtil.isNotEmpty(addressMilkDistributionInfo)) {

            List<MilkDistributionInfo> wednesday = addressMilkDistributionInfo.getWednesday();

            Long wednesdayLong = checkDaySum(wednesday);
            if (wednesdayLong > 0) {

                return true;
            }
            List<MilkDistributionInfo> friday = addressMilkDistributionInfo.getFriday();
            Long fridayLong = checkDaySum(friday);
            if (fridayLong > 0) {
                return true;
            }

            List<MilkDistributionInfo> monday = addressMilkDistributionInfo.getMonday();
            Long mondayLong = checkDaySum(monday);
            if (mondayLong > 0) {
                return true;
            }
            List<MilkDistributionInfo> saturday = addressMilkDistributionInfo.getSaturday();
            Long saturdayLong = checkDaySum(saturday);
            if (saturdayLong > 0) {
                return true;
            }
            List<MilkDistributionInfo> thursday = addressMilkDistributionInfo.getThursday();
            Long thursdayLong = checkDaySum(thursday);
            if (thursdayLong > 0) {
                return true;
            }
            List<MilkDistributionInfo> tuesday = addressMilkDistributionInfo.getTuesday();
            Long tuesdayLong = checkDaySum(tuesday);
            if (tuesdayLong > 0) {
                return true;
            }
            return false;
        }
        return false;
    }

    private Long checkDaySum(List<MilkDistributionInfo> dayInfo) {
        if (CollUtil.isEmpty(dayInfo)) {
            return 0L;
        }

        if (dayInfo.size() > 0) {
            long sum = dayInfo.stream().mapToLong(MilkDistributionInfo::getQuantity).sum();
            return Convert.toLong(sum, 0L);
        }
        return 0L;
    }


    @Override
    @GlobalTransactional
    public Map<String, Object> checkMilkDistribution(CxrAddressBo bo) {
        if (bo == null || bo.getCxrSiteId() == null) {
            throw new IllegalArgumentException("参数异常");
        }
        log.info(StrUtil.format("排奶更改入参：{}", bo));
        LoginEmployee loginUser = CustomerLoginHelper.getLoginUser();
        bo.setUpdateByName(loginUser.getUserName());
        bo.setUpdateBy(loginUser.getUserId());
        bo.setUpdateByType(loginUser.getUserType());
        LocalTime time = LocalTime.now();
        CxrSite cxrSite = remoteSiteService.getCxrSite(bo.getCxrSiteId());
        if (bo.getMatchPages().equals("1")) {
            //排奶
            Map<String, Object> map = new HashMap<>();

            //拿出地址原来信息
            CxrCustomerChangeRecord record = remoteCustomerAddressService.getLastCustomerChangeRecord(bo.getId());

            CxrCustomerAddress address = null;
            if (ObjectUtil.isEmpty(record)) {
                address = remoteCustomerAddressService.getCustomerAddressById(bo.getId());
            } else {
                address = BeanUtil.copyProperties(record, CxrCustomerAddress.class);
                address.setId(bo.getId());
            }

            //转换排奶维度
            List<CxrSaleProduct> cxrSaleProductList = remoteCxrSaleProductService.queryProcuctNoDeleteStatus();
            CustomerAddressMilkDistributionInfo amDistributionInfo = PlanMilkDataConvertUtil.convetProductIdToDate(
                bo.getAmDistributionDTOList(), cxrSaleProductList);
            CustomerAddressMilkDistributionInfo pmDistributionInfo = PlanMilkDataConvertUtil.convetProductIdToDate(
                bo.getPmDistributionDTOList(), cxrSaleProductList);
            bo.setAmDistributionInfo(amDistributionInfo);
            bo.setPmDistributionInfo(pmDistributionInfo);

            // 进行排奶变更校验
            validateMilkDistributionChanges(bo, address);

            boolean b = true;
            LocalTime endTime = cxrSite.getChangeEndTime();

            if (time.isBefore(endTime)) {// 可能出现跨省配送的客户

                if (cxrSite.getChangeIntervalDays() == 1) {
                    b = remoteCustomerAddressService.updateChangeMilk(bo) > 0;
                } else {

                    boolean flag = remoteCustomerAddressService.updateCustomerReceiverPhone(bo);
                    if (!flag) {
                        throw new ServiceException("网络波动!请重试!");
                    }
                    CxrCustomerChangeRecord customerChangeRecord = BeanUtil.toBean(bo, CxrCustomerChangeRecord.class);
                    customerChangeRecord.setCustomerAddressId(bo.getId());
                    customerChangeRecord.setSiteId(bo.getCxrSiteId());

                    if (ObjectUtil.equals(bo.getAmDistributionStatus(), "1")) {
                        customerChangeRecord.setAmDistributionStatus("Y");
                    } else {
                        customerChangeRecord.setAmDistributionStatus("N");
                    }

                    if (ObjectUtil.equals(bo.getPmDistributionStatus(), "1")) {
                        customerChangeRecord.setPmDistributionStatus("Y");
                    } else {
                        customerChangeRecord.setPmDistributionStatus("N");
                    }

                    if (ObjectUtil.equals(bo.getIsShowPmDistribution(), "1")) {
                        customerChangeRecord.setIsShowPmDistribution("Y");
                    } else {
                        customerChangeRecord.setIsShowPmDistribution("N");
                    }

                    if (ObjectUtil.equals(bo.getIsShowAmDistribution(), "1")) {
                        customerChangeRecord.setIsShowAmDistribution("Y");
                    } else {
                        customerChangeRecord.setIsShowAmDistribution("N");
                    }
//                    mqUtil.sendSyncMessage(CustomerAddressConstant.CUSTOMER_ADDRESS_TOPIC,
//                        CustomerAddressConstant.CUSTOMER_SEPARATE_CHANGE_ADDRESS_SAVA_TAG,
//                        JSONUtil.toJsonStr(customerChangeRecord));
                    remoteCustomerAddressService.customerSeparateCchangeAddressSave(customerChangeRecord);
                }

            } else {

                boolean flag = remoteCustomerAddressService.updateCustomerReceiverPhone(bo);
                if (!flag) {
                    throw new ServiceException("网络波动!请重试!");
                }

                CxrCustomerChangeRecord customerChangeRecord = BeanUtil.toBean(bo, CxrCustomerChangeRecord.class);
                customerChangeRecord.setCustomerAddressId(bo.getId());
                customerChangeRecord.setSiteId(bo.getCxrSiteId());
                if (ObjectUtil.equals(bo.getAmDistributionStatus(), "1")) {
                    customerChangeRecord.setAmDistributionStatus("Y");
                } else {
                    customerChangeRecord.setAmDistributionStatus("N");
                }

                if (ObjectUtil.equals(bo.getPmDistributionStatus(), "1")) {
                    customerChangeRecord.setPmDistributionStatus("Y");
                } else {
                    customerChangeRecord.setPmDistributionStatus("N");
                }

                if (ObjectUtil.equals(bo.getIsShowPmDistribution(), "1")) {
                    customerChangeRecord.setIsShowPmDistribution("Y");
                } else {
                    customerChangeRecord.setIsShowPmDistribution("N");
                }

                if (ObjectUtil.equals(bo.getIsShowAmDistribution(), "1")) {
                    customerChangeRecord.setIsShowAmDistribution("Y");
                } else {
                    customerChangeRecord.setIsShowAmDistribution("N");
                }
                remoteCustomerAddressService.customerSeparateCchangeAddressSave(customerChangeRecord);
            }

            if (b) {
                if (DateUtils.getLocalDateFromDate(address.getCreateTime()).compareTo(LocalDate.now()) == 0) {
                    redisTemplate.opsForValue().set(
                        StrUtil.format(UpdateAddressRedisKey.CXR_UPDATE_CUSTOMER_ADDRESS_ID.getKey(), address.getId()),
                        1, 1, TimeUnit.DAYS);
                }
            }
            extracted(bo, address, b);

            map.put("flag", b);
            return map;
        } else {
            //暂停 起送

            Map<String, Object> map = new HashMap<>();

            if (bo.getIsTheFirst().equals("1")) {
                //异动时间的检验
                checkSiteTime(bo, map);

                return map;
            } else {
                checkSendOrStop(bo);
            }

            //根据配送状态滞空时间  --上午
            if (Convert.toStr(bo.getAmDistributionStatus(), SysYesNo.NO.getValue()).equals(SysYesNo.YES.getValue())) {
                bo.setAmDistributionSuspendStartTime(null);
                bo.setAmDistributionSuspendEndTime(null);
            } else {
                bo.setAmDistributionStartDeliveryTime(null);
            }

            if (Convert.toStr(bo.getPmDistributionStatus(), SysYesNo.NO.getValue()).equals(SysYesNo.YES.getValue())) {
                bo.setPmDistributionSuspendStartTime(null);
                bo.setPmDistributionSuspendEndTime(null);
            } else {
                bo.setPmDistributionStartDeliveryTime(null);
            }

            //拿出地址原来信息
            CxrCustomerChangeRecord record = remoteCustomerAddressService.getLastCustomerChangeRecord(bo.getId());

            CxrCustomerAddress address = null;
            if (ObjectUtil.isEmpty(record)) {
                address = remoteCustomerAddressService.getCustomerAddressById(bo.getId());
            } else {
                address = BeanUtil.copyProperties(record, CxrCustomerAddress.class);
                address.setId(bo.getId());
            }

            if (bo.getIsAmOrPm().equals("1")) {
                if (bo.getAmDistributionStatus().equals(SysYesNo.YES.getValue())) {
                    //早上  起送
                    if (ObjectUtil.isEmpty(bo.getAmDistributionStartDeliveryTime())) {
                        throw new ServiceException(
                            "请刷新重试");
                    }
                } else {
                    //早上  暂停
                    if (ObjectUtil.isEmpty(bo.getAmDistributionSuspendStartTime())) {
                        throw new ServiceException("请刷新重试!");
                    }
                }

                boolean b = false;
                LocalTime endTime = cxrSite.getChangeEndTime();

                if (time.isBefore(endTime)) {// 可能出现跨省配送的客户
                    if (cxrSite.getChangeIntervalDays() == 1) {
                        b = remoteCustomerAddressService.updateAmChangeSendOrStop(bo) > 0;
//                        if (b) {
//                            remoteCustomerAddressService.updateAmChangeSendOrStopChangeRecord(bo);
//                        }
                    } else {
                        CxrCustomerChangeRecord customerChangeRecord = BeanUtil.toBean(bo,
                            CxrCustomerChangeRecord.class);
                        customerChangeRecord.setCustomerAddressId(bo.getId());
                        customerChangeRecord.setSiteId(bo.getCxrSiteId());
                        customerChangeRecord.setAmDistributionStatus(
                            bo.getAmDistributionStatus()
                        );
                        customerChangeRecord.setPmDistributionStatus(
                            bo.getPmDistributionStatus()
                        );

                        remoteCustomerAddressService.customerSeparateCchangeAddressSave(customerChangeRecord);
                        b = true;
                    }
                } else {
                    CxrCustomerChangeRecord customerChangeRecord = BeanUtil.toBean(bo, CxrCustomerChangeRecord.class);
                    customerChangeRecord.setCustomerAddressId(bo.getId());
                    customerChangeRecord.setSiteId(bo.getCxrSiteId());
                    customerChangeRecord.setAmDistributionStatus(
                        bo.getAmDistributionStatus());
                    customerChangeRecord.setPmDistributionStatus(
                        bo.getPmDistributionStatus()
                    );
                    remoteCustomerAddressService.customerSeparateCchangeAddressSave(customerChangeRecord);

                    b = true;
                }
                map.put("flag", b);

                if (b) {
                    if (DateUtils.getLocalDateFromDate(address.getCreateTime()).compareTo(LocalDate.now()) == 0) {
                        redisTemplate.opsForValue().set(
                            StrUtil.format(UpdateAddressRedisKey.CXR_UPDATE_CUSTOMER_ADDRESS_ID.getKey(),
                                address.getId()), 1, 1, TimeUnit.DAYS);
                    }

                    AlterRecord alterRecord = bo.getAlterRecord();
                    alterRecord.setOldePmEnable(null);

                    AddressHistoryMqDoMain addressHistoryMqDoMain = new AddressHistoryMqDoMain();
                    addressHistoryMqDoMain.setAlterAmTaste(bo.getAlterAmTaste());
                    addressHistoryMqDoMain.setAlterPmTaste(bo.getAlterPmTaste());
                    addressHistoryMqDoMain.setCxrCustomerAddressId(bo.getId());
                    addressHistoryMqDoMain.setCxrCustomerId(bo.getCxrCustomerId());
                    addressHistoryMqDoMain.setAlterRecord(bo.getAlterRecord());
                    addressHistoryMqDoMain.setLoginInfo(LoginUtil.getLoginUser());

                    addressHistoryMqDoMain.setAmSendDate(bo.getAmDistributionStartDeliveryTime());
                    addressHistoryMqDoMain.setAmStopStartDate(bo.getAmDistributionSuspendStartTime());
                    addressHistoryMqDoMain.setAmStopEndDate(bo.getAmDistributionSuspendEndTime());
                    addressHistoryMqDoMain.setPmSendDate(address.getPmDistributionStartDeliveryTime());
                    addressHistoryMqDoMain.setPmStopStartDate(address.getPmDistributionSuspendStartTime());
                    addressHistoryMqDoMain.setPmStopEndDate(address.getPmDistributionSuspendEndTime());
                    addressHistoryMqDoMain.setAmAddressMilkDistributionInfo(address.getAmDistributionInfo());
                    addressHistoryMqDoMain.setPmAddressMilkDistributionInfo(address.getPmDistributionInfo());
                    addressHistoryMqDoMain.setAmDistributionStatus(bo.getAmDistributionStatus());
                    addressHistoryMqDoMain.setPmDistributionStatus(address.getPmDistributionStatus());

                    //旧的时间跟状态
                    addressHistoryMqDoMain.setOldAmDistributionStatus(address.getAmDistributionStatus());
                    addressHistoryMqDoMain.setOldPmDistributionStatus(address.getPmDistributionStatus());

                    addressHistoryMqDoMain.setOldAmSendDate(address.getAmDistributionStartDeliveryTime());
                    addressHistoryMqDoMain.setOldPmSendDate(address.getPmDistributionStartDeliveryTime());

                    addressHistoryMqDoMain.setOldAmStopStartDate(address.getAmDistributionSuspendStartTime());
                    addressHistoryMqDoMain.setOldAmStopEndDate(address.getAmDistributionSuspendEndTime());

                    addressHistoryMqDoMain.setOldPmStopStartDate(address.getPmDistributionSuspendStartTime());
                    addressHistoryMqDoMain.setOldPmStopEndDate(address.getPmDistributionSuspendEndTime());
                    addressHistoryMqDoMain.setUuid(UUID.randomUUID().toString());

                    mqUtil.sendSyncMessage(CustomerAddressConstant.CUSTOMER_ADDRESS_TOPIC,
                        CustomerAddressConstant.CUSTOMER_ADDRESS_HISTORY_TAG,
                        JSONUtil.toJsonStr(addressHistoryMqDoMain));
                }

            } else {

                if (bo.getPmDistributionStatus().equals(SysYesNo.YES.getValue())) {
                    //下午  起送
                    if (ObjectUtil.isEmpty(bo.getPmDistributionStartDeliveryTime())) {
                        throw new ServiceException(
                            "请刷新重试");
                    }
                } else {
                    //下午  暂停
                    if (ObjectUtil.isEmpty(bo.getPmDistributionSuspendStartTime())) {
                        throw new ServiceException("请刷新重试!");
                    }
                }

                boolean b = false;
                LocalTime endTime = cxrSite.getChangeEndTime();

                if (time.isBefore(endTime)) {// 可能出现跨省配送的客户
                    if (cxrSite.getChangeIntervalDays() == 1) {
                        b = remoteCustomerAddressService.updatePmChangeSendOrStop(bo) > 0;
//                        if (b) {
//                            remoteCustomerAddressService.updatePmChangeSendOrStopChangeCustomer(bo);
//                        }
                    } else {
                        CxrCustomerChangeRecord customerChangeRecord = BeanUtil.toBean(bo,
                            CxrCustomerChangeRecord.class);
                        customerChangeRecord.setCustomerAddressId(bo.getId());
                        customerChangeRecord.setSiteId(bo.getCxrSiteId());
                        customerChangeRecord.setAmDistributionStatus(
                            bo.getAmDistributionStatus());
                        customerChangeRecord.setPmDistributionStatus(
                            bo.getPmDistributionStatus()
                        );
                        remoteCustomerAddressService.customerSeparateCchangeAddressSave(customerChangeRecord);
                        b = true;
                    }

                } else {
                    CxrCustomerChangeRecord customerChangeRecord = BeanUtil.toBean(bo, CxrCustomerChangeRecord.class);
                    customerChangeRecord.setCustomerAddressId(bo.getId());
                    customerChangeRecord.setSiteId(bo.getCxrSiteId());
                    customerChangeRecord.setAmDistributionStatus(
                        bo.getAmDistributionStatus());
                    customerChangeRecord.setPmDistributionStatus(
                        bo.getPmDistributionStatus());
                    remoteCustomerAddressService.customerSeparateCchangeAddressSave(customerChangeRecord);
                    b = true;
                }

                map.put("flag", b);
                if (b) {

                    if (DateUtils.getLocalDateFromDate(address.getCreateTime()).compareTo(LocalDate.now()) == 0) {
                        redisTemplate.opsForValue().set(
                            StrUtil.format(UpdateAddressRedisKey.CXR_UPDATE_CUSTOMER_ADDRESS_ID.getKey(),
                                address.getId()), 1, 1, TimeUnit.DAYS);
                    }

                    AlterRecord alterRecord = bo.getAlterRecord();
                    alterRecord.setOldeAmEnable(null);
                    AddressHistoryMqDoMain addressHistoryMqDoMain = new AddressHistoryMqDoMain();
                    addressHistoryMqDoMain.setAlterAmTaste(bo.getAlterAmTaste());
                    addressHistoryMqDoMain.setAlterPmTaste(bo.getAlterPmTaste());
                    addressHistoryMqDoMain.setCxrCustomerAddressId(bo.getId());
                    addressHistoryMqDoMain.setCxrCustomerId(bo.getCxrCustomerId());
                    addressHistoryMqDoMain.setAlterRecord(bo.getAlterRecord());
                    addressHistoryMqDoMain.setLoginInfo(LoginUtil.getLoginUser());

                    addressHistoryMqDoMain.setAmSendDate(address.getAmDistributionStartDeliveryTime());
                    addressHistoryMqDoMain.setAmStopStartDate(address.getAmDistributionSuspendStartTime());
                    addressHistoryMqDoMain.setAmStopEndDate(address.getAmDistributionSuspendEndTime());
                    addressHistoryMqDoMain.setPmSendDate(bo.getPmDistributionStartDeliveryTime());
                    addressHistoryMqDoMain.setPmStopStartDate(bo.getPmDistributionSuspendStartTime());
                    addressHistoryMqDoMain.setPmStopEndDate(bo.getPmDistributionSuspendEndTime());
                    addressHistoryMqDoMain.setAmAddressMilkDistributionInfo(address.getAmDistributionInfo());
                    addressHistoryMqDoMain.setPmAddressMilkDistributionInfo(address.getPmDistributionInfo());
                    addressHistoryMqDoMain.setAmDistributionStatus(address.getAmDistributionStatus());
                    addressHistoryMqDoMain.setPmDistributionStatus(bo.getPmDistributionStatus());

                    //旧的时间跟状态
                    addressHistoryMqDoMain.setOldAmDistributionStatus(address.getAmDistributionStatus());
                    addressHistoryMqDoMain.setOldPmDistributionStatus(address.getPmDistributionStatus());

                    addressHistoryMqDoMain.setOldAmSendDate(address.getAmDistributionStartDeliveryTime());
                    addressHistoryMqDoMain.setOldPmSendDate(address.getPmDistributionStartDeliveryTime());

                    addressHistoryMqDoMain.setOldAmStopStartDate(address.getAmDistributionSuspendStartTime());
                    addressHistoryMqDoMain.setOldAmStopEndDate(address.getAmDistributionSuspendEndTime());

                    addressHistoryMqDoMain.setOldPmStopStartDate(address.getPmDistributionSuspendStartTime());
                    addressHistoryMqDoMain.setOldPmStopEndDate(address.getPmDistributionSuspendEndTime());
                    addressHistoryMqDoMain.setUuid(UUID.randomUUID().toString());

                    mqUtil.sendSyncMessage(CustomerAddressConstant.CUSTOMER_ADDRESS_TOPIC,
                        CustomerAddressConstant.CUSTOMER_ADDRESS_HISTORY_TAG,
                        JSONUtil.toJsonStr(addressHistoryMqDoMain));

                }
            }

            return map;
        }


    }


    private void extracted(CxrAddressBo bo, CxrCustomerAddress address, boolean b) {
        if (b) {
            // CxrCustomerAddress address = remoteCustomerAddressService.queryCustomerAddress(bo.getCxrCustomerId());
            AlterRecord alterRecord = bo.getAlterRecord();
            alterRecord.setNewPhone(bo.getReceiverPhone());
            AddressHistoryMqDoMain addressHistoryMqDoMain = new AddressHistoryMqDoMain();
            addressHistoryMqDoMain.setAlterAmTaste(bo.getAlterAmTaste());
            addressHistoryMqDoMain.setAlterPmTaste(bo.getAlterPmTaste());
            addressHistoryMqDoMain.setCxrCustomerAddressId(bo.getId());
            addressHistoryMqDoMain.setCxrCustomerId(bo.getCxrCustomerId());
            addressHistoryMqDoMain.setAlterRecord(alterRecord);
            addressHistoryMqDoMain.setLoginInfo(LoginUtil.getLoginUser());
            addressHistoryMqDoMain.setAmSendDate(bo.getAmDistributionStartDeliveryTime());
            addressHistoryMqDoMain.setAmStopStartDate(bo.getAmDistributionSuspendStartTime());
            addressHistoryMqDoMain.setAmStopEndDate(bo.getAmDistributionSuspendEndTime());
            addressHistoryMqDoMain.setPmSendDate(bo.getPmDistributionStartDeliveryTime());
            addressHistoryMqDoMain.setPmStopStartDate(bo.getPmDistributionSuspendStartTime());
            addressHistoryMqDoMain.setPmStopEndDate(bo.getPmDistributionSuspendEndTime());
            addressHistoryMqDoMain.setAmAddressMilkDistributionInfo(address.getAmDistributionInfo());
            addressHistoryMqDoMain.setPmAddressMilkDistributionInfo(address.getPmDistributionInfo());
            addressHistoryMqDoMain.setAmDistributionStatus(bo.getAmDistributionStatus());
            addressHistoryMqDoMain.setPmDistributionStatus(bo.getPmDistributionStatus());

            //旧的时间跟状态
            addressHistoryMqDoMain.setOldAmDistributionStatus(address.getAmDistributionStatus());
            addressHistoryMqDoMain.setOldPmDistributionStatus(address.getPmDistributionStatus());

            addressHistoryMqDoMain.setOldAmSendDate(address.getAmDistributionStartDeliveryTime());
            addressHistoryMqDoMain.setOldPmSendDate(address.getPmDistributionStartDeliveryTime());

            addressHistoryMqDoMain.setOldAmStopStartDate(address.getAmDistributionSuspendStartTime());
            addressHistoryMqDoMain.setOldAmStopEndDate(address.getAmDistributionSuspendEndTime());

            addressHistoryMqDoMain.setOldPmStopStartDate(address.getPmDistributionSuspendStartTime());
            addressHistoryMqDoMain.setOldPmStopEndDate(address.getPmDistributionSuspendEndTime());
            addressHistoryMqDoMain.setUuid(UUID.randomUUID().toString());

            mqUtil.sendSyncMessage(CustomerAddressConstant.CUSTOMER_ADDRESS_TOPIC,
                CustomerAddressConstant.CUSTOMER_ADDRESS_HISTORY_TAG, JSONUtil.toJsonStr(addressHistoryMqDoMain));
//            CxrCustomerAddress address=remoteCustomerAddressService.getCustomerAddressById(bo.getId());
//            remoteCustomerAddressHistoryService.addressHistoryAdd(address);
        }
    }

    //异动时间的检验
    private void checkSiteTime(CxrAddressBo bo, Map<String, Object> map) {
        //查询的地址id
        List<CxrCustomerAddress> getCustomerAddresses = remoteCustomerAddressService.getCustomerAddresses(bo.getId());
        if (CollUtil.isEmpty(getCustomerAddresses)) {
            throw new ServiceException("查询不到你的排奶地址，请刷新重试！");
        }
        //map集合
        Map<Long, CxrCustomerAddress> addressMap =
            getCustomerAddresses.stream().collect(Collectors.toMap(CxrCustomerAddress::getId, Function.identity(),
                (v1, v2) -> v2));

        CxrCustomerChangeRecord changeRecord = remoteCustomerAddressService.selectCustomerChangeRecord(bo.getId());
        CxrSite getCxrSite = remoteSiteService.getCxrSite(bo.getCxrSiteId());
        LocalTime changeEndTime = getCxrSite.getChangeEndTime();
        Long changeIntervalDays = getCxrSite.getChangeIntervalDays();
        Long changeIntervalDaysAfter = getCxrSite.getChangeIntervalDaysAfter();
        if (ObjectUtil.isNull(changeEndTime) || ObjectUtil.isNull(changeIntervalDays) || ObjectUtil.isNull(
            changeIntervalDaysAfter)) {
            throw new ServiceException("站点异动时间未配置,请联系管理员进行配置");
        }
        LocalTime now = LocalTime.now();
        Long intervalDay = 0L;

        boolean falg = true;
        //小于等于
        if (now.compareTo(changeEndTime) <= 0) {
            falg = false;
            // 8点前
            intervalDay = changeIntervalDays;
        } else {
            //8点后
            intervalDay = changeIntervalDaysAfter;
        }

        // 校验起送时间
        LocalDate localDate = LocalDate.now().plusDays(intervalDay);
        LocalDate nowDate = LocalDate.now().plusDays(2L);
        LocalDate oneDate = LocalDate.now().plusDays(1L);
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("MM月dd号");

        //替换值
        if ("1".equals(bo.getIsShowAmDistribution())) {
            bo.setIsShowAmDistribution(SysYesNo.YES.getValue());
        } else {
            bo.setIsShowAmDistribution(SysYesNo.NO.getValue());
        }

        if ("1".equals(bo.getIsShowPmDistribution())) {
            bo.setIsShowPmDistribution(SysYesNo.YES.getValue());
        } else {
            bo.setIsShowPmDistribution(SysYesNo.NO.getValue());
        }

        if ("1".equals(bo.getAmDistributionStatus())) {
            bo.setAmDistributionStatus(SysYesNo.YES.getValue());
        } else {
            bo.setAmDistributionStatus(SysYesNo.NO.getValue());
        }
        if ("1".equals(bo.getPmDistributionStatus())) {
            bo.setPmDistributionStatus(SysYesNo.YES.getValue());
        } else {
            bo.setPmDistributionStatus(SysYesNo.NO.getValue());
        }

        //根据每次循环拿出  原始的 数据
        CxrCustomerAddress address = addressMap.get(bo.getId());
        if (ObjectUtil.isNotEmpty(changeRecord)) {
            address.setAmDistributionInfo(changeRecord.getAmDistributionInfo());
            address.setAmDistributionStatus(changeRecord.getAmDistributionStatus());
            address.setAmDistributionStartDeliveryTime(changeRecord.getAmDistributionStartDeliveryTime());
            address.setAmDistributionSuspendStartTime(changeRecord.getAmDistributionSuspendStartTime());
            address.setAmDistributionSuspendEndTime(changeRecord.getAmDistributionSuspendEndTime());
            address.setIsShowAmDistribution(changeRecord.getIsShowAmDistribution());

            address.setPmDistributionInfo(changeRecord.getPmDistributionInfo());
            address.setPmDistributionStatus(changeRecord.getPmDistributionStatus());
            address.setPmDistributionStartDeliveryTime(changeRecord.getPmDistributionStartDeliveryTime());
            address.setPmDistributionSuspendStartTime(changeRecord.getPmDistributionSuspendStartTime());
            address.setPmDistributionSuspendEndTime(changeRecord.getPmDistributionSuspendEndTime());
            address.setIsShowPmDistribution(changeRecord.getIsShowPmDistribution());
        }
        if (bo.getIsAmOrPm().equals("1")) {
            amTimeCheck(bo, falg, localDate, nowDate, oneDate, dtf, address, map);
        } else {
            pmTimeCheck(bo, falg, localDate, nowDate, oneDate, dtf, address, map);
        }


    }


    //异动时间的检验
    private void checkSendOrStop(CxrAddressBo bo) {
        //查询的地址id
        List<CxrCustomerAddress> getCustomerAddresses = remoteCustomerAddressService.getCustomerAddresses(bo.getId());
        if (CollUtil.isEmpty(getCustomerAddresses)) {
            throw new ServiceException("查询不到你的排奶地址，请刷新重试！");
        }
        //map集合
        Map<Long, CxrCustomerAddress> addressMap =
            getCustomerAddresses.stream().collect(Collectors.toMap(CxrCustomerAddress::getId, Function.identity(),
                (v1, v2) -> v2));

        CxrCustomerChangeRecord changeRecord = remoteCustomerAddressService.selectCustomerChangeRecord(bo.getId());
        CxrSite getCxrSite = remoteSiteService.getCxrSite(bo.getCxrSiteId());
        LocalTime changeEndTime = getCxrSite.getChangeEndTime();
        Long changeIntervalDays = getCxrSite.getChangeIntervalDays();
        Long changeIntervalDaysAfter = getCxrSite.getChangeIntervalDaysAfter();
        if (ObjectUtil.isNull(changeEndTime) || ObjectUtil.isNull(changeIntervalDays) || ObjectUtil.isNull(
            changeIntervalDaysAfter)) {
            throw new ServiceException("站点异动时间未配置,请联系管理员进行配置");
        }
        LocalTime now = LocalTime.now();
        Long intervalDay = 0L;

        boolean falg = true;
        //小于等于
        if (now.compareTo(changeEndTime) <= 0) {
            falg = false;
            // 8点前
            intervalDay = changeIntervalDays;
        } else {
            //8点后
            intervalDay = changeIntervalDaysAfter;
        }

        // 校验起送时间
        LocalDate localDate = LocalDate.now().plusDays(intervalDay);
        LocalDate nowDate = LocalDate.now().plusDays(2L);
        LocalDate oneDate = LocalDate.now().plusDays(1L);
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("MM月dd号");

        //替换值
        if ("1".equals(bo.getIsShowAmDistribution())) {
            bo.setIsShowAmDistribution(SysYesNo.YES.getValue());
        } else {
            bo.setIsShowAmDistribution(SysYesNo.NO.getValue());
        }

        if ("1".equals(bo.getIsShowPmDistribution())) {
            bo.setIsShowPmDistribution(SysYesNo.YES.getValue());
        } else {
            bo.setIsShowPmDistribution(SysYesNo.NO.getValue());
        }

        if ("1".equals(bo.getAmDistributionStatus())) {
            bo.setAmDistributionStatus(SysYesNo.YES.getValue());
        } else {
            bo.setAmDistributionStatus(SysYesNo.NO.getValue());
        }
        if ("1".equals(bo.getPmDistributionStatus())) {
            bo.setPmDistributionStatus(SysYesNo.YES.getValue());
        } else {
            bo.setPmDistributionStatus(SysYesNo.NO.getValue());
        }

        //根据每次循环拿出  原始的 数据
        CxrCustomerAddress address = addressMap.get(bo.getId());
        if (ObjectUtil.isNotEmpty(changeRecord)) {
            address.setAmDistributionInfo(changeRecord.getAmDistributionInfo());
            address.setAmDistributionStatus(changeRecord.getAmDistributionStatus());
            address.setAmDistributionStartDeliveryTime(changeRecord.getAmDistributionStartDeliveryTime());
            address.setAmDistributionSuspendStartTime(changeRecord.getAmDistributionSuspendStartTime());
            address.setAmDistributionSuspendEndTime(changeRecord.getAmDistributionSuspendEndTime());
            address.setIsShowAmDistribution(changeRecord.getIsShowAmDistribution());

            address.setPmDistributionInfo(changeRecord.getPmDistributionInfo());
            address.setPmDistributionStatus(changeRecord.getPmDistributionStatus());
            address.setPmDistributionStartDeliveryTime(changeRecord.getPmDistributionStartDeliveryTime());
            address.setPmDistributionSuspendStartTime(changeRecord.getPmDistributionSuspendStartTime());
            address.setPmDistributionSuspendEndTime(changeRecord.getPmDistributionSuspendEndTime());
            address.setIsShowPmDistribution(changeRecord.getIsShowPmDistribution());
        }
        if (ObjectUtil.isNotEmpty(address)) {
            if (bo.getIsAmOrPm().equals("1")) {
                amTimeSet(bo, falg, localDate, nowDate, oneDate, dtf, address);
            } else {
                pmTimeSet(bo, falg, localDate, nowDate, oneDate, dtf, address);
            }
        }

    }


    private void amTimeCheck(CxrAddressBo bo, boolean falg, LocalDate localDate, LocalDate nowDate,
                             LocalDate oneDate, DateTimeFormatter dtf, CxrCustomerAddress address, Map<String, Object> map) {
        if (Convert.toStr(address.getAmDistributionStatus(), SysYesNo.NO.getValue())
            .equals(bo.getAmDistributionStatus())) { //配送状态一样
            if (bo.getAmDistributionStatus().equals("Y")) {//起送状态
                map.put("identical", "1");
                map.put("flag", true);
//                LocalDate amSendDate =
//                    bo.getAmDistributionStartDeliveryTime();//上午起送时间
//                if (ObjectUtil.isEmpty(amSendDate)) {
//                    throw new ServiceException("上午启送日期不能为空");
//                }
//                if (address.getAmDistributionStartDeliveryTime().compareTo(amSendDate) != 0) {//判断
//                    // 配送时间是否一样   ==0 一样   !=0不一样  就需要判断时间
////                        LocalDate localDate1 = DateUtil.toLocalDateTime(sendDate).toLocalDate();
//                    if (localDate.compareTo(amSendDate) > 0) {
//                        throw new ServiceException(StrUtil.format("上午启送日期最早可以选择{}", dtf.format(localDate)));
//                    }
//                }

            } else {//暂停状态    显示状态为Y
//                            上午暂停时间
                if (ObjectUtil.isEmpty(bo.getAmDistributionSuspendStartTime())) {
                    throw new ServiceException(bo.getTipCode(), StrUtil.format("上午暂停日期不能为空"));
                }
                LocalDate startStopDate =
                    bo.getAmDistributionSuspendStartTime();//暂停时间

                if (ObjectUtil.isNotEmpty(address.getAmDistributionSuspendStartTime())
                    && address.getAmDistributionSuspendStartTime().compareTo(startStopDate) != 0) {//不一样
                    if (falg) {//八点以后
                        //八点后
                        if (localDate.compareTo(startStopDate) > 0) {
                            throw new ServiceException(bo.getTipCode(), StrUtil.format("上午暂停开始日期最早可以选择{}",
                                dtf.format(localDate)));
                        }
                    } else {
                        //八点前
                        if (localDate.compareTo(startStopDate) > 0) {
                            throw new ServiceException(bo.getTipCode(), StrUtil.format("上午暂停开始日期最早可以选择{}",
                                dtf.format(localDate)));
                        }
                    }
                }

                if (ObjectUtil.isNotEmpty(bo.getAmDistributionSuspendEndTime())) {//结束时间不等于null
                    // 需要判断是否大于开始时间
                    LocalDate stopEndDate =
                        bo.getAmDistributionSuspendEndTime();//暂停时间

                    if (startStopDate.compareTo(stopEndDate) > 0) {
                        throw new ServiceException(bo.getTipCode(), "上午暂停结束日期不能大于暂停开始日期");
                    }
                }
                map.put("amStop",
                    bo.getAmDistributionSuspendStartTime() + "至" + Convert.toStr(bo.getAmDistributionSuspendEndTime(),
                        "永久"));
                map.put("flag", true);

            }
        } else {
            //上午配送状态   不一样
            if (bo.getAmDistributionStatus().equals(SysYesNo.YES.getValue())) {//配送状态
                //配送状态不一样  并且 是起送状态 证明和数据库中的数据肯定不一样   所以直接判断就ok

                //状态不一样  并且是起送   客户端没办法选择起送时间  需要 给一个异动时间
                bo.setAmDistributionStartDeliveryTime(localDate);
                map.put("actualDate", localDate);
                map.put("flag", true);
//                LocalDate amSendDate =
//                    bo.getAmDistributionStartDeliveryTime();//上午起送时间
//                if (ObjectUtil.isEmpty(amSendDate)) {
//                    throw new ServiceException("上午启送日期不能为空");
//                }
//
//                if (localDate.compareTo(amSendDate) > 0) {
//                    throw new ServiceException(StrUtil.format("上午启送日期最早可以选择{}", dtf.format(localDate)));
//                }

            } else {//暂停状态
                //证明一开始是起送的状态  直接判断时间就ok
                if (ObjectUtil.isEmpty(bo.getAmDistributionSuspendStartTime())) {
                    throw new ServiceException(bo.getTipCode(), "第{}个地址的上午暂停日期不能为空");
                }
                LocalDate startStopDate =
                    bo.getAmDistributionSuspendStartTime();//暂停时间

                if (falg) {//八点以后
                    //八点后
                    if (localDate.compareTo(startStopDate) > 0) {
                        throw new ServiceException(bo.getTipCode(),
                            StrUtil.format("上午暂停开始日期最早可以选择{}", dtf.format(localDate)));
                    }
                } else {
                    //八点前
                    if (localDate.compareTo(startStopDate) > 0) {
                        throw new ServiceException(bo.getTipCode(), StrUtil.format("上午暂停开始日期最早可以选择{}",
                            dtf.format(localDate)));
                    }
                }

                if (ObjectUtil.isNotEmpty(bo.getAmDistributionSuspendEndTime())) {//结束时间不等于null
                    // 需要判断是否大于开始时间
                    LocalDate stopEndDate =
                        bo.getAmDistributionSuspendEndTime();//暂停时间

                    if (startStopDate.compareTo(stopEndDate) > 0) {
                        throw new ServiceException(bo.getTipCode(), "上午暂停结束日期不能大于暂停开始日期");
                    }
                }

                map.put("flag", true);
                map.put("amStop",
                    bo.getAmDistributionSuspendStartTime() + "至" + Convert.toStr(bo.getAmDistributionSuspendEndTime(),
                        "永久"));
            }
        }

    }


    private void pmTimeCheck(CxrAddressBo bo, boolean falg, LocalDate localDate, LocalDate nowDate,
                             LocalDate oneDate, DateTimeFormatter dtf, CxrCustomerAddress address, Map<String, Object> map) {
        if (Convert.toStr(address.getPmDistributionStatus(), SysYesNo.NO.getValue())
            .equals(bo.getPmDistributionStatus())) { //配送状态一样
            if (bo.getPmDistributionStatus().equals("Y")) {//起送状态
                map.put("identical", "1");
                map.put("flag", true);
//                LocalDate pmSendDate =
//                    bo.getPmDistributionStartDeliveryTime();//上午起送时间
//                if (ObjectUtil.isEmpty(pmSendDate)) {
//                    throw new ServiceException("下午启送日期不能为空");
//                }
//                if (address.getPmDistributionStartDeliveryTime().compareTo(pmSendDate) != 0) {//判断
//                    // 配送时间是否一样   ==0 一样   !=0不一样  就需要判断时间
////                        LocalDate localDate1 = DateUtil.toLocalDateTime(sendDate).toLocalDate();
//                    if (localDate.compareTo(pmSendDate) > 0) {
//                        throw new ServiceException(StrUtil.format("下午启送日期最早可以选择{}", dtf.format(localDate)));
//                    }
//                }

            } else {//暂停状态    显示状态为Y
//                            上午暂停时间
                if (ObjectUtil.isEmpty(bo.getPmDistributionSuspendStartTime())) {
                    throw new ServiceException(bo.getTipCode(), StrUtil.format("下午暂停日期不能为空"));
                }
                LocalDate startStopDate =
                    bo.getPmDistributionSuspendStartTime();//暂停时间
                if (ObjectUtil.isNotEmpty(address.getPmDistributionSuspendStartTime())
                    && address.getPmDistributionSuspendStartTime().compareTo(startStopDate) != 0) {//不一样
                    if (falg) {//八点以后
                        //八点后
                        if (localDate.compareTo(startStopDate) > 0) {
                            throw new ServiceException(bo.getTipCode(), StrUtil.format("下午暂停开始日期最早可以选择{}",
                                dtf.format(localDate)));
                        }
                    } else {
                        //八点前
                        if (localDate.compareTo(startStopDate) > 0) {
                            throw new ServiceException(bo.getTipCode(), StrUtil.format("下午暂停开始日期最早可以选择{}",
                                dtf.format(localDate)));
                        }
                    }
                }

                if (ObjectUtil.isNotEmpty(bo.getPmDistributionSuspendEndTime())) {//结束时间不等于null
                    // 需要判断是否大于开始时间
                    LocalDate stopEndDate =
                        bo.getPmDistributionSuspendEndTime();//暂停时间
                    if (startStopDate.compareTo(stopEndDate) > 0) {
                        throw new ServiceException(bo.getTipCode(), "下午暂停结束日期不能大于暂停开始日期");
                    }
                }
                map.put("flag", true);
                map.put("pmStop",
                    bo.getPmDistributionSuspendStartTime() + "至" + Convert.toStr(bo.getPmDistributionSuspendEndTime(),
                        "永久"));
            }
        } else {
            //上午配送状态   不一样
            if (bo.getPmDistributionStatus().equals(SysYesNo.YES.getValue())) {//配送状态
                //配送状态不一样  并且 是起送状态 证明和数据库中的数据肯定不一样   所以直接判断就ok

                //状态不一样  并且是起送   客户端没办法选择起送时间  需要 给一个异动时间
                bo.setPmDistributionStartDeliveryTime(localDate);

                map.put("actualDate", localDate);
                map.put("flag", true);
//                LocalDate pmSendDate =
//                    bo.getPmDistributionStartDeliveryTime();//上午起送时间
//                if (ObjectUtil.isEmpty(pmSendDate)) {
//                    throw new ServiceException("下午启送日期不能为空");
//                }
//
//                if (localDate.compareTo(pmSendDate) > 0) {
//                    throw new ServiceException(StrUtil.format("下午启送日期最早可以选择{}", dtf.format(localDate)));
//                }

            } else {//暂停状态
                //证明一开始是起送的状态  直接判断时间就ok
                if (ObjectUtil.isEmpty(bo.getPmDistributionSuspendStartTime())) {
                    throw new ServiceException(bo.getTipCode(), "第{}个地址的下午暂停日期不能为空");
                }
                LocalDate startStopDate =
                    bo.getPmDistributionSuspendStartTime();//暂停时间

                if (falg) {//八点以后
                    //八点后
                    if (nowDate.compareTo(startStopDate) > 0) {
                        throw new ServiceException(bo.getTipCode(),
                            StrUtil.format("下午暂停开始日期最早可以选择{}", dtf.format(nowDate)));
                    }
                } else {
                    //八点前
                    if (oneDate.compareTo(startStopDate) > 0) {
                        throw new ServiceException(bo.getTipCode(), StrUtil.format("下午暂停开始日期最早可以选择{}",
                            dtf.format(oneDate)));
                    }
                }

                if (ObjectUtil.isNotEmpty(bo.getPmDistributionSuspendEndTime())) {//结束时间不等于null
                    // 需要判断是否大于开始时间
                    LocalDate stopEndDate =
                        bo.getPmDistributionSuspendEndTime();//暂停时间

                    if (startStopDate.compareTo(stopEndDate) > 0) {
                        throw new ServiceException(bo.getTipCode(), "下午暂停结束日期不能大于暂停开始日期");
                    }
                }
                map.put("pmStop",
                    bo.getPmDistributionSuspendStartTime() + "至" + Convert.toStr(bo.getPmDistributionSuspendEndTime(),
                        "永久"));
                map.put("flag", true);
            }
        }
    }


    private void amTimeSet(CxrAddressBo bo, boolean falg, LocalDate localDate, LocalDate nowDate,
                           LocalDate oneDate, DateTimeFormatter dtf, CxrCustomerAddress address) {
        if (Convert.toStr(address.getAmDistributionStatus(), SysYesNo.NO.getValue())
            .equals(bo.getAmDistributionStatus())) { //配送状态一样
            if (bo.getAmDistributionStatus().equals("Y")) {//起送状态

            } else {//暂停状态    显示状态为Y
//                            上午暂停时间
                if (ObjectUtil.isEmpty(bo.getAmDistributionSuspendStartTime())) {
                    throw new ServiceException(bo.getTipCode(), StrUtil.format("上午暂停日期不能为空"));
                }
                LocalDate startStopDate =
                    bo.getAmDistributionSuspendStartTime();//暂停时间

                if (ObjectUtil.isNotEmpty(address.getAmDistributionSuspendStartTime())
                    && address.getAmDistributionSuspendStartTime().compareTo(startStopDate) != 0) {//不一样
                    if (falg) {//八点以后
                        //八点后
                        if (localDate.compareTo(startStopDate) > 0) {
                            throw new ServiceException(bo.getTipCode(), StrUtil.format("上午暂停开始日期最早可以选择{}",
                                dtf.format(localDate)));
                        }
                    } else {
                        //八点前
                        if (localDate.compareTo(startStopDate) > 0) {
                            throw new ServiceException(bo.getTipCode(), StrUtil.format("上午暂停开始日期最早可以选择{}",
                                dtf.format(localDate)));
                        }
                    }
                }

                if (ObjectUtil.isNotEmpty(bo.getAmDistributionSuspendEndTime())) {//结束时间不等于null
                    // 需要判断是否大于开始时间
                    LocalDate stopEndDate =
                        bo.getAmDistributionSuspendEndTime();//暂停时间

                    if (startStopDate.compareTo(stopEndDate) > 0) {
                        throw new ServiceException(bo.getTipCode(), "上午暂停结束日期不能大于暂停开始日期");
                    }
                }
            }
        } else {
            //上午配送状态   不一样
            if (bo.getAmDistributionStatus().equals(SysYesNo.YES.getValue())) {//配送状态
                //配送状态不一样  并且 是起送状态 证明和数据库中的数据肯定不一样   所以直接判断就ok

                //状态不一样  并且是起送   客户端没办法选择起送时间  需要 给一个异动时间
                bo.setAmDistributionStartDeliveryTime(localDate);
//                LocalDate amSendDate =
//                    bo.getAmDistributionStartDeliveryTime();//上午起送时间
//                if (ObjectUtil.isEmpty(amSendDate)) {
//                    throw new ServiceException("上午启送日期不能为空");
//                }
//
//                if (localDate.compareTo(amSendDate) > 0) {
//                    throw new ServiceException(StrUtil.format("上午启送日期最早可以选择{}", dtf.format(localDate)));
//                }

            } else {//暂停状态
                //证明一开始是起送的状态  直接判断时间就ok
                if (ObjectUtil.isEmpty(bo.getAmDistributionSuspendStartTime())) {
                    throw new ServiceException(bo.getTipCode(), "第{}个地址的上午暂停日期不能为空");
                }
                LocalDate startStopDate =
                    bo.getAmDistributionSuspendStartTime();//暂停时间

                if (falg) {//八点以后
                    //八点后
                    if (nowDate.compareTo(startStopDate) > 0) {
                        throw new ServiceException(bo.getTipCode(),
                            StrUtil.format("上午暂停开始日期最早可以选择{}", dtf.format(nowDate)));
                    }
                } else {
                    //八点前
                    if (oneDate.compareTo(startStopDate) > 0) {
                        throw new ServiceException(bo.getTipCode(), StrUtil.format("上午暂停开始日期最早可以选择{}",
                            dtf.format(oneDate)));
                    }
                }

                if (ObjectUtil.isNotEmpty(bo.getAmDistributionSuspendEndTime())) {//结束时间不等于null
                    // 需要判断是否大于开始时间
                    LocalDate stopEndDate =
                        bo.getAmDistributionSuspendEndTime();//暂停时间

                    if (startStopDate.compareTo(stopEndDate) > 0) {
                        throw new ServiceException(bo.getTipCode(), "上午暂停结束日期不能大于暂停开始日期");
                    }
                }
            }
        }

    }


    private void pmTimeSet(CxrAddressBo bo, boolean falg, LocalDate localDate, LocalDate nowDate,
                           LocalDate oneDate, DateTimeFormatter dtf, CxrCustomerAddress address) {
        if (Convert.toStr(address.getPmDistributionStatus(), SysYesNo.NO.getValue())
            .equals(bo.getPmDistributionStatus())) { //配送状态一样
            if (bo.getPmDistributionStatus().equals("Y")) {//起送状态
//                LocalDate pmSendDate =
//                    bo.getPmDistributionStartDeliveryTime();//上午起送时间
//                if (ObjectUtil.isEmpty(pmSendDate)) {
//                    throw new ServiceException("下午启送日期不能为空");
//                }
//                if (address.getPmDistributionStartDeliveryTime().compareTo(pmSendDate) != 0) {//判断
//                    // 配送时间是否一样   ==0 一样   !=0不一样  就需要判断时间
////                        LocalDate localDate1 = DateUtil.toLocalDateTime(sendDate).toLocalDate();
//                    if (localDate.compareTo(pmSendDate) > 0) {
//                        throw new ServiceException(StrUtil.format("下午启送日期最早可以选择{}", dtf.format(localDate)));
//                    }
//                }

            } else {//暂停状态    显示状态为Y
//                            上午暂停时间
                if (ObjectUtil.isEmpty(bo.getPmDistributionSuspendStartTime())) {
                    throw new ServiceException(bo.getTipCode(), StrUtil.format("下午暂停日期不能为空"));
                }
                LocalDate startStopDate =
                    bo.getPmDistributionSuspendStartTime();//暂停时间

                if (ObjectUtil.isNotEmpty(address.getPmDistributionSuspendStartTime())
                    && address.getPmDistributionSuspendStartTime().compareTo(startStopDate) != 0) {//不一样
                    if (falg) {//八点以后
                        //八点后
                        if (localDate.compareTo(startStopDate) > 0) {
                            throw new ServiceException(bo.getTipCode(), StrUtil.format("下午暂停开始日期最早可以选择{}",
                                dtf.format(localDate)));
                        }
                    } else {
                        //八点前
                        if (localDate.compareTo(startStopDate) > 0) {
                            throw new ServiceException(bo.getTipCode(), StrUtil.format("下午暂停开始日期最早可以选择{}",
                                dtf.format(localDate)));
                        }
                    }
                }

                if (ObjectUtil.isNotEmpty(bo.getPmDistributionSuspendEndTime())) {//结束时间不等于null
                    // 需要判断是否大于开始时间
                    LocalDate stopEndDate =
                        bo.getPmDistributionSuspendEndTime();//暂停时间
                    if (startStopDate.compareTo(stopEndDate) > 0) {
                        throw new ServiceException(bo.getTipCode(), "下午暂停结束日期不能大于暂停开始日期");
                    }
                }
            }
        } else {
            //上午配送状态   不一样
            if (bo.getPmDistributionStatus().equals(SysYesNo.YES.getValue())) {//配送状态
                //配送状态不一样  并且 是起送状态 证明和数据库中的数据肯定不一样   所以直接判断就ok

                //状态不一样  并且是起送   客户端没办法选择起送时间  需要 给一个异动时间
                bo.setPmDistributionStartDeliveryTime(localDate);

//                LocalDate pmSendDate =
//                    bo.getPmDistributionStartDeliveryTime();//上午起送时间
//                if (ObjectUtil.isEmpty(pmSendDate)) {
//                    throw new ServiceException("下午启送日期不能为空");
//                }
//
//                if (localDate.compareTo(pmSendDate) > 0) {
//                    throw new ServiceException(StrUtil.format("下午启送日期最早可以选择{}", dtf.format(localDate)));
//                }

            } else {//暂停状态
                //证明一开始是起送的状态  直接判断时间就ok
                if (ObjectUtil.isEmpty(bo.getPmDistributionSuspendStartTime())) {
                    throw new ServiceException(bo.getTipCode(), "第{}个地址的下午暂停日期不能为空");
                }
                LocalDate startStopDate =
                    bo.getPmDistributionSuspendStartTime();//暂停时间

                if (falg) {//八点以后
                    //八点后
                    if (localDate.compareTo(startStopDate) > 0) {
                        throw new ServiceException(bo.getTipCode(),
                            StrUtil.format("下午暂停开始日期最早可以选择{}", dtf.format(nowDate)));
                    }
                } else {
                    //八点前
                    if (localDate.compareTo(startStopDate) > 0) {
                        throw new ServiceException(bo.getTipCode(), StrUtil.format("下午暂停开始日期最早可以选择{}",
                            dtf.format(oneDate)));
                    }
                }

                if (ObjectUtil.isNotEmpty(bo.getPmDistributionSuspendEndTime())) {//结束时间不等于null
                    // 需要判断是否大于开始时间
                    LocalDate stopEndDate =
                        bo.getPmDistributionSuspendEndTime();//暂停时间

                    if (startStopDate.compareTo(stopEndDate) > 0) {
                        throw new ServiceException(bo.getTipCode(), "下午暂停结束日期不能大于暂停开始日期");
                    }
                }
            }
        }
    }


    @Override
    public Boolean checkIsAllowedModify() {
        LoginEmployee loginUser = CustomerLoginHelper.getLoginUser();
        String account = loginUser.getAccount();

        UserOrderDTO userOrderDTO = remoteOrderService.queryCustomerFirstOrder(account);
        if (null != userOrderDTO) {
            // 判断首单来源，小程序 OR 配送端
            if (NumberUtil.equals(TerminalTypeEnums.disribution.getValue(), userOrderDTO.getTerminalType())) {
                Date orderDate = userOrderDTO.getOrderDate();
                if (null != orderDate) {
                    LocalDate orderLocalDate = orderDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                    LocalDate now = LocalDate.now();
                    // 首单来源配送端 AND 订单日期 == 当前日期
                    return !orderLocalDate.isEqual(now);
                }
            }
        }

        return true;
    }

    /**
     * 校验排奶变更状态是否一致
     * 比较前端传入的变更状态与后端计算的变更状态，如果不一致则抛出异常提醒用户刷新页面重试
     *
     * @param bo 前端传入的地址BO对象，包含amDistributionDTOList和pmDistributionDTOList
     * @param address 数据库中的地址信息，包含amDistributionInfo和pmDistributionInfo
     */
    private void validateMilkDistributionChanges(CxrAddressBo bo, CxrCustomerAddress address) {
        log.info("开始校验排奶变更状态，地址ID：{}", bo.getId());

        try {
            // 校验上午排奶变更状态
            validateDistributionChanges(
                bo.getAlterAmTaste(),
                bo.getAmDistributionInfo(),
                address.getAmDistributionInfo(),
                "上午"
            );

            // 校验下午排奶变更状态
            validateDistributionChanges(
                bo.getAlterPmTaste(),
                bo.getPmDistributionInfo(),
                address.getPmDistributionInfo(),
                "下午"
            );

            log.info("排奶变更状态校验通过，地址ID：{}", bo.getId());
        } catch (Exception e) {
            log.error("排奶变更状态校验失败，地址ID：{}，错误信息：{}", bo.getId(), e.getMessage());
            throw e;
        }
    }

    /**
     * 校验单个时段（上午或下午）的排奶变更状态
     *
     * @param frontendDTOList 前端传入的排奶DTO列表，包含变更状态标识
     * @param frontendDistributionInfo 前端传入的排奶信息
     * @param backendDistributionInfo 后端数据库中的排奶信息
     * @param timeSlot 时段标识（上午/下午），用于错误提示
     */
    private void validateDistributionChanges(
            List<MilkDistributionDTO> frontendDTOList,
            CustomerAddressMilkDistributionInfo frontendDistributionInfo,
            CustomerAddressMilkDistributionInfo backendDistributionInfo,
            String timeSlot) {

        if (CollUtil.isEmpty(frontendDTOList)) {
            log.debug("{}排奶DTO列表为空，跳过校验", timeSlot);
            return;
        }

        // 计算后端的变更状态
        Map<Long, Boolean[]> backendChangeStatus = calculateBackendChangeStatus(
            frontendDistributionInfo, backendDistributionInfo);

        // 比较前端和后端的变更状态
        for (MilkDistributionDTO dto : frontendDTOList) {
            Long productId = dto.getProductId();
            Boolean[] backendChanges = backendChangeStatus.get(productId);

            if (backendChanges == null) {
                log.warn("{}排奶校验：产品ID {}在后端计算结果中不存在", timeSlot, productId);
                continue;
            }

            // 比较各个星期的变更状态
            // 索引对应：0-周一, 1-周二, 2-周三, 3-周四, 4-周五, 5-周六
            boolean[] frontendChanges = {
                dto.isModc(),  // 周一
                dto.isTudc(),  // 周二
                dto.isWedc(),  // 周三
                dto.isThdc(),  // 周四
                dto.isFrdc(),  // 周五
                dto.isSadc()   // 周六
            };

            String[] dayNames = {"周一", "周二", "周三", "周四", "周五", "周六"};

            for (int i = 0; i < frontendChanges.length; i++) {
                if (frontendChanges[i] != backendChanges[i]) {
                    String errorMsg = String.format(
                        "排奶变更状态不一致！%s产品[%s]的%s变更状态前后端不匹配，请刷新页面重试。前端状态：%s，后端状态：%s",
                        timeSlot, dto.getProductName(), dayNames[i],
                        frontendChanges[i], backendChanges[i]
                    );
                    log.error(errorMsg);
                    throw new ServiceException("排奶信息失败，请刷新页面重试");
                }
            }
        }

        log.debug("{}排奶变更状态校验通过", timeSlot);
    }

    /**
     * 计算后端的排奶变更状态
     * 通过比较前端传入的排奶信息与数据库中的排奶信息，计算出每个产品每天的变更状态
     *
     * @param frontendInfo 前端传入的排奶信息
     * @param backendInfo 数据库中的排奶信息
     * @return Map<产品ID, Boolean数组[周一到周六的变更状态]>
     */
    private Map<Long, Boolean[]> calculateBackendChangeStatus(
            CustomerAddressMilkDistributionInfo frontendInfo,
            CustomerAddressMilkDistributionInfo backendInfo) {

        Map<Long, Boolean[]> changeStatus = new HashMap<>();

        if (frontendInfo == null || backendInfo == null) {
            log.debug("前端或后端排奶信息为空，返回空的变更状态");
            return changeStatus;
        }

        // 比较各个星期的排奶信息
        compareAndSetChangeStatus(changeStatus, frontendInfo.getMonday(), backendInfo.getMonday(), 0);
        compareAndSetChangeStatus(changeStatus, frontendInfo.getTuesday(), backendInfo.getTuesday(), 1);
        compareAndSetChangeStatus(changeStatus, frontendInfo.getWednesday(), backendInfo.getWednesday(), 2);
        compareAndSetChangeStatus(changeStatus, frontendInfo.getThursday(), backendInfo.getThursday(), 3);
        compareAndSetChangeStatus(changeStatus, frontendInfo.getFriday(), backendInfo.getFriday(), 4);
        compareAndSetChangeStatus(changeStatus, frontendInfo.getSaturday(), backendInfo.getSaturday(), 5);

        return changeStatus;
    }

    /**
     * 比较单天的排奶信息并设置变更状态
     *
     * @param changeStatus 变更状态Map
     * @param frontendDayInfo 前端单天排奶信息
     * @param backendDayInfo 后端单天排奶信息
     * @param dayIndex 星期索引（0-5对应周一到周六）
     */
    private void compareAndSetChangeStatus(
            Map<Long, Boolean[]> changeStatus,
            List<MilkDistributionInfo> frontendDayInfo,
            List<MilkDistributionInfo> backendDayInfo,
            int dayIndex) {

        // 将后端信息转换为Map便于查找
        Map<Long, Long> backendQuantityMap = new HashMap<>();
        if (CollUtil.isNotEmpty(backendDayInfo)) {
            for (MilkDistributionInfo info : backendDayInfo) {
                backendQuantityMap.put(info.getProductId(), info.getQuantity());
            }
        }

        // 比较前端信息
        if (CollUtil.isNotEmpty(frontendDayInfo)) {
            for (MilkDistributionInfo frontendInfo : frontendDayInfo) {
                Long productId = frontendInfo.getProductId();
                Long frontendQuantity = frontendInfo.getQuantity();
                Long backendQuantity = backendQuantityMap.getOrDefault(productId, 0L);

                // 初始化产品的变更状态数组
                Boolean[] productChangeStatus = changeStatus.computeIfAbsent(productId, k -> new Boolean[6]);
                for (int i = 0; i < 6; i++) {
                    if (productChangeStatus[i] == null) {
                        productChangeStatus[i] = false;
                    }
                }

                // 判断是否有变更
                boolean hasChange = !ObjectUtil.equals(frontendQuantity, backendQuantity);
                productChangeStatus[dayIndex] = hasChange;

                log.debug("产品ID：{}，星期{}，前端数量：{}，后端数量：{}，是否变更：{}",
                    productId, dayIndex + 1, frontendQuantity, backendQuantity, hasChange);
            }
        }
    }

}



