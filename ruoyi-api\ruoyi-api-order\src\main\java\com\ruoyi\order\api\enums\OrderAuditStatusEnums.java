package com.ruoyi.order.api.enums;

import cn.hutool.core.util.NumberUtil;

/**
 * Description:
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/8/7 16:46
 */
public enum OrderAuditStatusEnums {

    /**
     *
     */
    WAIT_AUDIT(1, "待审核"),
    AUDITED(2, "已审核"),
    REJECT(3, "拒绝"),
    NO_AUDIT(4, "不需要审核"),
    TURN_DOWN(6, "已驳回");

    private int value;
    private String desc;

    OrderAuditStatusEnums(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static String getdesc(int value) {
        for (OrderAuditStatusEnums orderAuditStatusEnums : values()) {
            if (NumberUtil.equals(orderAuditStatusEnums.getValue(), value)) {
                return orderAuditStatusEnums.getDesc();
            }
        }
        return "";
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
