package com.ruoyi.order.manager.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.business.base.api.domain.CxrCustomer;
import com.ruoyi.business.base.api.domain.CxrEmployee;
import com.ruoyi.business.base.api.domain.CxrLongMilkStock;
import com.ruoyi.business.base.api.domain.CxrSite;
import com.ruoyi.business.base.api.domain.dto.TikTokOrderGoodsDTO;
import com.ruoyi.business.base.api.domain.json.BusinessAgent;
import com.ruoyi.business.base.api.domain.vo.CustomerMilkHistoryVO;
import com.ruoyi.business.base.api.dubbo.*;
import com.ruoyi.business.base.api.enums.OrderTypeEnums;
import com.ruoyi.common.core.enums.DeleteStatus;
import com.ruoyi.common.core.enums.OccupationStatus;
import com.ruoyi.common.core.enums.SaleStatus;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.service.DictService;
import com.ruoyi.common.core.utils.BeanCopyUtils;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.enums.AuditStatusEnums;
import com.ruoyi.common.enums.TerminalTypeEnums;
import com.ruoyi.common.redis.annotation.RedisDistributedLock;
import com.ruoyi.common.rocketmq.constant.mall.MQConstant;
import com.ruoyi.common.rocketmq.constant.order.OrderConstant;
import com.ruoyi.common.rocketmq.utils.MqUtil;
import com.ruoyi.common.satoken.utils.helper.LoginHelper;
import com.ruoyi.core.base.domain.CxrOrderAfterSale;
import com.ruoyi.core.base.domain.vo.CxrOrderAfterSaleVo;
import com.ruoyi.core.base.enums.RefundStatusEnums;
import com.ruoyi.core.base.mapper.CxrOrderAfterSaleMapper;
import com.ruoyi.order.api.domain.dto.TikTokReturnOrderDTO;
import com.ruoyi.order.api.enums.PayStatusEnums;
import com.ruoyi.order.common.domain.bo.CxrUserReturnOrderBO;
import com.ruoyi.order.common.domain.vo.CxrUserReturnOrderVO;
import com.ruoyi.order.common.domain.vo.HistoryStatisticsVo;
import com.ruoyi.order.common.domain.vo.OnlineRefundVo;
import com.ruoyi.order.common.domain.vo.OrderHistoryReturnVo;
import com.ruoyi.order.common.entity.CxrUserOrder;
import com.ruoyi.order.common.entity.CxrUserReturnOrder;
import com.ruoyi.order.common.mapper.CxrCustomerMapper;
import com.ruoyi.order.common.mapper.CxrUserReturnOrderMapper;
import com.ruoyi.order.disribution.domain.CxrPaymentDetails;
import com.ruoyi.order.disribution.mapper.CxrPaymentDetailsMapper;
import com.ruoyi.order.disribution.service.CommonOrderService;
import com.ruoyi.order.extend.mapper.CxrEmployeeMapper;
import com.ruoyi.order.extend.mapper.CxrSiteMapper;
import com.ruoyi.order.manager.domain.entity.MallOrderGoodsEntity;
import com.ruoyi.order.manager.domain.vo.RecordRefundListTotalVo;
import com.ruoyi.order.manager.domain.vo.RecordRefundListVo;
import com.ruoyi.order.manager.mapper.MallOrderGoodsMapper;
import com.ruoyi.order.manager.mapper.MallOrderMapper;
import com.ruoyi.order.manager.service.CxrUserOrderService;
import com.ruoyi.order.manager.service.CxrUserReturnOrderService;
import com.ruoyi.order.manager.strategy.behavior.AbstractCxrUserOrderBehavior;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/8/15 14:10
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class CxrUserReturnOrderServiceImpl
    extends ServiceImpl<CxrUserReturnOrderMapper, CxrUserReturnOrder>
    implements CxrUserReturnOrderService {

    @DubboReference
    private final RemoteEmployeeService remoteEmployeeService;

    private final AbstractCxrUserOrderBehavior abstractCxrUserOrderBehavior;

    @DubboReference
    private RemoteCustomerService remoteCustomerService;

    @DubboReference
    private RemoteSiteService remoteSiteService;

    private final CxrUserOrderService orderService;

    private final CxrUserReturnOrderMapper cxrUserReturnOrderMapper;

    private final CxrOrderAfterSaleMapper cxrOrderAfterSaleMapper;

    private final CommonOrderService commonOrderService;

    private final CxrEmployeeMapper commonEmployeeMapper;

    private final CxrSiteMapper commonSiteMapper;

    private final CxrCustomerMapper customerMapper;

    @Autowired
    private MallOrderGoodsMapper mallOrderGoodsMapper;
    @Autowired
    private MallOrderMapper mallOrderMapper;

    @DubboReference
    private RemoteLongMilkStockService remoteLongMilkStockService;

    @Autowired
    private MqUtil mqUtil;

    private final CxrPaymentDetailsMapper paymentDetailsMapper;


    private static final String ORDER_IMAGE = "鲜羊奶";

    private static final String GOODS_NAME = "group1/M00/00/15/rBPvwmNu_XOAN2lQAEiIoP39hZo569.png";

    @DubboReference
    private RemoteTikTokService remoteTikTokService;

    /**
     * 对订单新增接口
     *
     * @param cxrUserReturnOrderBO
     * @return
     */
    @Override
    public Long returnOrderAdd(CxrUserReturnOrderBO cxrUserReturnOrderBO) {
        commonOrderService.selectEmployeeLimit(cxrUserReturnOrderBO.getBusinessAgent(), null);
        CxrCustomer cxrCustomer = remoteCustomerService.getById(cxrUserReturnOrderBO.getCustomerId());
        if (cxrCustomer.getCustomerStock() == 0) {
            throw new ServiceException("客户库存为0不能提交退订单!");
        }
        orderBusinessAgentComplete(cxrUserReturnOrderBO.getBusinessAgent());
        OrderTypeEnums orderTypeEnums = OrderTypeEnums.getType(cxrUserReturnOrderBO.getOrderType());
        return abstractCxrUserOrderBehavior.executeOrderAddOrUpdate(cxrUserReturnOrderBO, orderTypeEnums);
    }


    /**
     * 根据支付订单生成退订单
     *
     * @param cxrUserReturnOrderBO
     * @return
     */
    @Override
    public Long returnOrderAdd(Long afterSalesId) {

        CxrOrderAfterSale cxrOrderAfterSale = cxrOrderAfterSaleMapper.selectById(afterSalesId);
        CxrUserOrder order = orderService.lambdaQuery()
            .eq(CxrUserOrder::getOrderNo, cxrOrderAfterSale.getOrderNo())
            .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()).one();
        int freshMilkCancelQuantity = order.getOrderQuantity() + order.getFreshMilkGiveQuantity();

        CxrCustomer cxrCustomer = remoteCustomerService.getById(order.getCustomerId());
        Integer customerStock = cxrCustomer.getCustomerStock();
        if (customerStock == 0) {
            throw new ServiceException("客户库存为0不能提交退订单!");
        }
        log.info("库存数量：{}，退订数量{}", customerStock, freshMilkCancelQuantity);
        if (freshMilkCancelQuantity > customerStock) {
            return null;
        }

        Long orderId = order.getId();
        Integer longMilkGiveQuantity = order.getLongMilkGiveQuantity();
        if (longMilkGiveQuantity > 0) {
            CxrLongMilkStock cxrLongMilkStock = remoteLongMilkStockService.queryByOrderId(orderId);
            if (cxrLongMilkStock.getApplyNum() > 0 || cxrLongMilkStock.getUnregNum() > 0) {
                return null;
            }
        }

        CxrUserReturnOrder returnOrder = cxrUserReturnOrderMapper.selectOne(
            new LambdaQueryWrapper<CxrUserReturnOrder>().eq(CxrUserReturnOrder::getOrderId, orderId));
        if (returnOrder != null) {
            throw new ServiceException("订单已经生成退订单");
        }

        CxrUserReturnOrderBO cxrUserReturnOrderBO = new CxrUserReturnOrderBO();
        OrderTypeEnums orderType = OrderTypeEnums.RETURN_ORDER;
        cxrUserReturnOrderBO.setOrderType(orderType.getValue());
        cxrUserReturnOrderBO.setOrderDate(new Date());
        cxrUserReturnOrderBO.setSiteId(order.getSiteId());
        cxrUserReturnOrderBO.setOrderId(orderId);

        cxrUserReturnOrderBO.setBusinessAgent(order.getBusinessAgent());
        cxrUserReturnOrderBO.setCustomerId(order.getCustomerId());
        cxrUserReturnOrderBO.setCustomerPhone(order.getCustomerPhone());
        cxrUserReturnOrderBO.setCustomerName(order.getCustomerName());
        cxrUserReturnOrderBO.setCustomerAdress(order.getCustomerAdress());
        cxrUserReturnOrderBO.setFreshMilkRefundQuantity(order.getOrderQuantity());
        cxrUserReturnOrderBO.setFreshMilkCancelQuantity(freshMilkCancelQuantity);
        cxrUserReturnOrderBO.setLongMilkCancelQuantity(longMilkGiveQuantity);
        cxrUserReturnOrderBO.setFreshMilkCancelRestQuantity(customerStock - freshMilkCancelQuantity);
        cxrUserReturnOrderBO.setRefundAmount(order.getAmount());
        cxrUserReturnOrderBO.setAmount(order.getAmount());
        cxrUserReturnOrderBO.setRebates(BigDecimal.ZERO);
        cxrUserReturnOrderBO.setAfterSalesId(afterSalesId);
        orderBusinessAgentComplete(cxrUserReturnOrderBO.getBusinessAgent());
        Long id = abstractCxrUserOrderBehavior.executeOrderAddOrUpdate(cxrUserReturnOrderBO, orderType);
        if (id != null) {
            boolean audit = orderService.audit(id, true, null, null);
            log.info("退订单审核结果：{}", audit);
            if (audit) {
                //收钱退款
                mqUtil.sendSyncMessage(OrderConstant.ORDER_TOPIC, OrderConstant.ORDER_REFUND_TAG, id.toString());

                //去掉锁库存代码
//                if (cxrOrderAfterSale.getLockFlag()){
//                    returnOrder = cxrUserReturnOrderMapper.selectOne(new LambdaQueryWrapper<CxrUserReturnOrder>().eq(CxrUserReturnOrder::getUserOrderId, id));
//                    Map<String,Object> map = new HashMap<>();
//                    map.put("orderId",orderId);
//                    map.put("afterSalesId",afterSalesId);
//                    map.put("customerId",returnOrder.getCustomerId());
//                    map.put("qty",0 - returnOrder.getFreshMilkCancelQuantity());
//                    mqUtil.sendSyncMessage(OrderConstant.ORDER_TOPIC,OrderConstant.ORDER_REFUND_CUSTOMER_TAG, JSONObject.toJSONString(map));
//                }

                return id;
            } else {
                orderService.deleteOrder(id);
                return null;
            }
        }
        return id;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    @RedisDistributedLock("'cxr:tiktokOrder:tiktokOrderRefund' + #orderNo")
    public TikTokReturnOrderDTO tiktokOrderRefund(String orderNo) {

        CxrUserOrder order = orderService.getBaseMapper().selectOne(Wrappers.<CxrUserOrder>lambdaQuery().eq(CxrUserOrder::getOrderNo, orderNo)
            .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));
        int freshMilkCancelQuantity = order.getOrderQuantity() + order.getFreshMilkGiveQuantity();

        CxrCustomer cxrCustomer = remoteCustomerService.getById(order.getCustomerId());
        Integer customerStock = cxrCustomer.getCustomerStock();

        log.info("库存数量：{}，退订数量{}", customerStock, freshMilkCancelQuantity);

        Long orderId = order.getId();
        Integer longMilkGiveQuantity = order.getLongMilkGiveQuantity();
        CxrUserReturnOrder returnOrder = cxrUserReturnOrderMapper.selectOne(Wrappers.<CxrUserReturnOrder>lambdaQuery().eq(CxrUserReturnOrder::getOrderId, orderId));
        if (returnOrder != null) {
            log.info("订单已经生成退订单");
            Long userOrderId = returnOrder.getUserOrderId();
            CxrUserOrder cxrUserOrder = orderService.getById(userOrderId);
            if (cxrUserOrder.getAuditStatus() == AuditStatusEnums.Audit.code().intValue()) {
                TikTokReturnOrderDTO tikTokReturnOrderDTO = new TikTokReturnOrderDTO();
                tikTokReturnOrderDTO.setCxrReturnOrderId(userOrderId);
                tikTokReturnOrderDTO.setActualRefundFreshMilkQty(cxrUserOrder.getFreshMilkReturnQuantity());
                if ("".equals(cxrUserOrder.getTiktokOrderRefundNo())) {
                    orderService.lambdaUpdate().eq(CxrUserOrder::getId, orderId).set(CxrUserOrder::getTiktokOrderRefundNo, cxrUserOrder.getOrderNo()).update();
                }
                mqUtil.sendSyncMessage(OrderConstant.ORDER_TOPIC, OrderConstant.ORDER_EMPLOYEE_TAG, userOrderId.toString());
                return tikTokReturnOrderDTO;
            }
            orderService.lambdaUpdate().eq(CxrUserOrder::getId, orderId).set(CxrUserOrder::getTiktokOrderRefundNo, "").update();
            return null;
        } else {
            orderService.lambdaUpdate().eq(CxrUserOrder::getId, orderId).set(CxrUserOrder::getTiktokOrderRefundNo, "").update();
        }

        CxrUserReturnOrderBO cxrUserReturnOrderBO = new CxrUserReturnOrderBO();
        OrderTypeEnums orderType = OrderTypeEnums.RETURN_ORDER;
        cxrUserReturnOrderBO.setOrderType(orderType.getValue());
        cxrUserReturnOrderBO.setOrderDate(new Date());
        cxrUserReturnOrderBO.setSiteId(order.getSiteId());
        cxrUserReturnOrderBO.setOrderId(orderId);

        cxrUserReturnOrderBO.setBusinessAgent(order.getBusinessAgent());
        cxrUserReturnOrderBO.setCustomerId(order.getCustomerId());
        cxrUserReturnOrderBO.setCustomerPhone(order.getCustomerPhone());
        cxrUserReturnOrderBO.setCustomerName(order.getCustomerName());
        cxrUserReturnOrderBO.setCustomerAdress(order.getCustomerAdress());
        cxrUserReturnOrderBO.setFreshMilkRefundQuantity(order.getOrderQuantity());
        cxrUserReturnOrderBO.setFreshMilkCancelQuantity(freshMilkCancelQuantity);
        cxrUserReturnOrderBO.setLongMilkCancelQuantity(longMilkGiveQuantity);
        cxrUserReturnOrderBO.setFreshMilkCancelRestQuantity(customerStock - freshMilkCancelQuantity);
        cxrUserReturnOrderBO.setRefundAmount(order.getAmount());
        cxrUserReturnOrderBO.setAmount(order.getAmount());
        cxrUserReturnOrderBO.setRebates(BigDecimal.ZERO);
        cxrUserReturnOrderBO.setTerminalType(TerminalTypeEnums.tiktok.getValue());

        orderBusinessAgentComplete(cxrUserReturnOrderBO.getBusinessAgent());

        Long id = abstractCxrUserOrderBehavior.executeOrderAddOrUpdate(cxrUserReturnOrderBO, orderType);
        log.info("原单{},创建退订单返回：{}", orderNo, id);
        if (id == null) {
            log.info("抖音订单{}未生成退订单!", orderNo);
            return null;
        }
        try {
            boolean audit = orderService.audit(id, true, null, null);
            log.info("{}退订单审核结果：{}", id, audit);
        } catch (Exception e) {
            log.error("原支付单号：{},{}", orderNo, e.getMessage());
        }

        CxrUserOrder refundOrder = orderService.getById(id);
        if (refundOrder == null) {
            log.info("抖音订单{}未生成退订单!2", orderNo);
            return null;
        }

        int auditStatus = AuditStatusEnums.Audit.code();
        log.info("抖音订单{}退订单审核结果2:{}", orderNo, auditStatus);
        if (auditStatus == refundOrder.getAuditStatus()) {
            TikTokReturnOrderDTO tikTokReturnOrderDTO = new TikTokReturnOrderDTO();
            tikTokReturnOrderDTO.setCxrReturnOrderId(id);
            tikTokReturnOrderDTO.setActualRefundFreshMilkQty(freshMilkCancelQuantity);
            orderService.lambdaUpdate().eq(CxrUserOrder::getId, orderId).set(CxrUserOrder::getTiktokOrderRefundNo, refundOrder.getOrderNo()).update();
            mqUtil.sendSyncMessage(OrderConstant.ORDER_TOPIC, OrderConstant.ORDER_EMPLOYEE_TAG, id.toString());
            return tikTokReturnOrderDTO;
        }
        orderService.deleteOrder(id);
        log.info("原支付单号{}，退订单审核失败{}", orderNo, id);
        return null;
    }

    /**
     * 默认执行退款操作
     *
     * @param afterSalesId
     * @return
     */
    @Override
    public Boolean returnGiveOrderAddAndAudit(Long afterSalesId) {
        return this.returnGiveOrderAddAndAudit(afterSalesId, true);
    }

    /**
     * 根据支付订单生成退订单
     *
     * @param
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean returnGiveOrderAddAndAudit(Long afterSalesId, Boolean refundActive) {

        CxrOrderAfterSale cxrOrderAfterSale = cxrOrderAfterSaleMapper.selectById(afterSalesId);
        Long customerId = cxrOrderAfterSale.getCustomerId();
        CxrCustomer cxrCustomer = remoteCustomerService.getById(customerId);
        Integer customerStock = cxrCustomer.getCustomerStock();

        Integer returnFreshMilkQty = cxrOrderAfterSale.getReturnFreshMilkQty();
        log.info("库存数量：{}，退订数量{}", customerStock, returnFreshMilkQty);

        //鲜奶赠送单
        String giveMallOrderNo = cxrOrderAfterSale.getGiveMallOrderNo();
        CxrUserOrder order = orderService.lambdaQuery()
            .eq(CxrUserOrder::getOrderNo, giveMallOrderNo)
            .eq(CxrUserOrder::getOrderType, OrderTypeEnums.GIVE_ORDER.getValue())
            .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()).one();
        Long orderId = order.getId();

        CxrUserReturnOrderBO cxrUserReturnOrderBO = new CxrUserReturnOrderBO();
        OrderTypeEnums orderType = OrderTypeEnums.RETURN_ORDER;
        cxrUserReturnOrderBO.setOrderType(orderType.getValue());
        cxrUserReturnOrderBO.setOrderDate(new Date());
        cxrUserReturnOrderBO.setSiteId(order.getSiteId());
        cxrUserReturnOrderBO.setOrderId(orderId);

        cxrUserReturnOrderBO.setBusinessAgent(order.getBusinessAgent());
        cxrUserReturnOrderBO.setCustomerId(order.getCustomerId());
        cxrUserReturnOrderBO.setCustomerPhone(order.getCustomerPhone());
        cxrUserReturnOrderBO.setCustomerName(order.getCustomerName());
        cxrUserReturnOrderBO.setCustomerAdress(order.getCustomerAdress());
        cxrUserReturnOrderBO.setFreshMilkRefundQuantity(order.getOrderQuantity());
        cxrUserReturnOrderBO.setFreshMilkCancelQuantity(returnFreshMilkQty);
        cxrUserReturnOrderBO.setLongMilkCancelQuantity(0);
        cxrUserReturnOrderBO.setFreshMilkCancelRestQuantity(customerStock - returnFreshMilkQty);
        cxrUserReturnOrderBO.setRefundAmount(BigDecimal.ZERO);
        cxrUserReturnOrderBO.setAmount(BigDecimal.ZERO);
        cxrUserReturnOrderBO.setRebates(BigDecimal.ZERO);
        cxrUserReturnOrderBO.setAfterSalesId(afterSalesId);
        orderBusinessAgentComplete(cxrUserReturnOrderBO.getBusinessAgent());
        Long id = abstractCxrUserOrderBehavior.executeOrderAddOrUpdate(cxrUserReturnOrderBO, orderType);
        if (id != null) {
            boolean audit = abstractCxrUserOrderBehavior.executeCxrUserOrderAuditSubLock(id, true);
            log.info("退订单审核结果：{}", audit);
            if (audit) {
                if (refundActive) {
                    mqUtil.sendSyncMessage(MQConstant.MALL_ORDER_TOPIC, MQConstant.MC_AFTER_RF_REFUND_TAG,
                        afterSalesId.toString());
                }
                //设置已打款
                orderService.lambdaUpdate().set(CxrUserOrder::getRefundSuccessFlag, true)
                    .eq(CxrUserOrder::getId, id).update();
                return true;
            } else {
                orderService.del(id, null);
            }
        }
        return false;
    }

    /**
     * 退订单更新
     *
     * @param cxrUserReturnOrderBO
     * @return
     */
    @Override
    public boolean updateReturnOrder(CxrUserReturnOrderBO cxrUserReturnOrderBO) {
        commonOrderService.selectEmployeeLimit(cxrUserReturnOrderBO.getBusinessAgent(),
            cxrUserReturnOrderBO.getUserOrderId());
        orderBusinessAgentComplete(cxrUserReturnOrderBO.getBusinessAgent());
        OrderTypeEnums orderTypeEnums = OrderTypeEnums.getType(cxrUserReturnOrderBO.getOrderType());
        return abstractCxrUserOrderBehavior.executeOrderUpdate(cxrUserReturnOrderBO, orderTypeEnums);
    }

    /**
     * 根据id查询详情
     *
     * @param id
     * @return
     */
    @Override
    public CxrUserReturnOrderVO detail(Long id) {
        // 订单审核  没用
//        abstractCxrUserOrderBehavior.executeOrderDetail(id);
        // 查询returnorder表
        CxrUserReturnOrder cxrUserReturnOrder =
            getBaseMapper()
                .selectOne(
                    new LambdaQueryWrapper<CxrUserReturnOrder>()
                        .eq(CxrUserReturnOrder::getUserOrderId, id));

        CxrUserOrderService cxrUserOrderService = SpringUtils.getBean(CxrUserOrderService.class);

        if (ObjectUtil.isEmpty(cxrUserReturnOrder)) {
            CxrUserOrder cxrUserOrder = cxrUserOrderService.getBaseMapper().selectById(id);
            CxrUserReturnOrderVO cxrUserReturnOrderVO =
                BeanUtil.copyProperties(cxrUserOrder, CxrUserReturnOrderVO.class);
            //            CxrUserReturnOrderVO

            if (ObjectUtil.isNotEmpty(cxrUserOrder)) {
                TikTokOrderGoodsDTO dto = remoteTikTokService.getOrderGoods(cxrUserOrder.getId());
                if (ObjectUtil.isNotEmpty(dto)) {
                    cxrUserReturnOrderVO.setTiktokOrderNo(dto.getOutOrderNo());
                }
            }
            return cxrUserReturnOrderVO;
        }
        // 只查询  auditStatus

        CxrUserOrder cxrUserOrder =
            cxrUserOrderService.getOne(
                new LambdaQueryWrapper<CxrUserOrder>()
                    .select(CxrUserOrder::getAuditStatus, CxrUserOrder::getId, CxrUserOrder::getCustomerAdress,
                        CxrUserOrder::getOrderQuantity, CxrUserOrder::getRefundNoAuditReasons, CxrUserOrder::getDeliverySites, CxrUserOrder::getCustomerId)
                    .eq(CxrUserOrder::getId, cxrUserReturnOrder.getUserOrderId()));

        // copy
        CxrUserReturnOrderVO cxrUserReturnOrderVO =
            BeanUtil.copyProperties(cxrUserReturnOrder, CxrUserReturnOrderVO.class);

//        if (StrUtil.isNotEmpty(cxrUserOrder.getDeliverySites())) {
//            List<DeliverySiteDTO> dtoList = JSONUtil.toList(cxrUserOrder.getDeliverySites(), DeliverySiteDTO.class);
//            cxrUserReturnOrderVO.setDeliverySites(dtoList);
//        }
        cxrUserReturnOrderVO.setDeliverySites(cxrUserOrder.getDeliverySites());
        cxrUserReturnOrderVO.setRefundNoAuditReasons(cxrUserOrder.getRefundNoAuditReasons());
        TikTokOrderGoodsDTO dto = remoteTikTokService.getOrderGoods(cxrUserReturnOrder.getOrderId());
        if (ObjectUtil.isNotEmpty(dto)) {
            cxrUserReturnOrderVO.setTiktokOrderNo(dto.getOutOrderNo());
            cxrUserReturnOrderVO.setOrderQuantity(cxrUserOrder.getOrderQuantity());
        }

        DictService dictService = SpringUtils.getBean(DictService.class);
        String rrCode = cxrUserReturnOrderVO.getRrCode();
        if (StringUtils.isNotBlank(rrCode)) {
            cxrUserReturnOrderVO.setRrName(dictService.getDictLabel("order_refund_reason", rrCode, ","));
        }
        // 添加审核状态

        CxrUserOrder userOrder = null;
        if (ObjectUtil.isNotEmpty(cxrUserReturnOrder.getPayOrderId())) {
            userOrder = cxrUserOrderService.getById(cxrUserReturnOrder.getPayOrderId());
            OnlineRefundVo refundVo = new OnlineRefundVo();

            List<CxrUserOrder> userOrders = orderService.getBaseMapper()
                .selectList(new LambdaQueryWrapper<CxrUserOrder>()
                    .eq(CxrUserOrder::getPayOrderId, cxrUserReturnOrder.getPayOrderId())
                    .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                );
            processAnOrderHistory(userOrders, refundVo);
            if (CollUtil.isNotEmpty(refundVo.getOrderHistoryList())) {
                cxrUserReturnOrderVO.setOrderHistoryList(refundVo.getOrderHistoryList());
                cxrUserReturnOrderVO.setHistoryStatisticsVo(refundVo.getHistoryStatisticsVo());
            }
        }

        if (ObjectUtil.isNotEmpty(userOrder) &&
            ObjectUtil.equals(userOrder.getTerminalType(),
                TerminalTypeEnums.xcx.getValue())) {

            MallOrderGoodsEntity orderGoods = mallOrderGoodsMapper.queryByOrderId(userOrder.getOrderNo());
            if (ObjectUtil.isNotEmpty(orderGoods)) {
                cxrUserReturnOrderVO.setOrderImage(orderGoods.getListPicUrl());
                cxrUserReturnOrderVO.setGoodsName(orderGoods.getGoodsName());
                cxrUserReturnOrderVO.setSpecifications(orderGoods.getGoodsSpecifitionNameValue());
                cxrUserReturnOrderVO.setCouponAmount(orderGoods.getCouponAmount());
            }
        } else {
            cxrUserReturnOrderVO.setOrderImage(GOODS_NAME);
            cxrUserReturnOrderVO.setGoodsName(ORDER_IMAGE);
            cxrUserReturnOrderVO.setCouponAmount(new BigDecimal(0));
        }

        if (ObjectUtil.isNotEmpty(userOrder)) {
            cxrUserReturnOrderVO.setOrderInfoQuantity(userOrder.getOrderQuantity());
            cxrUserReturnOrderVO.setInfoAmount(userOrder.getAmount());
            cxrUserReturnOrderVO.setPracticalAmount(userOrder.getAmount());// 小程序例外
            cxrUserReturnOrderVO.setInfoLongMilkGiveQuantity(userOrder.getLongMilkGiveQuantity());
            cxrUserReturnOrderVO.setInfoFreshMilkGiveQuantity(userOrder.getFreshMilkGiveQuantity());
            // 常温奶系列
            CxrLongMilkStock cxrLongMilkStock =
                remoteLongMilkStockService.queryByOrderId(userOrder.getId());
            if (ObjectUtil.isNotEmpty(cxrLongMilkStock)) {
                cxrUserReturnOrderVO.setLongMilkId(cxrLongMilkStock.getId());
                cxrUserReturnOrderVO.setLongMilkQuantity(userOrder.getLongMilkGiveQuantity());
                cxrUserReturnOrderVO.setApplyNum(cxrLongMilkStock.getApplyNum());
                cxrUserReturnOrderVO.setSurplusNum(cxrLongMilkStock.getSurplusNum());
                cxrUserReturnOrderVO.setOverdueNum(cxrLongMilkStock.getOverdueNum());

            } else {
                cxrUserReturnOrderVO.setLongMilkQuantity(0);
                cxrUserReturnOrderVO.setApplyNum(0);
                cxrUserReturnOrderVO.setSurplusNum(0);
                cxrUserReturnOrderVO.setOverdueNum(0);
            }
        }

        cxrUserReturnOrderVO.setAuditStatus(cxrUserOrder.getAuditStatus());
        cxrUserReturnOrderVO.setId(cxrUserOrder.getId());
        cxrUserReturnOrderVO.setCustomerAdress(ObjectUtil.isEmpty(cxrUserOrder.getCustomerAdress()) ?
            cxrUserReturnOrder.getCustomerAdress()
            :
            cxrUserOrder.getCustomerAdress());
        // 设置员工等级

        Long afterSalesId = cxrUserReturnOrder.getAfterSalesId();
        if (afterSalesId != null) {
            CxrOrderAfterSale cxrOrderAfterSale = cxrOrderAfterSaleMapper.selectById(afterSalesId);
            if (cxrOrderAfterSale != null) {
                cxrUserReturnOrderVO.setAfterSalesNo(cxrOrderAfterSale.getAfterSaleNo());
                CxrOrderAfterSaleVo cxrOrderAfterSaleVo = new CxrOrderAfterSaleVo();
                BeanUtils.copyProperties(cxrOrderAfterSale, cxrOrderAfterSaleVo);
                cxrUserReturnOrderVO.setCxrOrderAfterSaleVo(cxrOrderAfterSaleVo);
                cxrUserReturnOrderVO.setOrderMoney(
                    cxrOrderAfterSale.getTotalAmount().subtract(cxrOrderAfterSale.getDiscountsAmount()));
            }
        }
        // 添加站点信息
        CxrSite cxrSite = remoteSiteService.queryId(cxrUserReturnOrder.getSiteId());
        if (!ObjectUtil.isEmpty(cxrSite)) {
            // 省市区
            cxrUserReturnOrderVO.setProvince(cxrSite.getProvice());
            cxrUserReturnOrderVO.setArea(cxrSite.getArea());
            cxrUserReturnOrderVO.setCity(cxrSite.getCity());
        }
        // 查询奶量
        CustomerMilkHistoryVO customerMilkHistoryVO =
            remoteCustomerService.customerMilkHistoryVO(cxrUserReturnOrderVO.getCustomerId());
        // 前端要的一些字段
        cxrUserReturnOrderVO.setSiteName(cxrSite.getName());
        // 添加剩余量
        cxrUserReturnOrderVO.setSurplusQuantity(customerMilkHistoryVO.getCustomerStock());
        cxrUserReturnOrderVO.setLongMilkSurplusQuantity(
            customerMilkHistoryVO.getLongMilkcustomerStock());
        // 鲜奶剩余数量  计算    鲜奶剩余量 -退奶总数量
        cxrUserReturnOrderVO.setFreshMilkRestQuantity(
            customerMilkHistoryVO.getCustomerStock() - cxrUserReturnOrder.getFreshMilkCancelQuantity());

        // 设置时间
        cxrUserReturnOrderVO.setOrderDate(cxrUserReturnOrder.getOrderDate());
        return cxrUserReturnOrderVO;
    }

    @Override
    public CustomerMilkHistoryVO customerMilkHistoryVO(Long customerId) {

        CustomerMilkHistoryVO customerMilkHistoryVO =
            remoteCustomerService.customerMilkHistoryVO(customerId);

        return customerMilkHistoryVO;
    }

    @Override
    public boolean refundPayment(Long id) {
        CxrUserOrder returnOrder = orderService.getById(id);
        CxrUserReturnOrder userReturnOrder = cxrUserReturnOrderMapper.selectOne(
            new LambdaQueryWrapper<CxrUserReturnOrder>().eq(CxrUserReturnOrder::getUserOrderId, id));
        if (!returnOrder.getRefundSuccessFlag() && userReturnOrder.getAfterSalesId() != null) {
            boolean update = orderService.lambdaUpdate().set(CxrUserOrder::getRefundSuccessFlag, true)
                .eq(CxrUserOrder::getId, returnOrder.getId()).update();
            if (update) {
                return cxrOrderAfterSaleMapper.update(null, Wrappers.lambdaUpdate(CxrOrderAfterSale.class)
                    .set(CxrOrderAfterSale::getSaleStatus, SaleStatus.COMPLETED.getValue())
                    .set(CxrOrderAfterSale::getReturnStatus, RefundStatusEnums.REFUND_FINISH.getValue())
                    .eq(CxrOrderAfterSale::getId, userReturnOrder.getAfterSalesId())) > 0;
            }
            return false;
        }
        return false;
    }

    @Override
    public OnlineRefundVo onlineRefund(Long id) {
        CxrUserOrder userOrder = orderService.getById(id);

        if (ObjectUtil.isEmpty(userOrder)) {
            throw new ServiceException("订单不存在请刷新页面后重试！");
        }

        if (ObjectUtil.equals(userOrder.getTerminalType(), TerminalTypeEnums.manager.getValue())) {
            throw new ServiceException("后台新增订单暂不支持在线退款！");
        }

        OnlineRefundVo refundVo = BeanUtil.copyProperties(userOrder, OnlineRefundVo.class);
        refundVo.setOrderDate(new Date());
        refundVo.setTotalQuantity(userOrder.getOrderQuantity());
        if (ObjectUtil.equals(userOrder.getTerminalType(), TerminalTypeEnums.xcx.getValue())) {
            MallOrderGoodsEntity orderGoods = mallOrderGoodsMapper.queryByOrderId(userOrder.getOrderNo());
            if (ObjectUtil.isNotEmpty(orderGoods)) {
                refundVo.setOrderImage(orderGoods.getListPicUrl());
                refundVo.setGoodsName(orderGoods.getGoodsName());
                refundVo.setSpecifications(orderGoods.getGoodsSpecifitionNameValue());
                refundVo.setCouponAmount(orderGoods.getCouponAmount());
            }

        } else {

            refundVo.setOrderImage(GOODS_NAME);
            refundVo.setGoodsName(ORDER_IMAGE);
            refundVo.setCouponAmount(new BigDecimal(0));
        }

        // 鲜奶客户库存查当前
        CxrCustomer customer = customerMapper.selectById(userOrder.getCustomerId());
        refundVo.setSurplusQuantity(customer.getCustomerStock());// 默认查询该客户当前账户的鲜奶剩余数量

        refundVo.setOrderInfoQuantity(userOrder.getOrderQuantity());
        refundVo.setInfoAmount(userOrder.getAmount());
        refundVo.setPracticalAmount(userOrder.getAmount());// 小程序例外

        refundVo.setInfoLongMilkGiveQuantity(userOrder.getLongMilkGiveQuantity());
        refundVo.setInfoFreshMilkGiveQuantity(userOrder.getFreshMilkGiveQuantity());

        List<CxrUserOrder> userOrders = orderService.getBaseMapper()
            .selectList(new LambdaQueryWrapper<CxrUserOrder>()
                .eq(CxrUserOrder::getPayOrderId, id)
                .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            );

        Integer freshMilk =
            userOrder.getOrderQuantity() + userOrder.getFreshMilkGiveQuantity()
                - userOrder.getFreshMilkSentQuantity();
        if (CollUtil.isNotEmpty(userOrders)) {
            List<Long> returnOrderIds = userOrders.stream()
                .filter(x -> ObjectUtil.equals(x.getAuditStatus(), AuditStatusEnums.ToAudit.code()) ||
                    ObjectUtil.equals(x.getAuditStatus(), AuditStatusEnums.Audit.code())
                ).map(CxrUserOrder::getId).collect(Collectors.toList());

            List<CxrUserReturnOrder> userReturnOrders = cxrUserReturnOrderMapper.selectList(
                new LambdaQueryWrapper<CxrUserReturnOrder>()
                    .in(CxrUserReturnOrder::getUserOrderId, returnOrderIds)
                    .eq(CxrUserReturnOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            );
            if (CollUtil.isNotEmpty(userReturnOrders)) {
                int sum = userReturnOrders.stream().mapToInt(CxrUserReturnOrder::getFreshMilkCancelQuantity).sum();
                refundVo.setFreshMilkCancelRestQuantity(freshMilk - sum);
                refundVo.setFreshMilkCancelQuantity(freshMilk - sum);
            } else {
                refundVo.setFreshMilkCancelRestQuantity(freshMilk);
                refundVo.setFreshMilkCancelQuantity(freshMilk);
            }
        } else {
            refundVo.setFreshMilkCancelRestQuantity(freshMilk);
            refundVo.setFreshMilkCancelQuantity(freshMilk);
        }

        // 常温奶系列
        CxrLongMilkStock cxrLongMilkStock =
            remoteLongMilkStockService.queryByOrderId(userOrder.getId());
        if (ObjectUtil.isNotEmpty(cxrLongMilkStock)) {
            refundVo.setLongMilkId(cxrLongMilkStock.getId());
            refundVo.setLongMilkQuantity(userOrder.getLongMilkGiveQuantity());
            refundVo.setApplyNum(cxrLongMilkStock.getApplyNum());
            refundVo.setSurplusNum(cxrLongMilkStock.getSurplusNum());
            refundVo.setOverdueNum(cxrLongMilkStock.getOverdueNum());
            refundVo.setLongMilkSurplusQuantity(cxrLongMilkStock.getSurplusNum());
            refundVo.setLongMilkSentQuantity(cxrLongMilkStock.getApplyNum());

        } else {
            refundVo.setLongMilkQuantity(0);
            refundVo.setApplyNum(0);
            refundVo.setSurplusNum(0);
            refundVo.setOverdueNum(0);
            refundVo.setLongMilkSurplusQuantity(0);
        }

        refundVo.setFreshMilkCancelRestQuantity(
            refundVo.getFreshMilkCancelRestQuantity() - refundVo.getFreshMilkCancelQuantity());
        List<Long> proxyIds = userOrder.getBusinessAgent().stream().filter(x -> ObjectUtil.isNotEmpty(x.getProxyId())
        ).map(BusinessAgent::getProxyId).collect(Collectors.toList());

        refundVo.setBusinessAgent(checkSalesAgent(proxyIds));
        refundVo.setCheckCustomerPhoneMap(checkCustomerPhone(userOrder.getCustomerPhone()));
        processAnOrderHistory(userOrders, refundVo);
        return refundVo;
    }

    @Override
    public Map<String, Object> checkCustomerPhone(String phone) {

        Map<String, Object> data = new HashMap<>();

        List<CxrEmployee> cxrEmployees = commonEmployeeMapper.selectList(new LambdaQueryWrapper<CxrEmployee>()
            .select(CxrEmployee::getId, CxrEmployee::getPhone, CxrEmployee::getName,
                CxrEmployee::getEmergencyContactPhone, CxrEmployee::getFamilyDepositBankCardPhone)
            .eq(CxrEmployee::getPhone, phone)
            .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
        );
        if (CollUtil.isNotEmpty(cxrEmployees)) {
            List<String> collect = cxrEmployees.stream().map(x -> {
                return StrUtil.format("{} 手机号 {}", x.getName(), x.getPhone());
            }).collect(Collectors.toList());
            data.put("cellPhone", collect);
        }

        List<CxrEmployee> emergencyContact = commonEmployeeMapper.selectList(new LambdaQueryWrapper<CxrEmployee>()
            .select(CxrEmployee::getId, CxrEmployee::getPhone, CxrEmployee::getName,
                CxrEmployee::getEmergencyContactPhone, CxrEmployee::getFamilyDepositBankCardPhone)
            .eq(CxrEmployee::getEmergencyContactPhone, phone)
            .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
        );
        if (CollUtil.isNotEmpty(emergencyContact)) {
            List<String> collect = emergencyContact.stream().map(x -> {
                return StrUtil.format("{} 紧急联系人手机号 {}", x.getName(), phone);
            }).collect(Collectors.toList());
            data.put("emergencyContact", collect);
        }

        List<CxrEmployee> familyDepositBankCardPhone = commonEmployeeMapper.selectList(
            new LambdaQueryWrapper<CxrEmployee>()
                .select(CxrEmployee::getId, CxrEmployee::getPhone, CxrEmployee::getName,
                    CxrEmployee::getEmergencyContactPhone, CxrEmployee::getFamilyDepositBankCardPhone)
                .eq(CxrEmployee::getFamilyDepositBankCardPhone, phone)
                .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
        );

        if (CollUtil.isNotEmpty(familyDepositBankCardPhone)) {
            List<String> collect = familyDepositBankCardPhone.stream().map(x -> {
                return StrUtil.format("{} 家属开卡手机号 {}", x.getName(), phone);
            }).collect(Collectors.toList());
            data.put("familyCard", collect);
        }

        return CollUtil.isNotEmpty(data) ? data : null;
    }

    @Override
    public List<BusinessAgent> checkSalesAgent(List<Long> salesAgenIds) {
        List<CxrEmployee> cxrEmployees = commonEmployeeMapper.selectList(
            new LambdaQueryWrapper<CxrEmployee>()
                .in(CxrEmployee::getId, salesAgenIds)
                .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
        );

        if (CollUtil.isEmpty(cxrEmployees)) {
            return null;
        }

        List<Long> siteIds = cxrEmployees.stream().map(CxrEmployee::getCxrSiteId).collect(Collectors.toList());
        List<CxrSite> cxrSites = commonSiteMapper.selectList(
            new LambdaQueryWrapper<CxrSite>()
                .in(CxrSite::getId, siteIds)
                .eq(CxrSite::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
        );

        Map<Long, String> siteMap = cxrSites.stream().collect(Collectors.toMap(a -> a.getId(), a -> a.getName()));

        List<BusinessAgent> businessAgents = cxrEmployees.stream().map(x -> {
            BusinessAgent agent = new BusinessAgent();
            agent.setSiteName(siteMap.get(x.getCxrSiteId()));

            if (ObjectUtil.isNotEmpty(x.getQuitTime())) {

                Instant instant = x.getQuitTime().toInstant();
                ZoneId zoneId = ZoneId.systemDefault();
                LocalDateTime quitTime = instant.atZone(zoneId).toLocalDateTime().plusMonths(3);
                LocalDateTime threeMonthsAgo = LocalDateTime.now();
                if (quitTime.isBefore(threeMonthsAgo)) {
                    CxrEmployee siteEmployee = commonEmployeeMapper.selectOne(new LambdaQueryWrapper<CxrEmployee>()
                        .like(CxrEmployee::getName, "站点")
                        .eq(CxrEmployee::getCxrSiteId, x.getCxrSiteId())
                        .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                    );
                    if (ObjectUtil.isNotEmpty(siteEmployee)) {
                        agent.setProxyId(siteEmployee.getId());
                        agent.setProxyName(siteEmployee.getName());
                        agent.setProxyNo(siteEmployee.getJobNumber());
                        agent.setLevel(Integer.parseInt(siteEmployee.getEmployeeLevelType()));
                        agent.setRemark(StrUtil.format("{}已终止合作，将由站点代理进行补充", x.getName()));
                    }
                } else {
                    agent.setProxyId(x.getId());
                    agent.setProxyName(x.getName());
                    agent.setProxyNo(x.getJobNumber());
                    agent.setLevel(Integer.parseInt(x.getEmployeeLevelType()));
                }
                return agent;
            }
            agent.setProxyId(x.getId());
            agent.setProxyName(x.getName());
            agent.setProxyNo(x.getJobNumber());
            agent.setLevel(Integer.parseInt(x.getEmployeeLevelType()));

            return agent;
        }).collect(Collectors.toList());

        return businessAgents;
    }

    @Override
    public List<RecordRefundListVo> recordRefundList(Long id) {
        List<CxrUserOrder> orderList = orderService.getBaseMapper().selectList(new LambdaQueryWrapper<CxrUserOrder>()
            .eq(CxrUserOrder::getPayOrderId, id)
            .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
        );
        if (CollUtil.isNotEmpty(orderList)) {
            List<Long> orderId = orderList.stream().map(CxrUserOrder::getId)
                .collect(
                    Collectors.toList());
            List<CxrUserReturnOrder> CxrUserReturnOrderList =
                cxrUserReturnOrderMapper.selectList(new LambdaQueryWrapper<CxrUserReturnOrder>()
                    .in(CxrUserReturnOrder::getUserOrderId, orderId)
                    .eq(CxrUserReturnOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                );
            CxrUserOrder cxrUserOrder = orderService.getBaseMapper().selectOne(new LambdaQueryWrapper<CxrUserOrder>()
                .eq(CxrUserOrder::getId, id)
                .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            );
            Map<String, CxrPaymentDetails> paymentDetailsMap = null;
            Map<String, CxrPaymentDetails> paymentMap = null;
            CxrUserOrder payOrder = null;
            if (ObjectUtil.isNotEmpty(cxrUserOrder)) {
                List<CxrPaymentDetails> cxrPaymentDetailsList =
                    paymentDetailsMapper.selectList(new LambdaQueryWrapper<CxrPaymentDetails>()
                        .eq(CxrPaymentDetails::getOutOrderNo, cxrUserOrder.getThirdOrderNo())
                        .eq(CxrPaymentDetails::getPayStatus, PayStatusEnums.PAY_SUCCEEDED.getValue())
                        .eq(CxrPaymentDetails::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                    );

                paymentDetailsMap =
                    cxrPaymentDetailsList.stream().collect(Collectors.toMap(CxrPaymentDetails::getOutOrderNo,
                        Function.identity(), (v1, v2) -> v2));


                //支付的主订单
                payOrder = orderService.getBaseMapper().selectOne(new LambdaQueryWrapper<CxrUserOrder>()
                    .eq(CxrUserOrder::getId, cxrUserOrder.getId())
                    .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                );
                if (ObjectUtil.isNotEmpty(payOrder) && payOrder.getOrderType() == OrderTypeEnums.CONTRACT_ORDER.getValue()) {
                    String subOrderNo = payOrder.getOrderNo().substring(0, cxrUserOrder.getOrderNo().indexOf("-"));
                    List<CxrPaymentDetails> paymentDetailsList =
                        paymentDetailsMapper.selectList(new LambdaQueryWrapper<CxrPaymentDetails>()
                            .eq(CxrPaymentDetails::getUserOrderNo, subOrderNo)
                            .eq(CxrPaymentDetails::getPayStatus, PayStatusEnums.PAY_SUCCEEDED.getValue())
                            .eq(CxrPaymentDetails::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                        );
                    paymentMap =
                        paymentDetailsList.stream().collect(Collectors.toMap(CxrPaymentDetails::getUserOrderNo,
                            Function.identity(), (v1, v2) -> v2));
                }


            }
            Map<Long, CxrUserOrder> orderMap = orderList.stream().collect(Collectors.toMap(CxrUserOrder::getId,
                Function.identity(), (v1, v2) -> v2));

            List<RecordRefundListVo> recordRefundListVoList = BeanCopyUtils.copyList(CxrUserReturnOrderList,
                RecordRefundListVo.class);
            for (RecordRefundListVo vo : recordRefundListVoList) {
                if (ObjectUtil.isNotEmpty(payOrder)) {
                    vo.setThirdOrderNo(payOrder.getThirdOrderNo());
                    if (payOrder.getOrderType() == OrderTypeEnums.CONTRACT_ORDER.getValue()) {
                        String subOrderNo = payOrder.getOrderNo().substring(0, cxrUserOrder.getOrderNo().indexOf("-"));
                        vo.setUserOrderNo(subOrderNo);
                    }

                }
                CxrUserOrder userOrder = orderMap.get(vo.getUserOrderId());
                if (ObjectUtil.isNotEmpty(userOrder)) {
                    vo.setOrderNo(userOrder.getOrderNo());
                    vo.setOrderDate(userOrder.getOrderDate());
                    vo.setAuditStatus(userOrder.getAuditStatus());
                    vo.setPaymentStatus(userOrder.getPaymentStatus());
                }
                if (payOrder.getOrderType() != OrderTypeEnums.CONTRACT_ORDER.getValue()) {
                    CxrPaymentDetails paymentDetails = paymentDetailsMap.get(vo.getThirdOrderNo());
                    if (ObjectUtil.isNotEmpty(paymentDetails)) {
                        vo.setPayPlatform(paymentDetails.getPayPlatform());
                        vo.setPayPlatformSn(paymentDetails.getTradeNo());
                        vo.setMchntName(paymentDetails.getMchntName());
                        vo.setMchntCd(paymentDetails.getOutOrderNo());
                    }
                }
                if (payOrder.getOrderType() == OrderTypeEnums.CONTRACT_ORDER.getValue()) {
                    CxrPaymentDetails paymentDetails = paymentMap.get(vo.getUserOrderNo());
                    if (ObjectUtil.isNotEmpty(paymentDetails)) {
                        vo.setPayPlatform(paymentDetails.getPayPlatform());
                        vo.setPayPlatformSn(paymentDetails.getTradeNo());
                        vo.setMchntName(paymentDetails.getMchntName());
                        vo.setMchntCd(paymentDetails.getOutOrderNo());
                    }
                }
            }
            return recordRefundListVoList;
        }
        return Collections.emptyList();
    }

    @Override
    public RecordRefundListTotalVo recordRefundListTotal(Long id) {
        RecordRefundListTotalVo vo = new RecordRefundListTotalVo();
        List<CxrUserOrder> orderList = orderService.getBaseMapper().selectList(new LambdaQueryWrapper<CxrUserOrder>()
            .eq(CxrUserOrder::getPayOrderId, id)
            .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
        );
        if (CollUtil.isNotEmpty(orderList)) {
            //查询退订单里面的主订单id
            List<Long> orderId = orderList.stream().map(CxrUserOrder::getId)
                .collect(
                    Collectors.toList());
            List<CxrUserReturnOrder> CxrUserReturnOrderList =
                cxrUserReturnOrderMapper.selectList(new LambdaQueryWrapper<CxrUserReturnOrder>()
                    .in(CxrUserReturnOrder::getUserOrderId, orderId)
                    .eq(CxrUserReturnOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                );

            int freshMilkRefundQuantityTotal = CxrUserReturnOrderList.stream()
                .mapToInt(CxrUserReturnOrder::getFreshMilkRefundQuantity)
                .sum();
            vo.setFreshMilkRefundQuantityTotal(freshMilkRefundQuantityTotal);
            int freshMilkCancelQuantityTotal = CxrUserReturnOrderList.stream()
                .mapToInt(CxrUserReturnOrder::getFreshMilkCancelQuantity)
                .sum();
            vo.setFreshMilkCancelQuantityTotal(freshMilkCancelQuantityTotal);
            BigDecimal refundAmountTotal = CxrUserReturnOrderList.stream()
                .map(CxrUserReturnOrder::getRefundAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setRefundAmountTotal(refundAmountTotal);
        }
        return vo;
    }

    @Override
    @Transactional
    public boolean refundOrderRemit(Long id) {
        CxrUserOrder cxrUserOrder = orderService.getBaseMapper().selectOne(new LambdaQueryWrapper<CxrUserOrder>()
            .eq(CxrUserOrder::getId, id)
            .eq(CxrUserOrder::getAuditStatus, AuditStatusEnums.Audit.code())
            .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            .and(wapper -> wapper
                .eq(CxrUserOrder::getPaymentStatus, RefundStatusEnums.REFUND_FAIL.getValue())
                .or()
                .eq(CxrUserOrder::getPaymentStatus, RefundStatusEnums.REFUND_WAIT.getValue())
            ));
        if (ObjectUtil.isNotEmpty(cxrUserOrder)) {
            //已退款+退款总的订单合计
            List<CxrUserOrder> userOrders = orderService.getBaseMapper()
                .selectList(new LambdaQueryWrapper<CxrUserOrder>()
                    .select(CxrUserOrder::getPayOrderId, CxrUserOrder::getAmount)
                    .eq(CxrUserOrder::getPayOrderId, cxrUserOrder.getPayOrderId())
                    .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.not_deleted)
                    .eq(CxrUserOrder::getAuditStatus, AuditStatusEnums.Audit.code())
                    .and(wapper -> wapper
                        .eq(CxrUserOrder::getPaymentStatus, RefundStatusEnums.REFUND_IN.getValue())
                        .or()
                        .eq(CxrUserOrder::getPaymentStatus, RefundStatusEnums.REFUND_FINISH.getValue())
                    )
                );
            //退订单的主单 进行比较
            CxrUserOrder UserOrder = orderService.getBaseMapper().selectOne(new LambdaQueryWrapper<CxrUserOrder>()
                .eq(CxrUserOrder::getId, cxrUserOrder.getPayOrderId())
                .and(wapper -> wapper
                    .eq(CxrUserOrder::getAuditStatus, AuditStatusEnums.Audit.code())
                    .or()
                    .eq(CxrUserOrder::getPayStatus, PayStatusEnums.PAY_SUCCEEDED.getValue()))
                .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));

            if (ObjectUtil.isNotEmpty(UserOrder)) {
                //支付总金额
                BigDecimal totalAmount = UserOrder.getAmount();
                //已退金额+加上申请金额
                BigDecimal reduce = userOrders.stream().map(CxrUserOrder::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                reduce = reduce.add(cxrUserOrder.getAmount());
                if (reduce.compareTo(totalAmount) > 0) {
                    throw new ServiceException("退款总金额大于支付金额!");
                }
                boolean success = orderService.getBaseMapper().update(null, new LambdaUpdateWrapper<CxrUserOrder>()
                    .eq(CxrUserOrder::getId, cxrUserOrder.getId())
                    .set(CxrUserOrder::getPaymentStatus, RefundStatusEnums.REFUND_IN.getValue())
                    .set(CxrUserOrder::getUpdateTime, new Date())
                    .set(CxrUserOrder::getUpdateBy, LoginHelper.getLoginUser().getUserId())
                    .set(CxrUserOrder::getUpdateByName, LoginHelper.getLoginUser().getUserName())
                    .set(CxrUserOrder::getUpdateByType, LoginHelper.getLoginUser().getUserType())
                ) > 0;
                if (!success) {
                    throw new ServiceException("修改订单状态失败!!!");
                }

                //进行修改售后的状态
                CxrOrderAfterSale cxrOrderAfterSale = cxrOrderAfterSaleMapper.selectOne(
                    new LambdaQueryWrapper<CxrOrderAfterSale>()
                        .eq(CxrOrderAfterSale::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                        .eq(CxrOrderAfterSale::getRefundOrderId, id)
                );
                if (ObjectUtil.isNotEmpty(cxrOrderAfterSale)) {

                    Boolean b = cxrOrderAfterSaleMapper.update(null, new LambdaUpdateWrapper<CxrOrderAfterSale>()
                        .set(CxrOrderAfterSale::getSaleStatus, SaleStatus.PENDING.getValue())
                        .set(CxrOrderAfterSale::getReturnStatus, RefundStatusEnums.REFUND_FINISH.getValue())
                        .eq(CxrOrderAfterSale::getId, cxrOrderAfterSale.getId())
                    ) > 0;
                    if (!b) {
                        throw new ServiceException("售后状态修改失败!");
                    }
                }
                mqUtil.sendSyncMessage(OrderConstant.ORDER_TOPIC, OrderConstant.RETURN_ORDER_REFUND_ACTIVE_TAG,
                    String.valueOf(id));
                return true;
            }
        }
        return false;
    }

    @Override
    @GlobalTransactional
    public Long tiktokAutoOrderRefund(CxrUserReturnOrderBO cxrUserReturnOrderBO) {
        if (ObjectUtil.isEmpty(cxrUserReturnOrderBO.getId())) {
            return -1L;
        }
        commonOrderService.selectEmployeeLimit(cxrUserReturnOrderBO.getBusinessAgent(), null);
        CxrCustomer cxrCustomer = remoteCustomerService.getById(cxrUserReturnOrderBO.getCustomerId());
        if (cxrCustomer.getCustomerStock() == 0) {
            throw new ServiceException("客户库存为0不能提交退订单!");
        }
        orderBusinessAgentComplete(cxrUserReturnOrderBO.getBusinessAgent());
        OrderTypeEnums orderTypeEnums = OrderTypeEnums.getType(cxrUserReturnOrderBO.getOrderType());
        CxrUserOrder order = orderService.queryById(cxrUserReturnOrderBO.getId());
        if (ObjectUtil.isNotEmpty(order)
            && order.getTerminalType() == TerminalTypeEnums.tiktok.getValue()) {
            List<CxrUserReturnOrder> cxrUserReturnOrderList =
                cxrUserReturnOrderMapper.selectList(new LambdaQueryWrapper<CxrUserReturnOrder>()
                    .select(CxrUserReturnOrder::getId, CxrUserReturnOrder::getOrderId, CxrUserReturnOrder::getUserOrderId)
                    .eq(CxrUserReturnOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                    .eq(CxrUserReturnOrder::getOrderId, cxrUserReturnOrderBO.getId())
                );
            if (ObjectUtil.isNotEmpty(cxrUserReturnOrderList)) {
                List<Long> userReturnOrderIds = cxrUserReturnOrderList.stream().map(CxrUserReturnOrder::getUserOrderId).collect(Collectors.toList());
                Long count = orderService.getBaseMapper()
                    .selectCount(new LambdaQueryWrapper<CxrUserOrder>()
                        .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                        .in(CxrUserOrder::getId, userReturnOrderIds)
                    );
                if (count > 0l) {
                    throw new ServiceException("退订单已创建请刷新客户订单页面!!!");
                }
            }
            Long orderReturnId = abstractCxrUserOrderBehavior.executeOrderAddOrUpdate(cxrUserReturnOrderBO,
                orderTypeEnums);
            if (ObjectUtil.isNotEmpty(orderReturnId)) {
                CxrUserOrder userOrders = orderService.getBaseMapper()
                    .selectOne(new LambdaQueryWrapper<CxrUserOrder>()
                        .select(CxrUserOrder::getOrderNo, CxrUserOrder::getId, CxrUserOrder::getAccountingType,
                            CxrUserOrder::getProductName, CxrUserOrder::getPromotionCommission,
                            CxrUserOrder::getProductTypeName, CxrUserOrder::getChannel)
                        .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                        .eq(CxrUserOrder::getId, cxrUserReturnOrderBO.getId())
                    );
                if (ObjectUtil.isNotEmpty(userOrders)) {
                    //退订单表
                    cxrUserReturnOrderMapper.update(null, new LambdaUpdateWrapper<CxrUserReturnOrder>()
                        .eq(CxrUserReturnOrder::getUserOrderId, orderReturnId)
                        .set(CxrUserReturnOrder::getOrderId, cxrUserReturnOrderBO.getId())
                    );
                    //退订单--主表
                    orderService.getBaseMapper().update(null, new LambdaUpdateWrapper<CxrUserOrder>()
                        .eq(CxrUserOrder::getId, orderReturnId)
                        .set(CxrUserOrder::getAccountingType, userOrders.getAccountingType())
                        .set(CxrUserOrder::getProductName, userOrders.getProductName())
                        .set(CxrUserOrder::getPromotionCommission, userOrders.getPromotionCommission())
                        .set(CxrUserOrder::getProductTypeName, userOrders.getProductTypeName())
                        .set(CxrUserOrder::getChannel, userOrders.getChannel())
                    );
                }

                CxrUserOrder payUser = orderService.getBaseMapper()
                    .selectOne(new LambdaQueryWrapper<CxrUserOrder>()
                        .select(CxrUserOrder::getOrderNo)
                        .eq(CxrUserOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                        .eq(CxrUserOrder::getId, orderReturnId)
                    );
                //支付订单的订单号
                if (ObjectUtil.isNotEmpty(payUser)) {
                    orderService.getBaseMapper().update(null, new LambdaUpdateWrapper<CxrUserOrder>()
                        .eq(CxrUserOrder::getId, cxrUserReturnOrderBO.getId())
                        .set(CxrUserOrder::getTiktokOrderRefundNo, payUser.getOrderNo())
                    );
                }
            }
            return orderReturnId;
        }
        return -1L;
    }

    private void orderBusinessAgentComplete(List<BusinessAgent> businessAgents) {
        if (CollectionUtil.isEmpty(businessAgents)) {
            throw new ServiceException("请先选择销售代理");
        }

        Set<Long> proxyIds = businessAgents.stream().map(BusinessAgent::getProxyId).collect(Collectors.toSet());
        List<CxrEmployee> cxrEmployees = commonEmployeeMapper.selectBatchIds(proxyIds);
        Map<Long, CxrSite> cxrSiteMap = null;
        Map<Long, CxrEmployee> employeesMap = null;
        if (CollUtil.isNotEmpty(cxrEmployees)) {
            employeesMap = cxrEmployees.stream().collect(Collectors.toMap(a -> a.getId(), a -> a));
            List<Long> siteIds = cxrEmployees.stream().map(CxrEmployee::getCxrSiteId).collect(Collectors.toList());
            List<CxrSite> cxrSites = commonSiteMapper.selectBatchIds(siteIds);
            cxrSiteMap = cxrSites.stream().collect(Collectors.toMap(a -> a.getId(), a -> a));
        }


        for (BusinessAgent agent : businessAgents) {
            CxrEmployee employee = employeesMap.get(agent.getProxyId());
            if (ObjectUtil.isEmpty(agent.getSiteName())) {
                agent.setSiteName(employee.getSiteName());
            }
            if (ObjectUtil.isEmpty(agent.getRegionId())) {
                CxrSite cxrSite = cxrSiteMap.get(employee.getCxrSiteId());
                if (ObjectUtil.isNotEmpty(cxrSite)) {
                    agent.setRegionId(cxrSite.getCxrRootRegionId());
                    agent.setRegionName(cxrSite.getCxrRootRegionName());
                }
            }
            agent.setOccupationStatus(OccupationStatus.INDUCTION.getValue().equals(employee.getOccupationStatus()));
        }
    }

    private void processAnOrderHistory(List<CxrUserOrder> userOrders, OnlineRefundVo refundVo) {

        if (CollUtil.isNotEmpty(userOrders)) {

            List<Long> userIds = userOrders.stream().map(CxrUserOrder::getId).collect(Collectors.toList());

            List<CxrUserReturnOrder> userReturnOrders = cxrUserReturnOrderMapper.selectList(
                new LambdaQueryWrapper<CxrUserReturnOrder>()
                    .in(CxrUserReturnOrder::getUserOrderId, userIds)
                    .eq(CxrUserReturnOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            );

            Map<Long, List<CxrUserReturnOrder>> userReturnMap = userReturnOrders.stream()
                .collect(Collectors.groupingBy(CxrUserReturnOrder::getUserOrderId));

            final Integer[] freshMilkRefundQuantityTotal = {0};
            final Integer[] freshMilkCancelQuantityTotal = {0};
            final BigDecimal[] amount = {BigDecimal.ZERO};

            List<OrderHistoryReturnVo> orderHistoryReturnVos = userOrders.stream().map(
                x -> {
                    OrderHistoryReturnVo result = new OrderHistoryReturnVo();
                    result.setOrderDate(x.getOrderDate());
                    result.setId(x.getId());
                    result.setOrderNo(x.getOrderNo());
                    result.setAmount(x.getAmount());
                    result.setAuditStatus(x.getAuditStatus());
                    result.setPaymentStatus(x.getPaymentStatus());

                    List<CxrUserReturnOrder> cxrUserReturnOrders = userReturnMap.get(x.getId());

                    int freshMilkCancelQuantity = cxrUserReturnOrders.stream()
                        .mapToInt(CxrUserReturnOrder::getFreshMilkCancelQuantity)
                        .sum();

                    int freshMilkRefundQuantity = cxrUserReturnOrders.stream()
                        .mapToInt(CxrUserReturnOrder::getFreshMilkRefundQuantity)
                        .sum();

                    result.setFreshMilkCancelQuantity(freshMilkCancelQuantity);
                    result.setFreshMilkRefundQuantity(freshMilkRefundQuantity);

                    freshMilkRefundQuantityTotal[0] = freshMilkRefundQuantityTotal[0] + freshMilkRefundQuantity;
                    freshMilkCancelQuantityTotal[0] = freshMilkCancelQuantityTotal[0] + freshMilkCancelQuantity;
                    amount[0] = amount[0].add(x.getAmount());
                    return result;
                }
            ).collect(Collectors.toList());
            refundVo.setOrderHistoryList(orderHistoryReturnVos);
            HistoryStatisticsVo historyStatisticsVo = new HistoryStatisticsVo();
            historyStatisticsVo.setRefundAmount(amount[0]);
            historyStatisticsVo.setFreshMilkCancelQuantity(freshMilkCancelQuantityTotal[0]);
            historyStatisticsVo.setFreshMilkRefundQuantity(freshMilkRefundQuantityTotal[0]);
            refundVo.setHistoryStatisticsVo(historyStatisticsVo);
        }

    }
}
