package com.ruoyi.order.disribution.domain.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.business.base.api.domain.CxrCustomerAddress;
import com.ruoyi.business.base.api.domain.json.BusinessAgent;
import com.ruoyi.business.base.api.domain.json.ContractOrderCustomerInfo;
import com.ruoyi.business.base.api.domain.json.CustomerInfo;
import com.ruoyi.order.common.domain.vo.CxrUserReturnOrderVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

// todo 还需要拿小区 和 小区 id
@Data
public class OrderVO implements Serializable {

    /**
     * 编号
     */
    @ApiModelProperty("主键 id")
    private Long id;

    /**
     * 公司id
     */
    @ApiModelProperty("公司id")
    private Long companyId;

    /**
     * 公司名称
     */
    @ApiModelProperty("公司名称")
    private String companyName;

    /**
     * 大区id
     */
    @ApiModelProperty("大区id")
    private Long bigAreaId;

    /**
     * 大区名称
     */
    @ApiModelProperty("大区名称")
    private String bigAreaName;

    /**
     * 省
     */
    @ApiModelProperty("省")
    private String province;

    /**
     * 市
     */
    @ApiModelProperty("市")
    private String city;

    /**
     * 区
     */
    @ApiModelProperty("区")
    private String area;

    /**
     * 站点名称
     */
    @ApiModelProperty("站点名称")
    private String siteName;

    /**
     * 站点地址
     */
    @ApiModelProperty("站点地址")
    private String siteAdress;

    /**
     * 站点id
     */
    @ApiModelProperty("站点id")
    private Long siteId;

    /**
     * 业务代理字符串; 以逗号分割
     */
    @ApiModelProperty("业务代理详细信息")
    //    @TableField(typeHandler = TypeHandlerConstant.BusinessAgentTypeHandler.class)
    private List<BusinessAgent> businessAgent;

    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称;只取第一个录入的数据")
    private String customerName;

    /**
     * 客户电话;只取第一个录入的数据
     */
    @ApiModelProperty("客户电话;只取第一个录入的数据")
    private String customerPhone;

    /**
     * 客户地址; 只取第一个录入的数据
     */
    @ApiModelProperty("客户地址; 只取第一个录入的数据")
    private String customerAdress;

    /**
     * 转入客户姓名
     */
    @ApiModelProperty("转入客户姓名")
    private String customerNameSwitch;

    /**
     * 转入客户地址
     */
    @ApiModelProperty("转入客户地址")
    private String customerAdressSwitch;

    /**
     * 转入客户手机号
     */
    @ApiModelProperty("转入客户手机号")
    private String customerPhoneSwitch;

    /**
     * 1微信;2 支付宝
     */
    @ApiModelProperty("支付来源")
    private String paymentSouce;

    /**
     * 订单类型;1、新订单2、增订单3、续订单4、转单5、换单6、退订单7、赠送单
     */
    @ApiModelProperty("订单类型")
    private Short orderType;

    /**
     * 0数量续单：用于客户账户没有鲜奶了;并且停奶超过了15天。15天后续单的，显示“超过15天”。
     */
    @ApiModelProperty("0数量续单")
    private Boolean zeroQuantityRenewal;

    /**
     * 转换类型，用于订单类型为“换单”的订单，显示客户兑换的商品的类目;，剔除重复的 多个以逗号分割
     */
    @ApiModelProperty("转换类型")
    private String conversionType;

    /**
     * 单据;订单单据号
     */
    @ApiModelProperty("单据")
    private String orderNo;

    /**
     * 商户单号
     */
    @ApiModelProperty("商户单号")
    private String merchantOrderNo;

    /**
     * 订购数量：鲜奶订购数量
     */
    @ApiModelProperty("订购数量")
    private Integer orderQuantity;

    /**
     * 鲜奶赠送数量
     */
    @ApiModelProperty("鲜奶赠送数量")
    private Integer freshMilkGiveQuantity;

    /**
     * 常温奶赠送数量
     */
    @ApiModelProperty("常温奶赠送数量")
    private Integer longMilkGiveQuantity;

    /**
     * 超送数量：根据订单套餐超送规则计算出来的超送数量;祥看原型
     */
    @ApiModelProperty("超送数量")
    private Integer excessQuantity;

    /**
     * 鲜奶已送数量
     */
    @ApiModelProperty("鲜奶已送数量")
    private Integer freshMilkSentQuantity;

    /**
     * 常温奶已送数量
     */
    @ApiModelProperty("常温奶已送数量")
    private Integer longMilkSentQuantity;

    @ApiModelProperty(value = "剩余数量")
    private Integer surplusQuantity;

    /**
     * 转换数量，订单类型为“转单”的时候才会有值;即转换的鲜奶数量
     */
    @ApiModelProperty("转换数量")
    private Integer conversionQuantity;

    /**
     * 单价：鲜奶单价
     */
    @ApiModelProperty("单价：鲜奶单价")
    private BigDecimal unitPrice;

    /**
     * 鲜奶退款盒数
     */
    @ApiModelProperty("鲜奶退款盒数")
    private Integer freshMilkRefundQuantity;

    /**
     * 鲜奶退订总数量
     */
    @ApiModelProperty("鲜奶退订总数量")
    private Integer freshMilkCancelQuantity;

    /**
     * 常温奶退订金额
     */
    @ApiModelProperty("常温奶退订金额")
    private BigDecimal refundAmount;

    /**
     * 金额：该笔订单的总金额
     */
    @ApiModelProperty("金额：该笔订单的总金额")
    private BigDecimal amount;

    /**
     * 刷卡金额
     */
    @ApiModelProperty("刷卡金额")
    private BigDecimal creditCardAmount;

    @ApiModelProperty("常温奶已送金额")
    private BigDecimal longMilkSentAmount;

    /**
     * 订单日期
     */
    @ApiModelProperty("订单日期")
    private Date orderDate;

    /**
     * 1 待审核 、 2 已审核 、 3.已拒绝
     */
    @ApiModelProperty("1 待审核 、 2 已审核 、 3.已拒绝")
    private Integer auditStatus;

    /**
     * 审核人
     */
    @ApiModelProperty("审核人")
    private Long auditBy;

    /**
     * 审核人
     */
    @ApiModelProperty("审核时间")
    private Date auditTime;

    /**
     * 是否促销单;true 是 false 否
     */
    @ApiModelProperty("是否促销单;true 是 false 否")
    private Boolean promotionalOrderFlag;

    /**
     * 是否师徒单;true是 false 否
     */
    @ApiModelProperty("是否师徒单;true是 false 否")
    private Boolean apprenticeOrderFlag;

    /**
     * 来源 1 后端 2 配送端
     */
    @ApiModelProperty("源  1 后端 2 配送端")
    @TableField(fill = FieldFill.INSERT)
    private Short terminalType;

    /**
     * 单据图片
     */
    @ApiModelProperty("单据图片")
    //    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> orderImages;

    /**
     * 支付截图
     */
    @ApiModelProperty("支付截图")
    //    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> playImages;

    /**
     * 客户信息
     */
    @ApiModelProperty(" 客户信息")
    //    @TableField(typeHandler = TypeHandlerConstant.CustomerInfoTypeHandler.class)
    private List<CustomerInfo> customerInfoList;

    /**
     * 鲜奶退订数量
     */
    @ApiModelProperty(" 鲜奶退订信息")
    private Integer freshMilkReturnQuantity;

    @ApiModelProperty(" 支付状态")
    private Integer payStatus;

    // 完善订单入口
    @ApiModelProperty(name = "订单完善状态")
    private Integer perfectStatus;

    /**
     * 合订单客户信息
     */
    @ApiModelProperty(" 合订单客户信息")
    //    @TableField(typeHandler = TypeHandlerConstant.ContractOrderCustomerInfoTypeHandler.class)
    private List<ContractOrderCustomerInfo> contractOrderExt;

    /**
     * 支付时间
     */
    @ApiModelProperty("支付时间")
    private Date payTime;

    @ApiModelProperty("remark")
    private String remark;

    @ApiModelProperty("合订单总金额")
    private BigDecimal contractTotalAmount;

    @ApiModelProperty("合订单总订购数量")
    private Integer contractTotalOrderQuantity;

    @ApiModelProperty("转出客户地址")
    private String customAdress;

    @ApiModelProperty("转入客户地址")
    private String customAdressSwitch;

    /** 扩展字段 */

    /** 其他实体 */

    /**
     * 小区名字 小区id
     */
    @ApiModelProperty("小区名字")
    private String quartersName;

    @ApiModelProperty("小区id")
    private Long cxrResidentialQuartersId;

    @ApiModelProperty("扩展的实体")
    private CxrUserReturnOrderVO cxrUserReturnOrderVO;

    @ApiModelProperty("客户地址扩展实体")
    private CxrCustomerAddress cxrCustomerAddress;

    /**
     * 鲜奶退订总数量
     */
    @ApiModelProperty("常温奶退订数量")
    private Integer longMilkCancelQuantity;

    /**
     * 鲜奶退订总数量
     */
    @ApiModelProperty("常温奶剩余数量")
    private Integer longMilkRestQuantity;

    @ApiModelProperty("活动id")
    private Long activityId;

    @ApiModelProperty("活动名称")
    private String activityName;

    @ApiModelProperty("活动对象")
    private String giveGiftList;

    @ApiModelProperty("套餐价格")
    private BigDecimal setMealPrice;

    @ApiModelProperty("分摊费用")
    private BigDecimal apportionMoney;

    @ApiModelProperty("套餐数量")
    private Integer setMealQty;

    @ApiModelProperty("创建人")
    private String createByName;

    @ApiModelProperty("抖音订单号")
    private String tiktokOrderNo;


    @ApiModelProperty("配送站点")
    private String deliverySites;

    @ApiModelProperty("退款审核不通过原因")
    private String refundNoAuditReasons;
}
