<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">

    <springProperty name="port" scope="context" source="server.port" defaultValue="0000"/>
    <springProperty name="appName" scope="context" source="spring.application.name"/>

    <!-- 日志存放路径 -->
    <property name="log.path" value="logs/${project.artifactId}/${port}"/>
    <!-- 日志输出格式 -->
    <property name="console.pattern"
        value="%yellow(%date{yyyy-MM-dd HH:mm:ss.SSS}) | %blue([%X{tl}]) | %highlight(%-5level) | %blue(%thread) | %green(%logger{36}) | %cyan(%msg%n)"/>
    <property name="log.pattern"
        value="%date{yyyy-MM-dd HH:mm:ss.SSS}| [%X{tl}] | %-5level | %thread| %logger{36} | %msg%n "/>

    <!-- 控制台输出 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${console.pattern}</pattern>
        </encoder>
    </appender>


    <!-- error级别企业微信告警 -->
    <appender name="errorAlarm" class="com.ruoyi.common.log.wxAlarmLogBack.abstractService.impl.WxWorkServiceImpl">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
        </filter>
    </appender>

    <!-- 日志输出 -->
    <appender name="fileLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/app.log</file>
        <!-- 循环政策：基于时间创建日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 日志文件名格式 -->
            <fileNamePattern>${log.path}/bak/app.%d{yyyy-MM-dd_HH}.log.gz</fileNamePattern>
            <!-- 日志最大的历史 60天 -->
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>


    <!-- Spring日志级别控制  -->
    <logger name="org.springframework" level="warn"/>
    <logger name="com.ruoyi.job" level="info"/>

    <root level="info">
        <appender-ref ref="console"/>
        <appender-ref ref="fileLog"/>
        <appender-ref ref="errorAlarm"/>
    </root>
</configuration>
