package com.ruoyi.calculate.orderCount.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.calculate.orderCount.domain.CxrRegionStaitemDailyReport;
import com.ruoyi.common.mybatis.annotation.DataColumn;
import com.ruoyi.common.mybatis.annotation.DataPermission;
import com.ruoyi.common.mybatis.enums.DataDeptType;
import com.ruoyi.core.base.domain.bo.CxrRegionStaitemDailyReportBo;
import com.ruoyi.core.base.domain.vo.CxrRegionStaitemDailyReportVo;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【cxr_region_staitem_daily_report(区域单数统计明细日报表)】的数据库操作Mapper
* @createDate 2025-07-03 16:06:20
* @Entity com.ruoyi.job.common.domain.CxrRegionStaitemDailyReport
*/
public interface CxrRegionStaitemDailyReportMapper extends BaseMapper<CxrRegionStaitemDailyReport> {

    /**
     * 分页查询区域单数统计明细 - 公司维度
     *
     * @param bo 查询参数
     * @param page 分页参数
     * @return 分页结果
     */
    @DataPermission(value = {
        @DataColumn(key = "deptName", value = "company_id")
    }, perms = {
        "calculate:cxrRegionStaitemDailyReport:page"
    },deptType= DataDeptType.DEPT_SITE)
    Page<CxrRegionStaitemDailyReportVo> selectCompanyReportPage(@Param("bo") CxrRegionStaitemDailyReportBo bo, Page<Object> page);

    /**
     * 分页查询区域单数统计明细 - 大区维度
     *
     * @param bo 查询参数
     * @param page 分页参数
     * @return 分页结果
     */
    @DataPermission(value = {
        @DataColumn(key = "deptName", value = "root_region_id")
    }, perms = {
        "calculate:cxrRegionStaitemDailyReport:page"
    },deptType= DataDeptType.DEPT_SITE)
    Page<CxrRegionStaitemDailyReportVo> selectRootRegionReportPage(@Param("bo") CxrRegionStaitemDailyReportBo bo, Page<Object> page);

    /**
     * 分页查询区域单数统计明细 - 区域维度
     *
     * @param bo 查询参数
     * @param page 分页参数
     * @return 分页结果
     */
    @DataPermission(value = {
        @DataColumn(key = "deptName", value = "region_id")
    }, perms = {
        "calculate:cxrRegionStaitemDailyReport:page"
    },deptType= DataDeptType.DEPT_SITE)
    Page<CxrRegionStaitemDailyReportVo> selectRegionReportPage(@Param("bo") CxrRegionStaitemDailyReportBo bo, Page<Object> page);

    /**
     * 分页查询区域单数统计明细 - 站点维度
     *
     * @param bo 查询参数
     * @param page 分页参数
     * @return 分页结果
     */
    @DataPermission(value = {
        @DataColumn(key = "deptName", value = "site_id")
    }, perms = {
        "calculate:cxrRegionStaitemDailyReport:page"
    },deptType= DataDeptType.DEPT_SITE)
    Page<CxrRegionStaitemDailyReportVo> selectSiteReportPage(@Param("bo") CxrRegionStaitemDailyReportBo bo, Page<Object> page);

    /**
     * 分页查询区域单数统计明细 - 代理维度
     *
     * @param bo 查询参数
     * @param page 分页参数
     * @return 分页结果
     */
    @DataPermission(value = {
        @DataColumn(key = "deptName", value = "site_id")
    }, perms = {
        "calculate:cxrRegionStaitemDailyReport:page"
    },deptType= DataDeptType.DEPT_SITE)
    Page<CxrRegionStaitemDailyReportVo> selectAgentReportPage(@Param("bo") CxrRegionStaitemDailyReportBo bo, Page<Object> page);

    /**
     * 合计查询当期数据 - 公司维度
     *
     * @param bo 查询参数
     * @return 合计结果
     */
    @DataPermission(value = {
        @DataColumn(key = "deptName", value = "company_id")
    }, perms = {
        "calculate:cxrRegionStaitemDailyReport:page"
    },deptType= DataDeptType.DEPT_SITE)
    CxrRegionStaitemDailyReportVo sumCompanyReport(@Param("bo") CxrRegionStaitemDailyReportBo bo);

    /**
     * 合计查询当期数据 - 大区维度
     *
     * @param bo 查询参数
     * @return 合计结果
     */
    @DataPermission(value = {
        @DataColumn(key = "deptName", value = "root_region_id" )
    }, perms = {
        "calculate:cxrRegionStaitemDailyReport:page"
    },deptType= DataDeptType.REGION)
    CxrRegionStaitemDailyReportVo sumRootRegionReport(@Param("bo") CxrRegionStaitemDailyReportBo bo);

    /**
     * 合计查询当期数据 - 区域维度
     *
     * @param bo 查询参数
     * @return 合计结果
     */
    @DataPermission(value = {
        @DataColumn(key = "deptName", value = "region_id")
    }, perms = {
        "calculate:cxrRegionStaitemDailyReport:page"
    },deptType= DataDeptType.DEPT_SITE)
    CxrRegionStaitemDailyReportVo sumRegionReport(@Param("bo") CxrRegionStaitemDailyReportBo bo);

    /**
     * 合计查询当期数据 - 站点维度
     *
     * @param bo 查询参数
     * @return 合计结果
     */
    @DataPermission(value = {
        @DataColumn(key = "deptName", value = "site_id")
    }, perms = {
        "calculate:cxrRegionStaitemDailyReport:page"
    },deptType= DataDeptType.DEPT_SITE)
    CxrRegionStaitemDailyReportVo sumSiteReport(@Param("bo") CxrRegionStaitemDailyReportBo bo);

    /**
     * 合计查询当期数据 - 代理维度
     *
     * @param bo 查询参数
     * @return 合计结果
     */
    @DataPermission(value = {
        @DataColumn(key = "deptName", value = "site_id")
    }, perms = {
        "calculate:cxrRegionStaitemDailyReport:page"
    },deptType= DataDeptType.DEPT_SITE)
    CxrRegionStaitemDailyReportVo sumAgentReport(@Param("bo") CxrRegionStaitemDailyReportBo bo);

}




