package com.ruoyi.business.cxrEmployeeLeave.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.ruoyi.business.api.domain.CxrEmployeeLeave;
import com.ruoyi.business.base.api.domain.CxrEmployee;
import com.ruoyi.business.base.api.domain.CxrEmployeePost;
import com.ruoyi.business.base.api.domain.CxrRegion;
import com.ruoyi.business.base.api.domain.CxrSite;
import com.ruoyi.business.base.api.domain.dto.EmployeeQueryDTO;
import com.ruoyi.business.base.api.domain.vo.EmployeeInfoVO;
import com.ruoyi.business.base.api.dubbo.RemoteCustomerDistributionDetailService;
import com.ruoyi.business.base.api.dubbo.RemoteEmployeePostService;
import com.ruoyi.business.base.api.dubbo.RemoteRegionService;
import com.ruoyi.business.base.api.dubbo.RemoteSiteService;
import com.ruoyi.business.base.api.model.LoginEmployee;
import com.ruoyi.business.common.mapper.CxrEmployeeMapper;
import com.ruoyi.business.common.mapper.CxrEmployeePostMapper;
import com.ruoyi.business.cxrEmployeeDimissionApply.mapper.CxrSiteMapper;
import com.ruoyi.business.cxrEmployeeLeave.config.CxrLeaveRemindConfig;
import com.ruoyi.business.cxrEmployeeLeave.domain.CxrEmployeeLeaveLog;
import com.ruoyi.business.cxrEmployeeLeave.domain.bo.CxrEmployeeLeaveAddBo;
import com.ruoyi.business.cxrEmployeeLeave.domain.bo.CxrEmployeeLeaveAuditBo;
import com.ruoyi.business.cxrEmployeeLeave.domain.bo.CxrEmployeeLeaveBo;
import com.ruoyi.business.cxrEmployeeLeave.domain.bo.CxrEmployeeLeaveCheckBo;
import com.ruoyi.business.cxrEmployeeLeave.domain.dto.EmpLeaveChangeDTO;
import com.ruoyi.business.cxrEmployeeLeave.domain.vo.*;
import com.ruoyi.business.cxrEmployeeLeave.enums.PeopleLeaveTypeEnums;
import com.ruoyi.business.cxrEmployeeLeave.enums.PeopleLeveaEnums;
import com.ruoyi.business.cxrEmployeeLeave.mapper.CxrEmployeeLeaveMapper;
import com.ruoyi.business.cxrEmployeeLeave.mapper.CxrEmployeelLeaveLogMapper;
import com.ruoyi.business.cxrEmployeeLeave.service.ICxrEmployeeLeaveService;
import com.ruoyi.business.cxrYangMlik.mapper.SysDeptMapper;
import com.ruoyi.business.logger.OptLoggerManager;
import com.ruoyi.common.core.domain.ExcelReportDTO;
import com.ruoyi.common.core.domain.TableDataInfo;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.enums.TerminalTypeEnums;
import com.ruoyi.common.excel.utils.ExcelExportObj;
import com.ruoyi.common.excel.utils.ExcelMqUtil;
import com.ruoyi.common.mybatis.core.page.PageQuery;
import com.ruoyi.common.mybatis.core.page.PageTableDataInfo;
import com.ruoyi.common.mybatis.enums.DataScope;
import com.ruoyi.common.redis.utils.RedisTemplateUtils;
import com.ruoyi.common.rocketmq.constant.employee.SaleAccessCycleConstant;
import com.ruoyi.common.rocketmq.utils.MqUtil;
import com.ruoyi.common.satoken.utils.LoginTypeUtil;
import com.ruoyi.common.satoken.utils.LoginUtil;
import com.ruoyi.common.satoken.utils.helper.LoginHelper;
import com.ruoyi.common.satoken.utils.helper.StaffLoginHelper;
import com.ruoyi.system.api.domain.SysDept;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.model.LoginUser;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * 员工请假管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-07-20
 */
@RequiredArgsConstructor
@Service
@GlobalTransactional(rollbackFor = Exception.class)
@Slf4j
public class CxrEmployeeLeaveServiceImpl
    extends ServiceImpl<CxrEmployeeLeaveMapper, CxrEmployeeLeave>
    implements ICxrEmployeeLeaveService {

    private final CxrEmployeeLeaveMapper baseMapper;
    private final CxrEmployeePostMapper cxrEmployeePostMapper;

    private final CxrEmployeeMapper cxrEmployeeMapper;

    @Autowired
    private CxrLeaveRemindConfig cxrLeaveRemindConfig;

    @DubboReference
    private RemoteEmployeePostService remoteEmployeePostService;

    private final CxrSiteMapper cxrSiteMapper;

    private final CxrEmployeelLeaveLogMapper cxrEmployeelLeaveLogMapper;
    private final OptLoggerManager optLoggerManager;

    @DubboReference
    private RemoteRegionService remoteRegionService;

    @DubboReference
    private RemoteSiteService remoteSiteService;

    private final SysDeptMapper sysDeptMapper;
    private final MqUtil mqUtil;

    @DubboReference
    private RemoteCustomerDistributionDetailService remoteCustomerDistributionDetailService;

    @Override
    public CxrEmployeeLeaveVo detail(Long id) {
        CxrEmployeeLeaveVo employeeLeaveVo = baseMapper.selectVoById(id);
        if (employeeLeaveVo == null) {
            throw new ServiceException("员工请假已删除");
        }
        return baseMapper.selectVoById(id);
    }

    @Override
    public PageTableDataInfo<CxrEmployeeLeaveListVo> page(
        CxrEmployeeLeaveBo cxrEmployeeLeaveBo, PageQuery pageQuery) {
        // LambdaQueryWrapper<CxrEmployeeLeave> lambdaQueryWrapper =
        // buildLambdaQueryWrapper(cxrEmployeeLeaveBo);
        MPJLambdaWrapper<CxrEmployeeLeave> lambdaWrapper =
            new MPJLambdaWrapper<CxrEmployeeLeave>()
                .selectAll(CxrEmployeeLeave.class)
                .selectAs(CxrEmployee::getName, CxrEmployeeLeaveListVo::getAgentName)
                .selectAs(CxrEmployee::getJobNumber, CxrEmployeeLeaveListVo::getEmployeeJobNumber)
                .selectAs(CxrEmployee::getPhone, CxrEmployeeLeaveListVo::getEmployeePhone)
                .selectAs(CxrEmployee::getInductionTime, CxrEmployeeLeaveListVo::getEmployeeHireDate)
                .leftJoin(
                    CxrEmployee.class,
                    wrapper ->
                        wrapper
                            .eq(CxrEmployee::getId, CxrEmployeeLeave::getEmployeeId)
                            .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()))
                .leftJoin(
                    CxrSite.class,
                    wrapper ->
                        wrapper
                            .eq(CxrSite::getId, CxrEmployeeLeave::getCxrSiteId)
                            .eq(CxrSite::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()))
                .eq(CxrEmployeeLeave::getDeleteStatus, DeleteStatus.not_deleted)
                .eq(
                    cxrEmployeeLeaveBo.getEmployeeId() != null,
                    CxrEmployeeLeave::getEmployeeId,
                    cxrEmployeeLeaveBo.getEmployeeId())
                .like(
                    cxrEmployeeLeaveBo.getCxrSiteName() != null,
                    CxrSite::getName,
                    cxrEmployeeLeaveBo.getCxrSiteName())
                .like(
                    cxrEmployeeLeaveBo.getAgentName() != null,
                    CxrEmployee::getName,
                    cxrEmployeeLeaveBo.getAgentName())
                .eq(
                    cxrEmployeeLeaveBo.getTypeId() != null,
                    CxrEmployeeLeave::getTypeId,
                    cxrEmployeeLeaveBo.getTypeId())
                .ge(
                    cxrEmployeeLeaveBo.getStartTime() != null
                        && cxrEmployeeLeaveBo.getEndTime() == null,
                    CxrEmployeeLeave::getStartTime,
                    cxrEmployeeLeaveBo.getStartTime())
                .le(
                    cxrEmployeeLeaveBo.getStartTime() == null
                        && cxrEmployeeLeaveBo.getEndTime() != null,
                    CxrEmployeeLeave::getEndTime,
                    cxrEmployeeLeaveBo.getEndTime())
                .in(CxrEmployeeLeave::getSysDeptId, LoginUtil.getLoginUser().getDeptIds());

        IPage<CxrEmployeeLeaveListVo> result =
            baseMapper.selectJoinPage(pageQuery.build(), CxrEmployeeLeaveListVo.class, lambdaWrapper);
        if (result.getRecords() != null && result.getRecords().size() != 0) {
            List<CxrEmployeeLeaveListVo> voList = result.getRecords();
            for (CxrEmployeeLeaveListVo vo : voList) {
                vo.setTotalTime(
                    DateUtils.calculateFormat(
                        DateUtils.diffMillisecond(vo.getEndTime(), vo.getStartTime()) + 86400000));
            }
        }
        return PageTableDataInfo.build(result);
    }

    @Override
    public PageTableDataInfo<CxrEmployeeLeaveListVo> saStaffPage(
        CxrEmployeeLeaveBo cxrEmployeeLeaveBo, PageQuery pageQuery) {
        Set<Long> siteIds = new HashSet<>();
        // 如果当前登录员工是大区经理则返回该区下全部站点的请假信息
        List<CxrRegion> regions =
            remoteRegionService.selectListByEmployeeId(LoginUtil.getLoginUser().getUserId());
        if (CollUtil.isNotEmpty(regions)) {
            // 区域下所有的站点ID
            siteIds.addAll(
                remoteSiteService.selectSiteIdsByRegionIds(
                    regions.stream().map(CxrRegion::getId).collect(Collectors.toList())));
        }
        // TODO: 2022/7/26 这里跟前端显示页面有冲突 先根据前端页面选择的站点进行查询
        if (ObjectUtil.isNotEmpty(cxrEmployeeLeaveBo.getCxrSiteId())) {
            siteIds.add(cxrEmployeeLeaveBo.getCxrSiteId());
        }
        // 如果前端没选默认为自己站点的请假信息
        if (siteIds.size() == 0) {
            siteIds.add(LoginUtil.getLoginUser().getUserId());
        }
        // 全部站点请假信息
        IPage<CxrEmployeeLeaveListVo> result =
            baseMapper.pageByCondition(
                pageQuery.build(), this.buildQueryWrapper(cxrEmployeeLeaveBo, siteIds));
        if (result.getRecords() != null && result.getRecords().size() != 0) {
            List<CxrEmployeeLeaveListVo> voList = result.getRecords();
            for (CxrEmployeeLeaveListVo vo : voList) {
                vo.setTotalTime(
                    DateUtils.calculateFormat(
                        DateUtils.differenceMillisecond(vo.getEndTime(), vo.getStartTime()) + 86400000));
            }
        }
        return PageTableDataInfo.build(result);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addLeave(CxrEmployeeLeaveAddBo cxrEmployeeLeaveAddBo) {
        // 判断请假时间是否重复
        LambdaQueryWrapper<CxrEmployeeLeave> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CxrEmployeeLeave::getEmployeeId, cxrEmployeeLeaveAddBo.getEmployeeId());
        wrapper.eq(CxrEmployeeLeave::getDeleteStatus, DeleteStatus.NOT_DELETED);
        wrapper.apply(
            "((start_time <= {0} AND end_time >= {1}) OR (start_time <= {2} AND end_time >= {3}))",
            cxrEmployeeLeaveAddBo.getStartTime(),
            cxrEmployeeLeaveAddBo.getStartTime(),
            cxrEmployeeLeaveAddBo.getEndTime(),
            cxrEmployeeLeaveAddBo.getEndTime());
        Long count = baseMapper.selectCount(wrapper);
        if (count > 0) {
            throw new ServiceException("请假时间重复");
        }
        CxrEmployeeLeave cxrEmployeeLeave =
            BeanUtil.toBean(cxrEmployeeLeaveAddBo, CxrEmployeeLeave.class);
        cxrEmployeeLeave.setCxrSiteId(StaffLoginHelper.getLoginUser().getSiteId());
        // 审批状态
        cxrEmployeeLeave.setApprovalStatus(LeaveApprovalStatus.CHECK_PEND.getValue());
        baseMapper.insert(cxrEmployeeLeave);
    }

    @Override
    public CxrEmployeeLeaveVo getLeaveVoById(Long id) {
        QueryWrapper<SysUser> wrapper = Wrappers.query();
        wrapper.eq("l.delete_status", DeleteStatus.not_deleted);
        wrapper.eq("l.id", id);
        CxrEmployeeLeaveVo cxrEmployeeLeaveVo = baseMapper.getLeaveVoById(wrapper);
        if (cxrEmployeeLeaveVo == null) {
            throw new ServiceException("数据已经不存在");
        }
        return cxrEmployeeLeaveVo;
    }

    private Wrapper<SysUser> buildQueryWrapper(
        CxrEmployeeLeaveBo cxrEmployeeLeaveBo, Set<Long> siteIds) {
        Map<String, Object> params = cxrEmployeeLeaveBo.getParams();
        QueryWrapper<SysUser> wrapper = Wrappers.query();
        wrapper
            .eq("l.delete_status", DeleteStatus.not_deleted)
            .eq(
                ObjectUtil.isNotNull(cxrEmployeeLeaveBo.getSysDeptId()),
                "l.sys_dept_id",
                cxrEmployeeLeaveBo.getSysDeptId())
            .like(
                StringUtils.isNotBlank(cxrEmployeeLeaveBo.getCxrSiteName()),
                "d.name",
                cxrEmployeeLeaveBo.getCxrSiteName())
            .eq(
                ObjectUtil.isNotNull(cxrEmployeeLeaveBo.getTypeId()),
                "l.type_id",
                cxrEmployeeLeaveBo.getTypeId())
            //            .between(params.get("beginTime") != null && params.get("endTime") != null,
            //                "u.create_time", params.get("beginTime"), params.get("endTime"))
            .ge(
                ObjectUtil.isNotNull(cxrEmployeeLeaveBo.getStartTime()),
                "l.start_time",
                cxrEmployeeLeaveBo.getStartTime())
            .le(
                ObjectUtil.isNotNull(cxrEmployeeLeaveBo.getEndTime()),
                "l.end_time",
                cxrEmployeeLeaveBo.getEndTime())
            .in(ObjectUtil.isNotNull(siteIds), "d.id", siteIds);
        return wrapper;
    }

    private final LambdaQueryWrapper<CxrEmployeeLeave> buildLambdaQueryWrapper(
        CxrEmployeeLeaveBo cxrEmployeeLeaveBo) {

        LambdaQueryWrapper<CxrEmployeeLeave> lambdaQueryWrapper =
            Wrappers.lambdaQuery(CxrEmployeeLeave.class);
        lambdaQueryWrapper.eq(
            cxrEmployeeLeaveBo.getEmployeeId() != null,
            CxrEmployeeLeave::getEmployeeId,
            cxrEmployeeLeaveBo.getEmployeeId());
        lambdaQueryWrapper.eq(
            cxrEmployeeLeaveBo.getStartTime() != null,
            CxrEmployeeLeave::getStartTime,
            cxrEmployeeLeaveBo.getStartTime());
        lambdaQueryWrapper.eq(
            cxrEmployeeLeaveBo.getEndTime() != null,
            CxrEmployeeLeave::getEndTime,
            cxrEmployeeLeaveBo.getEndTime());
        lambdaQueryWrapper.eq(
            cxrEmployeeLeaveBo.getTypeId() != null,
            CxrEmployeeLeave::getTypeId,
            cxrEmployeeLeaveBo.getTypeId());
        lambdaQueryWrapper.eq(
            StringUtils.isNotBlank(cxrEmployeeLeaveBo.getMemo()),
            CxrEmployeeLeave::getMemo,
            cxrEmployeeLeaveBo.getMemo());
        lambdaQueryWrapper.eq(
            cxrEmployeeLeaveBo.getApplyTime() != null,
            CxrEmployeeLeave::getApplyTime,
            cxrEmployeeLeaveBo.getApplyTime());
        lambdaQueryWrapper.eq(
            cxrEmployeeLeaveBo.getApprovalUserId() != null,
            CxrEmployeeLeave::getApprovalUserId,
            cxrEmployeeLeaveBo.getApprovalUserId());
        lambdaQueryWrapper.eq(
            StringUtils.isNotBlank(cxrEmployeeLeaveBo.getApprovalStatus()),
            CxrEmployeeLeave::getApprovalStatus,
            cxrEmployeeLeaveBo.getApprovalStatus());
        lambdaQueryWrapper.eq(
            cxrEmployeeLeaveBo.getMode() != null,
            CxrEmployeeLeave::getMode,
            cxrEmployeeLeaveBo.getMode());
        lambdaQueryWrapper.eq(
            cxrEmployeeLeaveBo.getCxrSiteId() != null,
            CxrEmployeeLeave::getCxrSiteId,
            cxrEmployeeLeaveBo.getCxrSiteId());
        return lambdaQueryWrapper;
    }

    @Override
    public Boolean add(CxrEmployeeLeaveBo cxrEmployeeLeaveBo) {
        verifyParam(cxrEmployeeLeaveBo);
        CxrEmployeeLeave cxrEmployeeLeave = BeanUtil.toBean(cxrEmployeeLeaveBo, CxrEmployeeLeave.class);
        // 判断请假时间是否重复
        LambdaQueryWrapper<CxrEmployeeLeave> wrapper = Wrappers.lambdaQuery();
        wrapper
            .eq(CxrEmployeeLeave::getEmployeeId, cxrEmployeeLeave.getEmployeeId())
            .ge(CxrEmployeeLeave::getStartTime, cxrEmployeeLeave.getStartTime())
            .le(CxrEmployeeLeave::getEndTime, cxrEmployeeLeave.getEndTime())
            .eq(CxrEmployeeLeave::getDeleteStatus, DeleteStatus.not_deleted);
        Long count = baseMapper.selectCount(wrapper);
        if (count > 0) {
            throw new ServiceException("请假时间重复");
        }
        // 审批状态
        cxrEmployeeLeave.setApprovalStatus(null);

        boolean flag = baseMapper.insert(cxrEmployeeLeave) > 0;
        if (flag) {
            cxrEmployeeLeaveBo.setId(cxrEmployeeLeave.getId());
        }
        return flag;
    }

    @Override
    public Boolean edit(CxrEmployeeLeaveBo cxrEmployeeLeaveBo) {
        verifyParam(cxrEmployeeLeaveBo);
        CxrEmployeeLeave cxrEmployeeLeave = BeanUtil.toBean(cxrEmployeeLeaveBo, CxrEmployeeLeave.class);
        LambdaUpdateWrapper<CxrEmployeeLeave> lambdaUpdateWrapper =
            Wrappers.lambdaUpdate(CxrEmployeeLeave.class);
        lambdaUpdateWrapper.eq(CxrEmployeeLeave::getId, cxrEmployeeLeaveBo.getId());
        lambdaUpdateWrapper.eq(CxrEmployeeLeave::getEmployeeId, cxrEmployeeLeaveBo.getEmployeeId());
        lambdaUpdateWrapper.eq(CxrEmployeeLeave::getStartTime, cxrEmployeeLeaveBo.getStartTime());
        lambdaUpdateWrapper.eq(CxrEmployeeLeave::getEndTime, cxrEmployeeLeaveBo.getEndTime());
        lambdaUpdateWrapper.eq(CxrEmployeeLeave::getTypeId, cxrEmployeeLeaveBo.getTypeId());
        lambdaUpdateWrapper.eq(CxrEmployeeLeave::getMemo, cxrEmployeeLeaveBo.getMemo());
        //                lambdaUpdateWrapper.eq(CxrEmployeeLeave::getFileOneUrl,
        // cxrEmployeeLeaveBo.getFileOneUrl());
        //                lambdaUpdateWrapper.eq(CxrEmployeeLeave::getFileTwoUrl,
        // cxrEmployeeLeaveBo.getFileTwoUrl());
        //                lambdaUpdateWrapper.eq(CxrEmployeeLeave::getFileThreeUrl,
        // cxrEmployeeLeaveBo.getFileThreeUrl());
        //                lambdaUpdateWrapper.eq(CxrEmployeeLeave::getFileFourUrl,
        // cxrEmployeeLeaveBo.getFileFourUrl());
        //                lambdaUpdateWrapper.eq(CxrEmployeeLeave::getFileFiveUrl,
        // cxrEmployeeLeaveBo.getFileFiveUrl());
        lambdaUpdateWrapper.eq(CxrEmployeeLeave::getApplyTime, cxrEmployeeLeaveBo.getApplyTime());
        lambdaUpdateWrapper.eq(
            CxrEmployeeLeave::getApprovalUserId, cxrEmployeeLeaveBo.getApprovalUserId());
        lambdaUpdateWrapper.eq(
            CxrEmployeeLeave::getApprovalStatus, cxrEmployeeLeaveBo.getApprovalStatus());
        lambdaUpdateWrapper.eq(CxrEmployeeLeave::getMode, cxrEmployeeLeaveBo.getMode());
        lambdaUpdateWrapper.eq(CxrEmployeeLeave::getCxrSiteId, cxrEmployeeLeaveBo.getCxrSiteId());
        lambdaUpdateWrapper.eq(CxrEmployeeLeave::getRevision, cxrEmployeeLeaveBo.getRevision());
        lambdaUpdateWrapper.eq(CxrEmployeeLeave::getCreateBy, cxrEmployeeLeaveBo.getCreateBy());
        lambdaUpdateWrapper.eq(CxrEmployeeLeave::getCreateByName, cxrEmployeeLeaveBo.getCreateByName());
        lambdaUpdateWrapper.eq(CxrEmployeeLeave::getCreateByType, cxrEmployeeLeaveBo.getCreateByType());
        lambdaUpdateWrapper.eq(CxrEmployeeLeave::getCreateTime, cxrEmployeeLeaveBo.getCreateTime());
        lambdaUpdateWrapper.eq(CxrEmployeeLeave::getUpdateBy, cxrEmployeeLeaveBo.getUpdateBy());
        lambdaUpdateWrapper.eq(CxrEmployeeLeave::getUpdateByName, cxrEmployeeLeaveBo.getUpdateByName());
        lambdaUpdateWrapper.eq(CxrEmployeeLeave::getUpdateByType, cxrEmployeeLeaveBo.getUpdateByType());
        lambdaUpdateWrapper.eq(CxrEmployeeLeave::getUpdateTime, cxrEmployeeLeaveBo.getUpdateTime());
        lambdaUpdateWrapper.eq(CxrEmployeeLeave::getDeleteBy, cxrEmployeeLeaveBo.getDeleteBy());
        lambdaUpdateWrapper.eq(CxrEmployeeLeave::getDeleteByName, cxrEmployeeLeaveBo.getDeleteByName());
        lambdaUpdateWrapper.eq(CxrEmployeeLeave::getDeleteByType, cxrEmployeeLeaveBo.getDeleteByType());
        lambdaUpdateWrapper.eq(CxrEmployeeLeave::getDeleteTime, cxrEmployeeLeaveBo.getDeleteTime());
        lambdaUpdateWrapper.eq(CxrEmployeeLeave::getDeleteStatus, cxrEmployeeLeaveBo.getDeleteStatus());
        lambdaUpdateWrapper.eq(CxrEmployeeLeave::getSortNum, cxrEmployeeLeaveBo.getSortNum());
        lambdaUpdateWrapper.eq(CxrEmployeeLeave::getRemark, cxrEmployeeLeaveBo.getRemark());
        lambdaUpdateWrapper.eq(CxrEmployeeLeave::getSysDeptId, cxrEmployeeLeaveBo.getSysDeptId());
        lambdaUpdateWrapper.eq(CxrEmployeeLeave::getSpareId, cxrEmployeeLeaveBo.getSpareId());
        return baseMapper.update(cxrEmployeeLeave, lambdaUpdateWrapper) > 0;
    }

    @Override
    public Boolean remove(Set<Long> idSet) {

        // TODO 做一些业务上的校验,判断是否需要校验

        return baseMapper.deleteBatchIds(idSet) > 0;
    }

    /**
     * 校验参数
     */
    private void verifyParam(CxrEmployeeLeaveBo cxrEmployeeLeaveBo) {
        if (Objects.equals(cxrEmployeeLeaveBo.getTypeId(), (long) PeopleLeveaEnums.CANCEL_LEAVE.getValue())) {
            if (cxrEmployeeLeaveBo.getEndTime().before(cxrEmployeeLeaveBo.getStartTime())) {
                throw new ServiceException("销假开始时间不能超过销假结束时间");
            }
            CxrEmployeeLeave originalLeave = baseMapper.selectById(cxrEmployeeLeaveBo.getLeaveId());
            if (cxrEmployeeLeaveBo.getStartTime().before(originalLeave.getStartTime())) {
                throw new ServiceException("销假开始时间不能超过请假开始时间");
            }
            if (cxrEmployeeLeaveBo.getEndTime().compareTo(originalLeave.getEndTime()) != 0) {
                throw new ServiceException("销假结束时间必须等于请假结束时间");
            }
            if (!originalLeave.getApprovalStatus().equals(LeaveApprovalStatus.APPROV.getValue())) {
                throw new ServiceException("请假单未通过审核,不能销假");
            }
            List<CxrEmployeeLeaveVo> pendingCancelLeaveList = this.getPendingCancelLeave(cxrEmployeeLeaveBo.getId(),
                originalLeave.getId());
            if (!pendingCancelLeaveList.isEmpty()) {
                throw new ServiceException("该销假单有未审核的销假记录,请处理后再提交");
            }
        } else {
            if (cxrEmployeeLeaveBo.getEndTime().before(cxrEmployeeLeaveBo.getStartTime())) {
                throw new ServiceException("请假开始时间不能超过请假结束时间");
            }
            if (cxrEmployeeLeaveBo.getTypeId() == null) {
                throw new ServiceException("请选择请假类型");
            }

            // 判断请假时间是否重复
            CxrEmployeeLeaveCheckBo cxrEmployeeLeaveCheckBo = new CxrEmployeeLeaveCheckBo();
            cxrEmployeeLeaveCheckBo.setEmployeeId(cxrEmployeeLeaveBo.getEmployeeId());
            cxrEmployeeLeaveCheckBo.setStartTime(cxrEmployeeLeaveBo.getStartTime());
            cxrEmployeeLeaveCheckBo.setEndTime(cxrEmployeeLeaveBo.getEndTime());
            cxrEmployeeLeaveCheckBo.setExcludeId(cxrEmployeeLeaveBo.getId());
            CxrEmployeeLeaveCheckVo cxrEmployeeLeaveCheckVo = this.checkLeaveTimeConflict(cxrEmployeeLeaveCheckBo);
            if (cxrEmployeeLeaveCheckVo.getHasConflict()) {
                throw new ServiceException(cxrEmployeeLeaveCheckVo.getMessage());
            }
        }

    }

    // 配送端的查询和请假信息
    @Override
    public Page<CxrEmployeeLeaveListVo> peopleLeave(
        CxrEmployeeLeaveBo cxrEmployeeLeaveBo, PageQuery pageQuery) {
        Page<CxrEmployeeLeaveListVo> page = baseMapper.page(pageQuery.build(), cxrEmployeeLeaveBo);
        return page;
    }

    // 根据id查询员工请假的信息
    @Override
    public CxrEmployeeLeaveVo getLeaveid(Long id) {
        CxrEmployeeLeaveVo cxrEmployeeLeaveVo = baseMapper.selectVoById(id, CxrEmployeeLeaveVo.class);
        return cxrEmployeeLeaveVo;
    }

    @Override
    @Transactional
    public Integer addPeopleleave(CxrEmployeeLeaveAddBo cxrEmployeeLeaveAddBo) {
        this.verifyParam(BeanUtil.toBean(cxrEmployeeLeaveAddBo, CxrEmployeeLeaveBo.class));
        CxrEmployeeLeave cxrEmployeeLeave =
            BeanUtil.toBean(cxrEmployeeLeaveAddBo, CxrEmployeeLeave.class);

        //判断当前用户是否是主管
        List<CxrEmployeePost> cxrEmployeePosts =
            cxrEmployeePostMapper.selectList(new LambdaQueryWrapper<CxrEmployeePost>()
                .eq(CxrEmployeePost::getChargeStatus, ChargeStatus.IN_PROGRESS.getValue())
                .eq(CxrEmployeePost::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .in(CxrEmployeePost::getCxrEmployeeId, LoginUtil.getLoginUser().getUserId(),
                    cxrEmployeeLeaveAddBo.getEmployeeId())
            );
        Set<String> postIds = cxrEmployeePosts.stream().map(e -> e.getCxrPostId().getValue())
            .collect(Collectors.toSet());
//        if (!postIds.contains(PostType.DIRECTOR.getValue())) {
//            throw new ServiceException("主管才能发起请假申请！");
//        }
        List<CxrEmployeePost> empManger = cxrEmployeePosts.stream().filter(
            s -> s.getCxrEmployeeId().equals(cxrEmployeeLeaveAddBo.getEmployeeId()) && Integer.parseInt(s.getCxrPostId().getValue())
                >= Integer.parseInt(PostType.REGION_MANAGER.getValue())).collect(toList());

        if (CollectionUtil.isNotEmpty(empManger)) {
            throw new ServiceException("经理及以上请假暂不能从系统申请");
        }
        if (Objects.equals(cxrEmployeeLeaveAddBo.getTypeId(),
            (long) PeopleLeveaEnums.CANCEL_LEAVE.getValue())) {
            CxrEmployeeLeave orginLeave = baseMapper.selectById(cxrEmployeeLeaveAddBo.getLeaveId());
            cxrEmployeeLeaveAddBo.setDelayFlag(orginLeave.getDelayFlag());
            cxrEmployeeLeave.setSrcLeaveTypeId(orginLeave.getTypeId());
        }
//        // 判断补假的时间是否重复
//        if (cxrEmployeeLeaveAddBo.getMode().equals(1L)) {
//            LambdaQueryWrapper<CxrEmployeeLeave> wrapper = Wrappers.lambdaQuery();
//            wrapper
//                .eq(CxrEmployeeLeave::getEmployeeId, cxrEmployeeLeave.getEmployeeId())
//                .eq(CxrEmployeeLeave::getMode, cxrEmployeeLeave.getMode())
//                .ge(CxrEmployeeLeave::getStartTime, cxrEmployeeLeave.getStartTime())
//                .le(CxrEmployeeLeave::getEndTime, cxrEmployeeLeave.getEndTime())
//                .eq(CxrEmployeeLeave::getDeleteStatus, DeleteStatus.not_deleted);
//            Long count = baseMapper.selectCount(wrapper);
//            if (count > 0) {
//                throw new ServiceException("补假时间重复");
//            }
//        }

//        if (ObjectUtil.isNull(cxrEmployeeLeaveAddBo.getMode())) {
//            throw new ServiceException("请假方式必填");
//        }
//        if (PeopleLeaveTypeEnums.WRITTEN_LEAVE.getValue()
//            == cxrEmployeeLeaveAddBo.getMode().intValue()) {
//            cxrEmployeeLeave.setMode(cxrEmployeeLeaveAddBo.getMode());
//            if (remoteCustomerDistributionDetailService.countEmAddress(
//                cxrEmployeeLeaveAddBo.getEmployeeId())
//                > 0) {
//                return 1;
//            }
//        }
//        if (PeopleLeaveTypeEnums.TAKE_LEAVE.getValue() == cxrEmployeeLeaveAddBo.getMode().intValue()) {
//            cxrEmployeeLeave.setMode(cxrEmployeeLeaveAddBo.getMode());
//        }

//        if (ObjectUtil.isNull(cxrEmployeeLeaveAddBo.getMode())) {
//            throw new ServiceException("请假类型必填");
//        }
//        if (PeopleLeveaEnums.PERSONAL_LEAVE.getValue() == cxrEmployeeLeaveAddBo.getTypeId().intValue()
//            || PeopleLeveaEnums.SICK_LEAVE.getValue() == cxrEmployeeLeaveAddBo.getTypeId().intValue()
//            || PeopleLeveaEnums.MARRIAGE_LEAVE.getValue()
//            == cxrEmployeeLeaveAddBo.getTypeId().intValue()
//            || PeopleLeveaEnums.MATERNITY_LEAVE.getValue()
//            == cxrEmployeeLeaveAddBo.getTypeId().intValue()
//            || PeopleLeveaEnums.PATERNITY_LEAVE.getValue()
//            == cxrEmployeeLeaveAddBo.getTypeId().intValue()
//            || PeopleLeveaEnums.FUNERAL_LEAVE.getValue() == cxrEmployeeLeaveAddBo.getTypeId().intValue()
//            || PeopleLeveaEnums.OTHER_LEAVE.getValue()
//            == cxrEmployeeLeaveAddBo.getTypeId().intValue()) {
//
//        }
        cxrEmployeeLeave.setTypeId(cxrEmployeeLeaveAddBo.getTypeId());
        cxrEmployeeLeave.setFileOneUrl(cxrEmployeeLeaveAddBo.getFileOneUrl());
        cxrEmployeeLeave.setEmployeeId(cxrEmployeeLeaveAddBo.getEmployeeId());
        cxrEmployeeLeave.setStartTime(cxrEmployeeLeaveAddBo.getStartTime());
        cxrEmployeeLeave.setEndTime(cxrEmployeeLeaveAddBo.getEndTime());
        int days = (int) DateUtil.betweenDay(cxrEmployeeLeaveAddBo.getStartTime(),
            cxrEmployeeLeaveAddBo.getEndTime(), false) + 1;
        if (Objects.equals(cxrEmployeeLeaveAddBo.getTypeId(),
            (long) PeopleLeveaEnums.CANCEL_LEAVE.getValue())) {
            cxrEmployeeLeave.setDays(-days);
            cxrEmployeeLeave.setActDays(-days);
        } else {
            cxrEmployeeLeave.setDays(days);
            cxrEmployeeLeave.setActDays(days);
        }

        cxrEmployeeLeave.setActStartTime(cxrEmployeeLeaveAddBo.getStartTime().toInstant()
            .atZone(ZoneId.systemDefault())
            .toLocalDate());
        cxrEmployeeLeave.setActEndTime(cxrEmployeeLeaveAddBo.getEndTime().toInstant()
            .atZone(ZoneId.systemDefault())
            .toLocalDate());

        if (cxrEmployeeLeaveAddBo.getDelayFlag()) {
            cxrEmployeeLeave.setApprovalStatus(LeaveApprovalStatus.CHECK_PEND.getValue());
        } else {
            cxrEmployeeLeave.setApprovalStatus(LeaveApprovalStatus.APPROV.getValue());
            cxrEmployeeLeave.setApprovalUserId(1L);
            cxrEmployeeLeave.setApprovalUserName("admin");
            cxrEmployeeLeave.setApprovalTime(new Date());
        }

        cxrEmployeeLeave.setCreateTime(new Date());
        cxrEmployeeLeave.setApplyTime(new Date());
//        cxrEmployeeLeave.setRevision(cxrEmployeeLeaveAddBo.getRevision() + 1);
        //补充基础数据
        Long employeeId = cxrEmployeeLeaveAddBo.getEmployeeId();
        CxrEmployee cxrEmployee = cxrEmployeeMapper.selectById(employeeId);
        Long cxrSiteId = cxrEmployee.getCxrSiteId();
        CxrSite cxrSite = cxrSiteMapper.selectById(cxrSiteId);
        Long cxrRegionId = cxrSite.getCxrRootRegionId();
        CxrRegion cxrRegion = remoteRegionService.queryById(cxrRegionId);
        SysDept sysRegionDept = sysDeptMapper.selectById(cxrRegionId);
        SysDept sysRootRegionDept = sysDeptMapper.selectById(sysRegionDept.getParentId());
        SysDept company = sysDeptMapper.selectById(cxrSite.getCurrentDeptId());
        cxrEmployeeLeave.setAgentName(cxrEmployee.getName());
        cxrEmployeeLeave.setAgentCode(cxrEmployee.getJobNumber());
        cxrEmployeeLeave.setAgentLevel(cxrEmployee.getEmployeeLevelType());
        cxrEmployeeLeave.setCxrSiteId(cxrSiteId);
        cxrEmployeeLeave.setSiteName(cxrSite.getName());
        cxrEmployeeLeave.setSiteCode(cxrSite.getSiteMark());
        cxrEmployeeLeave.setRegionId(cxrRegion.getId());
        cxrEmployeeLeave.setRegionName(cxrRegion.getName());
        cxrEmployeeLeave.setRootRegionId(sysRootRegionDept.getDeptId());
        cxrEmployeeLeave.setRootRegionName(sysRootRegionDept.getDeptName());
        cxrEmployeeLeave.setCompanyId(cxrSite.getCurrentDeptId());
        cxrEmployeeLeave.setCompanyName(company.getDeptName());
        cxrEmployeeLeave.setSysDeptId(LoginUtil.getLoginUser().getDeptId());
        cxrEmployeeLeave.setCreateByName(LoginUtil.getLoginUser().getUserName());
        cxrEmployeeLeave.setMode((long) PeopleLeaveTypeEnums.WRITTEN_LEAVE.getValue());
        baseMapper.insert(cxrEmployeeLeave);
        if (!Objects.equals(cxrEmployeeLeaveAddBo.getTypeId(),
            (long) PeopleLeveaEnums.CANCEL_LEAVE.getValue())) {

            // 添加到日志表中
            CxrEmployeeLeaveLog cxrEmployeeLeaveLog =
                BeanUtil.copyProperties(cxrEmployeeLeave, CxrEmployeeLeaveLog.class);
            cxrEmployeelLeaveLogMapper.insertOrUpdate(cxrEmployeeLeaveLog);
        } else {
            CxrEmployeeLeaveAuditBo cxrEmployeeLeaveAuditBo = new CxrEmployeeLeaveAuditBo();
            cxrEmployeeLeaveAuditBo.setApprovalStatus(cxrEmployeeLeave.getApprovalStatus());
            this.updateOriginalLeave(cxrEmployeeLeaveAuditBo, cxrEmployeeLeave);
        }

        return 0;
    }

    // 后台请假分页查询
    @Override
    public PageTableDataInfo<CxrEmployeeLeaveListVo> pagePeopleLeave(
        CxrEmployeeLeaveBo cxrEmployeeLeaveBo, PageQuery pageQuery) {

        DataScope dataScope = new DataScope();
        dataScope.setUserType(UserType.SYS_USER);

        IPage<CxrEmployeeLeaveListVo> result = baseMapper.pagePeopleLeave(pageQuery.build(), cxrEmployeeLeaveBo);
        if (result.getRecords() != null && result.getRecords().size() != 0) {
            List<CxrEmployeeLeaveListVo> voList = result.getRecords();
            for (CxrEmployeeLeaveListVo vo : voList) {
                vo.setTotalTime(
                    DateUtils.calculateFormat(
                        DateUtils.diffMillisecond(vo.getEndTime(), vo.getStartTime()) + 86400000));
            }
        }
        return PageTableDataInfo.build(result);
    }

    //删除
    @Transactional
    @Override
    public void deleteId(CxrEmployeeLeaveBo cxrEmployeeLeaveBo) {
        CxrEmployeeLeave cxrEmployeeLeave = BeanUtil.toBean(cxrEmployeeLeaveBo, CxrEmployeeLeave.class);
        CxrEmployeeLeave cxrEmployeeLeaveOld = baseMapper.selectById(cxrEmployeeLeaveBo.getId());
        if (!cxrEmployeeLeaveOld.getApprovalStatus().equals(LeaveApprovalStatus.CHECK_PEND.getValue())) {
            throw new ServiceException("该条数据状态已变更，不允许删除");
        }
        baseMapper.update(null, new LambdaUpdateWrapper<CxrEmployeeLeave>()
            .eq(CxrEmployeeLeave::getId, cxrEmployeeLeave.getId())
            .set(CxrEmployeeLeave::getDeleteStatus, DeleteStatus.DELETED.getValue())
            .set(CxrEmployeeLeave::getUpdateTime, new Date())
            .set(CxrEmployeeLeave::getUpdateBy, LoginUtil.getLoginUser().getUserId())
            .set(CxrEmployeeLeave::getDeleteTime, new Date())
            .set(CxrEmployeeLeave::getDeleteBy, LoginUtil.getLoginUser().getUserId())
            .set(CxrEmployeeLeave::getDeleteByName, LoginUtil.getLoginUser().getUserName())
        );
        cxrEmployeeLeave.setSysDeptId(LoginUtil.getLoginUser().getDeptId());
        cxrEmployeeLeave.setCreateByName(LoginUtil.getLoginUser().getUserName());
        cxrEmployeeLeave.setCreateBy(LoginUtil.getLoginUser().getUserId());
        cxrEmployeeLeave.setCreateTime(new Date());
        cxrEmployeeLeave.setFileOneUrl(cxrEmployeeLeaveBo.getFileOneUrl());
        cxrEmployeeLeave.setEmployeeId(LoginUtil.getLoginUser().getUserId());
        cxrEmployeeLeave.setStartTime(cxrEmployeeLeaveBo.getStartTime());
        cxrEmployeeLeave.setEndTime(cxrEmployeeLeaveBo.getEndTime());
        cxrEmployeeLeave.setApprovalStatus(cxrEmployeeLeaveBo.getApprovalStatus());
        cxrEmployeeLeave.setUpdateBy(LoginUtil.getLoginUser().getUserId());
        cxrEmployeeLeave.setDeleteStatus(DeleteStatus.deleted);
        CxrEmployeeLeaveLog cxrEmployeeLeaveLog =
            BeanUtil.copyProperties(cxrEmployeeLeave, CxrEmployeeLeaveLog.class);
        cxrEmployeelLeaveLogMapper.insertOrUpdate(cxrEmployeeLeaveLog);
    }

    //
    @Transactional
    @Override
    public void editId(CxrEmployeeLeaveBo cxrEmployeeLeaveBo) {
        this.verifyParam(cxrEmployeeLeaveBo);
        CxrEmployeeLeave cxrEmployeeLeave = BeanUtil.toBean(cxrEmployeeLeaveBo, CxrEmployeeLeave.class);
        LambdaUpdateWrapper<CxrEmployeeLeave> lambdaUpdateWrapper =
            Wrappers.lambdaUpdate(CxrEmployeeLeave.class);
        lambdaUpdateWrapper.eq(CxrEmployeeLeave::getId, cxrEmployeeLeaveBo.getId());
        CxrEmployeeLeave cxrEmployeeLeaveOld = baseMapper.selectById(cxrEmployeeLeaveBo.getId());
        if (cxrEmployeeLeaveOld.getDeleteStatus().equals(DeleteStatus.DELETED.getValue())) {
            throw new ServiceException("该条数据已被撤销，无法编辑！");
        }
        if (!cxrEmployeeLeaveOld.getApprovalStatus().equals(LeaveApprovalStatus.CHECK_PEND.getValue())) {
            throw new ServiceException("该申请不是待审核状态，请刷新页面重试！");
        }
        int days = (int) DateUtil.betweenDay(cxrEmployeeLeave.getStartTime(),
            cxrEmployeeLeave.getEndTime(), false) + 1;
        if (Objects.equals(cxrEmployeeLeave.getTypeId(),
            (long) PeopleLeveaEnums.CANCEL_LEAVE.getValue())) {
            cxrEmployeeLeave.setDays(-days);
            cxrEmployeeLeave.setActDays(-days);
        } else {
            cxrEmployeeLeave.setDays(days);
            cxrEmployeeLeave.setActDays(days);
        }

        cxrEmployeeLeave.setActStartTime(cxrEmployeeLeave.getStartTime().toInstant()
            .atZone(ZoneId.systemDefault())
            .toLocalDate());
        cxrEmployeeLeave.setActEndTime(cxrEmployeeLeave.getEndTime().toInstant()
            .atZone(ZoneId.systemDefault())
            .toLocalDate());
        boolean b = baseMapper.update(cxrEmployeeLeave, lambdaUpdateWrapper) > 0;
        CxrEmployeeLeaveLog cxrEmployeeLeaveLog =
            BeanUtil.copyProperties(cxrEmployeeLeave, CxrEmployeeLeaveLog.class);
//        cxrEmployeeLeaveLog.setCxrSiteId(null);
        cxrEmployeeLeaveLog.setSysDeptId(LoginUtil.getLoginUser().getDeptId());
        cxrEmployeeLeaveLog.setCreateByName(LoginUtil.getLoginUser().getUserName());
        cxrEmployeeLeaveLog.setCreateBy(LoginUtil.getLoginUser().getUserId());
        cxrEmployeeLeaveLog.setCreateTime(new Date());
        cxrEmployeeLeaveLog.setApplyTime(new Date());
        cxrEmployeeLeaveLog.setFileOneUrl(cxrEmployeeLeaveBo.getFileOneUrl());
        cxrEmployeeLeaveLog.setEmployeeId(cxrEmployeeLeaveBo.getEmployeeId());
        cxrEmployeeLeaveLog.setStartTime(cxrEmployeeLeaveBo.getStartTime());
        cxrEmployeeLeaveLog.setEndTime(cxrEmployeeLeaveBo.getEndTime());
        cxrEmployeeLeaveLog.setApprovalStatus(cxrEmployeeLeaveBo.getApprovalStatus());
        cxrEmployeeLeaveLog.setUpdateBy(LoginUtil.getLoginUser().getUserId());
        cxrEmployeeLeaveLog.setUpdateTime(new Date());

        cxrEmployeelLeaveLogMapper.insertOrUpdate(cxrEmployeeLeaveLog);
    }

    @Transactional
    @Override
    public void redo(CxrEmployeeLeaveBo cxrEmployeeLeaveBo) {
        this.baseMapper.update(null, new LambdaUpdateWrapper<CxrEmployeeLeave>()
            .eq(CxrEmployeeLeave::getId, cxrEmployeeLeaveBo.getId())
            .set(CxrEmployeeLeave::getApprovalStatus, LeaveApprovalStatus.CHECK_PEND.getValue())
            .set(CxrEmployeeLeave::getApprovalTime, null)
            .set(CxrEmployeeLeave::getApprovalUserId, null)
            .set(CxrEmployeeLeave::getApprovalNotes, null)
            .set(CxrEmployeeLeave::getUpdateBy, LoginUtil.getLoginUser().getUserId())
            .set(CxrEmployeeLeave::getUpdateTime, new Date())
        );
        this.edit(cxrEmployeeLeaveBo);

    }

    @Override
    public void employeeLeaveExport(CxrEmployeeLeaveBo bo, HttpServletResponse response) {
        int pageSize = 1000;
        long pageCount = 1;
        LoginUser loginUser = LoginHelper.getLoginUser();
        PageQuery querybo = new PageQuery();
        querybo.setPageNum(1);
        List<CxrEmployeeLeaveListVo> records = new ArrayList<>();
        IPage<CxrEmployeeLeaveListVo> result = baseMapper.pagePeopleLeave(querybo.build().setSize(1), bo);
        Long splitTotal = result.getTotal();
        ExcelReportDTO excelReportDTO = ExcelMqUtil.excelSendRecord(bo.getHtmlName(), splitTotal, loginUser.getUserId(),
            loginUser.getUserType(), loginUser.getUserName());
        for (int current = 1; current <= pageCount; current++) {
            PageQuery query = new PageQuery();
            query.setPageNum(current);
            query.setPageSize(pageSize);
            IPage<CxrEmployeeLeaveListVo> recruitStatistics = this.baseMapper.pagePeopleLeave(query.build(), bo);
            long total = splitTotal;
            pageCount = total % pageSize > 0 ? (total / pageSize + 1) : total / pageSize;
            // 数据组装
            records.addAll(recruitStatistics.getRecords());
            ExcelMqUtil.excelSendIncrementData(excelReportDTO.getUniqueMark(),
                Long.valueOf(recruitStatistics.getSize()), null);
        }
        employeeLeaveExportStatistics(records, response, excelReportDTO);
    }

    private void employeeLeaveExportStatistics(List<CxrEmployeeLeaveListVo> records, HttpServletResponse response, ExcelReportDTO excelReportDTO) {
        List<List> listList = records.stream().map(x -> {
            List<Object> list = new ArrayList<>();
            list.add(x.getCompanyName());
            list.add(x.getRootRegionName());
            list.add(x.getRegionName());
            list.add(x.getCxrSiteName());
            list.add(x.getAgentName());
            list.add(x.getEmployeeJobNumber());
            list.add(x.getAgentLevel());
            list.add(PeopleLeveaEnums.getType(x.getTypeId()).getDesc());
            list.add(x.getDelayFlag() ? "是" : "否");
            list.add(x.getStartTime());
            list.add(x.getEndTime());
            list.add(x.getDays());
            list.add("天");
            list.add(x.getCreateByName());
            list.add(x.getCreateTime());

            list.add(x.getApprovalUserName());
            list.add(x.getApprovalTime());

            list.add(LeaveApprovalStatus.getByValue(x.getApprovalStatus()).getDesc());
            return list;
        }).collect(toList());
        ExcelExportObj excelExportObj = new ExcelExportObj(excelReportDTO.getFileDir(), "请假导出.xlsx",
            CxrEmployeeLeaveExport.class);
        excelExportObj.writeManySheet(listList);
        excelExportObj.pathClose();
    }

    // 后端新增 -废弃，使用addPeopleleave
    @Override
    public void addPeople(CxrEmployeeLeaveBo cxrEmployeeLeaveBo) {
        verifyParam(cxrEmployeeLeaveBo);
        CxrEmployeeLeave cxrEmployeeLeave = BeanUtil.toBean(cxrEmployeeLeaveBo, CxrEmployeeLeave.class);
        // 判断请假时间是否重复
        if (cxrEmployeeLeaveBo.getMode().equals(0L)) {
            CxrEmployeeLeave cxrEmployeeLeave1 = new CxrEmployeeLeave();
            cxrEmployeeLeave1.setEndTime(cxrEmployeeLeaveBo.getEndTime());
            cxrEmployeeLeave1.setStartTime(cxrEmployeeLeaveBo.getStartTime());
            cxrEmployeeLeave1.setEmployeeId(cxrEmployeeLeaveBo.getEmployeeId());
            List<CxrEmployeeLeave> selectDays = baseMapper.selectDays(cxrEmployeeLeave1);
            if (CollUtil.isNotEmpty(selectDays)) {
                throw new ServiceException("请假时间重复");
            }
        }
        // 判断补假的时间是否重复
        if (cxrEmployeeLeaveBo.getMode().equals(1L)) {
            LambdaQueryWrapper<CxrEmployeeLeave> wrapper = Wrappers.lambdaQuery();
            wrapper
                .eq(CxrEmployeeLeave::getEmployeeId, cxrEmployeeLeave.getEmployeeId())
                .eq(CxrEmployeeLeave::getMode, cxrEmployeeLeave.getMode())
                .ge(CxrEmployeeLeave::getStartTime, cxrEmployeeLeave.getStartTime())
                .le(CxrEmployeeLeave::getEndTime, cxrEmployeeLeave.getEndTime())
                .eq(CxrEmployeeLeave::getDeleteStatus, DeleteStatus.not_deleted);
            Long count = baseMapper.selectCount(wrapper);
            if (count > 0) {
                throw new ServiceException("补假时间重复");
            }
        }
        cxrEmployeeLeave.setSysDeptId(LoginHelper.getLoginUser().getDeptId());
        cxrEmployeeLeave.setCreateByName(LoginHelper.getLoginUser().getUserName());

        if (ObjectUtil.isNull(cxrEmployeeLeaveBo.getMode())) {
            throw new ServiceException("请假方式必填");
        }
        if (PeopleLeaveTypeEnums.WRITTEN_LEAVE.getValue() == cxrEmployeeLeaveBo.getMode().intValue()) {
            cxrEmployeeLeave.setMode(cxrEmployeeLeaveBo.getMode());
            if (remoteCustomerDistributionDetailService.countEmAddress(cxrEmployeeLeaveBo.getEmployeeId())
                > 0) {
                throw new ServiceException("请将路线进行转拨才可以请假");
            }
        }

        if (PeopleLeaveTypeEnums.TAKE_LEAVE.getValue() == cxrEmployeeLeaveBo.getMode().intValue()) {
            cxrEmployeeLeave.setMode(cxrEmployeeLeaveBo.getMode());
        }

        if (ObjectUtil.isNull(cxrEmployeeLeaveBo.getMode())) {
            throw new ServiceException("请假类型必填");
        }
        if (PeopleLeveaEnums.PERSONAL_LEAVE.getValue() == cxrEmployeeLeaveBo.getTypeId().intValue()
            || PeopleLeveaEnums.SICK_LEAVE.getValue() == cxrEmployeeLeaveBo.getTypeId().intValue()
            || PeopleLeveaEnums.MARRIAGE_LEAVE.getValue() == cxrEmployeeLeaveBo.getTypeId().intValue()
            || PeopleLeveaEnums.MATERNITY_LEAVE.getValue() == cxrEmployeeLeaveBo.getTypeId().intValue()
            || PeopleLeveaEnums.PATERNITY_LEAVE.getValue() == cxrEmployeeLeaveBo.getTypeId().intValue()
            || PeopleLeveaEnums.FUNERAL_LEAVE.getValue() == cxrEmployeeLeaveBo.getTypeId().intValue()
            || PeopleLeveaEnums.OTHER_LEAVE.getValue() == cxrEmployeeLeaveBo.getTypeId().intValue()) {
            cxrEmployeeLeave.setTypeId(cxrEmployeeLeaveBo.getTypeId());
        }
        cxrEmployeeLeave.setRemark(cxrEmployeeLeaveBo.getRemark());
        cxrEmployeeLeave.setFileOneUrl(cxrEmployeeLeaveBo.getFileOneUrl());
        cxrEmployeeLeave.setEmployeeId(cxrEmployeeLeaveBo.getEmployeeId());
        cxrEmployeeLeave.setApplyTime(new Date());
        cxrEmployeeLeave.setStartTime(cxrEmployeeLeaveBo.getStartTime());
        cxrEmployeeLeave.setEndTime(cxrEmployeeLeaveBo.getEndTime());
        cxrEmployeeLeave.setCreateBy(LoginHelper.getLoginUser().getUserId());
        cxrEmployeeLeave.setApprovalStatus(LeaveApprovalStatus.CHECK_PEND.getValue());
        cxrEmployeeLeave.setCreateTime(new Date());
        cxrEmployeeLeave.setRevision(cxrEmployeeLeaveBo.getRevision() + 1);
        baseMapper.insert(cxrEmployeeLeave);
        // 添加到日志表中
        cxrEmployeeLeave.setUpdateBy(LoginHelper.getLoginUser().getUserId());
        cxrEmployeeLeave.setEndTime(new Date());
        CxrEmployeeLeaveLog cxrEmployeeLeaveLog =
            BeanUtil.copyProperties(cxrEmployeeLeave, CxrEmployeeLeaveLog.class);
        cxrEmployeelLeaveLogMapper.insertOrUpdate(cxrEmployeeLeaveLog);
    }

    // 后台依据id进行查询
    @Override
    public CxrEmployeeLeaveVo gethtId(Long id) {
        QueryWrapper<SysUser> wrapper = Wrappers.query();
        wrapper.eq("l.delete_status", DeleteStatus.not_deleted);
        wrapper.eq("l.id", id);
        CxrEmployeeLeaveVo cxrEmployeeLeaveVo = baseMapper.getLeaveVoById(wrapper);
        if (cxrEmployeeLeaveVo == null) {
            throw new ServiceException("数据已经不存在");
        }
        return cxrEmployeeLeaveVo;
    }

    @Override
    public CxrEmployeeLeaveCheckVo checkLeaveTimeConflict(CxrEmployeeLeaveCheckBo checkBo) {
        CxrEmployeeLeaveCheckVo result = new CxrEmployeeLeaveCheckVo();

        // 构建查询条件 - 需要查询所有冲突记录，不限制limit 1
        List<CxrEmployeeLeave> conflictLeaves = baseMapper.selectAllConflictDays(
            checkBo.getEmployeeId(),
            checkBo.getStartTime(),
            checkBo.getEndTime()
        );

        // 如果有排除ID，过滤掉该记录
        if (checkBo.getExcludeId() != null && CollUtil.isNotEmpty(conflictLeaves)) {
            conflictLeaves = conflictLeaves.stream()
                .filter(leave -> !leave.getId().equals(checkBo.getExcludeId()))
                .collect(Collectors.toList());
        }

        if (CollUtil.isEmpty(conflictLeaves)) {
            result.setHasConflict(false);
            result.setMessage("请假时间无冲突");
            result.setConflictsByStatus(new ArrayList<>());
        } else {
            result.setHasConflict(true);

            // 按审核状态分组处理冲突记录
            Map<String, List<CxrEmployeeLeave>> groupedByStatus = conflictLeaves.stream()
                .collect(Collectors.groupingBy(CxrEmployeeLeave::getApprovalStatus));

            List<CxrEmployeeLeaveCheckVo.ConflictByStatus> conflictsByStatus = new ArrayList<>();
            int totalConflictCount = 0;

            for (Map.Entry<String, List<CxrEmployeeLeave>> entry : groupedByStatus.entrySet()) {
                String status = entry.getKey();
                List<CxrEmployeeLeave> leavesInStatus = entry.getValue();

                // 计算该状态下所有记录的交叉日期
                Set<String> conflictDatesSet = new HashSet<>();
                for (CxrEmployeeLeave leave : leavesInStatus) {
                    List<String> intersectDates = calculateIntersectDates(
                        checkBo.getStartTime(),
                        checkBo.getEndTime(),
                        leave.getActStartTime(),
                        leave.getActEndTime()
                    );
                    conflictDatesSet.addAll(intersectDates);
                }

                // 转换为有序列表
                List<String> conflictDates = new ArrayList<>(conflictDatesSet);
                conflictDates.sort(String::compareTo);

                CxrEmployeeLeaveCheckVo.ConflictByStatus conflictByStatus = new CxrEmployeeLeaveCheckVo.ConflictByStatus();
                conflictByStatus.setApprovalStatus(status);
                conflictByStatus.setApprovalStatusDesc(LeaveApprovalStatus.getByValue(status).getDesc());
                conflictByStatus.setConflictDates(conflictDates);
                conflictByStatus.setConflictCount(leavesInStatus.size());

                conflictsByStatus.add(conflictByStatus);
                totalConflictCount += leavesInStatus.size();
            }

            result.setConflictsByStatus(conflictsByStatus);
            result.setMessage(StrUtil.format("存在{}条冲突的请假记录", totalConflictCount));
        }

        return result;
    }

    /**
     * 计算两个日期区间的交叉日期
     *
     * @param inputStart 输入的开始日期
     * @param inputEnd   输入的结束日期
     * @param leaveStart 请假记录的开始日期
     * @param leaveEnd   请假记录的结束日期
     * @return 交叉日期列表（格式：yyyy-MM-dd）
     */
    private List<String> calculateIntersectDates(Date inputStart, Date inputEnd, LocalDate leaveStart, LocalDate leaveEnd) {
        List<String> intersectDates = new ArrayList<>();

        // 如果请假记录的日期为空，返回空列表
        if (leaveStart == null || leaveEnd == null) {
            return intersectDates;
        }

        // 转换Date为LocalDate
        LocalDate inputStartLocal = inputStart.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate inputEndLocal = inputEnd.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        // 计算交叉区间的开始和结束日期
        LocalDate intersectStart = inputStartLocal.isAfter(leaveStart) ? inputStartLocal : leaveStart;
        LocalDate intersectEnd = inputEndLocal.isBefore(leaveEnd) ? inputEndLocal : leaveEnd;

        // 如果有交叉，生成交叉日期列表
        if (!intersectStart.isAfter(intersectEnd)) {
            LocalDate current = intersectStart;
            while (!current.isAfter(intersectEnd)) {
                intersectDates.add(current.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                current = current.plusDays(1);
            }
        }

        return intersectDates;
    }


    @Override
    public List<CxrRegionManagerVo> getRegionManagerByAgentId(Long agentId) {
        if (agentId == null) {
            throw new ServiceException("代理ID不能为空");
        }
        // 1. 根据代理ID查询员工信息，获取站点ID
        CxrEmployee agent = cxrEmployeeMapper.selectById(agentId);
        if (agent == null) {
            throw new ServiceException("未找到该代理信息");
        }
        CxrSite cxrSite = cxrSiteMapper.selectById(agent.getCxrSiteId());

        List<String> list = new ArrayList<>();
        list.add(PostType.REGION_MANAGER.getValue());
        String value = JSONUtil.toJsonStr(list);
        //查询所有 职位是经理   ,  拓展经理的职位
        List<CxrEmployeePost> cxrEmployeePost =
            cxrEmployeePostMapper.selectList(new LambdaQueryWrapper<CxrEmployeePost>()
                .eq(CxrEmployeePost::getChargeStatus, ChargeStatus.IN_PROGRESS.getValue())
                .eq(CxrEmployeePost::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .eq(CxrEmployeePost::getCxrRegionId, cxrSite.getCxrRootRegionId())
                .apply("JSON_OVERLAPS(JSON_EXTRACT(cxr_post_id,'$.value'),{0})", value)
            );
        if (CollectionUtil.isEmpty(cxrEmployeePost)) {
            return new ArrayList<>();
        }
        List<Long> longList = cxrEmployeePost.stream().map(CxrEmployeePost::getCxrEmployeeId).collect(toList());
        List<CxrEmployee> cxrEmployees = cxrEmployeeMapper.selectBatchIds(longList);
        List<CxrRegionManagerVo> cxrEmployees1 = new ArrayList<>();
        for (CxrEmployee cxrEmployee : cxrEmployees) {
            // 6. 组装返回结果
            CxrRegionManagerVo result = new CxrRegionManagerVo();
            result.setManagerId(cxrEmployee.getId());
            result.setManagerName(cxrEmployee.getName());
            result.setManagerJobNumber(cxrEmployee.getJobNumber());
            cxrEmployees1.add(result);
        }

        return cxrEmployees1;
    }

    @Override
    public List<CxrEmployeeLeaveVo> getCancelLeavesByLeaveId(Long leaveId) {
        if (leaveId == null) {
            throw new ServiceException("请假单ID不能为空");
        }
        // 构建查询条件
        LambdaQueryWrapper<CxrEmployeeLeave> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CxrEmployeeLeave::getLeaveId, leaveId)  // 关联的请假单ID
            .eq(CxrEmployeeLeave::getTypeId, PeopleLeveaEnums.CANCEL_LEAVE.getValue())  // 销假类型
            .eq(CxrEmployeeLeave::getApprovalStatus, LeaveApprovalStatus.APPROV.getValue())
            .eq(CxrEmployeeLeave::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())  // 未删除
            .orderByAsc(CxrEmployeeLeave::getCreateTime);  // 按创建时间倒序

        // 查询销假记录
        List<CxrEmployeeLeaveVo> cancelLeaves = baseMapper.selectVoList(queryWrapper, CxrEmployeeLeaveVo.class);
        return cancelLeaves;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditLeave(CxrEmployeeLeaveAuditBo auditBo) {
        if (auditBo.getId() == null) {
            throw new ServiceException("请假记录ID不能为空");
        }
        if (StringUtils.isBlank(auditBo.getApprovalStatus())) {
            throw new ServiceException("审批状态不能为空");
        }

        // 1. 查询请假记录
        CxrEmployeeLeave leave = baseMapper.selectById(auditBo.getId());
        if (leave == null) {
            throw new ServiceException("请假记录不存在");
        }
        if (leave.getDeleteStatus().equals(DeleteStatus.DELETED.getValue())) {
            throw new ServiceException("该条数据已被撤销，不需要审核！");
        }

        // 2. 检查请假记录状态
        if (!LeaveApprovalStatus.CHECK_PEND.getValue().equals(leave.getApprovalStatus())) {
            throw new ServiceException("该请假记录不是待审核状态，请刷新页面重试");
        }
        //只能销售代理所在 区域经理审核.可能调岗，要实时查
        Long employeeId = leave.getEmployeeId();
        CxrEmployee cxrEmployee = cxrEmployeeMapper.selectById(employeeId);
        Long cxrSiteId = cxrEmployee.getCxrSiteId();
        CxrSite cxrSite = cxrSiteMapper.selectById(cxrSiteId);
        List<CxrEmployeePost> cxrEmployeePosts =
            cxrEmployeePostMapper.selectList(new LambdaQueryWrapper<CxrEmployeePost>()
                .eq(CxrEmployeePost::getCxrRegionId, cxrSite.getCxrRootRegionId())
                .eq(CxrEmployeePost::getChargeStatus, ChargeStatus.IN_PROGRESS.getValue())
                .eq(CxrEmployeePost::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .eq(CxrEmployeePost::getCxrEmployeeId, LoginUtil.getLoginUser().getUserId())
                .apply("JSON_OVERLAPS(JSON_EXTRACT(cxr_post_id,'$.value'),{0})", PostType.REGION_MANAGER.getValue())
            );
        // 检查审核人是否为区域经理
        if (cxrEmployeePosts.isEmpty() && LoginTypeUtil.getTerminalType() == TerminalTypeEnums.disribution.getValue()) {
            throw new ServiceException("只有该销售代理所在区域经理才能审核该请假申请");
        }

        // 4. 更新请假记录状态
        CxrEmployeeLeave updateLeave = new CxrEmployeeLeave();
        updateLeave.setId(auditBo.getId());
        updateLeave.setApprovalStatus(auditBo.getApprovalStatus());
        updateLeave.setApprovalNotes(auditBo.getApprovalNotes());
        updateLeave.setApprovalUserId(LoginUtil.getLoginUser().getUserId());
        updateLeave.setApprovalTime(new Date());
        updateLeave.setUpdateTime(new Date());
        updateLeave.setUpdateBy(LoginUtil.getLoginUser().getUserId());
        updateLeave.setApprovalUserName(LoginUtil.getLoginUser().getUserName());

        // 6. 更新请假记录
        int rows = baseMapper.updateById(updateLeave);
        if (rows <= 0) {
            throw new ServiceException("审核失败，请重试");
        }
        Long id = updateLeave.getId();
        Long originaLeaveId = updateOriginalLeave(auditBo, leave);
        if (originaLeaveId != null) {
            id = originaLeaveId;
        }

        // 7. 记录审核日志
        CxrEmployeeLeaveLog cxrEmployeeLeaveLog =
            BeanUtil.copyProperties(updateLeave, CxrEmployeeLeaveLog.class);
        cxrEmployeelLeaveLogMapper.insertOrUpdate(cxrEmployeeLeaveLog);
        mqUtil.sendDelaySyncMessageTransaction(SaleAccessCycleConstant.SALE_ACCESS_CYCLE_TOPIC,
            SaleAccessCycleConstant.EMP_LEAVE_CHANGE_TAG,
            JSONUtil.toJsonStr(EmpLeaveChangeDTO.builder().leaveId(id).build()), 5);

    }

    private Long updateOriginalLeave(CxrEmployeeLeaveAuditBo auditBo, CxrEmployeeLeave leave) {
        Long originaLeaveId = null;
        // 5. 如果是销假类型，且审核通过，则更新原请假记录的实际请假时间
        if (PeopleLeveaEnums.CANCEL_LEAVE.getValue() == leave.getTypeId() &&
            LeaveApprovalStatus.APPROV.getValue().equals(auditBo.getApprovalStatus()) &&
            leave.getLeaveId() != null) {

            // 查询原请假记录
            CxrEmployeeLeave originalLeave = baseMapper.selectById(leave.getLeaveId());
            if (originalLeave != null) {
                originaLeaveId = originalLeave.getId();
                // 销假的开始时间作为实际请假结束时间
                LocalDate actEndTime = leave.getStartTime().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate().minusDays(1);

                int days = (int) DateUtil.between(originalLeave.getStartTime(),
                    Date.from(actEndTime.atStartOfDay(ZoneId.systemDefault()).toInstant()), DateUnit.DAY, false) + 1;
                // 更新原请假记录的实际请假时间
                CxrEmployeeLeave updateOriginalLeave = new CxrEmployeeLeave();
                updateOriginalLeave.setId(originalLeave.getId());
                updateOriginalLeave.setActEndTime(actEndTime);
                updateOriginalLeave.setUpdateTime(new Date());
                updateOriginalLeave.setUpdateBy(LoginUtil.getLoginUser().getUserId());
                updateOriginalLeave.setActDays(days);
                int updateRows = baseMapper.updateById(updateOriginalLeave);
                if (updateRows <= 0) {
                    throw new ServiceException("更新原请假记录失败");
                }

                log.info("销假审核通过，已更新原请假记录实际时间 - 原请假ID: {}, 实际开始时间: {}, 实际结束时间: {}",
                    originalLeave.getId(), actEndTime);
            }
        }
        return originaLeaveId;
    }

    @Override
    public List<CxrEmployeeLeaveVo> getPendingCancelLeave(Long id, Long srcLeaveId) {
        if (srcLeaveId == null) {
            throw new ServiceException("请假记录ID不能为空");
        }

        // 构建查询条件：查询指定请假记录的待审核销假记录
        LambdaQueryWrapper<CxrEmployeeLeave> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CxrEmployeeLeave::getLeaveId, srcLeaveId)  // 关联的原请假记录ID
            .eq(CxrEmployeeLeave::getTypeId, (long) PeopleLeveaEnums.CANCEL_LEAVE.getValue())  // 销假类型
            .eq(CxrEmployeeLeave::getApprovalStatus, LeaveApprovalStatus.CHECK_PEND.getValue())  // 待审核状态
            .eq(CxrEmployeeLeave::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())  // 未删除
            .orderByDesc(CxrEmployeeLeave::getCreateTime);  // 按创建时间倒序

        // 查询待审核的销假记录
        List<CxrEmployeeLeaveVo> pendingCancelLeaves = baseMapper.selectVoList(queryWrapper, CxrEmployeeLeaveVo.class);
        // 如果有排除ID，过滤掉该记录
        if (id != null && CollUtil.isNotEmpty(pendingCancelLeaves)) {
            pendingCancelLeaves = pendingCancelLeaves.stream()
                .filter(leave -> !leave.getId().equals(id))
                .collect(Collectors.toList());
        }
        return pendingCancelLeaves != null ? pendingCancelLeaves : new ArrayList<>();
    }

    @Override
    public LeaveRemindVo getLeaveRemind(Long employeeId) {
        if (ObjectUtil.isNotEmpty(cxrLeaveRemindConfig.getSendCount())) {

            LocalDate now = LocalDate.now();
            LocalTime nowTime = LocalTime.now();
            //
            String am = cxrLeaveRemindConfig.getAm();
            String pm = cxrLeaveRemindConfig.getPm();

            try {
                LocalTime amTime = LocalTime.parse(am);
                LocalTime pmTime = LocalTime.parse(pm);

                if (nowTime.isAfter(amTime) && nowTime.isBefore(pmTime)) {
                    log.info("当前时间为上午，午请假提醒...");
                    // 调用提醒方法
                    String format = StrUtil.format("{}:{}:{}", now, am,
                        employeeId + "");
                    String md5Value = MD5.create().digestHex(format);
                    String redisKey = StrUtil.format("cxr:leave:leaveRemind:{}", md5Value);
                    Object redisValue = RedisTemplateUtils.getValue(redisKey);
                    if (ObjectUtil.isNull(redisValue)) {
                        RedisTemplateUtils.setValueTimeout(redisKey, 1, 60 * 60 * 12);
                        return remindEmployeeLeave(employeeId);
                    }
                } else if (nowTime.isAfter(pmTime)) {
                    // 当前时间在下午范围内
                    log.info("当前时间为下午，下午请假提醒...");
                    String format = StrUtil.format("{}:{}:{}", now, pm,
                        employeeId + "");
                    String md5Value = MD5.create().digestHex(format);
                    String redisKey = StrUtil.format("cxr:leave:leaveRemind:{}", md5Value);
                    Object redisValue = RedisTemplateUtils.getValue(redisKey);
                    if (ObjectUtil.isNull(redisValue)) {
                        RedisTemplateUtils.setValueTimeout(redisKey, 1, 60 * 60 * 12);
                        return remindEmployeeLeave(employeeId);
                    }
                } else {
                    log.info("当前不在设定的提醒时间段内");
                }
            } catch (Exception e) {
                log.error("解析提醒时间失败，请检查配置格式是否为HH:mm", e);
            }
        }
        return null;
    }

    private LeaveRemindVo remindEmployeeLeave(Long employeeId) {
        //待处理 且 需要考核延期”的请假数据
        LeaveRemindVo vo = new LeaveRemindVo();
        LoginEmployee loginUser = StaffLoginHelper.getLoginUser();
        List<CxrEmployeePost> cxrEmployeePosts = cxrEmployeePostMapper.selectList(
            new LambdaQueryWrapper<CxrEmployeePost>()
                .eq(CxrEmployeePost::getCxrEmployeeId, employeeId)
                .eq(CxrEmployeePost::getChargeStatus, ChargeStatus.IN_PROGRESS.getValue())
                .eq(CxrEmployeePost::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .apply("JSON_OVERLAPS(JSON_EXTRACT(cxr_post_id,'$.value'),{0})", PostType.DIRECTOR.getValue()));
        Set<Long> querySiteIds = new HashSet<>();
        if (ObjectUtil.isNotNull(cxrEmployeePosts)) {
            Set<Long> siteIds = cxrEmployeePosts.stream().map(x -> x.getCxrSiteId()).collect(Collectors.toSet());
            querySiteIds.addAll(siteIds);
        }

        List<CxrEmployeePost> managerPost =
            cxrEmployeePostMapper.selectList(new LambdaQueryWrapper<CxrEmployeePost>()
                .eq(CxrEmployeePost::getCxrEmployeeId, employeeId)
                .eq(CxrEmployeePost::getChargeStatus, ChargeStatus.IN_PROGRESS.getValue())
                .eq(CxrEmployeePost::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .apply("JSON_OVERLAPS(JSON_EXTRACT(cxr_post_id,'$.value'),{0})", PostType.REGION_MANAGER.getValue()));
        if (CollectionUtil.isNotEmpty(managerPost)) {
            //取区域id
            List<Long> regionIds = managerPost.stream().map(CxrEmployeePost::getCxrRegionId)
                .collect(Collectors.toList());
            //获取站点id
            List<CxrSite> siteList = cxrSiteMapper.selectList(new LambdaQueryWrapper<CxrSite>()
                .eq(CxrSite::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .in(CxrSite::getCxrRegionId, regionIds));

            Set<Long> siteIds = siteList.stream().map(CxrSite::getId).collect(Collectors.toSet());
            querySiteIds.addAll(siteIds);
        }

        // 经理进来获取站点id
        List<CxrEmployeeLeave> employeeLeaves = baseMapper.selectList(new LambdaQueryWrapper<CxrEmployeeLeave>()
            .select(CxrEmployeeLeave::getId, CxrEmployeeLeave::getEmployeeId, CxrEmployeeLeave::getCxrSiteId, CxrEmployeeLeave::getCreateTime)
            .eq(CxrEmployeeLeave::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            .eq(CxrEmployeeLeave::getDelayFlag, 1)
            .and(Wrapper -> Wrapper.
                eq(CxrEmployeeLeave::getEmployeeId, employeeId)
                .or()
                .in(CollUtil.isNotEmpty(querySiteIds), CxrEmployeeLeave::getCxrSiteId, querySiteIds)
            )
            .orderByAsc(CxrEmployeeLeave::getCreateTime)
        );
        if (CollUtil.isNotEmpty(employeeLeaves)) {
            Integer maxPostId = loginUser.getCxrPostIds().stream().collect(Collectors.toList())
                .stream().mapToInt(x -> Integer.valueOf(x.getValue())).max().getAsInt();
            vo.setMaxPostId(maxPostId);
            vo.setLeaveDetailId(employeeLeaves.get(0).getId());
            vo.setCount(employeeLeaves.size());
            return vo;
        }
        return null;
    }


    @Override
    public List<CxrSite> getSite(EmployeeQueryDTO employeeQueryDTO) {
        ArrayList<PostType> postTypes = new ArrayList<>();
        postTypes.add(PostType.REGION_MANAGER);
        postTypes.add(PostType.DIRECTOR);
        List<CxrEmployeePost> cxrEmployeePosts = remoteEmployeePostService.cxrEmployeePostByType(
            LoginUtil.getLoginUser().getUserId(), postTypes);
        List<Long> siteIdList =
            cxrEmployeePosts.stream().filter(x -> x.getCxrPostId().getValue().equals(PostType.DIRECTOR.getValue())).map(CxrEmployeePost::getCxrSiteId).distinct()
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(siteIdList)) siteIdList.add(-1L);
        List<Long> regionIdList =
            cxrEmployeePosts.stream().filter(x -> x.getCxrPostId().getValue().equals(PostType.REGION_MANAGER.getValue())).map(CxrEmployeePost::getCxrRegionId).distinct()
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(regionIdList)) regionIdList.add(-1L);
        if (CollUtil.isEmpty(siteIdList) && CollUtil.isEmpty(regionIdList)) return Collections.emptyList();
        List<CxrSite> cxrSites = cxrSiteMapper.selectList(new LambdaQueryWrapper<CxrSite>()
            .and(w -> w
                .in(CxrSite::getId, siteIdList)
                .or()
                .in(CxrSite::getCxrRootRegionId, regionIdList)
            )
            .eq(CxrSite::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            .like(StringUtils.isNotBlank(employeeQueryDTO.getSiteName()), CxrSite::getName, employeeQueryDTO.getSiteName())
            .like(StringUtils.isNotBlank(employeeQueryDTO.getRegionName()), CxrSite::getCxrRootRegionName,
                employeeQueryDTO.getRegionName())
            .orderByDesc(CxrSite::getId)
        );

        return cxrSites;
    }

    @Override
    public TableDataInfo<EmployeeInfoVO> getEmployeeInfo(EmployeeQueryDTO employeeQueryDTO) {
        List<CxrSite> site = this.getSite(employeeQueryDTO);
        if (CollUtil.isEmpty(site)) {
            return new TableDataInfo();
        }
        List<Long> longList = site.stream().map(CxrSite::getId).collect(Collectors.toList());
        Page<CxrEmployee> page = new Page<>(employeeQueryDTO.getPageNum(), employeeQueryDTO.getPageSize());
        Page<CxrEmployee> cxrEmployeePage = cxrEmployeeMapper.selectPage(page, new LambdaQueryWrapper<CxrEmployee>()
            .in(CxrEmployee::getCxrSiteId, longList)
            .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            .eq(CxrEmployee::getOccupationStatus, OccupationStatus.INDUCTION.getValue())
            .eq(CxrEmployee::getAccountFlag, 1)
            .and(StrUtil.isNotBlank(employeeQueryDTO.getSearch()),
                wrapper -> wrapper
                    .like(CxrEmployee::getName, employeeQueryDTO.getSearch())
                    .or()
                    .like(CxrEmployee::getJobNumber, employeeQueryDTO.getSearch())
                    .or()
                    .like(CxrEmployee::getPhone, employeeQueryDTO.getSearch())
            ).orderByDesc(CxrEmployee::getId)
        );
        TableDataInfo tableDataInfo = new TableDataInfo(cxrEmployeePage.getRecords(), cxrEmployeePage.getTotal());

        tableDataInfo.setPageSize(Long.valueOf(employeeQueryDTO.getPageSize()));
        tableDataInfo.setPageNum(Long.valueOf(employeeQueryDTO.getPageNum()));
        return tableDataInfo;
    }
}
