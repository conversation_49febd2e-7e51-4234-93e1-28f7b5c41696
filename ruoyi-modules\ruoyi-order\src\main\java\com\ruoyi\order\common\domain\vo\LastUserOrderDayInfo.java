package com.ruoyi.order.common.domain.vo;

import com.cxrry.logger.api.annotation.LogTag;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class LastUserOrderDayInfo implements Serializable {

    private Integer id;

    @ApiModelProperty("单据")
    private String orderNo;

    @ApiModelProperty("下单时间")
    private Date orderDate;


    @LogTag(alias = "鲜奶订购数量")
    @ApiModelProperty(name = "鲜奶订购数量", notes = "")
    private Integer orderQuantity=0;
    /**
     * 鲜奶赠送数量
     */
    @LogTag(alias = "鲜奶赠送数量")
    @ApiModelProperty(name = "鲜奶赠送数量", notes = "")
    private Integer freshMilkGiveQuantity=0;

    @ApiModelProperty("订购金额")
    private BigDecimal amount=BigDecimal.ZERO;

    @ApiModelProperty(value = "常温奶赠送数量")
    private Integer longMilkGiveQuantity = 0;
}
