package com.ruoyi.business.base.dubbo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.business.base.api.domain.CxrCustomer;
import com.ruoyi.business.base.api.domain.CxrCustomerStockDetail;
import com.ruoyi.business.base.api.domain.CxrLongMilkStock;
import com.ruoyi.business.base.api.domain.CxrLongMilkStockDTO;
import com.ruoyi.business.base.api.dubbo.RemoteLongMilkStockService;
import com.ruoyi.business.base.api.enums.longMilk.LongMilkApplyStatus;
import com.ruoyi.business.base.api.enums.longMilk.LongMilkStatus;
import com.ruoyi.business.base.api.model.CxrLongMilkStockVO;
import com.ruoyi.business.base.customer.service.ICxrCustomerService;
import com.ruoyi.business.base.customerStockDetai.service.CxrCustomerStockDetailService;
import com.ruoyi.business.base.cxrLongMilkStock.domain.LongMilkStockTotal;
import com.ruoyi.business.base.cxrLongMilkStock.domain.vo.CxrLongMilkStockJOBVo;
import com.ruoyi.business.base.cxrLongMilkStock.mapper.CxrLongMilkStockMapper;
import com.ruoyi.business.base.cxrLongMilkStock.service.CxrLongMilkStockService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.PageTableDataInfo;
import com.ruoyi.common.core.enums.DeleteStatus;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.enums.TerminalTypeEnums;
import com.ruoyi.common.satoken.utils.helper.CustomerLoginHelper;
import com.ruoyi.message.api.RemoteMessageService;
import com.ruoyi.order.api.RemoteOrderService;
import io.seata.spring.annotation.GlobalTransactional;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

@Slf4j
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteLongMilkStockServiceImpl implements RemoteLongMilkStockService {

    private final CxrLongMilkStockService cxrLongMilkStockService;

    private final CxrLongMilkStockMapper cxrLongMilkStockMapper;

    @DubboReference
    private RemoteOrderService orderService;

    @DubboReference(timeout = 40000)
    private RemoteMessageService remoteMessageService;


    private final CxrCustomerStockDetailService cxrCustomerStockDetailService;

    private final ICxrCustomerService cxrCustomerService;


    @Override
    public PageTableDataInfo<CxrLongMilkStockVO> customerPage(Date startDate, Date endDate, String applyStatus,
        PageQuery pageQuery) {
        return cxrLongMilkStockService.customerPage(startDate, endDate, applyStatus, pageQuery);
    }


    @Override
    public CxrLongMilkStockVO selectOne(Long id) {
        CxrLongMilkStock cxrLongMilkStock = cxrLongMilkStockService
            .getOne(new LambdaQueryWrapper<CxrLongMilkStock>()
                .eq(CxrLongMilkStock::getId, id)
                .eq(CxrLongMilkStock::getDeleteStatus, DeleteStatus.not_deleted)
            );
        if (cxrLongMilkStock == null) {
            throw new ServiceException("没有这个库存记录");
        }

        CxrLongMilkStockVO cxrLongMilkStockVO = BeanUtil.copyProperties(cxrLongMilkStock, CxrLongMilkStockVO.class);

        Long cxrUserOrderId = cxrLongMilkStock.getCxrUserOrderId();
        String orderNo = orderService.queryOrderNoById(cxrUserOrderId);
        cxrLongMilkStockVO.setOrderNo(orderNo);

        return cxrLongMilkStockVO;
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public boolean updateOne(CxrLongMilkStockVO vo) {

        //更新用户库存
        List<CxrCustomerStockDetail> cxrCustomerStockDetailList = new ArrayList<>();
        CxrCustomerStockDetail customerStockDetail = new CxrCustomerStockDetail();
        customerStockDetail.setTerminalType(TerminalTypeEnums.manager.getValue());
        customerStockDetail.setLongMilkQuantity(-vo.getCurrentApplySum());
        customerStockDetail.setLongMilkGiveQuantity(-vo.getCurrentApplySum());
        customerStockDetail.setRemark(
            StrUtil.format("常温奶申领---扣减,时间为={},数量={}", LocalDateTime.now().toString(),
                vo.getCurrentApplySum()));
        cxrCustomerStockDetailList.add(customerStockDetail);
        CxrCustomer customer = cxrCustomerService.getById(vo.getCxrCustomerId());
        if (!cxrCustomerStockDetailService.addRecord(vo.getCxrCustomerId(), cxrCustomerStockDetailList,
            customer.getRevision())) {
            log.info(">>>>>>>>>>>>>>>>>>>常温奶申领-----更新用户库存和记录---失败");
            throw new ServiceException(">>>>>>>>>>>>>>>>>>>常温奶申领-----更新用户库存和记录---失败");
        }

        CxrLongMilkStock longMilkStock = cxrLongMilkStockService.getById(vo.getId());
        if (longMilkStock.getLockFlag()){
            throw new ServiceException("该订单常温奶已经锁定，不能申领");
        }

        CxrLongMilkStock cxrLongMilkStock = BeanUtil.copyProperties(vo, CxrLongMilkStock.class);
        Long revision = cxrLongMilkStock.getRevision();

        cxrLongMilkStock.setRevision(revision + 1);
        boolean update = cxrLongMilkStockService.update(cxrLongMilkStock, new LambdaUpdateWrapper<CxrLongMilkStock>()
            .eq(CxrLongMilkStock::getId, cxrLongMilkStock.getId()).eq(CxrLongMilkStock::getRevision, revision));
        if (!update) {
            log.info(">>>>>>>>>>>>>>>>>>>常温奶申领-----更新常温奶库存表---失败");
            throw new ServiceException(">>>>>>>>>>>>>>>>>>>常温奶申领-----更新常温奶库存表---失败");
        }
        return update;
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public boolean addOne(CxrLongMilkStockVO vo) {
        CxrLongMilkStock cxrLongMilkStock = BeanUtil.copyProperties(vo, CxrLongMilkStock.class);
        return cxrLongMilkStockService.save(cxrLongMilkStock);
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public boolean save(CxrLongMilkStockDTO dto) {

        CxrLongMilkStock cxrLongMilkStock = BeanUtil.copyProperties(dto, CxrLongMilkStock.class);

        return cxrLongMilkStockService.save(cxrLongMilkStock);
    }

    @Override
    public boolean save(CxrLongMilkStock stock) {

        return cxrLongMilkStockService.save(stock);
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public boolean saveBatch(List<CxrLongMilkStockDTO> dtos) {
        List<CxrLongMilkStock> stocks = BeanUtil.copyToList(dtos, CxrLongMilkStock.class);

        return cxrLongMilkStockService.saveBatch(stocks);
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public boolean updateReturnOrderLongMilkStock(CxrLongMilkStockDTO dto) {
        List<CxrLongMilkStock> cxrLongMilkStocks =
            cxrLongMilkStockMapper.selectList(new LambdaQueryWrapper<CxrLongMilkStock>()
                .eq(CxrLongMilkStock::getCxrCustomerId, dto.getCxrCustomerId())
                .eq(CxrLongMilkStock::getLongMilkStatus, LongMilkStatus.AVAILABLE.getValue())
                .gt(CxrLongMilkStock::getSurplusNum, 0)
                .orderByAsc(CxrLongMilkStock::getExpireTime)
            );
        //中间值
        Long orderId = dto.getOrderId();
        if (orderId != null){
            Collections.sort(cxrLongMilkStocks,(a, b) -> a.getCxrUserOrderId().equals(orderId) ? -1 : 0);
        }

        List<CxrLongMilkStock> stocks = new ArrayList<>();

        //剩余退订数
        int surplusReturnQty = dto.getSentNum();
        for (CxrLongMilkStock s : cxrLongMilkStocks) {

            //本次退订数
            int returnQty = s.getSurplusNum() > surplusReturnQty ? surplusReturnQty : s.getSurplusNum();

            //剩余退订数
            surplusReturnQty = surplusReturnQty - s.getSurplusNum();

            s.setUpdateBy(dto.getUpdateBy());
            s.setUpdateTime(dto.getUpdateTime());
            s.setUpdateByName(dto.getUpdateByName());
            s.setUpdateByType(dto.getUpdateByType());

            if (surplusReturnQty <= 0) {
                s.setSurplusNum(s.getSurplusNum() - returnQty);
                if (ObjectUtil.isEmpty(s.getUnregNum())) {
                    s.setUnregNum(returnQty);
                } else {
                    s.setUnregNum(returnQty + s.getUnregNum());
                }
                stocks.add(s);
                break;
            } else {
                if (ObjectUtil.isEmpty(s.getUnregNum())) {
                    s.setUnregNum(returnQty);
                } else {
                    s.setUnregNum(returnQty + s.getUnregNum());
                }
                s.setSurplusNum(0);
                s.setLongMilkStatus(LongMilkStatus.UNSUBSCRIBE.getValue());
                stocks.add(s);
            }

        }

        if (surplusReturnQty > 0) {
            throw new ServiceException("常温奶库存不足");
        }

        if (CollUtil.isNotEmpty(stocks)) {
            return cxrLongMilkStockMapper.updateByIdAndRevisionBatch(stocks);
        }

        return true;


    }

    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean returnOrderLongMilkAddAndSubStock(CxrLongMilkStockDTO dto) {

//        List<CxrLongMilkStock> cxrLongMilkStocks = cxrLongMilkStockMapper.selectList(new LambdaQueryWrapper<CxrLongMilkStock>()
//                .eq(CxrLongMilkStock::getCxrCustomerId, dto.getCxrCustomerId())
//                .eq(CxrLongMilkStock::getLongMilkStatus, LongMilkStatus.AVAILABLE.getValue())
//                .gt(CxrLongMilkStock::getSurplusNum, 0)
//                .orderByAsc(CxrLongMilkStock::getExpireTime)
//            );
//
//        List<CxrLongMilkStock> stocks = new ArrayList<>();
//        //退订单  数量
//        int qutry = dto.getSentNum();
//        for (CxrLongMilkStock s : cxrLongMilkStocks) {
//
//            //剩余数量  = 剩余数量 - 退订数量
//            int surplusNum = s.getSurplusNum() - qutry;
//
//            if (surplusNum >= 0) {
//                //剩余数量
//                s.setSurplusNum(surplusNum);
//                //退订数量
//                s.setUnregNum(ObjectUtil.isEmpty(s.getUnregNum()) ? qutry : s.getUnregNum() + qutry);
//                stocks.add(s);
//                break;
//            } else {
//
//                //剩余数量不够减
//                //退订数量 = 退订数量 + 剩余数量
//                s.setUnregNum(ObjectUtil.isEmpty(s.getUnregNum()) ?s.getSurplusNum() : (s.getSurplusNum() + s.getUnregNum()));
//                s.setSurplusNum(0);
//                s.setLongMilkStatus(LongMilkStatus.UNSUBSCRIBE.getValue());
//                qutry = qutry - s.getSurplusNum();
//                stocks.add(s);
//            }
//
//            s.setUpdateBy(dto.getUpdateBy());
//            s.setUpdateTime(dto.getUpdateTime());
//            s.setUpdateByName(dto.getUpdateByName());
//            s.setUpdateByType(dto.getUpdateByType());
//        }
//
//        if(CollUtil.isNotEmpty(stocks)){
//            return cxrLongMilkStockMapper.updateByIdAndRevisionBatch(stocks);
//        }

        int qutry = dto.getUnregNum();
        if (qutry > 0) {
            dto.setSentNum(qutry);
            return updateReturnOrderLongMilkStock(dto);
        } else {
            //查询退订数量 > 0 的记录加库存
            List<CxrLongMilkStock> cxrLongMilkStocks = cxrLongMilkStockMapper.selectList(
                new LambdaQueryWrapper<CxrLongMilkStock>()
                    .eq(CxrLongMilkStock::getCxrCustomerId, dto.getCxrCustomerId())
                    .gt(CxrLongMilkStock::getUnregNum, 0)
                    .last(" ORDER BY long_milk_status = '2',long_milk_status = '3',long_milk_status = '1'")
            );

            LocalDate now = LocalDate.now();
            List<CxrLongMilkStock> addStocks = new ArrayList<>();

            //剩余要加库存
            int addQty = 0 - qutry;
            for (CxrLongMilkStock s : cxrLongMilkStocks) {

                LocalDate expireTime = DateUtils.getLocalDateFromDate(s.getExpireTime());

                //本次加库存数量
                int currentAddQty = s.getUnregNum() > addQty ? addQty : s.getUnregNum();

                addQty = addQty - s.getUnregNum();

                if (addQty <= 0) {
                    //剩余数量
                    s.setSurplusNum(s.getSurplusNum() + currentAddQty);
                    //退订数量
                    s.setUnregNum(s.getUnregNum() - currentAddQty);

                    if (LongMilkStatus.UNSUBSCRIBE.getValue().equals(s.getLongMilkStatus())) {
                        s.setLongMilkStatus(now.isAfter(expireTime) ? LongMilkStatus.EXPIRED.getValue()
                            : LongMilkStatus.AVAILABLE.getMessage());
                    }
                    addStocks.add(s);
                    break;
                } else {
                    //剩余数量
                    s.setSurplusNum(s.getSurplusNum() + s.getUnregNum());
                    if (LongMilkStatus.UNSUBSCRIBE.getValue().equals(s.getLongMilkStatus())) {
                        s.setLongMilkStatus(now.isAfter(expireTime) ? LongMilkStatus.EXPIRED.getValue()
                            : LongMilkStatus.AVAILABLE.getMessage());
                    }
                    //退订数量0
                    s.setUnregNum(0);
                }

                s.setUpdateBy(dto.getUpdateBy());
                s.setUpdateTime(dto.getUpdateTime());
                s.setUpdateByName(dto.getUpdateByName());
                s.setUpdateByType(dto.getUpdateByType());

            }

            if (addQty > 0) {
                throw new ServiceException("常温奶库存不足");
            }

            if (CollUtil.isNotEmpty(addStocks)) {
                log.info("修改常温奶库存：{}", JSONObject.toJSONString(addStocks));
                return cxrLongMilkStockMapper.updateByIdAndRevisionBatch(addStocks);
            }
        }
        return true;
    }

    public static void main(String[] args) {

    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public void updateLongMlikStockPast() {
        LocalDate now = LocalDate.now();
        cxrLongMilkStockMapper.updateLongMlikStockPast(now, LongMilkStatus.AVAILABLE.getValue(),
            LongMilkStatus.EXPIRED.getValue());
    }

    @Override
    public Object getCustomerGiveTotal() {
        LongMilkStockTotal customerGiveTotal =
            cxrLongMilkStockMapper.getCustomerGiveTotal(CustomerLoginHelper.getLoginUser().getUserId());

        if (ObjectUtil.isNotEmpty(customerGiveTotal.getCountNum()) && customerGiveTotal.getCountNum() > 0) {
            return 3;
        }

        if (ObjectUtil.isEmpty(customerGiveTotal)) {
            return 0;
        }

        Integer num = customerGiveTotal.getSurplusNum();
//            +customerGiveTotal.getUnregNum() - customerGiveTotal.getApplyNum()-customerGiveTotal.getOverdueNum();
        if (num == 0) {
            return 0;
        }

        if (num < 0) {
            return 0;
        }

        if (num >= 400) {
            return 3;
        } else {
            return 2;
        }
    }

    @Override
    public boolean remoteLongMilkStockService() {
        List<CxrLongMilkStockJOBVo> milkStockJOBVos = cxrLongMilkStockMapper.queryLongMilkStock();

        if (CollectionUtil.isNotEmpty(milkStockJOBVos) && milkStockJOBVos.size() > 0) {
            for (CxrLongMilkStockJOBVo stockJOBVo : milkStockJOBVos) {
                Integer num = stockJOBVo.getSurplusNum();
//                CxrLongMilkStockJOBTotalVo customerGiveTotal = cxrLongMilkStockMapper.queryLongMilkStockTotal(stockJOBVo.getCustomerId());
//                Integer num = customerGiveTotal.getSurplusNum();
//                    customerGiveTotal.getSentNum()- customerGiveTotal.getApplyNum()-customerGiveTotal.getOverdueNum()
//                    ;
                //            +customerGiveTotal.getUnregNum()
                if (num > 0) {
                    Date date = new Date();
                    int days = (int) ((stockJOBVo.getExpireTime().getTime() - date.getTime()) / (1000 * 3600 * 24));
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

                    Instant instant = stockJOBVo.getExpireTime().toInstant();
                    ZoneId zone = ZoneId.systemDefault();
                    LocalDateTime dateTime = LocalDateTime.ofInstant(instant, zone);
                    remoteMessageService.sumWxGiftSend(stockJOBVo.getOpenId(), stockJOBVo.getCustomerName(),
                        LocalDate.now(), num, days,
                        dateTime.toLocalDate().minusDays(1)
                    );
                }
            }
        }
        return true;
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public int updateLongMilk(Long id, int num, int count) {
        int update = cxrLongMilkStockMapper.update(null, new LambdaUpdateWrapper<CxrLongMilkStock>()
            .eq(CxrLongMilkStock::getCxrUserOrderId, id)
            .eq(CxrLongMilkStock::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            .set(CxrLongMilkStock::getSurplusNum, num)
            .set(CxrLongMilkStock::getSentNum, count)
        );
        if (update < 0) {
            throw new ServiceException("修改失败!");
        }
        return update;
    }

    @Override
    public CxrLongMilkStock queryByOrderId(Long id) {
        CxrLongMilkStock cxrLongMilkStock = cxrLongMilkStockMapper.selectOne(new LambdaQueryWrapper<CxrLongMilkStock>()
            .eq(CxrLongMilkStock::getCxrUserOrderId, id)
            .eq(CxrLongMilkStock::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));
        return cxrLongMilkStock;
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public void delLongMilk(Long id) {
        CxrLongMilkStock cxrLongMilkStock = cxrLongMilkStockMapper.selectOne(
            new LambdaQueryWrapper<CxrLongMilkStock>().eq(CxrLongMilkStock::getCxrUserOrderId, id));
        if (cxrLongMilkStock == null) {
            return;
        }
        if (cxrLongMilkStock.getApplyNum() != null && cxrLongMilkStock.getApplyNum() > 0) {
            throw new ServiceException("常温奶已经申领，不允许修改常温奶数量");
        }
        cxrLongMilkStockMapper.delete(
            new LambdaQueryWrapper<CxrLongMilkStock>().eq(CxrLongMilkStock::getCxrUserOrderId, id));
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public void updateLongMilks(Long orderId,Integer longMilkGiveQuantity){
        CxrLongMilkStock longMilkStock = queryByOrderId(orderId);
        if (ObjectUtil.isNotEmpty(longMilkStock)) {

            Integer sentNum = longMilkStock.getSentNum();
            //数量相等 说明没有修改
            if (sentNum.equals(longMilkGiveQuantity)){
                return;
            }

            String longMilkStatus = longMilkStock.getLongMilkStatus();
            //已经过期不能修改
            if (LongMilkStatus.EXPIRED.getValue().equals(longMilkStatus)){
                throw new ServiceException("常温奶已经到期不能修改");
            }

            Long revision = longMilkStock.getRevision();
            String applyStatus = longMilkStock.getApplyStatus();
            LambdaUpdateWrapper<CxrLongMilkStock> updateWrapper = new LambdaUpdateWrapper<CxrLongMilkStock>()
                .eq(CxrLongMilkStock::getId, longMilkStock.getId())
                .eq(CxrLongMilkStock::getRevision, revision)
                .set(CxrLongMilkStock::getRevision, revision + 1)
                .set(CxrLongMilkStock::getSentNum,longMilkGiveQuantity);


            Integer applyNum = longMilkStock.getApplyNum();
            Integer overdueNum = longMilkStock.getOverdueNum();
            Integer unregNum = longMilkStock.getUnregNum();

            //修改后的剩余数量
            int surplusNum = longMilkGiveQuantity - applyNum - overdueNum - unregNum;
            updateWrapper.set(CxrLongMilkStock::getSurplusNum,surplusNum);

            if (surplusNum > 0){
                //剩余库存大于 0 ，则状态修改为部分申领
                if (LongMilkApplyStatus.DONE.getValue().equals(applyStatus)){
                    updateWrapper.set(CxrLongMilkStock::getApplyStatus,LongMilkApplyStatus.PARTIAL.getValue());
                }
            }else if (surplusNum == 0){
                //剩余库存等于 0 ，则状态修改为全部申领
                if (LongMilkApplyStatus.INITIAL.getValue().equals(applyStatus) || LongMilkApplyStatus.PARTIAL.getValue().equals(applyStatus)){
                    updateWrapper.set(CxrLongMilkStock::getApplyStatus,LongMilkApplyStatus.DONE.getValue());
                }
            }else {
                throw new ServiceException("常温奶库存不足，数量不能小于" + (applyNum + overdueNum + unregNum));
            }
            cxrLongMilkStockMapper.update(null, updateWrapper);
        }
    }


    @Override
    public boolean updateLongMilkStockByTransfOrder(Long id, Long revision, Integer conversionQuantity, boolean flag) {
        return cxrLongMilkStockMapper.updateLongMilkStockByTransfOrder(id, revision, conversionQuantity, flag);
    }

    @Override
    public boolean removerLongMilkStockByTransferOrder(Long id, Long revision) {

        return cxrLongMilkStockMapper.updateDeleteLongMilkStockByTransfOrder(id, revision);
    }

    @Override
    public Boolean updateById(CxrLongMilkStock cxrLongMilkStock, Long cxrCustomerId) {
        return cxrLongMilkStockService.lambdaUpdate().set(CxrLongMilkStock::getCxrCustomerId,cxrCustomerId).eq(CxrLongMilkStock::getId,cxrLongMilkStock.getId()).update();
    }
}
