package com.ruoyi.business.base.excelReportFormsExport.controller;

import com.ruoyi.business.api.domain.bo.CxrReportFormsExportBO;
import com.ruoyi.business.api.domain.vo.CxrReportFormsExportVo;
import com.ruoyi.business.base.excelReportFormsExport.service.ICxrReportFormsExportService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.mybatis.core.page.PageTableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * 文件下载(CxrReportFormsExport)表控制层
 *
 * <AUTHOR>
 * @since 2023-06-27 16:54:37
 */

@RestController
@Api(tags = "excel文件下载(CxrReportFormsExport)")
@RequestMapping("cxrReportFormsExport")
public class CxrReportFormsExportController {

    /**
     * 服务对象
     */
    @Resource
    private ICxrReportFormsExportService cxrReportFormsExportService;

    @ApiOperation("excel 分页有查询列表")
    @PostMapping("/page")
    public R<PageTableDataInfo<CxrReportFormsExportVo>> page(
        @RequestBody CxrReportFormsExportBO cxrReportFormsExportBO) {
        return R.ok(cxrReportFormsExportService.queryPage(cxrReportFormsExportBO));
    }

    @ApiOperation("excel 分页有查询列表")
    @PostMapping("/downLoad/{id}")
    public void page(@PathVariable("id") Long id, HttpServletResponse response) throws Exception {
        cxrReportFormsExportService.downLoad(id, response);
    }

    @ApiOperation("excel 分页固定数据列表")
    @PostMapping("/reportFormsExporPage")
    public R<PageTableDataInfo<CxrReportFormsExportVo>> reportFormsExporPage(
        @RequestBody CxrReportFormsExportBO cxrReportFormsExportBO) {
        return R.ok(cxrReportFormsExportService.reportFormsExporPage(cxrReportFormsExportBO));
    }


}

