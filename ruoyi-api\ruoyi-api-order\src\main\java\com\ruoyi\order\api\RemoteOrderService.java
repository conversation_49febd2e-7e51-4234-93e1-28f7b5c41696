package com.ruoyi.order.api;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.business.base.api.domain.bo.CxrChannelCommissionConfigBo;
import com.ruoyi.business.base.api.domain.dto.DailyIntergerDto;
import com.ruoyi.business.base.api.domain.vo.CommonExchangeOrderVo;
import com.ruoyi.business.base.api.domain.vo.CustomersVo;
import com.ruoyi.common.order.wap.entity.FuyouNotifyResponse;
import com.ruoyi.common.order.wap.entity.PayNotifyUrlResponse;
import com.ruoyi.common.sms.entity.SumOrderParams;
import com.ruoyi.order.api.domain.bo.CustomerOrderStatisticsBo;
import com.ruoyi.order.api.domain.dto.TikTokReturnOrderDTO;
import com.ruoyi.order.api.domain.dto.UserOrderDTO;
import com.ruoyi.order.api.domain.vo.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Description: 订单对外暴露接口
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/8/23 11:13
 */
public interface RemoteOrderService {

    /**
     * 返回查询支付状态
     *
     * @return
     */
    int verifyPayStatus(String client_sn);

    /**
     * 返回查询支付状态
     *
     * @return
     */
    void orderMQAfterHandler(PayNotifyUrlResponse payNotifyUrlResponse);

    /**
     * 富友回调成功后订单处理
     *
     * @param fuYouNotifyResponse
     */
    void fuyouPayResultHandler(FuyouNotifyResponse fuYouNotifyResponse);

    /**
     * 合订单 处理支付完成后 客户信息的创建 、销售代理业绩计算
     *
     * @return
     */
    void contractOrdrSplithandler(Long cxrUserOrderId);

    /**
     * 查询客户订单
     *
     * @param customerPhone
     */
    UserOrderDTO queryCustomerlastOrder(String customerPhone);

    /**
     * 查询客户第一个订单（新订单/续订单/增订单/合订单/赠送单/转单）
     *
     * @param customerPhone
     * @param customerId
     * @return
     */
    UserOrderDTO queryCustomerFirstOrderV2(String customerPhone, Long customerId);

    /**
     * 查询客户第一个订单（新订单/续订单/增订单/合订单）
     *
     * @param customerPhone
     * @return
     */
    UserOrderDTO queryCustomerFirstOrder(String customerPhone);

    public <T extends Serializable> T queryOrderById(Long id);

    <T> T queryOrderNoById(Long id);

    /**
     * 订单支持成功之后 客户处理
     *
     * @param cxrUserOrderId
     */
    public void orderMQAfterCustomerHandler(Long cxrUserOrderId);

    /**
     * 订单支持成功之后 员工处理
     *
     * @param cxrUserOrderId
     */
    public void orderMQAfterEmployeeHandler(Long cxrUserOrderId);

    /**
     * 发送微信消息 和短信消息
     *
     * @param toLong
     */
    void sendOrderMessage(Long userOrderId);

    void sendOrderSmsMessage(SumOrderParams sumOrderParams);

    void orderPayNotify(PayNotifyUrlResponse payNotifyUrlResponse);

    Long instrtAnyExchangeOrder(CommonExchangeOrderVo commonExchangeOrderVo);

    void delOrderById(Long userOrderId);

    /**
     * 增订续
     *
     * @param id
     */
    void saveLongMilkStock(Long id);

    /**
     * 退
     *
     * @param id
     */
    void returnOrderLongMilkStock(Long id);

    /**
     * 合
     *
     * @param id
     */
    void saveContractOrderLongMilk(Long id);

    // 统计鲜奶订购数量   / 客户转出鲜奶数量           / 转入鲜奶数量      /退订数量      年
    DailyIntergerDto sumOrderQuantity(LocalDate day, String phone);

    List<UserOrderListVo> queryOrderListByDate(LocalDate startDate, LocalDate endDate);

    List<UserOrderListVo> queryReturnOrderListByDate(LocalDate startDate, LocalDate endDate);

    /**
     * 订单已送数量（开单的时候填写的已送数量）
     *
     * @param siteId
     * @param startTime
     * @param endTime
     * @return
     */
    Integer statisticUserOrder(Long siteId, LocalDateTime startTime, LocalDateTime endTime);

    List<BigDecimal> sumNewOrder(LocalDate now, short orderType, Long cxrEmployeeId);

    /**
     * 统计退订单
     *
     * @param now
     * @return
     */
    List<DailyIntergerDto> sumReturnOrder(LocalDate now);

    //    List<DailyIntergerDto> sumRenewalOrder(LocalDate now,short orderType);

    List<BigDecimal> countValidOrder(LocalDate now, Long cxrEmployeeId);

    IPage<CustomerOrderStatisticsVo> orderStatistics(
        CustomerOrderStatisticsBo bo, Page<Object> build);

    CustomerOrderTotal TotalOrderStatistics(CustomerOrderStatisticsBo bo);

    CustomerOrderStatisticsDTO customerOrderStatistics(Long customerId, Integer year);

    /**
     * 新增续合 鲜奶赠送数量 审核 或支付完成后的
     *
     * @param min
     * @return
     */
    List<DailyIntergerDto> sumGiveFreshMilkNumber(LocalDate min);

    /**
     * 新增续合 鲜奶赠送数量 审核 或支付完成后的 根据站点
     *
     * @param min
     * @return
     */
    DailyIntergerDto sumGiveFreshMilkNumberBySite(LocalDate min, Long siteId);

    List<DailyIntergerDto> sumReturnFreshMilkNumber(LocalDate min);

    DailyIntergerDto sumReturnFreshMilkNumberBySite(LocalDate min, Long siteId);

    boolean addImportUserOrder(List<CxrOrderListImportVo> list) throws ParseException;

    List<UserOrderListVo> getPhone(String phone);

    Integer customerSwitchPhone(String phone);
    Integer queryCustomerSwitch(Long customerId);

    void orderHandler(Long orderId);

    boolean importUserOrderPlus(List<CxrOrderListImportPlusVo> list, Long sysDept)
        throws ParseException;

    boolean importExecutorUserOrderPlus(List<CxrOrderListImportPlusVo> list, Long sysDept)
        throws ParseException;

    void updataOrderProxyId();

    void updataOrderCustomerInfoList();

    void huiBoSynchronouslyOrder() throws Exception;

    void huiBoRealTimeSynOrder(Long id);

    void huiBoCustomeOrder(LocalDateTime localDateTime);

    void deleteNotPayCxrUserOrder();


    IPage<String> selectPagePhone(LocalDate now, Page page);

    DailyIntergerDto sumTranInMilk(LocalDate date, String phone);

    DailyIntergerDto sumTranOutMilk(LocalDate date, String phone);

    DailyIntergerDto returnMilk(LocalDate date, String phone);

    void saveTransferOrderLongMilk(Long transferOrderId);

    List<UserOrderDTO> queryOrderByids(List<Long> orderIds);

    boolean updateOrderPhone(CustomersVo cxrCustomer);

    List<CustomerOrderRecentlyDate> selectRecentlyOrderDate(List<Long> cxrCustomerIds);

    CustomerOrderRecentlyDate selectRecentlyOrderDateByCustomerId(Long customerId);

    Map<Long, String> queryorderNoByIds(List<Long> orderIds);

    List<UserOrderDTO> getCustomerIdByOrder(Long cxrCustomerId);

    Map<Long, Date> queryXcxGiveOrderByCustomerId(List<Long> customerIds, LocalDate localDate);

    void orderRefund(long parseLong);

    Long returnOrderAdd(Long id);

    TikTokReturnOrderDTO tiktokOrderRefund(String orderNo);

    Boolean returnGiveOrderAddAndAudit(Long id);

    Boolean returnGiveOrderAddAndAudit(Long id, Boolean refundActive);

    Long cancelOrderAfterSale(Long id);

    void transferOrderAuditSuccessCustomerMessage(Long id);

    void transferOrderAuditSuccessEmployeeMessage(Long id);

    List<UserReturnOrderVo> getUserReturnOrderByAfterSalesId(Long afterSalesId);


    UserOrderListVo queryByCustomerId(Long id);

    /**
     * 小程序零售&售后工单支付退款
     */
    Boolean orderXcxRetailAfterRefund(Long cxrOrderAfterSaleId);

    /**
     * 转奶工单判断是否首单
     *
     * @param outCustomerPhone
     * @param inCustomerPhone
     * @return
     */
    Boolean isFristTransferMilk(String outCustomerPhone, String inCustomerPhone);


    /**
     * 查询微信退款结果并更新退款记录
     */
    void queryAndUpdateWxRefundResult();

    /**
     * 微信退款成功回调同步更新相关记录和状态
     */
    void updatePaymentRefundRecord(String notifyResult);


    /**
     * 逻辑删除两天前创建未支付的订单
     */
    void logicDeleteNotPayCxrUserOrder();


    void syncPayInfo(Long id, boolean sc);

    void syncOrder(LocalDateTime date);

    /**
     * 根据客户id分页查询客户订单
     *
     * @param customerId
     * @param size       每页显示条数
     * @param current    当前页
     * @return
     */
    Page<UserOrderDTO> queryUserOrderByCustomerId(Long customerId, long size, long current);

    /**
     * 退订单-退款执行
     */
    void retrunOrderRefundActive(Long userOrderReturnID);

    void wxReturnOrderRefundCallback(String content);

    /**
     * 通过赠品订单号查询主订单号发货状态
     *
     * @param giftOrderNoList
     * @return
     */
    Map<String, JSONObject> getMainOrderStatusList(Collection<String> giftOrderNoList, Integer syncType);

    void updateChannelCommission(CxrChannelCommissionConfigBo cxrChannelCommissionConfigBo);

    BigDecimal countValidNewOrder(LocalDate s, Long cxrEmployeeId);

    CustomerOrderTotal TotalOrderStatisticsUpgrade(CustomerOrderStatisticsBo bo);

    IPage<CustomerOrderStatisticsVo> orderFixationStatistics(CustomerOrderStatisticsBo bo, Page<Object> build);

    CustomerOrderTotal TotalOrderFixationStatisticsUpgrade(CustomerOrderStatisticsBo bo);

    List<LocalDateTime> getMirrorImageTime();

    void userOrderMirrorImageJob(LocalDate now);
}
