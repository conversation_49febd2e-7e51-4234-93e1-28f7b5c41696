package com.ruoyi.order.common.domain.bo;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.mybatis.core.page.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/8/8 19:39
 */
@ApiModel
@Data
public class CxrUserOrderQueryBo extends PageQuery implements Serializable {

    private Long id;

    /**
     * 公司id
     */
    @ApiModelProperty("所属公司id")
    private Long companyId;

    /**
     * 公司id
     */
    @ApiModelProperty("所属公司id")
    private Long deptId;

    /**
     * 大区id
     */
    @ApiModelProperty("大区id")
    private Long bigAreaId;

    /**
     * 站点id
     */
    @ApiModelProperty("站点id")
    private Long siteId;

    @ApiModelProperty(value = "销售代理id集合")
    private List<Long> proxyIds;

    @ApiModelProperty(value = "类型标签")
    private String label;

    /**
     * 订单类型;
     */
    @ApiModelProperty(value = "订单类型")
    private Integer orderType;

    /**
     * 付款来源
     */
    @ApiModelProperty(value = "付款来源")
    private String paymentSouce;

    /**
     * 客户电话;只取第一个录入的数据
     */
    @ApiModelProperty(value = "客户电话")
    private String customerPhone;

    /**
     * 客户地址; 只取第一个录入的数据
     */
    @ApiModelProperty(value = "客户地址")
    private String customerAdress;

    /**
     * 单据;订单单据号
     */
    @ApiModelProperty(value = "单据")
    private String orderNo;

    /**
     * 商户单号
     */
    @ApiModelProperty(value = "商户单号")
    private String merchantOrderNo;

    /**
     * 1 待审核 、 2 已审核 、 3.已拒绝
     */
    @ApiModelProperty("1 待审核 、 2 已审核 、 3.已拒绝")
    private Integer auditStatus;

    /**
     * 转入客户手机号
     */
    @ApiModelProperty("转入客户手机号")
    private String customerPhoneSwitch;

    @ApiModelProperty(value = "订单开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDateTime;

    @ApiModelProperty(value = "订单结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDateTime;

    @ApiModelProperty(value = "修改订单开始时间")
    private LocalDateTime startUpdateTime;

    @ApiModelProperty(value = "修改订单结束时间")
    private LocalDateTime endUpdateTime;

    private String proxyIdsJson;

    /**
     * 是否促销单;true 是 false 否
     */
    private Boolean promotionalOrderFlag;

    /**
     * 是否师徒单;true是 false 否
     */
    private Boolean apprenticeOrderFlag;

    /**
     * 来源 1 后端 2 配送端
     */
    @ApiModelProperty("源  1 后端 2 配送端")
    private Short terminalType;
    /**
     * 是否导出退订单;true 是 false 否
     */
    private Boolean exportReturnFlag = false;

    @ApiModelProperty(value = "活动id")
    private Long activityId;

    @ApiModelProperty(value = "打款状态:0.未打款、1.退款中,2.已打款、3.打款失败")
    private Integer paymentStatus;


    @ApiModelProperty(value = "核算类型")
    private Integer accountingType;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "商品类型名称")
    private String productTypeName;

    @ApiModelProperty(value = "渠道推广提成：1.有，0.无")
    private Integer promotionCommission;

    /**
     * 是否有佣金
     */
    @TableField(value = "promotion_commission_flag")
    private Integer promotionCommissionFlag;

    @ApiModelProperty(value = "异常订单状态  1：异常")
    private Integer abnormalTag;

    private String deliverySite;

//    public Date getStartDateTime() {
//        if (ObjectUtil.isNotNull(this.startDateTime)) {
//            return startDateTime;
//        } else {
////            Date tomorrowDate = DateUtils.getTomorrowDate();
////            Date date = DateUtils.addDays(tomorrowDate, -180);
//            return null;
//        }
//    }
//
//    public Date getEndDateTime() {
//        if (ObjectUtil.isNull(this.endDateTime)) {
//            this.endDateTime = new Date();
//        }
//
//        LocalDateTime startLocalDateTime = LocalDateTime
//            .ofInstant(this.getStartDateTime().toInstant(), ZoneId.systemDefault());
//        LocalDateTime endLocalDateTime = LocalDateTime
//            .ofInstant(this.endDateTime.toInstant(), ZoneId.systemDefault());
//
//        LocalDate startDate = startLocalDateTime.toLocalDate();
//        LocalDate endDate = endLocalDateTime.toLocalDate();
//        long daysBetween = ChronoUnit.DAYS.between(startDate, endDate);
//        if (daysBetween > 180) {
//            LocalDateTime adjustedStartDateTime = endLocalDateTime.minusDays(180);
//            this.startDateTime = Date
//                .from(adjustedStartDateTime.atZone(ZoneId.systemDefault()).toInstant());
//        }
//        this.endDateTime = Date.from(endLocalDateTime.atZone(ZoneId.systemDefault()).toInstant());
//        return this.endDateTime;
//    }

    public String getProxyIdsJson() {
        String s = JSONUtil.toJsonStr(proxyIds);
        return s;
    }

    public Boolean getPromotionalOrderFlag() {
        if (StrUtil.isNotBlank(label)) {
            if (StrUtil.equals(label, "1")) {
                return true;
            }
        }
        return false;
    }

    public Boolean getApprenticeOrderFlag() {
        if (StrUtil.isNotBlank(label)) {
            if (StrUtil.equals(label, "0")) {
                return true;
            }
        }
        return false;
    }
}
