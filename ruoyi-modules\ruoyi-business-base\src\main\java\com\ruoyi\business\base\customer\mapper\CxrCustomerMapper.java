package com.ruoyi.business.base.customer.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.business.base.CxrCustomerPhoneModify.domain.vo.ModifyCustomerPhoneVo;
import com.ruoyi.business.base.api.domain.CxrCustomer;
import com.ruoyi.business.base.api.domain.CxrRoadWay;
import com.ruoyi.business.base.api.domain.bo.CxrCustomerAddressBo;
import com.ruoyi.business.base.api.model.TagCustomerVo;
import com.ruoyi.business.base.customer.domain.bo.CxrCustomerAddressABo;
import com.ruoyi.business.base.customer.domain.bo.MilkDistributionDetailedBo;
import com.ruoyi.business.base.customer.domain.dto.CountAndSumQuantity;
import com.ruoyi.business.base.customer.domain.vo.*;
import com.ruoyi.business.base.violationOrderUserWarning.domian.vo.ViolationCustomerVo;
import com.ruoyi.common.core.domain.LoginInfo;
import com.ruoyi.common.mybatis.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * 客户Mapper接口
 *
 * <AUTHOR>
 * @date 2022-07-02
 */
public interface CxrCustomerMapper extends BaseMapperPlus<CxrCustomerMapper, CxrCustomer, CxrCustomerVo> {

    CxrCustomer getById(Long id);


    /**
     * 统计
     *
     * @param id
     * @param del
     * @return
     */
    CxrCustomerCountVo selectByCustomerId(@Param("id") Long id, @Param("del") String del);


    /**
     * 查询客户
     *
     * @param localDate
     * @param employeeIds
     * @param siteIds
     * @param page
     * @return
     */
    IPage<Long> queryDistributionMilk(@Param("deliveryLocalDate") LocalDate deliveryLocalDate, @Param(
        "employeeIds") List<Long> employeeIds, @Param("siteIds") List<Long> siteIds,
                                      @Param("suspendLocalDate") LocalDate suspendLocalDate,
                                      IPage<Long> page);


    Integer queryStockByid(Long id);

    CxrCustomer queryByPhone(@Param("phone") String phone, @Param("del") String del);

    @InterceptorIgnore(tenantLine = "on")
    boolean updateShouldSendBatch(@Param("id") Long id, @Param("i") Integer i);

    boolean updateCustomerFreshMilkRoadWaySentAdd(@Param("min") LocalDate minusDays);

    boolean updateRoadWayChangeRecord();

    @InterceptorIgnore(tenantLine = "on")
    boolean updateFreshMilkRoadWayChange(@Param("List") List<CxrCustomer> cxrCustomers);

    /**
     * 查询库存大于0 的客户
     *
     * @param queryPage
     * @return
     */
    IPage<CxrCustomer> queryStockGTZero(IPage<CxrCustomer> queryPage);

    Long customerStockTotal(@Param("bo") CxrCustomerAddressABo bo, @Param("del") String del);


    List<CxrCustomerListVo> customerPageTwo(@Param("siteIds") List<Long> customerIds,
                                            @Param("bo") CxrCustomerAddressABo bo, @Param("notDel") String notDel, @Param("offset") Long offset,
                                            @Param("size") Long size);

    CountAndSumQuantity countCustomerTwo(@Param("siteIds") List<Long> siteIds, @Param("bo") CxrCustomerAddressABo bo,
                                         @Param("notDel") String value);

    CountAndSumQuantity countCustomerTwoV2(@Param("siteIds") List<Long> siteIds, @Param("bo") CxrCustomerAddressABo bo,
                                           @Param("notDel") String value);

    Page<HuiBoRenewalPortVo> huiBoRenewalPortraitStock(IPage page, @Param("startTime") LocalDateTime startTime,
                                                       @Param("endTime") LocalDateTime endTime);


    HuiBoRenewalPortVo huiBoRenewalPortraitStockById(@Param("id") Long id);

    Page<CxrRoadWay> getDateRoadWays(IPage page, @Param("now") LocalDate now);

    /**
     * 只查询 客户手机号和名称 id
     *
     * @param
     * @return
     */
    List<CxrCustomer> queryById(@Param("phones") List<String> ids);

    Long queryCustomerIdByPhone(@Param("phone") String customerPhone);

    ModifyCustomerPhoneVo getModifyCustomerPhoneVo(@Param("userId") Long userId);

    Page<CxrCustomer> selectCustomerWarningJobUpdateExpirationTime(IPage page);

    Page<CxrCustomer> selectCustomerWarningJobUpdateExpirationTimePageIds(IPage page);

    List<CxrCustomer> selectCustomerWarningJobUpdateExpirationTimeByIds(@Param("ids") Collection<Long> ids);


    void updateBatchExpirationTime(@Param("cxrCustomers") List<CxrCustomer> cxrCustomers);

    List<Long> selectActiveyCustomer(@Param("phone") String phone);


    IPage<CxrCustomer> queryPageByPhone(@Param("page") IPage<CxrCustomer> queryPage,
                                        @Param("bo") CxrCustomerAddressBo cxrCustomerAddress);

    int orderRefundUpdateLockStock(@Param("customerId") Long customerId, @Param("qty") Integer qty);

    int del(@Param("id") Long id, @Param("num") Integer num, @Param("loginUser") LoginInfo loginUser);

    List<TagCustomerVo> getCustomerBy(@Param("customerIds") Collection<Long> customerIds);

    List<TagCustomerVo> selectAllBy(@Param("maxCustomerId") Long maxCustomerId, @Param("pageSize") Integer pageSize);

    List<Long> loadCustomerIdsBy(@Param("maxCustomerId") Long maxCustomerId, @Param("pageSize") Integer pageSize);

    List<ViolationCustomerVo> violationQueryCustomer(@Param("customerPhone") String customerPhone, @Param("limit") Integer limit);

    CustomerStockSumVo customerStockSumInfo(@Param("customerId") Long customerId);
}
