<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.order.common.mapper.CxrUserOrderMirrorImageMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.order.common.entity.CxrUserOrderMirrorImage">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="createByName" column="create_by_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateByName" column="update_by_name" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleteStatus" column="delete_status" jdbcType="VARCHAR"/>
        <result property="companyId" column="company_id" jdbcType="BIGINT"/>
        <result property="companyName" column="company_name" jdbcType="VARCHAR"/>
        <result property="bigAreaId" column="big_area_id" jdbcType="BIGINT"/>
        <result property="bigAreaName" column="big_area_name" jdbcType="VARCHAR"/>
        <result property="province" column="province" jdbcType="VARCHAR"/>
        <result property="city" column="city" jdbcType="VARCHAR"/>
        <result property="area" column="area" jdbcType="VARCHAR"/>
        <result property="siteName" column="site_name" jdbcType="VARCHAR"/>
        <result property="siteAdress" column="site_adress" jdbcType="VARCHAR"/>
        <result property="siteId" column="site_id" jdbcType="BIGINT"/>
        <result property="businessAgent" column="business_agent"
                typeHandler="com.ruoyi.order.common.typeHandler.TypeHandlerConstant$BusinessAgentTypeHandler"/>
        <result property="customerName" column="customer_name" jdbcType="VARCHAR"/>
        <result property="customerPhone" column="customer_phone" jdbcType="VARCHAR"/>
        <result property="customerAdress" column="customer_adress" jdbcType="VARCHAR"/>
        <result property="customerNameSwitch" column="customer_name_switch" jdbcType="VARCHAR"/>
        <result property="customerAdressSwitch" column="customer_adress_switch" jdbcType="VARCHAR"/>
        <result property="customerPhoneSwitch" column="customer_phone_switch" jdbcType="VARCHAR"/>
        <result property="customerIdSwitch" column="customer_id_switch" jdbcType="BIGINT"/>
        <result property="paymentSouce" column="payment_souce" jdbcType="VARCHAR"/>
        <result property="orderType" column="order_type" jdbcType="INTEGER"/>
        <result property="zeroQuantityRenewal" column="zero_quantity_renewal" jdbcType="TINYINT"/>
        <result property="conversionType" column="conversion_type" jdbcType="VARCHAR"/>
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="merchantOrderNo" column="merchant_order_no" jdbcType="VARCHAR"/>
        <result property="sqbSn" column="sqb_sn" jdbcType="VARCHAR"/>
        <result property="orderQuantity" column="order_quantity" jdbcType="INTEGER"/>
        <result property="freshMilkGiveQuantity" column="fresh_milk_give_quantity" jdbcType="INTEGER"/>
        <result property="longMilkGiveQuantity" column="long_milk_give_quantity" jdbcType="INTEGER"/>
        <result property="excessQuantity" column="excess_quantity" jdbcType="INTEGER"/>
        <result property="freshMilkSentQuantity" column="fresh_milk_sent_quantity" jdbcType="INTEGER"/>
        <result property="longMilkSentQuantity" column="long_milk_sent_quantity" jdbcType="INTEGER"/>
        <result property="surplusQuantity" column="surplus_quantity" jdbcType="INTEGER"/>
        <result property="conversionQuantity" column="conversion_quantity" jdbcType="INTEGER"/>
        <result property="unitPrice" column="unit_price" jdbcType="DECIMAL"/>
        <result property="amount" column="amount" jdbcType="DECIMAL"/>
        <result property="creditCardAmount" column="credit_card_amount" jdbcType="DECIMAL"/>
        <result property="orderDate" column="order_date" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="auditStatus" column="audit_status" jdbcType="TINYINT"/>
        <result property="auditBy" column="audit_by" jdbcType="BIGINT"/>
        <result property="auditByName" column="audit_by_name" jdbcType="VARCHAR"/>
        <result property="auditTime" column="audit_time" jdbcType="TIMESTAMP"/>
        <result property="promotionalOrderFlag" column="promotional_order_flag" jdbcType="TINYINT"/>
        <result property="apprenticeOrderFlag" column="apprentice_order_flag" jdbcType="TINYINT"/>
        <result property="terminalType" column="terminal_type" jdbcType="TINYINT"/>
        <result property="customerInfoList" column="customer_info_list"
                typeHandler="com.ruoyi.order.common.typeHandler.TypeHandlerConstant$CustomerInfoTypeHandler"/>
        <!--        <result property="orderImages" column="order_images" jdbcType="OTHER"/>-->
        <!--        <result property="playImages" column="play_images" jdbcType="OTHER"/>-->
        <result property="payStatus" column="pay_status" jdbcType="TINYINT"/>
        <result property="perfectStatus" column="perfect_status" jdbcType="TINYINT"/>
        <result property="freshMilkReturnQuantity" column="fresh_milk_return_quantity" jdbcType="INTEGER"/>
        <result property="orderStatus" column="order_status" jdbcType="INTEGER"/>
        <result property="payTime" column="pay_time" jdbcType="TIMESTAMP"/>
        <result property="contractTotalAmount" column="contract_total_amount" jdbcType="DECIMAL"/>
        <result property="contractTotalOrderQuantity" column="contract_total_order_quantity" jdbcType="INTEGER"/>
        <result property="exchangeProductId" column="exchange_product_id" jdbcType="BIGINT"/>
        <result property="exchangeSum" column="exchange_sum" jdbcType="INTEGER"/>
        <result property="milkExchangeSum" column="milk_exchange_sum" jdbcType="INTEGER"/>
        <result property="exchangeProductName" column="exchange_product_name" jdbcType="VARCHAR"/>
        <!--        <result property="spec" column="spec" jdbcType="OTHER"/>-->
        <result property="isImport" column="is_import" jdbcType="TINYINT"/>
        <result property="activityGiveQuantity" column="activity_give_quantity" jdbcType="INTEGER"/>
        <result property="activitySentQuantity" column="activity_sent_quantity" jdbcType="INTEGER"/>
        <result property="customerId" column="customer_id" jdbcType="BIGINT"/>
        <result property="activityType" column="activity_type" jdbcType="VARCHAR"/>
        <result property="thirdOrderNo" column="third_order_no" jdbcType="VARCHAR"/>
        <result property="newCustomerFlag" column="new_customer_flag" jdbcType="VARCHAR"/>
        <result property="outLongMilkOrderNo" column="out_long_milk_order_no" jdbcType="VARCHAR"/>
        <result property="activityId" column="activity_id" jdbcType="BIGINT"/>
        <!--        <result property="giveGiftList" column="give_gift_list" jdbcType="OTHER"/>-->
        <result property="activityName" column="activity_name" jdbcType="VARCHAR"/>
        <result property="setMealPrice" column="set_meal_price" jdbcType="DECIMAL"/>
        <result property="setMealQty" column="set_meal_qty" jdbcType="INTEGER"/>
        <result property="pigcFlag" column="pigc_flag" jdbcType="TINYINT"/>
        <result property="apportionMoney" column="apportion_money" jdbcType="DECIMAL"/>
        <result property="auditCount" column="audit_count" jdbcType="TINYINT"/>
        <result property="thirdOrderNoTime" column="third_order_no_time" jdbcType="TIMESTAMP"/>
        <result property="contractTypeTag" column="contract_type_tag" jdbcType="TINYINT"/>
        <result property="refundSuccessFlag" column="refund_success_flag" jdbcType="TINYINT"/>
        <result property="createByCode" column="create_by_code" jdbcType="VARCHAR"/>
        <result property="payPlaformType" column="pay_plaform_type" jdbcType="INTEGER"/>
        <result property="scanQrTimes" column="scan_qr_times" jdbcType="INTEGER"/>
        <result property="coType" column="co_type" jdbcType="TINYINT"/>
        <result property="paymentStatus" column="payment_status" jdbcType="TINYINT"/>
        <result property="paymentFailureReasons" column="payment_failure_reasons" jdbcType="VARCHAR"/>
        <result property="payOrderId" column="pay_order_id" jdbcType="BIGINT"/>
        <result property="tiktokOrderRefundNo" column="tiktok_order_refund_no" jdbcType="VARCHAR"/>
        <result property="platformDiscount" column="platform_discount" jdbcType="DECIMAL"/>
        <result property="payAmount" column="pay_amount" jdbcType="DECIMAL"/>
        <result property="channel" column="channel" jdbcType="INTEGER"/>
        <result property="productType" column="product_type" jdbcType="VARCHAR"/>
        <result property="productId" column="product_id" jdbcType="VARCHAR"/>
        <result property="productName" column="product_name" jdbcType="VARCHAR"/>
        <result property="accountingType" column="accounting_type" jdbcType="TINYINT"/>
        <result property="promotionCommission" column="promotion_commission" jdbcType="TINYINT"/>
        <result property="commissionConfigId" column="commission_config_id" jdbcType="BIGINT"/>
        <result property="productTypeName" column="product_type_name" jdbcType="VARCHAR"/>
        <result property="abnormalTag" column="abnormal_tag" jdbcType="INTEGER"/>
        <result property="abnormalTime" column="abnormal_time" jdbcType="TIMESTAMP"/>
        <result property="oldCustomer" column="old_customer" jdbcType="TINYINT"/>
        <result property="zero15DayAfter" column="zero15_day_after" jdbcType="TINYINT"/>
        <result property="deliverySites" column="delivery_sites" jdbcType="VARCHAR"/>
        <result property="refundNoAuditReasons" column="refund_no_audit_reasons" jdbcType="VARCHAR"/>
        <result property="mirrorImageTime" column="mirror_image_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,create_by_name,create_time,
        update_by_name,update_time,delete_status,
        company_id,company_name,big_area_id,
        big_area_name,province,city,
        area,site_name,site_adress,
        site_id,business_agent,customer_name,
        customer_phone,customer_adress,customer_name_switch,
        customer_adress_switch,customer_phone_switch,customer_id_switch,
        payment_souce,order_type,zero_quantity_renewal,
        conversion_type,order_no,merchant_order_no,
        sqb_sn,order_quantity,fresh_milk_give_quantity,
        long_milk_give_quantity,excess_quantity,fresh_milk_sent_quantity,
        long_milk_sent_quantity,surplus_quantity,conversion_quantity,
        unit_price,amount,credit_card_amount,
        order_date,remark,audit_status,
        audit_by,audit_by_name,audit_time,
        promotional_order_flag,apprentice_order_flag,terminal_type,
        customer_info_list,order_images,play_images,
        pay_status,perfect_status,fresh_milk_return_quantity,
        contract_order_ext,order_status,pay_time,
        contract_total_amount,contract_total_order_quantity,exchange_product_id,
        exchange_sum,milk_exchange_sum,exchange_product_name,
        spec,is_import,activity_give_quantity,
        activity_sent_quantity,customer_id,activity_type,
        third_order_no,new_customer_flag,out_long_milk_order_no,
        activity_id,give_gift_list,activity_name,
        set_meal_price,set_meal_qty,pigc_flag,
        apportion_money,audit_count,third_order_no_time,
        contract_type_tag,refund_success_flag,create_by_code,
        pay_plaform_type,scan_qr_times,co_type,
        payment_status,payment_failure_reasons,pay_order_id,
        tiktok_order_refund_no,platform_discount,pay_amount,
        product_name,accounting_type,promotion_commission,
        commission_config_id,product_type_name,abnormal_tag,
        abnormal_time,old_customer,zero15_day_after,
        delivery_sites,refund_no_audit_reasons,mirror_image_time
    </sql>
    <insert id="orderMirrorImageSave">
        INSERT INTO cxr_user_order_mirror_image (`id`, `create_by_name`, `create_time`, `update_by_name`, `update_time`,
                                                 `delete_status`, `company_id`, `company_name`, `big_area_id`,
                                                 `big_area_name`, `province`, `city`, `area`, `site_name`,
                                                 `site_adress`, `site_id`, `business_agent`, `customer_name`,
                                                 `customer_phone`, `customer_adress`, `customer_name_switch`,
                                                 `customer_adress_switch`, `customer_phone_switch`,
                                                 `customer_id_switch`, `payment_souce`, `order_type`,
                                                 `zero_quantity_renewal`, `conversion_type`, `order_no`,
                                                 `merchant_order_no`, `sqb_sn`, `order_quantity`,
                                                 `fresh_milk_give_quantity`, `long_milk_give_quantity`,
                                                 `excess_quantity`, `fresh_milk_sent_quantity`,
                                                 `long_milk_sent_quantity`, `surplus_quantity`, `conversion_quantity`,
                                                 `unit_price`, `amount`, `credit_card_amount`, `order_date`, `remark`,
                                                 `audit_status`, `audit_by`, `audit_by_name`, `audit_time`,
                                                 `promotional_order_flag`, `apprentice_order_flag`, `terminal_type`,
                                                 `customer_info_list`, `order_images`, `play_images`, `pay_status`,
                                                 `perfect_status`, `fresh_milk_return_quantity`, `contract_order_ext`,
                                                 `order_status`, `pay_time`, `contract_total_amount`,
                                                 `contract_total_order_quantity`, `exchange_product_id`, `exchange_sum`,
                                                 `milk_exchange_sum`, `exchange_product_name`, `spec`, `is_import`,
                                                 `activity_give_quantity`, `activity_sent_quantity`, `customer_id`,
                                                 `activity_type`, `third_order_no`, `new_customer_flag`,
                                                 `out_long_milk_order_no`, `activity_id`, `give_gift_list`,
                                                 `activity_name`, `set_meal_price`, `set_meal_qty`, `pigc_flag`,
                                                 `apportion_money`, `audit_count`, `third_order_no_time`,
                                                 `contract_type_tag`, `refund_success_flag`, `create_by_code`,
                                                 `pay_plaform_type`, `scan_qr_times`, `co_type`, `payment_status`,
                                                 `payment_failure_reasons`, `pay_order_id`, `tiktok_order_refund_no`,
                                                 `platform_discount`, `pay_amount`, `channel`, `product_type`,
                                                 `product_id`, `product_name`, `accounting_type`,
                                                 `promotion_commission`, `commission_config_id`, `product_type_name`,
                                                 `abnormal_tag`, `abnormal_time`, `old_customer`, `zero15_day_after`,
                                                 `delivery_sites`, `refund_no_audit_reasons`, `mirror_image_time`)
        SELECT `id`,
               `create_by_name`,
               `create_time`,
               `update_by_name`,
               `update_time`,
               `delete_status`,
               `company_id`,
               `company_name`,
               `big_area_id`,
               `big_area_name`,
               `province`,
               `city`,
               `area`,
               `site_name`,
               `site_adress`,
               `site_id`,
               `business_agent`,
               `customer_name`,
               `customer_phone`,
               `customer_adress`,
               `customer_name_switch`,
               `customer_adress_switch`,
               `customer_phone_switch`,
               `customer_id_switch`,
               `payment_souce`,
               `order_type`,
               `zero_quantity_renewal`,
               `conversion_type`,
               `order_no`,
               `merchant_order_no`,
               `sqb_sn`,
               `order_quantity`,
               `fresh_milk_give_quantity`,
               `long_milk_give_quantity`,
               `excess_quantity`,
               `fresh_milk_sent_quantity`,
               `long_milk_sent_quantity`,
               `surplus_quantity`,
               `conversion_quantity`,
               `unit_price`,
               `amount`,
               `credit_card_amount`,
               `order_date`,
               `remark`,
               `audit_status`,
               `audit_by`,
               `audit_by_name`,
               `audit_time`,
               `promotional_order_flag`,
               `apprentice_order_flag`,
               `terminal_type`,
               `customer_info_list`,
               `order_images`,
               `play_images`,
               `pay_status`,
               `perfect_status`,
               `fresh_milk_return_quantity`,
               `contract_order_ext`,
               `order_status`,
               `pay_time`,
               `contract_total_amount`,
               `contract_total_order_quantity`,
               `exchange_product_id`,
               `exchange_sum`,
               `milk_exchange_sum`,
               `exchange_product_name`,
               `spec`,
               `is_import`,
               `activity_give_quantity`,
               `activity_sent_quantity`,
               `customer_id`,
               `activity_type`,
               `third_order_no`,
               `new_customer_flag`,
               `out_long_milk_order_no`,
               `activity_id`,
               `give_gift_list`,
               `activity_name`,
               `set_meal_price`,
               `set_meal_qty`,
               `pigc_flag`,
               `apportion_money`,
               `audit_count`,
               `third_order_no_time`,
               `contract_type_tag`,
               `refund_success_flag`,
               `create_by_code`,
               `pay_plaform_type`,
               `scan_qr_times`,
               `co_type`,
               `payment_status`,
               `payment_failure_reasons`,
               `pay_order_id`,
               `tiktok_order_refund_no`,
               `platform_discount`,
               `pay_amount`,
               `channel`,
               `product_type`,
               `product_id`,
               `product_name`,
               `accounting_type`,
               `promotion_commission`,
               `commission_config_id`,
               `product_type_name`,
               `abnormal_tag`,
               `abnormal_time`,
               `old_customer`,
               `zero15_day_after`,
               `delivery_sites`,
               `refund_no_audit_reasons`,
               #{currentDate}
        FROM cxr_user_order
        WHERE date_format(order_date, '%Y-%m-%d') >= #{startDate}
          and order_date  <![CDATA[ <= ]]> #{lastDayOfPreviousMonth}
          and delete_status = 0
          and (pay_status = 3 or audit_status = 2)
          and is_import is null
    </insert>
</mapper>
