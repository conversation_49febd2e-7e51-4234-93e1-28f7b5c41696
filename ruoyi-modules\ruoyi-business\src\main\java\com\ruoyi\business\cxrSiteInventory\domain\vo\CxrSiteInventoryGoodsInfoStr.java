package com.ruoyi.business.cxrSiteInventory.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class CxrSiteInventoryGoodsInfoStr implements Serializable {
    /**
     * 商品id
     */
    @ApiModelProperty("商品id")
    private String productId;

    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    private String productName;

    /**
     * 商品别名
     */
    @ApiModelProperty("商品别名")
    private String productAlias;
    /**
     * 盘点单商品数量
     */
    @ApiModelProperty("商品数量")
    private Long quantity = 0L;

    /**
     * 库存数量
     */
    @ApiModelProperty("库存数量")
    private Long systemQuantity = 0L;

    /**
     * 差异数
     */
    @ApiModelProperty("差异数")
    private Long quantityVariance = 0L;
}
