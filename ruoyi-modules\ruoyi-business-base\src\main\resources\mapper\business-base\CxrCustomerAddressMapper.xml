<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.business.base.customerAddress.mapper.CxrCustomerAddressMapper">

    <resultMap type="com.ruoyi.business.base.api.domain.CxrCustomerAddress" id="CxrCustomerAddressResult">
        <result property="id" column="id"/>
        <result property="cxrCustomerId" column="cxr_customer_id"/>
        <result property="sysDeptId" column="sys_dept_id"/>
        <result property="receiverName" column="receiver_name"/>
        <result property="receiverPhone" column="receiver_phone"/>
        <result property="detailDistributionAddress" column="detail_distribution_address"/>
        <result property="sysAreaId" column="sys_area_id"/>
        <result property="cxrSiteId" column="cxr_site_id"/>
        <result property="cxrEmployeeId" column="cxr_employee_id"/>
        <result property="isShowAmDistribution" column="is_show_am_distribution"/>
        <result property="amDistributionStatus" column="am_distribution_status"/>
        <result property="amDistributionStartDeliveryTime" column="am_distribution_start_delivery_time"/>
        <result property="amDistributionSuspendStartTime" column="am_distribution_suspend_start_time"/>
        <result property="amDistributionSuspendEndTime" column="am_distribution_suspend_end_time"/>
        <result property="isShowPmDistribution" column="is_show_pm_distribution"/>
        <result property="pmDistributionStatus" column="pm_distribution_status"/>
        <result property="pmDistributionStartDeliveryTime" column="pm_distribution_start_delivery_time"/>
        <result property="pmDistributionSuspendStartTime" column="pm_distribution_suspend_start_time"/>
        <result property="pmDistributionSuspendEndTime" column="pm_distribution_suspend_end_time"/>
        <result property="revision" column="revision"/>
        <result property="createBy" column="create_by"/>
        <result property="createByName" column="create_by_name"/>
        <result property="createByType" column="create_by_type"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateByName" column="update_by_name"/>
        <result property="updateByType" column="update_by_type"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleteBy" column="delete_by"/>
        <result property="deleteByName" column="delete_by_name"/>
        <result property="deleteByType" column="delete_by_type"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="deleteStatus" column="delete_status"/>
        <result property="sortNum" column="sort_num"/>
        <result property="remark" column="remark"/>
        <result property="spareId" column="spare_id"/>
        <result property="amDistributionInfo" column="am_distribution_info"
                typeHandler="com.ruoyi.business.base.api.typeHandler.TypeHandlerConstant$CustomerAddressMilkDistributionInfoTypeHandler"/>
        <result property="pmDistributionInfo" column="pm_distribution_info"
                typeHandler="com.ruoyi.business.base.api.typeHandler.TypeHandlerConstant$CustomerAddressMilkDistributionInfoTypeHandler"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
    </resultMap>

    <resultMap id="addressVoResulMap" type="com.ruoyi.business.base.customerAddress.domain.vo.CxrCustomerAddrVo">
        <result property="id" column="id"/>
        <result property="cxrCustomerId" column="cxr_customer_id"/>
        <result property="receiverName" column="receiver_name"/>
        <result property="receiverPhone" column="receiver_phone"/>
        <result property="detailDistributionAddress" column="detail_distribution_address"/>
        <result property="sysAreaId" column="sys_area_id"/>
        <result property="cxrSiteId" column="cxr_site_id"/>
        <result property="cxrEmployeeId" column="cxr_employee_id"/>
        <result property="amDistributionStatus" column="am_distribution_status"/>
        <result property="amDistributionStartDeliveryTime" column="am_distribution_start_delivery_time"/>
        <result property="amDistributionSuspendStartTime" column="am_distribution_suspend_start_time"/>
        <result property="amDistributionSuspendEndTime" column="am_distribution_suspend_end_time"/>
        <result property="isShowPmDistribution" column="is_show_pm_distribution"/>
        <result property="isShowAmDistribution" column="is_show_am_distribution"/>
        <result property="pmDistributionStatus" column="pm_distribution_status"/>
        <result property="pmDistributionStartDeliveryTime" column="pm_distribution_start_delivery_time"/>
        <result property="pmDistributionSuspendStartTime" column="pm_distribution_suspend_start_time"/>
        <result property="pmDistributionSuspendEndTime" column="pm_distribution_suspend_end_time"/>
        <result property="amDistributionInfo" column="am_distribution_info"
                typeHandler="com.ruoyi.business.base.api.typeHandler.TypeHandlerConstant$CustomerAddressMilkDistributionInfoTypeHandler"/>
        <result property="pmDistributionInfo" column="pm_distribution_info"
                typeHandler="com.ruoyi.business.base.api.typeHandler.TypeHandlerConstant$CustomerAddressMilkDistributionInfoTypeHandler"/>
        <result column="provice" property="provice"/>
        <result column="city" property="city"/>
        <result column="area" property="area"/>
        <result column="update_time" property="updateTime"/>
        <result property="createByType" column="create_by_type"/>
        <result column="change_status" property="changeStatus"/>
        <result column="cxr_residential_quarters_id" property="cxrResidentialQuartersId"/>
        <collection property="cxrSaleProducts" ofType="com.ruoyi.business.api.domain.vo.SaleProductListVo">
            <id column="t2_id" property="id" jdbcType="INTEGER"/>
            <result column="name" property="name" jdbcType="VARCHAR"/>
            <result column="product_code" property="productCode" jdbcType="VARCHAR"/>
            <result column="cxr_product_form_id" property="cxrProductFormId" jdbcType="INTEGER"/>
            <result column="product_alias" property="productAlias" jdbcType="INTEGER"/>
        </collection>
    </resultMap>


    <resultMap id="addressVoResulBo" type="com.ruoyi.business.base.api.domain.vo.CxrAddressVo">
        <result property="id" column="id"/>
        <result property="cxrCustomerId" column="cxr_customer_id"/>
        <result property="receiverName" column="receiver_name"/>
        <result property="receiverPhone" column="receiver_phone"/>
        <result property="detailDistributionAddress" column="detail_distribution_address"/>
        <result property="sysAreaId" column="sys_area_id"/>
        <result property="cxrSiteId" column="cxr_site_id"/>
        <result property="cxrEmployeeId" column="cxr_employee_id"/>
        <result property="amDistributionStatus" column="am_distribution_status"/>
        <result property="amDistributionStartDeliveryTime" column="am_distribution_start_delivery_time"/>
        <result property="amDistributionSuspendStartTime" column="am_distribution_suspend_start_time"/>
        <result property="amDistributionSuspendEndTime" column="am_distribution_suspend_end_time"/>
        <result property="isShowPmDistribution" column="is_show_pm_distribution"/>
        <result property="isShowAmDistribution" column="is_show_am_distribution"/>
        <result property="pmDistributionStatus" column="pm_distribution_status"/>
        <result property="pmDistributionStartDeliveryTime" column="pm_distribution_start_delivery_time"/>
        <result property="pmDistributionSuspendStartTime" column="pm_distribution_suspend_start_time"/>
        <result property="pmDistributionSuspendEndTime" column="pm_distribution_suspend_end_time"/>
        <result property="amDistributionInfo" column="am_distribution_info"
                typeHandler="com.ruoyi.business.base.api.typeHandler.TypeHandlerConstant$CustomerAddressMilkDistributionInfoTypeHandler"/>
        <result property="pmDistributionInfo" column="pm_distribution_info"
                typeHandler="com.ruoyi.business.base.api.typeHandler.TypeHandlerConstant$CustomerAddressMilkDistributionInfoTypeHandler"/>
        <result column="provice" property="provice"/>
        <result column="city" property="city"/>
        <result column="area" property="area"/>
        <result column="update_time" property="updateTime"/>
        <result column="change_status" property="changeStatus"/>
        <result column="cxr_residential_quarters_id" property="cxrResidentialQuartersId"/>
        <collection property="cxrSaleProducts" ofType="com.ruoyi.business.api.domain.vo.SaleProductListVo">
            <id column="t2_id" property="id" jdbcType="INTEGER"/>
            <result column="name" property="name" jdbcType="VARCHAR"/>
            <result column="product_code" property="productCode" jdbcType="VARCHAR"/>
            <result column="cxr_product_form_id" property="cxrProductFormId" jdbcType="INTEGER"/>
            <result column="product_alias" property="productAlias" jdbcType="INTEGER"/>
        </collection>
    </resultMap>


    <select id="queryCxrCustomerId" resultType="java.lang.String">
        SELECT
        t2.cxr_customer_id
        FROM
        (
        SELECT
        t.cxr_customer_id,
        t.provice,
        t.city,
        t.area,
        t.detail_distribution_address,
        ROW_NUMBER() over ( PARTITION BY t.cxr_customer_id ORDER BY t.create_time ASC ) rak
        FROM

        cxr_customer_address t
        WHERE
        t.delete_status = '0'
        ) t2
        <where>
            t2.rak = 1
            <if test="cxrCustomerBo.provice!=null and cxrCustomerBo.provice!=''">
                and t2.provice like concat('%',#{cxrCustomerBo.provice,jdbcType=VARCHAR},'%')
            </if>

            <if test="cxrCustomerBo.city!=null and cxrCustomerBo.city!=''">
                and t2.city like concat('%',#{cxrCustomerBo.city,jdbcType=VARCHAR},'%')
            </if>

            <if test="cxrCustomerBo.area!=null and cxrCustomerBo.area!=''">
                and t2.area like concat('%',#{cxrCustomerBo.area,jdbcType=VARCHAR},'%')
            </if>

            <if test="cxrCustomerBo.detailDistributionAddress!=null and cxrCustomerBo.detailDistributionAddress!=''">
                and t2.detail_distribution_address like
                concat('%',#{cxrCustomerBo.detailDistributionAddress,jdbcType=VARCHAR},'%')
            </if>

        </where>


    </select>
    <select id="findByiCxrCustomerids"
            resultType="com.ruoyi.business.base.api.domain.CxrCustomerAddress">

        SELECT
        t2.cxr_customer_id,
        t2.provice,
        t2.city,
        t2.area,
        t2.detail_distribution_address
        FROM
        (
        SELECT
        t.cxr_customer_id,
        t.provice,
        t.city,
        t.area,
        t.detail_distribution_address,
        ROW_NUMBER() over ( PARTITION BY t.cxr_customer_id ORDER BY t.create_time ASC ) rak
        FROM

        cxr_customer_address t
        WHERE
        t.delete_status = '0'
        ) t2
        <where>
            t2.rak = 1
            <if test="cxrCustomerids!=null and cxrCustomerids.size() != 0">
                and t2.cxr_customer_id in
                <foreach collection="cxrCustomerids" index="index" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>

        </where>
    </select>


    <select id="queryAddressWithQuarters"
            resultType="com.ruoyi.business.base.api.domain.CustomerAddressWithQuarters">

        SELECT
        a.id id ,
        a.cxr_customer_id cxrCustomerId,
        a.receiver_name receiverName,
        a.receiver_phone receiverPhone,
        a.cxr_customer_id cxrCustomerId,
        a.provice provice,
        a.city city,
        a.area area,
        a.detail_distribution_address detailDistributionAddress,
        b.id cxrResidentialQuartersId ,
        b.name quarters
        FROM
        cxr_customer_address a join `cxr_residential_quarters` b on
        a.cxr_residential_quarters_id = b.id
        <where>
            <if test="customerAddress.cxrCustomerId!=null">
                and a.cxr_customer_id = #{customerAddress.cxrCustomerId}
            </if>
            <if test="customerAddress.receiverName!=null and customerAddress.receiverName!=''">
                and a.receiver_name like concat('%',#{customerAddress.receiverName,jdbcType=VARCHAR},'%')
            </if>

            <if test="customerAddress.receiverPhone!=null and customerAddress.receiverPhone!=''">
                and a.receiver_phone like concat('%',#{customerAddress.receiverPhone,jdbcType=VARCHAR},'%')
            </if>
        </where>

    </select>
    <select id="getAddressIdByEmId" resultType="java.lang.Long">
        select id from
        cxr_customer_address t
        <where>
            t.cxr_employee_id=#{id}
            and t.delete_status=#{del}
        </where>


    </select>


    <select id="listAddPeople" resultType="com.ruoyi.business.base.customerAddress.domain.vo.CxrCustomerAddressVo">
        SELECT
        a.id,
        a.cxr_site_id,
        a.cxr_customer_id,
        a.cxr_employee_id,
        c.id as cxrCustomerId,
        a.receiver_name as receiverName,
        a.detail_distribution_address,
        c.provice as province,
        c.city,
        c.area,
        c.phone
        FROM
        cxr_customer_address a
        LEFT JOIN cxr_customer c ON a.cxr_customer_id = c.id
        <where>
            <if test="bo.receiverName!=null and bo.receiverName!=''">
                and a.receiver_name like concat('%',#{bo.receiverName},'%')
            </if>
            <if test="bo.phone!=null and bo.phone!=''">
                and c.phone like concat('%',#{bo.phone},'%')
            </if>
            <if test="bo.detailDistributionAddress!=null and bo.detailDistributionAddress!=''">
                and a.detail_distribution_address like concat('%',#{bo.detailDistributionAddress},'%')
            </if>
            <if test="bo.name!=null and bo.name!=''">
                and c.NAME like concat('%',#{bo.name},'%')
            </if>
            <if test="bo.province!=null and bo.province!=''">
                and c.provice like concat('%',#{bo.province},'%')
            </if>
            <if test="bo.city!=null and bo.city!=''">
                and c.city like concat('%',#{bo.city},'%')
            </if>
            <if test="bo.area!=null and bo.area!=''">
                and c.area like concat('%',#{bo.area},'%')
            </if>
            <if test="bo.cxrSiteId!=null and bo.cxrSiteId!=''">
                and a.cxr_site_id=#{bo.cxrSiteId}
            </if>
            <if test="bo.cxrEmployeeId!=null and bo.cxrEmployeeId!=''">
                and a.cxr_employee_id =#{bo.cxrEmployeeId}
            </if>
            and a.delete_status=0
        </where>
    </select>
    <select id="selectAddressById"
            resultType="com.ruoyi.business.base.customerAddress.domain.vo.CxrCustomerAddrVo">
        select t.id
        , t.receiver_name
        , t.receiver_phone
        , t.provice
        , t.city
        , t.area
        , t.defalut_account_address
        , t.detail_distribution_address
        , t2.name as cxrResidentialQuartersName
        , t3.name as cxrSiteName
        , t4.name as cxrEmployeeName
        , t1.id as cxrCustomerId
        , t2.id as cxrResidentialQuartersId
        , t3.id as cxrSiteId
        , t4.id as cxrEmployeeName
        from cxr_customer_address t
        left join cxr_customer t1 on t1.id = t.cxr_customer_id
        left join cxr_residential_quarters t2 on t2.id = t.cxr_residential_quarters_id
        left join cxr_site t3 on t3.id = t.cxr_site_id
        left join cxr_employee t4 on t4.id = t.cxr_employee_id
        <where>
            t.id =#{id}
            and t.delete_status =#{del}
        </where>

    </select>
    <select id="selectAddressByHave" resultMap="CxrCustomerAddressResult">
        select *
        from cxr_customer_address where id in
        <foreach collection="set" index="i" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and (pm_distribution_status=#{status} or am_distribution_status=#{status})
    </select>


    <update id="updatemilkInfo">
        <foreach collection="addresses" index="i" item="bo" separator=";">
            update cxr_customer_address
            <set>
                is_show_am_distribution=#{bo.isShowAmDistribution},
                am_distribution_status=#{bo.amDistributionStatus},
                am_distribution_start_delivery_time=#{bo.amDistributionStartDeliveryTime},
                am_distribution_suspend_start_time=#{bo.amDistributionSuspendStartTime},
                am_distribution_suspend_end_time=#{bo.amDistributionSuspendEndTime},
                is_show_pm_distribution=#{bo.isShowPmDistribution},
                pm_distribution_status=#{bo.pmDistributionStatus},
                pm_distribution_start_delivery_time=#{bo.pmDistributionStartDeliveryTime},
                pm_distribution_suspend_start_time=#{bo.pmDistributionSuspendStartTime},
                pm_distribution_suspend_end_time=#{bo.pmDistributionSuspendEndTime},
                am_distribution_info=#{bo.amDistributionInfo ,typeHandler=com.ruoyi.business.base.api.typeHandler.TypeHandlerConstant$CustomerAddressMilkDistributionInfoTypeHandler},
                pm_distribution_info=#{bo.pmDistributionInfo,typeHandler=com.ruoyi.business.base.api.typeHandler.TypeHandlerConstant$CustomerAddressMilkDistributionInfoTypeHandler},
                update_by=#{bo.updateBy},
                update_by_name=#{bo.updateByName},
                update_by_type=#{bo.updateByType},
                update_time=now(),
            </set>
            where id=#{bo.id}

        </foreach>


    </update>

    <update id="updatemilkInfoCustomerChange">
        <foreach collection="addresses" index="i" item="bo" separator=";">
            update cxr_customer_change_record
            <set>
                is_show_am_distribution=#{bo.isShowAmDistribution},
                am_distribution_status=#{bo.amDistributionStatus},
                am_distribution_start_delivery_time=#{bo.amDistributionStartDeliveryTime},
                am_distribution_suspend_start_time=#{bo.amDistributionSuspendStartTime},
                am_distribution_suspend_end_time=#{bo.amDistributionSuspendEndTime},
                is_show_pm_distribution=#{bo.isShowPmDistribution},
                pm_distribution_status=#{bo.pmDistributionStatus},
                pm_distribution_start_delivery_time=#{bo.pmDistributionStartDeliveryTime},
                pm_distribution_suspend_start_time=#{bo.pmDistributionSuspendStartTime},
                pm_distribution_suspend_end_time=#{bo.pmDistributionSuspendEndTime},
                am_distribution_info=#{bo.amDistributionInfo ,typeHandler=com.ruoyi.business.base.api.typeHandler.TypeHandlerConstant$CustomerAddressMilkDistributionInfoTypeHandler},
                pm_distribution_info=#{bo.pmDistributionInfo,typeHandler=com.ruoyi.business.base.api.typeHandler.TypeHandlerConstant$CustomerAddressMilkDistributionInfoTypeHandler},
                update_by=#{bo.updateBy},
                update_by_name=#{bo.updateByName},
                update_by_type=#{bo.updateByType},
                update_time=now(),
            </set>
            where customer_address_id=#{bo.id} and delete_status=0

        </foreach>


    </update>


    <update id="updatemilkSentStop">
        <foreach collection="addresses" index="i" item="bo" separator=";">
            update cxr_customer_address
            <set>
                is_show_am_distribution=#{bo.isShowAmDistribution},
                am_distribution_status=#{bo.amDistributionStatus},
                am_distribution_start_delivery_time=#{bo.amDistributionStartDeliveryTime},
                am_distribution_suspend_start_time=#{bo.amDistributionSuspendStartTime},
                am_distribution_suspend_end_time=#{bo.amDistributionSuspendEndTime},
                is_show_pm_distribution=#{bo.isShowPmDistribution},
                pm_distribution_status=#{bo.pmDistributionStatus},
                pm_distribution_start_delivery_time=#{bo.pmDistributionStartDeliveryTime},
                pm_distribution_suspend_start_time=#{bo.pmDistributionSuspendStartTime},
                pm_distribution_suspend_end_time=#{bo.pmDistributionSuspendEndTime},
                update_by=#{bo.updateBy},
                update_by_name=#{bo.updateByName},
                update_by_type=#{bo.updateByType},
                update_time=now(),
            </set>
            where id=#{bo.id}

        </foreach>


    </update>

    <select id="milkkDistributionDetailes"
            resultType="com.ruoyi.business.base.customer.domain.vo.MilkDistributionDetaileDTO">
        select t.id,
               t.distribution_date               as   distributionDate,
               date_format(t.distribution_date, '%c') monthData,
               date_format(t.distribution_date, '%d') dayData,
               (select sum(milk_distribution_total)
                from cxr_road_way
                where cxr_road_way.distribution_date = t.distribution_date
                  and cxr_road_way.cxr_customer_address_id = #{addressId}
                  and cxr_road_way.`status` = 1) as   milkDistributionTotal
        from cxr_road_way t
        where 1 = 1
          and t.cxr_customer_address_id = #{addressId}
          and t.cxr_customer_id = #{customerId}

          and t.create_time <![CDATA[ >= ]]> #{startDate}
          and t.create_time <![CDATA[ <= ]]> #{endDate}

          and t.`status` = 1
        ORDER BY t.create_time desc
    </select>

    <select id="milkDistributionDetail"
            resultMap="addressVoResulMap">
        select t.* ,t2.id as t2_id,t2.name ,t2.product_code,t2.cxr_product_form_id,t2.product_alias
        from cxr_customer_address t
        left join cxr_site_sale_product t1 on t1.cxr_site_id = t.cxr_site_id
        left join cxr_sale_product t2 on (t1.cxr_sale_product_id=t2.id and t2.delete_status=#{del})
        <where>t.id in
            <foreach collection="ids" separator="," open="(" close=")" item="item" index="i">
                #{item}
            </foreach>

            and t.delete_status=#{del}
        </where>
    </select>

    <select id="queryPlanGenRoadWay" resultMap="CxrCustomerAddressResult">

        select t.*
        from cxr_customer_address t
        inner join cxr_site site on site.id=t.cxr_site_id
        <where>
            t.cxr_customer_id = #{customerId} and t.delete_status = '0'
            and site.delete_status=0  and site.online_status=1
            <if test="employeeIds !=null and employeeIds.size() >0 ">
                AND cxr_employee_id in
                <foreach collection="employeeIds" item="value" separator="," open="(" close=")">
                    #{value}
                </foreach>
            </if>

            <if test="siteIds !=null and siteIds.size() >0 ">
                AND cxr_site_id in
                <foreach collection="siteIds" item="value" separator="," open="(" close=")">
                    #{value}
                </foreach>
            </if>
        </where>


    </select>
    <select id="allmilkDistributionDetail"
            resultMap="addressVoResulMap">
        select t.* ,t2.id as t2_id,t2.name ,t2.product_code,t2.cxr_product_form_id,t2.product_alias
        from cxr_customer_address t
        left join cxr_site_sale_product t1 on t1.cxr_site_id = t.cxr_site_id
        left join cxr_sale_product t2 on (t1.cxr_sale_product_id=t2.id and t2.delete_status=#{del})
        <where>
            t.cxr_customer_id =#{id}
        </where>
        and t.delete_status=#{del}
    </select>
    <select id="pageAddressByIdsAndOther"
            resultMap="addressVoResulMap">
        select t.id,t.receiver_name,t.receiver_phone,t.update_time from cxr_customer_address t
        <where>
            t.id in
            <foreach collection="ids" item="item" separator="," index="i" close=")" open="(">
                #{item}
            </foreach>

            <if test="bo.receiverPhone!=null and bo.receiverPhone !=''">
                and receiver_phone like concat('%',#{bo.receiverPhone},'%')
            </if>
            <if test="bo.detailDistributionAddress!=null and bo.detailDistributionAddress!=''">
                detail_distribution_address like concat('%',#{bo.detailDistributionAddress},'%')
            </if>
        </where>

    </select>


    <select id="staffCustomerWarningPage"
            resultType="com.ruoyi.business.base.customerWarning.domain.vo.CustomerWarningVo">
        SELECT
        t.id,
        t.cxr_customer_id as customerId,
        t.receiver_name AS customerName,
        t.receiver_phone AS customerPhone,
        t.defalut_account_address,
        concat( t.provice, t.city, t.area, t.detail_distribution_address ) AS addressDetail,
        t1.customer_stock AS customerStock,
        IFNULL(date_add(t1.customer_stock_zero_time,interval 1 day),t1.expiration_time) AS expirationTime,
        t1.customer_stock_zero_time

        FROM (select cxr_customer_id cxr_customer_id from cxr_customer_address
        <where>
            <if test="bo.employeeId !=null">
                cxr_employee_id = #{bo.employeeId}
            </if>
        </where>
        group by cxr_customer_id )tt
        inner JOIN cxr_customer t1 ON t1.id = tt.cxr_customer_id
        left join cxr_customer_address t on t.cxr_customer_id = t1.id
        WHERE
        1 = 1
        <if test="bo.employeeId !=null">
            AND t.cxr_employee_id = #{bo.employeeId}
        </if>
        <if test="bo.customerPhone!=null and bo.customerPhone!=''">
            AND t.receiver_phone
            like concat('%',#{bo.customerPhone},'%')
        </if>
        <if test="bo.addressDetail!=null and bo.addressDetail!=''">
            AND (
            INSTR(
            t.provice,#{bo.addressDetail}) > 0

            OR INSTR(
            t.city,#{bo.addressDetail}) >0

            OR INSTR(
            t.area,#{bo.addressDetail}) > 0

            OR INSTR( t.detail_distribution_address, #{bo.addressDetail}) > 0
            )
        </if>

        <if test="bo.querySum!=null and bo.querySum==0 ">
            AND
            t1.customer_stock = 0
            and date_add(t1.customer_stock_zero_time,interval 14 day) >= CURRENT_DATE()
        </if>

        <if test="bo.querySum!=null and bo.querySum==10 ">
            AND (
            t1.customer_stock <![CDATA[ >= ]]> 1
            and t1.customer_stock <![CDATA[ <= ]]>  #{bo.querySum}
            and !(t.am_distribution_status = 'N' and
            t.am_distribution_suspend_start_time  <![CDATA[ < ]]> date_add(current_date, interval -30 day) and
            t.pm_distribution_status = 'N' and
            t.pm_distribution_suspend_start_time <![CDATA[ < ]]> date_add(current_date, interval -30 day)
            ))
        </if>
        and t1.delete_status=0
        ORDER BY
        t1.expiration_time desc
    </select>

    <select id="getStaffCustomerWarningPage"
            resultType="com.ruoyi.business.base.customerWarning.domain.vo.CustomerWarningVo">
        SELECT
        ca.id id,
        c.id customerId,
        c.name customerName,
        c.phone customerPhone,
        c.customer_stock,
        concat( ca.provice, ca.city, ca.area, ca.detail_distribution_address) addressDetail,
        IFNULL(date_add(c.customer_stock_zero_time,interval 1 day),c.expiration_time) AS expirationTime,
        c.customer_stock_zero_time customerStockZeroTime
        FROM cxr_customer_address ca
        INNER JOIN cxr_employee e on e.id = ca.cxr_employee_id
        INNER JOIN cxr_customer c ON c.id = ca.cxr_customer_id
        INNER JOIN cxr_site s on s.id = ca.cxr_site_id
        INNER JOIN cxr_region r on r.id = s.cxr_region_id
        <where>
            <include refid="whereSql"/>
        </where>
        ORDER BY expirationTime desc
    </select>

    <select id="staffCustomerWarningCountPageByRegion"
            resultType="com.ruoyi.business.base.customerWarning.domain.vo.CustomerWarningVo">
        SELECT
        r.`name` regionName,
        count(0) warningCount
        FROM cxr_customer_address ca
        INNER JOIN cxr_employee e on e.id = ca.cxr_employee_id
        INNER JOIN cxr_customer c ON c.id = ca.cxr_customer_id
        INNER JOIN cxr_site s on s.id = ca.cxr_site_id
        INNER JOIN cxr_region r on r.id = s.cxr_region_id
        <where>
            <include refid="whereSql"/>
        </where>
        GROUP BY regionName
    </select>

    <select id="staffCustomerWarningCountPageBySite"
            resultType="com.ruoyi.business.base.customerWarning.domain.vo.CustomerWarningVo">
        SELECT
        r.`name` regionName,
        s.`name` siteName,
        count(0) warningCount
        FROM cxr_customer_address ca
        INNER JOIN cxr_employee e on e.id = ca.cxr_employee_id
        INNER JOIN cxr_customer c ON c.id = ca.cxr_customer_id
        INNER JOIN cxr_site s on s.id = ca.cxr_site_id
        INNER JOIN cxr_region r on r.id = s.cxr_region_id
        <where>
            <include refid="whereSql"/>
        </where>
        GROUP BY regionName,siteName
    </select>

    <select id="staffCustomerWarningCountPageByEmployee"
            resultType="com.ruoyi.business.base.customerWarning.domain.vo.CustomerWarningVo">
        SELECT
        r.`name` regionName,
        s.`name` siteName,
        ca.cxr_employee_id employeeId,
        e.`name` employeeName,
        count(0) warningCount
        FROM cxr_customer_address ca
        INNER JOIN cxr_employee e on e.id = ca.cxr_employee_id
        INNER JOIN cxr_customer c ON c.id = ca.cxr_customer_id
        INNER JOIN cxr_site s on s.id = ca.cxr_site_id
        INNER JOIN cxr_region r on r.id = s.cxr_region_id
        <where>
            <include refid="whereSql"/>
        </where>
        GROUP BY regionName,siteName,employeeId,employeeName
    </select>

    <select id="staffCustomerWarningCountTotal" resultType="java.lang.Integer">
        SELECT
        count(0) warningCount
        FROM cxr_customer_address ca
        INNER JOIN cxr_employee e on e.id = ca.cxr_employee_id
        INNER JOIN cxr_customer c ON c.id = ca.cxr_customer_id
        INNER JOIN cxr_site s on s.id = ca.cxr_site_id
        INNER JOIN cxr_region r on r.id = s.cxr_region_id
        <where>
            <include refid="whereSql"/>
        </where>
    </select>

    <sql id="whereSql">
        and ca.delete_status=0
        <if test="bo.regionName!=null">
            AND r.`name` = #{bo.regionName}
        </if>
        <if test="bo.siteName!=null">
            AND s.`name` = #{bo.siteName}
        </if>
        <if test="bo.querySum!=null and bo.querySum==0 ">
            AND c.customer_stock = 0
            AND c.customer_stock_zero_time >= date_add(CURRENT_DATE(),interval -14 day)
        </if>
        <if test="bo.querySum!=null and bo.querySum==10 ">
            AND c.customer_stock BETWEEN 1 and 10
            AND !(
            ca.am_distribution_status = 'N' and ca.am_distribution_suspend_end_time is null and
            ca.pm_distribution_status = 'N' and ca.pm_distribution_suspend_end_time is null
            )
        </if>
        <if test="bo.authSiteIds!=null and bo.authSiteIds.size>0">
            AND ca.cxr_site_id in
            <foreach collection="bo.authSiteIds" index="index" item="authSiteId" open="(" separator="," close=")">
                #{authSiteId,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="bo.customerPhone!=null and bo.customerPhone!=''">
            AND ca.receiver_phone = #{bo.customerPhone}
        </if>
        <if test="bo.employeeId!=null">
            AND ca.cxr_employee_id = #{bo.employeeId}
        </if>
        <if test="bo.employeeName!=null and bo.employeeName!=''">
            AND e.`name` like concat('%',#{bo.employeeName},'%')
        </if>
        <if test="bo.addressDetail!=null and bo.addressDetail!=''">
            AND concat(ca.provice, ca.city, ca.area, ca.detail_distribution_address) like
            concat('%',#{bo.addressDetail},'%')
        </if>
        <if test="bo.regionIds!=null and bo.regionIds.size>0">
            AND and r.id in
            <foreach collection="bo.regionIds" index="index" item="regionId" open="(" separator="," close=")">
                #{regionId,jdbcType=BIGINT}
            </foreach>
        </if>
    </sql>


    <select id="getAddressids" resultType="com.ruoyi.business.base.api.domain.vo.CxrAddressVo">
        SELECT a.id,
               e.NAME  as       name,
               e.phone as       phone,
               a.receiver_phone,
               a.receiver_name,
               a.cxr_customer_id,
               a.provice,
               a.city,
               a.area,
               a.cxr_site_id,
               r.name  as       rname,
               a.detail_distribution_address,
               a.create_by_type addressCreateByType,
               a.delete_status
        FROM cxr_customer_address a
                 LEFT JOIN cxr_employee e ON e.id = a.cxr_employee_id
                 LEFT JOIN cxr_residential_quarters r on a.cxr_residential_quarters_id = r.id
        WHERE a.cxr_customer_id = #{bo.cxrCustomerId}
          and e.delete_status = 0
          and a.delete_status = 0
    </select>

    <select id="checkmilkDistributionDetail"
            resultMap="addressVoResulBo">
        select t.* ,t2.id as t2_id,t2.name ,t2.product_code,t2.cxr_product_form_id,t2.product_alias, t.create_by_type
        addressCreateByType
        from cxr_customer_address t
        left join cxr_site_sale_product t1 on t1.cxr_site_id = t.cxr_site_id
        left join cxr_sale_product t2 on (t1.cxr_sale_product_id=t2.id and t2.delete_status=#{del})
        <where>
            t.cxr_customer_id =#{id}
        </where>
        and t.delete_status=#{del}
    </select>

    <update id="updateChangeMilk">
        update cxr_customer_address
        <set>
            receiver_phone=#{bo.receiverPhone},
            am_distribution_info=#{bo.amDistributionInfo ,typeHandler=com.ruoyi.business.base.api.typeHandler.TypeHandlerConstant$CustomerAddressMilkDistributionInfoTypeHandler},
            pm_distribution_info=#{bo.pmDistributionInfo,typeHandler=com.ruoyi.business.base.api.typeHandler.TypeHandlerConstant$CustomerAddressMilkDistributionInfoTypeHandler},
            update_by=#{bo.updateBy},
            update_by_name=#{bo.updateByName},
            update_by_type=#{bo.updateByType},
            update_time=now(),
        </set>
        where id=#{bo.id}
    </update>
    <update id="updateAmChangeSendOrStop">
        update cxr_customer_address
        <set>
            am_distribution_status=#{bo.amDistributionStatus},
            am_distribution_start_delivery_time=#{bo.amDistributionStartDeliveryTime},
            am_distribution_suspend_start_time=#{bo.amDistributionSuspendStartTime},
            am_distribution_suspend_end_time=#{bo.amDistributionSuspendEndTime},
            update_by=#{bo.updateBy},
            update_by_name=#{bo.updateByName},
            update_by_type=#{bo.updateByType},
            update_time=now(),
        </set>
        where id=#{bo.id}
    </update>
    <update id="updatePmChangeSendOrStop">
        update cxr_customer_address
        <set>
            pm_distribution_status=#{bo.pmDistributionStatus},
            pm_distribution_start_delivery_time=#{bo.pmDistributionStartDeliveryTime},
            pm_distribution_suspend_start_time=#{bo.pmDistributionSuspendStartTime},
            pm_distribution_suspend_end_time=#{bo.pmDistributionSuspendEndTime},
            update_by=#{bo.updateBy},
            update_by_name=#{bo.updateByName},
            update_by_type=#{bo.updateByType},
            update_time=now(),
        </set>
        where id=#{bo.id}
    </update>

    <update id="updateAmChangeSendOrStopChangeRecord">
        update cxr_customer_change_record
        <set>
            am_distribution_status=#{bo.amDistributionStatus},
            am_distribution_start_delivery_time=#{bo.amDistributionStartDeliveryTime},
            am_distribution_suspend_start_time=#{bo.amDistributionSuspendStartTime},
            am_distribution_suspend_end_time=#{bo.amDistributionSuspendEndTime},
            update_by=#{bo.updateBy},
            update_by_name=#{bo.updateByName},
            update_by_type=#{bo.updateByType},
            update_time=now(),
        </set>
        where customer_address_id=#{bo.id} and delete_status=0 ORDER BY effect_time desc limit 1
    </update>

    <update id="updatePmChangeSendOrStopChangeCustomer">
        update cxr_customer_change_record
        <set>
            pm_distribution_status=#{bo.pmDistributionStatus},
            pm_distribution_start_delivery_time=#{bo.pmDistributionStartDeliveryTime},
            pm_distribution_suspend_start_time=#{bo.pmDistributionSuspendStartTime},
            pm_distribution_suspend_end_time=#{bo.pmDistributionSuspendEndTime},
            update_by=#{bo.updateBy},
            update_by_name=#{bo.updateByName},
            update_by_type=#{bo.updateByType},
            update_time=now(),
        </set>
        where customer_address_id=#{bo.id} and delete_status=0 ORDER BY effect_time desc limit 1
    </update>

    <update id="updateChangeMilkChangeCustomer">
        update cxr_customer_change_record
        <set>
            am_distribution_info=#{bo.amDistributionInfo ,typeHandler=com.ruoyi.business.base.api.typeHandler.TypeHandlerConstant$CustomerAddressMilkDistributionInfoTypeHandler},
            pm_distribution_info=#{bo.pmDistributionInfo,typeHandler=com.ruoyi.business.base.api.typeHandler.TypeHandlerConstant$CustomerAddressMilkDistributionInfoTypeHandler},
            update_by=#{bo.updateBy},
            update_by_name=#{bo.updateByName},
            update_by_type=#{bo.updateByType},
            update_time=now(),
        </set>
        where customer_address_id=#{bo.id} and delete_status=0 ORDER BY effect_time desc limit 1
    </update>

    <update id="updateAddressSerialNumber">
        update cxr_customer_address b inner join (select tt.id, @rank := @rank + 1 AS rank_no
                                                  from (select t.id
                                                        from cxr_customer_address t
                                                        where t.cxr_customer_id = #{id}
                                                        order by t.defalut_account_address desc, t.create_time) tt,
                                                       (select @rank := 0) a) aa
            on aa.id = b.id
        set address_serial_number = rank_no
        where b.cxr_customer_id = #{id};
    </update>

    <update id="updateAddressSerialNumberBatch">
        <foreach collection="ids" item="value" separator="," open="(" close=")">
            update cxr_customer_address b inner join (select tt.id, @rank := @rank + 1 AS rank_no
            from (select t.id
            from cxr_customer_address t
            where t.cxr_customer_id = #{id}
            order by t.defalut_account_address desc, t.create_time) tt,
            (select @rank := 0) a) aa
            on aa.id = b.id
            set address_serial_number = rank_no
            where b.cxr_customer_id = #{value};
        </foreach>

    </update>

    <select id="customerWarningPage"
            resultType="com.ruoyi.business.base.customerWarning.domain.vo.CustomerWarningPageVo">
        SELECT
        t.id,
        any_value(t.receiver_phone) AS customerPhone,
        any_value(t.cxr_customer_id) AS customerId,
        any_value(t.receiver_name) AS customerName,

        any_value(t.defalut_account_address) as defalutAccountAddress,
        any_value(t.provice )as provice,
        any_value(t.city) as city,
        any_value(t.area) as area,
        any_value(t.detail_distribution_address) AS detailDistributionAddress,
        any_value(t2.NAME) AS siteName,
        any_value(t2.site_mark) AS siteMark,
        any_value(t2.cxr_region_id) as cxrRegionId,
        any_value(t3.NAME) AS employeeName,
        any_value(t3.job_number) as jobNumber,
        any_value(t4.dept_name) AS companyName,
        any_value(t1.customer_stock) AS customerStock,
        any_value(t.am_distribution_status) as amDistributionStatus ,
        any_value(t.pm_distribution_status) as pmDistributionStatus,
        any_value(t.am_friday) as amFriday,
        any_value(t.am_monday) as amMonday,
        any_value(t.am_tuesday) as amTuesday,
        any_value(t.am_saturday) as amSaturday,
        any_value(t.am_thursday) as amThursday,
        any_value(t.am_wednesday) as amWednesday,
        any_value(t.pm_friday) as pmFriday,
        any_value(t.pm_monday) as pmMonday,
        any_value(t.pm_tuesday) as pmTuesday,
        any_value(t.pm_saturday) as pmSaturday,
        any_value(t.pm_thursday) as pmThursday,
        any_value(t.pm_wednesday) as pmWednesday,
        CASE

        WHEN any_value(t.pm_distribution_status) = "N"
        AND any_value(t.am_distribution_status )= "N" THEN
        "停送" ELSE "启送"
        END AS distributionStatus,
        CASE

        WHEN any_value(t.pm_distribution_status) = "N"
        AND any_value(t.am_distribution_status) = "N" THEN
        "长期停奶" ELSE date_format( any_value(t1.expiration_time), '%Y-%m-%d' )
        END AS expirationTime,
        /* 起送日期 */
        CASE
        WHEN any_value(t.am_distribution_status) = 'Y' AND any_value(t.pm_distribution_status) = 'Y' THEN
        date_format(LEAST(any_value(t.am_distribution_start_delivery_time),
        any_value(t.pm_distribution_start_delivery_time)), '%Y-%m-%d' )
        WHEN any_value(t.am_distribution_status) = 'Y' THEN
        date_format(any_value(t.am_distribution_start_delivery_time), '%Y-%m-%d' )
        WHEN any_value(t.pm_distribution_status) = 'Y' THEN
        date_format(any_value(t.pm_distribution_start_delivery_time), '%Y-%m-%d' )
        ELSE NULL
        END AS sendTime,
        /* 暂停日期 */
        CASE
        WHEN any_value(t.am_distribution_status) = 'N' AND any_value(t.pm_distribution_status) = 'N' THEN
        CASE
        WHEN any_value(t.am_distribution_suspend_start_time) > any_value(t.pm_distribution_suspend_start_time) THEN
        CONCAT(
        DATE_FORMAT(any_value(t.am_distribution_suspend_start_time), '%Y-%m-%d'),
        ' 至 ',
        IFNULL(DATE_FORMAT(any_value(t.am_distribution_suspend_end_time), '%Y-%m-%d'), '长期')
        )
        ELSE
        CONCAT(
        DATE_FORMAT(any_value(t.pm_distribution_suspend_start_time), '%Y-%m-%d'),
        ' 至 ',
        IFNULL(DATE_FORMAT(any_value(t.pm_distribution_suspend_end_time), '%Y-%m-%d'), '长期')
        )
        END
        ELSE NULL
        END AS pauseTime


        FROM
        cxr_customer_address t
        Inner JOIN cxr_customer t1 ON t1.id = t.cxr_customer_id
        LEFT JOIN cxr_site t2 ON t2.id = t.cxr_site_id
        LEFT JOIN cxr_employee t3 ON t3.id = t.cxr_employee_id
        LEFT JOIN sys_dept t4 on t2.current_dept_id=t4.dept_id
        <include refid="customerWarningPageWhere"></include>
        GROUP BY t.id
        ORDER BY
        any_value( t.create_time) DESC
    </select>

    <sql id="customerWarningPageWhere">
        WHERE
        1 = 1
        <if test="bo.customerId!=null and bo.customerId!=''">
            AND t.cxr_customer_id = #{bo.customerId}
        </if>
        <if test="bo.customerPhone!=null and bo.customerPhone!=''">
            AND t.receiver_phone
            like concat('%',#{bo.customerPhone},'%')
        </if>


        <if test="bo.deptId!=null and bo.deptId!=''">
            AND t2.current_dept_id =#{bo.deptId}
        </if>

        <if test="bo.provice!=null and bo.provice!=''">
            and t.provice like concat('%',#{bo.provice,jdbcType=VARCHAR},'%')
        </if>

        <if test="bo.city!=null and bo.city!=''">
            and t.city like concat('%',#{bo.city,jdbcType=VARCHAR},'%')
        </if>

        <if test="bo.area!=null and bo.area!=''">
            and t.area like concat('%',#{bo.area,jdbcType=VARCHAR},'%')
        </if>


        <if test="bo.cxrRootRegionId!=null and bo.cxrRootRegionId!=''">
            and t2.cxr_region_id=#{bo.cxrRootRegionId}
        </if>

        <if test="bo.employeeName!=null and bo.employeeName!=''">
            and t3.name like concat('%',#{bo.employeeName,jdbcType=VARCHAR},'%')
        </if>

        <if test="bo.siteName!=null and bo.siteName!=''">
            and t2.name like concat('%',#{bo.siteName,jdbcType=VARCHAR},'%')
        </if>


        <if test="bo.detailDistributionAddress!=null and bo.detailDistributionAddress!=''">
            AND (
            INSTR(
            t.provice,#{bo.detailDistributionAddress}) > 0

            OR INSTR(
            t.city,#{bo.detailDistributionAddress}) >0

            OR INSTR(
            t.area,#{bo.detailDistributionAddress}) > 0

            OR INSTR( t.detail_distribution_address, #{bo.detailDistributionAddress}) > 0
            )
        </if>


        <if test="bo.defalutAccountAddress!=null and bo.defalutAccountAddress!=''">
            AND t.defalut_account_address=#{bo.defalutAccountAddress}
        </if>


        <if test="bo.querySum!=null and bo.querySum==0 ">
            AND
            t1.customer_stock <![CDATA[ = ]]> 0

        </if>

        <if test="bo.querySum!=null and bo.querySum==10 ">
            AND (
            t1.customer_stock <![CDATA[ > ]]> 0
            and t1.customer_stock <![CDATA[ <= ]]>  #{bo.querySum}
            )
        </if>

        <if test="bo.querySum!=null and bo.querySum==11 ">
            AND (
            t1.customer_stock <![CDATA[ > ]]> 10
            )
        </if>

        <if test="bo.querySum!=null and bo.querySum==12 ">
            AND (
            t1.customer_stock <![CDATA[ > ]]> 0
            )
        </if>

        <if test="bo.startExpirationTime != null">
            and t1.expiration_time  <![CDATA[ >= ]]> #{bo.startExpirationTime}
        </if>

        <if test="bo.endExpirationTime != null">
            and t1.expiration_time <![CDATA[ <= ]]> #{bo.endExpirationTime}
        </if>


        <if test="bo.distributionState!=null and bo.distributionState==1 ">
            AND (
            SELECT
            CASE

            WHEN
            ad.pm_distribution_status ="N"
            AND ad.am_distribution_status ="N"

            THEN
            "1" ELSE "0"
            END AS distributionStatus
            FROM
            cxr_customer_address ad
            WHERE
            ad.cxr_customer_id = t.cxr_customer_id
            limit 1
            ) = "1"
        </if>


        <if test="bo.distributionState!=null and bo.distributionState==0 ">
            AND (
            SELECT
            CASE

            WHEN
            ad.pm_distribution_status ="N"
            AND ad.am_distribution_status ="N"

            THEN
            "1" ELSE "0"
            END AS distributionStatus
            FROM
            cxr_customer_address ad
            WHERE
            ad.cxr_customer_id = t.cxr_customer_id
            limit 1
            ) = "0"
        </if>

        /* 起送日期筛选 */
        <if test="bo.startSendTime != null or bo.endSendTime != null">
            AND CASE
            WHEN t.am_distribution_status = 'Y' AND t.pm_distribution_status = 'Y' THEN
            LEAST(t.am_distribution_start_delivery_time, t.pm_distribution_start_delivery_time)
            <if test="bo.startSendTime != null">
                >= #{bo.startSendTime}
            </if>
            <if test="bo.endSendTime != null">
                <![CDATA[ <= ]]> #{bo.endSendTime}
            </if>
            WHEN t.am_distribution_status = 'Y' THEN
            t.am_distribution_start_delivery_time
            <if test="bo.startSendTime != null">
                >= #{bo.startSendTime}
            </if>
            <if test="bo.endSendTime != null">
                <![CDATA[ <= ]]> #{bo.endSendTime}
            </if>
            WHEN t.pm_distribution_status = 'Y' THEN
            t.pm_distribution_start_delivery_time
            <if test="bo.startSendTime != null">
                >= #{bo.startSendTime}
            </if>
            <if test="bo.endSendTime != null">
                <![CDATA[ <= ]]> #{bo.endSendTime}
            </if>
            ELSE 1=0
            END
        </if>

        /* 暂停日期筛选 */
        <if test="bo.startPauseTime != null or bo.endPauseTime != null">
            AND CASE
            WHEN t.am_distribution_status = 'N' AND t.pm_distribution_status = 'N' THEN
            GREATEST(t.am_distribution_suspend_start_time, t.pm_distribution_suspend_start_time)
            <if test="bo.startPauseTime != null">
                >= #{bo.startPauseTime}
            </if>
            <if test="bo.endPauseTime != null">
                <![CDATA[ <= ]]> #{bo.endPauseTime}
            </if>
            ELSE 1=0
            END
        </if>
        and t.delete_status=0


    </sql>

    <select id="totalCustomerStockSum" resultType="java.lang.Long">
        select ifnull(sum(t1.customer_stock), 0)
        FROM
        cxr_customer t1
        LEFT JOIN cxr_customer_address t ON t1.id = t.cxr_customer_id
        <if test="bo.defalutAccountAddress!=null and bo.defalutAccountAddress == 'Y'">
            and defalut_account_address='Y'
        </if>

        LEFT JOIN cxr_site t2 ON t2.id = t.cxr_site_id
        LEFT JOIN cxr_employee t3 ON t3.id = t.cxr_employee_id
        LEFT JOIN sys_dept t4 on t2.current_dept_id=t4.dept_id

        <include refid="customerWarningPageWhere"></include>
        ORDER BY
        t.create_time DESC
    </select>

    <select id="findbyDistributionSuspendEndTime"
            resultType="com.ruoyi.business.base.api.domain.CxrCustomerAddress">
        select *
        from cxr_customer_address t
        where <![CDATA[ am_distribution_suspend_end_time <= #{localDate} ]]> and am_distribution_suspend_end_time is not null
        union
        select *
        from cxr_customer_address t
        where <![CDATA[ pm_distribution_suspend_end_time <= #{localDate} ]]> and pm_distribution_suspend_end_time is not null

    </select>


    <select id="milkDistributionDetailedTotal"
            resultType="com.ruoyi.business.base.customer.domain.vo.CustomerTotalVo">

        SELECT (SELECT ifnull(
                                       sum(ifnull(t1.order_quantity, 0)) -
                                       sum(ifnull(t1.fresh_milk_sent_quantity, 0)) +
                                       sum(ifnull(t1.fresh_milk_give_quantity, 0)) -
                                       sum(ifnull(t1.milk_exchange_sum, 0)) -
                                       sum(ifnull(t1.conversion_quantity, 0)) -
                                       sum(ifnull(t1.fresh_milk_return_quantity, 0))
                           , 0)
                FROM cxr_user_order t1
                WHERE t1.customer_id = #{bo.customerId}
                  and (t1.pay_status = 3 or t1.audit_status = 2)
                  AND t1.delete_status = 0)                  AS totalOrderQuantity,

               (SELECT ifnull(sum(t2.total_sum), 0)
                FROM cxr_customer_distribution_list_record t2
                WHERE t2.cxr_customer_id = #{bo.customerId}) AS totalFreshMilkSentQuantity,

               (SELECT ifnull(sum(t2.milk_distribution_total), 0)
                FROM cxr_road_way t2
                WHERE t2.cxr_customer_id = #{bo.customerId}
                  and t2.distribution_date &lt;= #{bo.getToday}
                  and t2.status = 0
                  and t2.change_status = -1
                  AND t2.delete_status = 0)                  AS totalNotSentQuantity,


               (SELECT ifnull(sum(t2.total_sum), 0)
                FROM cxr_customer_distribution_list_record t2
                WHERE t2.customer_address_id = #{bo.addressId}
                  AND t2.monthmark = 1)                      AS TotalJanuary,


               (SELECT ifnull(sum(t2.total_sum), 0)
                FROM cxr_customer_distribution_list_record t2
                WHERE t2.customer_address_id = #{bo.addressId}
                  AND t2.monthmark = 2)                      AS TotalFebruary,

               (SELECT ifnull(sum(t2.total_sum), 0)
                FROM cxr_customer_distribution_list_record t2
                WHERE t2.customer_address_id = #{bo.addressId}
                  AND t2.monthmark = 3)                      AS TotalMarch,


               (SELECT ifnull(sum(t2.total_sum), 0)
                FROM cxr_customer_distribution_list_record t2
                WHERE t2.customer_address_id = #{bo.addressId}
                  AND t2.monthmark = 4)                      AS TotalApril,


               (SELECT ifnull(sum(t2.total_sum), 0)
                FROM cxr_customer_distribution_list_record t2
                WHERE t2.customer_address_id = #{bo.addressId}
                  AND t2.monthmark = 5)                      AS TotalMay,


               (SELECT ifnull(sum(t2.total_sum), 0)
                FROM cxr_customer_distribution_list_record t2
                WHERE t2.customer_address_id = #{bo.addressId}
                  AND t2.monthmark = 6)                      AS TotalJune,


               (SELECT ifnull(sum(t2.total_sum), 0)
                FROM cxr_customer_distribution_list_record t2
                WHERE t2.customer_address_id = #{bo.addressId}
                  AND t2.monthmark = 7)                      AS TotalJuly,

               (SELECT ifnull(sum(t2.total_sum), 0)
                FROM cxr_customer_distribution_list_record t2
                WHERE t2.customer_address_id = #{bo.addressId}
                  AND t2.monthmark = 8)                      AS TotalAugust,


               (SELECT ifnull(sum(t2.total_sum), 0)
                FROM cxr_customer_distribution_list_record t2
                WHERE t2.customer_address_id = #{bo.addressId}
                  AND t2.monthmark = 9)                      AS TotalSeptember,


               (SELECT ifnull(sum(t2.total_sum), 0)
                FROM cxr_customer_distribution_list_record t2
                WHERE t2.customer_address_id = #{bo.addressId}
                  AND t2.monthmark = 10)                     AS TotalOctober,


               (SELECT ifnull(sum(t2.total_sum), 0)
                FROM cxr_customer_distribution_list_record t2
                WHERE t2.customer_address_id = #{bo.addressId}
                  AND t2.monthmark = 11)                     AS TotalNovember,


               (SELECT ifnull(sum(t2.total_sum), 0)
                FROM cxr_customer_distribution_list_record t2
                WHERE t2.customer_address_id = #{bo.addressId}
                  AND t2.monthmark = 12)                     AS TotalDecember,

               lock_stock,
               ifnull(t.customer_stock, 0)                   as totalSurplusQuantity
        FROM cxr_customer t
        where t.id = #{bo.customerId}


    </select>
    <select id="getAddressIdByEmIdAndBo" resultType="com.ruoyi.business.base.api.domain.CxrAddressHistory">
        select history.id
        from (
            select history.id
            from cxr_address_history history
            where history.cxr_customer_address_id in(
                select t.id from
                cxr_customer_address t
                <where>
                    t.cxr_employee_id=#{id} and t.delete_status=#{del}
                    <if test="bo.receiverPhone!=null and  bo.receiverPhone!=''">
                        and t.receiver_phone like concat('%',#{bo.receiverPhone},'%')
                    </if>
                    <if test="bo.detailDistributionAddress !=null and bo.detailDistributionAddress!='' ">
                        and (
                        t.detail_distribution_address like concat('%',#{bo.detailDistributionAddress},'%')
                        or t.provice like concat('%',#{bo.detailDistributionAddress},'%')
                        or t.city like concat('%',#{bo.detailDistributionAddress},'%')
                        or t.area like concat('%',#{bo.detailDistributionAddress},'%')
                        )
                    </if>
                </where>
            )
            <if test="bo.type != null and bo.type==1">
                and history.addr_update_flag = 1
            </if>
            <if test="bo.type != null and bo.type==2">
                and history.phone_update_flag = 1
            </if>
            <if test="bo.type != null and bo.type==3">
                and history.milk_update_flag = 1
            </if>
            <if test="bo.type != null and bo.type==4">
                and history.am_pm_update_flag = 1
            </if>
            <if test="bo.type != null and bo.type==5">
                and history.bing_update_flag = 1
            </if>
            union
            select history.id
            from cxr_address_history history
            where history.cxr_customer_address_id is null
            and history.cxr_customer_id in(
                select t.cxr_customer_id from
                cxr_customer_address t
                <where>
                    t.cxr_employee_id=#{id}
                    and t.delete_status=#{del}
                    <if test="bo.receiverPhone!=null and  bo.receiverPhone!=''">
                        and t.receiver_phone like concat('%',#{bo.receiverPhone},'%')
                    </if>
                    <if test="bo.detailDistributionAddress !=null and bo.detailDistributionAddress!='' ">
                        and (
                        t.detail_distribution_address like concat('%',#{bo.detailDistributionAddress},'%')
                        or t.provice like concat('%',#{bo.detailDistributionAddress},'%')
                        or t.city like concat('%',#{bo.detailDistributionAddress},'%')
                        or t.area like concat('%',#{bo.detailDistributionAddress},'%')
                        )
                    </if>
                </where>
            )
            <if test="bo.type != null and bo.type==1">
                and history.addr_update_flag = 1
            </if>
            <if test="bo.type != null and bo.type==2">
                and history.phone_update_flag = 1
            </if>
            <if test="bo.type != null and bo.type==3">
                and history.milk_update_flag = 1
            </if>
            <if test="bo.type != null and bo.type==4">
                and history.am_pm_update_flag = 1
            </if>
            <if test="bo.type != null and bo.type==5">
                and history.bing_update_flag = 1
            </if>
        ) as history
        order by history.id desc
    </select>


    <sql id="History_Base_Column_List">
        id,alter_am_taste,alter_pm_taste,alter_record,cxr_customer_address_id,cxr_customer_id,
        sys_dept_id,revision,create_by,create_by_name,create_by_type,create_time,
        update_by,update_by_name,update_by_type,update_time,delete_by,delete_by_name,
        delete_by_type,delete_time,delete_status,sort_num,remark,spare_id,
        pm_milk_distribution_info,am_milk_distribution_info,am_stop_start_date,
        am_stop_end_date,am_send_date,pm_stop_start_date,pm_stop_end_date,
        pm_send_date,pm_distribution_status,am_distribution_status,effect_time,
        this_address
    </sql>

    <select id="getAddressIdByEmIdAndBoNew" resultType="com.ruoyi.business.base.api.domain.CxrAddressHistory">
        select s.*
        from
        (
            <if test="bo.type ==null or bo.type == 1">
                SELECT
                <include refid="History_Base_Column_List" />,
                1 AS type
                FROM cxr_address_history
                WHERE addr_update_flag = 1
                <if test="bo.customerAddrIds !=null and bo.customerAddrIds.size()>0">
                    and
                    cxr_customer_address_id in
                    <foreach collection="bo.customerAddrIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="bo.ids !=null and bo.ids.size()>0">
                    and
                    id in
                    <foreach collection="bo.ids" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>

                UNION ALL
            </if>

            <if test="bo.type == null or bo.type == 2">
                SELECT
                <include refid="History_Base_Column_List" />,
                2 AS type
                FROM cxr_address_history
                WHERE phone_update_flag = 1
                <if test="bo.customerIds !=null and bo.customerIds.size()>0">
                    and
                    cxr_customer_id in
                    <foreach collection="bo.customerIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="bo.ids !=null and bo.ids.size()>0">
                    and
                    id in
                    <foreach collection="bo.ids" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                UNION ALL
            </if>
            <if test="bo.type == null or bo.type == 3">
                SELECT
                <include refid="History_Base_Column_List" />,
                3 AS type
                FROM cxr_address_history
                WHERE milk_update_flag = 1
                <if test="bo.customerAddrIds !=null and bo.customerAddrIds.size()>0">
                    and
                    cxr_customer_address_id in
                    <foreach collection="bo.customerAddrIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="bo.ids !=null and bo.ids.size()>0">
                    and
                    id in
                    <foreach collection="bo.ids" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                UNION ALL
            </if>
            <if test="bo.type == null or bo.type == 4">
                SELECT
                <include refid="History_Base_Column_List" />,
                4 AS type
                FROM cxr_address_history
                WHERE am_pm_update_flag = 1
                <if test="bo.customerAddrIds !=null and bo.customerAddrIds.size()>0">
                    and
                    cxr_customer_address_id in
                    <foreach collection="bo.customerAddrIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="bo.ids !=null and bo.ids.size()>0">
                    and
                    id in
                    <foreach collection="bo.ids" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                UNION ALL
            </if>

            <if test="bo.type == null or   bo.type == 5">
                SELECT
                <include refid="History_Base_Column_List" />,
                5 AS type
                FROM cxr_address_history
                WHERE bing_update_flag = 1
                <if test="bo.customerIds !=null and bo.customerIds.size()>0">
                    and
                    cxr_customer_id in
                    <foreach collection="bo.customerIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="bo.ids !=null and bo.ids.size()>0">
                    and
                    id in
                    <foreach collection="bo.ids" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                UNION ALL
            </if>

            SELECT
            <include refid="History_Base_Column_List" />,
            -1 AS type
            FROM cxr_address_history
            WHERE bing_update_flag = 0 and milk_update_flag = 0 and am_pm_update_flag = 0 and phone_update_flag = 0 and addr_update_flag = 0
            <if test="bo.customerIds !=null and bo.customerIds.size()>0">
                and cxr_customer_id in
                <foreach collection="bo.customerIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="bo.ids !=null and bo.ids.size()>0">
                    and
                    id in
                    <foreach collection="bo.ids" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
        ) as s
    </select>

    <select id="cxrCustomerAddressDTOList" resultType="com.ruoyi.business.base.api.domain.dto.CxrCustomerAddressDTO">
        SELECT d.cxr_employee_id, e.name cxrEmployeeName
        FROM cxr_customer_address d
                 INNER JOIN cxr_employee e ON d.cxr_employee_id = e.id
        WHERE d.cxr_customer_id = #{customerId}
    </select>
    <select id="queryDetailAddressByCustomerId" resultType="java.lang.String">
        select detail_distribution_address
        from cxr_customer_address
        where cxr_customer_id = #{customerId}
          and defalut_account_address = 'Y';
    </select>


    <update id="updateByCustomerId">
        UPDATE cxr_customer_address
        SET am_distribution_start_delivery_time = IF(am_distribution_status = 'Y', #{deliveryDate},
                                                     am_distribution_start_delivery_time),
            pm_distribution_start_delivery_time = IF(pm_distribution_status = 'Y', #{deliveryDate},
                                                     pm_distribution_start_delivery_time)
        WHERE cxr_customer_id = #{customerId}
    </update>


    <update id="updateBatcham">
        <![CDATA[
        update cxr_customer_address t
        set t.am_distribution_status              = 'Y',
            t.am_distribution_start_delivery_time =#{deliveryTime},
            t.am_distribution_suspend_start_time  =null,
            t.am_distribution_suspend_end_time=null
        where t.am_distribution_suspend_end_time <= #{suspendEndTime}
          and t.am_distribution_suspend_end_time is not null
        ]]>
    </update>
    <update id="updateBatchpm">
        <![CDATA[
        update cxr_customer_address t
        set t.pm_distribution_status='Y',
            t.pm_distribution_start_delivery_time=#{deliveryTime},
            t.pm_distribution_suspend_start_time=null,
            t.pm_distribution_suspend_end_time=null
        where t.pm_distribution_suspend_end_time <= #{suspendEndTime}
          and t.pm_distribution_suspend_end_time is not null
        ]]>

    </update>

    <update id="updateCustomerReceiverPhone">
        update cxr_customer_address
        <set>
            receiver_phone=#{bo.receiverPhone},
            update_by=#{bo.updateBy},
            update_by_name=#{bo.updateByName},
            update_by_type=#{bo.updateByType},
            update_time=now(),
        </set>
        where id=#{bo.id}
    </update>


    <select id="customerAddressList" resultType="com.ruoyi.business.base.api.domain.CxrCustomerAddress">
        SELECT id,
               cxr_customer_id,
               receiver_name,
               receiver_phone,
               detail_distribution_address,
               cxr_residential_quarters_id,
               sys_area_id,
               cxr_site_id,
               cxr_employee_id,
               provice,
               city,
               area,
               defalut_account_address,
               am_distribution_info,
               pm_distribution_info,
               address_serial_number
        FROM cxr_customer_address
        where (defalut_account_address = 'Y' OR receiver_phone = #{phone})
          and cxr_customer_id = #{userId}
          AND delete_status = '0'

    </select>

    <update id="updateCustomerAddressMilkInfo">
        <foreach collection="addresses" index="i" item="bo" separator=";">
            update cxr_customer_address
            <set>
                is_show_am_distribution=#{bo.isShowAmDistribution},
                am_distribution_status=#{bo.amDistributionStatus},
                am_distribution_start_delivery_time=#{bo.amDistributionStartDeliveryTime},
                am_distribution_suspend_start_time=#{bo.amDistributionSuspendStartTime},
                am_distribution_suspend_end_time=#{bo.amDistributionSuspendEndTime},
                is_show_pm_distribution=#{bo.isShowPmDistribution},
                pm_distribution_status=#{bo.pmDistributionStatus},
                pm_distribution_start_delivery_time=#{bo.pmDistributionStartDeliveryTime},
                pm_distribution_suspend_start_time=#{bo.pmDistributionSuspendStartTime},
                pm_distribution_suspend_end_time=#{bo.pmDistributionSuspendEndTime},
                am_distribution_info=#{bo.amDistributionInfo ,typeHandler=com.ruoyi.business.base.api.typeHandler.TypeHandlerConstant$CustomerAddressMilkDistributionInfoTypeHandler},
                pm_distribution_info=#{bo.pmDistributionInfo,typeHandler=com.ruoyi.business.base.api.typeHandler.TypeHandlerConstant$CustomerAddressMilkDistributionInfoTypeHandler},
                change_status=#{bo.changeStatus},
                update_time=now(),
            </set>
            where id=#{bo.id}
        </foreach>


    </update>


    <select id="getCustomerAddressLoglat" resultType="com.ruoyi.business.base.api.domain.CxrCustomerAddress">
        SELECT id, provice, city, area, detail_distribution_address
        from cxr_customer_address
        where delete_status = 0
          and longitude is null
          and id > #{id}
        order by id
        limit #{limit}
    </select>

    <select id="getByCustomerId"
            resultType="com.ruoyi.business.base.customerAddress.domain.vo.CustomerAddressDetailVo">
        SELECT id,
               cxr_customer_id,
               receiver_name,
               receiver_phone,
               detail_distribution_address,
               provice,
               city,
               area,
               cxr_site_id,
               cxr_employee_id
        from cxr_customer_address
        where cxr_customer_id = #{id}
          and delete_status = 0
    </select>

    <select id="getCustomerDistanceByLngLat"
            resultType="com.ruoyi.business.base.customerAddress.domain.vo.CxrCustomerAddressVo">
        SELECT
        cxr_customer_id,latitude, longitude,cxr_site_id,cxr_employee_id,
        st_distance(POINT(#{longitude},#{latitude}), POINT(longitude,latitude)) * 111195 AS distance
        FROM cxr_customer_address
        WHERE 1=1
        <if test="siteId!=null">
            and cxr_site_id = #{siteId}
        </if>
        <if test="orderCustomerId!=null">
            and cxr_customer_id != #{orderCustomerId}
        </if>
        and delete_status =0
        and cxr_employee_id is not null
        and longitude BETWEEN #{beginLongitude} and #{endLongitude}
        and latitude BETWEEN #{beginLatitude} and #{endLatitude}
        ORDER BY distance LIMIT 1
    </select>

    <select id="customerPageQuery"
            resultType="com.ruoyi.business.base.customerAddress.domain.vo.CxrCustomerAddressQueryVo">
        select
        receiver_name as name,
        receiver_phone,
        cxr_customer_id,
        provice,
        city,
        area,
        detail_distribution_address,
        receiver_phone,
        cxr_site_id,
        cxr_employee_id,
        id
        FROM cxr_customer_address
        <where>
            <if test="bo.phone!=null and bo.phone!=''">
                and cxr_customer_id in (select id from cxr_customer where phone like concat(#{bo.phone},'%') AND
                delete_status = 0)
            </if>
            <if test="bo.receiverPhone!=null and bo.receiverPhone!=''">
                and receiver_phone like concat(#{bo.receiverPhone},'%')
            </if>
            <if test="bo.name!=null and bo.name!=''">
                and receiver_name like concat('%',#{bo.name},'%')
            </if>
            AND delete_status = 0
        </where>
    </select>


    <select id="getDeliveryInquiryList"
            resultType="com.ruoyi.business.base.customerAddress.domain.vo.DeliveryInquiryVo">
        SELECT id,
               st_distance(POINT(#{longitude}, #{latitude}),
                           POINT(longitude, latitude)) * 111195 / 1000 AS distance
        FROM cxr_customer_address
        WHERE 1 = 1
          and delete_status = 0
          and cxr_employee_id is not null
          and longitude BETWEEN #{beginLongitude} and #{endLongitude}
          and latitude BETWEEN #{beginLatitude} and #{endLatitude}

        HAVING <![CDATA[  distance <= #{scopeSum} ]]>
        ORDER BY distance asc
    </select>

    <select id="getCustomerAddressAndCustomer"
            resultType="com.ruoyi.business.base.customerAddress.domain.vo.CustomerAddressDetailVo">
        select
        receiver_name as receiverName,
        receiver_phone,
        provice,
        city,
        area,
        detail_distribution_address,
        cxr_site_id,
        cxr_employee_id,
        longitude,latitude,
        id
        FROM cxr_customer_address
        where
        1=1 and delete_status = 0
        and id in
        <foreach collection="ids" item="value" separator="," open="(" close=")">
            #{value}
        </foreach>
    </select>

    <update id="updateStockById">
        update cxr_customer_address
        set address_stock = address_stock - #{total}
        where id = #{id}
          AND address_stock >= #{total}
    </update>


    <select id="pageAdjustMilkRoadWayAddress"
            resultType="com.ruoyi.business.base.cxrAdjustMilkApply.domain.vo.AdjustMilkRoadWayVo">
        select
        cxr_customer_id,
        id as cxrCustomerAddressId,
        provice,city,area,receiver_name,receiver_phone,detail_distribution_address,
        (select name from cxr_employee where id=cxr_employee_id) as cxrEmployeeName,
        cxr_employee_id
        from cxr_customer_address
        <where>
            and delete_status = '0'
            and cxr_employee_id in
            <foreach collection="bo.userIds" separator="," index="i" open="(" close=")" item="item">
                #{item}
            </foreach>

            <if test="bo.customerName!=null and bo.customerName!=''">
                and receiver_name like concat('%',#{bo.customerName},'%')
            </if>

            <if test="bo.customerPhone!=null and bo.customerPhone!=''">
                and receiver_phone like concat('%',#{bo.customerPhone},'%')
            </if>

            <if test="bo.customerAddress!=null and bo.customerAddress!=''">
                # and detail_distribution_address like concat('%',#{bo.customerAddress},'%')
                and (
                detail_distribution_address like concat('%',#{bo.customerAddress},'%')
                or provice like concat('%',#{bo.customerAddress},'%')
                or city like concat('%',#{bo.customerAddress},'%')
                or area like concat('%',#{bo.customerAddress},'%')
                )
            </if>

        </where>
        ORDER BY create_time , cxr_customer_id;
    </select>

    <select id="pageCustomerAddress"
            resultType="com.ruoyi.business.base.cxrAdjustMilkApply.domain.vo.AdjustMilkCustomerVo">

        select defalut_account_address,
        cxr_customer_id,
        id as cxrCustomerAddressId,
        provice,city,area,receiver_name as customerName,receiver_phone as customerPhone,detail_distribution_address,
        cxr_employee_id
        from cxr_customer_address
        <where>
            and delete_status = '0'
            and cxr_employee_id in
            <foreach collection="bo.userIds" separator="," index="i" open="(" close=")" item="item">
                #{item}
            </foreach>

            <if test="bo.customerPhone!=null and bo.customerIds==null">
                and receiver_phone like concat('%',#{bo.customerPhone},'%')
            </if>

            <if test="bo.customerIds!=null and bo.customerIds.size>0">
                and cxr_customer_id in
                <foreach collection="bo.customerIds" separator="," index="i" open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>

            <if test="bo.customerName!=null and bo.customerName!=''">
                and receiver_name like concat('%',#{bo.customerName},'%')
            </if>

            <if test="bo.customerAddress!=null and bo.customerAddress!=''">
                AND (
                INSTR(
                provice,#{bo.customerAddress}) > 0

                OR INSTR(
                city,#{bo.customerAddress}) >0

                OR INSTR(
                area,#{bo.customerAddress}) > 0

                OR INSTR( detail_distribution_address, #{bo.customerAddress}) > 0
                )
            </if>

        </where>
        ORDER BY create_time , cxr_customer_id
    </select>

    <select id="ListCustomerAddress"
            resultType="com.ruoyi.business.base.cxrAdjustMilkApply.domain.vo.AdjustMilkCustomerVo">

        select defalut_account_address,
        cxr_customer_id,
        id as cxrCustomerAddressId,
        provice,city,area,receiver_name,receiver_phone,detail_distribution_address,
        cxr_employee_id
        from cxr_customer_address
        <where>
            and delete_status = '0'
            and cxr_customer_id in
            <foreach collection="bo.customerIds" separator="," index="i" open="(" close=")" item="item">
                #{item}
            </foreach>

        </where>
        ORDER BY create_time , cxr_customer_id;
    </select>

    <select id="violationUserPage"
            resultType="com.ruoyi.business.base.violationOrderUserWarning.domian.vo.ViolationUserPageVo">
        select t.id,t.customer_name,t.customer_phone,
        t.status,t.latest_alert_reason,t.latest_alert_time,
        t.alert_count,t.appeal_reason,t.appeal_time,t.latest_alert_item,
        t.latest_order_emp,
        t.appeal_by,t.appeal_by_name,t.create_time
        from violation_customer_matches_records t
        <where>
            EXISTS
            (select 1 from cxr_customer_address t1
            where
            1=1
            <if test="bo.siteIds!=null and bo.siteIds.size>0">
                and t1.cxr_site_id in
                <foreach collection="bo.siteIds" separator="," index="i" open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>


            <if test="bo.customerPhone != null and bo.customerPhone!= '' ">
                and (t1.receiver_phone like CONCAT('%', #{bo.customerPhone}, '%') or
                t.customer_phone like concat('%',#{bo.customerPhone},'%')
                )
            </if>
            and t1.cxr_customer_id =t.customer_id)

            <if test="bo.status!=null">
                and t.status = #{bo.status}
            </if>

            <if test="bo.startLatestAlertTime != null">
                and t.latest_alert_time  <![CDATA[ >= ]]> #{bo.startLatestAlertTime}
            </if>


            <if test="bo.endLatestAlertTime != null">
                and t.latest_alert_time <![CDATA[ <= ]]> #{bo.endLatestAlertTime}
            </if>

            <if test="bo.appealByName != null and bo.appealByName!= '' ">
                and t.latest_order_emp like CONCAT('%', #{bo.appealByName}, '%')
            </if>

        </where>
        order by t.create_time desc

    </select>


</mapper>
