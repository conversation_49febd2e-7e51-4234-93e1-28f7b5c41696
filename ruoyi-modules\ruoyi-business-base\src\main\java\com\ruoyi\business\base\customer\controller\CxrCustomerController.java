package com.ruoyi.business.base.customer.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.business.base.api.domain.CxrCustomerChangeRecord;
import com.ruoyi.business.base.api.domain.vo.CxrAddressHistoryVo;
import com.ruoyi.business.base.customer.domain.bo.CustomerLabeledBo;
import com.ruoyi.business.base.customer.domain.bo.CxrCustomerAddressABo;
import com.ruoyi.business.base.customer.domain.bo.MilkDistributionDetailedBo;
import com.ruoyi.business.base.customer.domain.vo.CxrCustomerCountVo;
import com.ruoyi.business.base.customer.domain.vo.CxrCustomerListVo;
import com.ruoyi.business.base.customer.domain.vo.CxrCustomerTransferMilkDetail;
import com.ruoyi.business.base.customer.service.ICxrCustomerService;
import com.ruoyi.business.base.customerAddress.domain.bo.CxrCustomerAddrBo;
import com.ruoyi.business.base.customerAddress.domain.vo.CxrCustomerAddrVo;
import com.ruoyi.business.base.customerDistributionListRecord.domain.CxrCustomerDistributionListRecord;
import com.ruoyi.business.base.cxrLabeled.domain.bo.CxrLabeledBo;
import com.ruoyi.business.base.cxrLabeled.domain.vo.CxrLabeledVo;
import com.ruoyi.business.base.roadWay.domain.bo.RoadWayTotalBo;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.phone.PhoneNumberValidator;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.mybatis.core.page.PageQuery;
import com.ruoyi.common.mybatis.core.page.PageTableDataInfo;
import com.ruoyi.common.rocketmq.constant.customer.CxrCustomerConstant;
import com.ruoyi.common.rocketmq.utils.MqUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 客户Controller 前端访问路由地址为:/base/cxrCustomer
 *
 * <AUTHOR>
 * @date 2022-07-02
 */
@Validated
@Api(value = "客户控制器", tags = {"客户管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/cxrCustomer")
public class CxrCustomerController extends BaseController {

    private final ICxrCustomerService iCxrCustomerService;

    @Autowired
    private MqUtil mqUtil;


    /**
     * YHJ
     *
     * @param bo
     * @return
     */
    @ApiOperation("分页查询《客户配送地址  可以用defalutAccountAddress 区分是不是默认地址》 YHJ 22-09-23")
    @SaCheckPermission("base:cxrCustomer:customerPage")
    @PostMapping("/customerPage")
    public R<PageTableDataInfo<CxrCustomerListVo>> customerPage(@RequestBody CxrCustomerAddressABo bo) {
        return R.ok(iCxrCustomerService.customerPage(bo));
    }

    /**
     * 详细查询《客户》 YHJ
     *
     * @param id 主键
     * @return
     */
    @ApiOperation("详细查询《客户》， YHJ客户的基本资料")
    @SaCheckPermission("base:cxrCustomer:detail")
    @GetMapping("/detail")
    public R<CxrCustomerCountVo> detail(@ApiParam("客户id主键")
    @NotNull(message = "主键不能为空")
    @RequestParam Long id) {
        return R.ok(iCxrCustomerService.detail(id));
    }


    /**
     * YHJ
     *
     * @param bo
     * @return
     */
    @ApiOperation("新增客户地址  YHJ")
    @SaCheckPermission("base:cxrCustomer:adressAdd")
    @PostMapping("/adressAdd")
    public R adressAdd(@Validated(AddGroup.class) @RequestBody CxrCustomerAddrBo bo) {
        if (bo.getCxrCustomerId() == null) {
            throw new ServiceException("客户id不能为空!");
        }
        return R.ok(iCxrCustomerService.addressAdd(bo));
    }

    /**
     * YHJ
     *
     * @param bo
     * @return
     */
    @ApiOperation("新增客户 和地址  YHJ")
    @SaCheckPermission("base:cxrCustomer:customerAdd")
    @PostMapping("/customerAdd")
    public R customerAdd(@Validated(AddGroup.class) @RequestBody CxrCustomerAddrBo bo) {
        return R.ok(iCxrCustomerService.customerAdd(bo) != null);
    }

    /**
     * 地址详情 YHJ
     *
     * @param id
     * @return
     */
    @ApiOperation("地址详情  YHJ")
    @SaCheckPermission("base:cxrCustomer:adressAdd")
    @GetMapping("/adressDetail")
    public R<CxrCustomerAddrVo> adressDetail(@ApiParam("地址id主键")
    @NotNull(message = "主键不能为空")
    @RequestParam Long id) {
        return R.ok(iCxrCustomerService.adressDetail(id));
    }


    /**
     * 编辑客户配送地址 YHJ
     *
     * @param
     */
    @ApiOperation("更新客户配送地址  YHJ ")
    @SaCheckPermission("base:cxrCustomer:edit")
    @Log(title = "客户", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody CxrCustomerAddrBo bo) {
        PhoneNumberValidator.isPhone(bo.getReceiverPhone());
        return toAjax(iCxrCustomerService.editAdress(bo) ? 1 : 0);
    }


    /**
     * 删除客户配送地址 YHJ
     *
     * @param idSet 主键集合
     * @return
     */
    @ApiOperation("删除客户配送地址 YHJ")
    @SaCheckPermission("base:cxrCustomer:remove")
    @Log(title = "客户", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    public R<Void> remove(@ApiParam("地址id主键集合")
    @NotEmpty(message = "主键集合不能为空")
    @RequestBody Set<Long> idSet) {
        return toAjax(iCxrCustomerService.remove(idSet) ? 1 : 0);
    }

    /**
     * 解除客户绑定 YHJ
     *
     * @param id 主键集合
     * @return
     */
    @ApiOperation("解除绑定 YHJ")
    @SaCheckPermission("base:cxrCustomer:removeBinding")
    @Log(title = "客户", businessType = BusinessType.UPDATE)
    @GetMapping("/removeBinding")
    public R<Void> removeBanding(@ApiParam("主键  客户id")
    @NotNull(message = "id不能为null")
    @RequestParam Long id,@RequestParam Integer flag) {
        return toAjax(iCxrCustomerService.removeBanding(id,flag) ? 1 : 0);
    }


    @ApiOperation("排奶--- YHJ")
    @SaCheckPermission("base:cxrCustomer:milkDistribution")
    @Log(title = "客户", businessType = BusinessType.UPDATE)
    @PostMapping("/milkDistribution")
    public R<String> milkDistribution(@Validated @NotNull @RequestBody List<CxrCustomerAddrBo> bos) {
        return iCxrCustomerService.milkDistribution(bos) ? R.ok("成功") : R.fail("失败");
    }


    @ApiOperation("排奶详情--- YHJ")
    @SaCheckPermission("base:cxrCustomer:milkDistributionDetail")
    @Log(title = "客户")
    @PostMapping("/milkDistributionDetail")
    public R<List<CxrCustomerAddrVo>> milkDistributionDetail(@ApiParam("地址id主键集合")
    @NotEmpty(message = "主键集合不能为空")
    @RequestBody Set<Long> idSet) {
        return R.ok(iCxrCustomerService.milkDistributionDetail(idSet));
    }

    @ApiOperation("客户全部地址 排奶详情--- YHJ")
    @SaCheckPermission("base:cxrCustomer:allMilkDistributionDetail")
    @Log(title = "客户")
    @GetMapping("/allMilkDistributionDetail")
    public R<List<CxrCustomerAddrVo>> milkDistributionDetail(@ApiParam("客户id")
    @NotNull(message = "客户id")
    @RequestParam Long id) {
        return R.ok(iCxrCustomerService.allMilkDistributionDetail(id));
    }


    @ApiOperation("路条统计--- YHJ")
    @SaCheckPermission("base:cxrCustomer:roadWayTotal")
    @Log(title = "客户")
    @PostMapping("/roadWayTotal")
    public R<List<CxrCustomerDistributionListRecord>> roadWayTotal(@Validated @RequestBody RoadWayTotalBo bo) {
        return R.ok(iCxrCustomerService.selectEveryDayMilkTotal(bo));
    }

    @ApiOperation("合计统计--- YHJ")
    @SaCheckPermission("base:cxrCustomer:cxrCustomerMilkTotal")
    @Log(title = "客户")
    @PostMapping("/cxrCustomerMilkTotal")
    public R<Map<String, Object>> cxrCustomerMilkTotal(@Validated @RequestBody RoadWayTotalBo bo) {
        return R.ok(iCxrCustomerService.selectYearMilkTotal(bo.getCxrCustomerId(), bo.getYear()));
    }

    @ApiOperation("查询地址更改记录 YHJ")
    @SaCheckPermission("base:cxrCustomer:queryAddressHistory")
    @Log(title = "客户")
    @PostMapping("/queryAddressHistory")
    public R<List<CxrAddressHistoryVo>> queryAddressHistory(@RequestBody CxrCustomerAddressABo bo) {
        return R.ok(iCxrCustomerService.queryHistory(bo));
    }

//    @ApiOperation("获取年份 YHJ")
//    @SaCheckPermission("base:cxrCustomer:getDate")
//    @Log(title = "客户")
//    @GetMapping("/getDate")
//    public R<List<String>> getDate(@ApiParam("客户id主键")
//    @NotNull(message = "客户id不能为空")
//    @RequestParam Long id) {
//        return R.ok(iCxrCustomerService.getDate(id));
//    }


    @ApiOperation("配送清单")
    @PostMapping("/milkDistributionDetailed")
    public R milkDistributionDetailed(@RequestBody MilkDistributionDetailedBo bo) {
        return R.ok(iCxrCustomerService.milkDistributionDetailed(bo));
    }

    @ApiOperation("客户库存")
    @PostMapping("/customerStockInfo")
    public R customerStockInfo() {
        return R.ok(iCxrCustomerService.customerStockInfo());
    }

    @ApiOperation("导出 做权限控制")
    @SaCheckPermission("base:cxrCustomer:export")
    @GetMapping("/export")
    public void export() {

    }


    @ApiOperation("分页查询标签")
    @SaCheckPermission("base:cxrCustomer:customerPage")
    @PostMapping("/labeledPage")
    public R<PageTableDataInfo<CxrLabeledVo>> labeledPage(@RequestBody CxrLabeledBo bo) {
        PageQuery pageQuery = BeanUtil.copyProperties(bo, PageQuery.class);
        return R.ok(iCxrCustomerService.labeledPage(bo, pageQuery));
    }

    @ApiOperation("新增标签")
    @SaCheckPermission("base:cxrCustomer:createLabeled")
    @PostMapping("/createLabeled")
    public R createLabeled(@RequestBody CustomerLabeledBo bo) {
        return R.ok(iCxrCustomerService.createLabeled(bo));
    }

    @ApiOperation("更新客户备注")
    @SaCheckPermission("base:cxrCustomer:updateCustomerRemark")
    @PostMapping("/updateCustomerRemark")
    public R updateCustomerRemark(@RequestParam String remark, @RequestParam Long customerId) {
        return R.ok(iCxrCustomerService.updateCustomerRemark(remark, customerId));
    }

    @ApiOperation("更新客户手机号")
    @SaCheckPermission("base:cxrCustomer:updateCustomerPhone")
//    @PostMapping("/updateCustomerPhone")
    public R updateCustomerPhone(@RequestParam String phone, @RequestParam Long customerId) {
        PhoneNumberValidator.isPhone(phone);
        iCxrCustomerService.updateCustomerPhone(phone, customerId,false);
        mqUtil.sendSyncMessage(CxrCustomerConstant.CUSTOMER_TOPIC, CxrCustomerConstant.YDS_CUSTOMER_SYNC_TAG,
            customerId.toString());
        return R.ok();
    }

    @ApiOperation("更新客户手机号")
    @SaCheckPermission("base:cxrCustomer:updateCustomerPhone")
    @PostMapping("/updateCustomerPhone")
    public R updatePhone(@RequestParam String phone, @RequestParam Long customerId,Long existCustomerId) {
        R r = iCxrCustomerService.updateCustomerPhone(phone, customerId, existCustomerId);
        if (r.getCode() == R.SUCCESS){
            if (existCustomerId != null){
                mqUtil.sendSyncMessage(CxrCustomerConstant.CUSTOMER_TOPIC, CxrCustomerConstant.YDS_CUSTOMER_SYNC_TAG, existCustomerId.toString());
            }
            mqUtil.sendSyncMessage(CxrCustomerConstant.CUSTOMER_TOPIC, CxrCustomerConstant.YDS_CUSTOMER_SYNC_TAG, customerId.toString());
        }
        return r;
    }


    @ApiOperation("修改客户名字")
    //   @SaCheckPermission("base:cxrCustomer:updateCustomerName")
    @PostMapping("/updateCustomerName")
    public R updateCustomerName(@RequestParam String name, @RequestParam Long customerId) {
        return R.ok(iCxrCustomerService.updateCustomerName(name, customerId));
    }


    @ApiOperation("查询客户转单鲜奶详情")
    //   @SaCheckPermission("base:cxrCustomer:updateCustomerName")
    @GetMapping("/queryCustomerTransferById")
    public R<CxrCustomerTransferMilkDetail> queryCustomerTransferById(@RequestParam Long customerId) {
        return R.ok(iCxrCustomerService.queryCustomerTransferById(customerId));
    }

    /**
     * 删除客户
     *
     * @param idSet 主键集合
     * @return
     */
    @ApiOperation("删除客户")
    @SaCheckPermission("base:cxrCustomer:remove")
    @Log(title = "客户", businessType = BusinessType.DELETE)
    @PostMapping("/del")
    public R<Void> del(@ApiParam("客户id主键集合")
    @NotEmpty(message = "客户id不能为空")
    @RequestBody Set<Long> ids) {
        return toAjax(iCxrCustomerService.del(ids) ? 1 : 0);
    }

    @ApiOperation("配送清单")
    @PostMapping("/saveCxrCustomerChangeRecordDemo")
    public R saveCxrCustomerChangeRecordDemo(@RequestBody CxrCustomerChangeRecord customerChangeRecord) {
        return R.ok(iCxrCustomerService.saveCxrCustomerChangeRecordDemo(customerChangeRecord));
    }
}
