package com.ruoyi.calculate.orderCount.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.calculate.orderCount.domain.CxrRegionStaitemDailyReport;
import com.ruoyi.core.base.domain.bo.CxrRegionStaitemDailyReportBo;
import com.ruoyi.core.base.domain.vo.CxrRegionStaitemDailyReportVo;

/**
* <AUTHOR>
* @description 针对表【cxr_region_staitem_daily_report(区域单数统计明细日报表)】的数据库操作Service
* @createDate 2025-07-03 16:06:20
*/
public interface CxrRegionStaitemDailyReportService extends IService<CxrRegionStaitemDailyReport> {

    /**
     * 分页查询区域单数统计明细
     *
     * @param bo 查询参数
     * @param page 分页参数
     * @return 分页结果
     */
    Page<CxrRegionStaitemDailyReportVo> page(CxrRegionStaitemDailyReportBo bo, Page<Object> page);

    /**
     * 合计查询区域单数统计明细
     *
     * @param bo 查询参数
     * @return 合计结果
     */
    CxrRegionStaitemDailyReportVo sum(CxrRegionStaitemDailyReportBo bo);

}
