package com.ruoyi.order.disribution.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.business.base.api.domain.CxrCustomerAddress;
import com.ruoyi.business.base.api.domain.CxrEmployee;
import com.ruoyi.business.base.api.domain.CxrEmployeePost;
import com.ruoyi.business.base.api.domain.CxrSite;
import com.ruoyi.business.base.api.domain.bo.CxrPostId;
import com.ruoyi.business.base.api.domain.dto.DailyPerformanceDto;
import com.ruoyi.business.base.api.domain.json.BusinessAgent;
import com.ruoyi.business.base.api.domain.json.CustomerInfo;
import com.ruoyi.business.base.api.dubbo.RemoteEmployeeAchievementDetailService;
import com.ruoyi.business.base.api.enums.OrderTypeEnums;
import com.ruoyi.business.base.api.model.LoginEmployee;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.domain.LoginInfo;
import com.ruoyi.common.core.enums.ChargeStatus;
import com.ruoyi.common.core.enums.DeleteStatus;
import com.ruoyi.common.core.enums.PostType;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.BeanCopyUtils;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.enums.TerminalTypeEnums;
import com.ruoyi.common.mybatis.page.PageResult;
import com.ruoyi.common.redis.utils.RedisLockUtils;
import com.ruoyi.common.satoken.utils.LoginUtil;
import com.ruoyi.common.satoken.utils.helper.StaffLoginHelper;
import com.ruoyi.order.api.enums.PayStatusEnums;
import com.ruoyi.order.common.domain.bo.SummaryOrderBo;
import com.ruoyi.order.common.domain.vo.OrderSearchReqVo;
import com.ruoyi.order.common.entity.CxrUserOrder;
import com.ruoyi.order.common.entity.CxrUserReturnOrder;
import com.ruoyi.order.common.mapper.CxrCustomerAddressMapper;
import com.ruoyi.order.common.mapper.CxrEmployeePostMapper;
import com.ruoyi.order.common.mapper.CxrUserOrderMapper;
import com.ruoyi.order.common.mapper.CxrUserReturnOrderMapper;
import com.ruoyi.order.disribution.domain.vo.ConsumerOrderVo;
import com.ruoyi.order.disribution.domain.vo.IncreaseOrderDetailVo;
import com.ruoyi.order.disribution.domain.vo.SummaryOrderVo;
import com.ruoyi.order.disribution.enums.OrderTypeHanderEnums;
import com.ruoyi.order.disribution.service.OrderManagementService;
import com.ruoyi.order.disribution.strategy.OrderSummaryCalculator;
import com.ruoyi.order.extend.mapper.CxrEmployeeMapper;
import com.ruoyi.order.extend.mapper.CxrSiteMapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Service
public class OrderManagementServiceImpl implements OrderManagementService {

    private final CxrUserOrderMapper cxrUserOrderMapper;
    private final CxrEmployeePostMapper cxrEmployeePostMapper;
    private final CxrSiteMapper cxrSiteMapper;
    private final CxrUserReturnOrderMapper cxrUserReturnOrderMapper;
    private final CxrCustomerAddressMapper cxrCustomerAddressMapper;
    private final CxrEmployeeMapper employeeMapper;

    @DubboReference
    private RemoteEmployeeAchievementDetailService remoteEmployeeAchievementDetailService;

    private final List<OrderSummaryCalculator> calculators;

    @Override
    public PageResult<List<ConsumerOrderVo>> listOrder(OrderSearchReqVo reqVo) {
        Integer pageNum = reqVo.getPageNo();
        Integer pageSize = reqVo.getPageSize();
        Page page = new Page(pageNum, pageSize);

        Date endDate = reqVo.getEndDate();
        if (endDate != null) {
            endDate.setHours(23);
            endDate.setMinutes(59);
            endDate.setSeconds(59);
        }

        LoginEmployee loginUser = StaffLoginHelper.getLoginUser();
        reqVo.setCreateBy(loginUser.getUserId());
        List<CxrPostId> cxrPostIds = loginUser.getCxrPostIds().stream().collect(Collectors.toList());
        if (cxrPostIds.size() > 0) {
            long count =
                cxrPostIds.stream()
                    .filter(
                        a ->
                            a.getName().equals("主管")
                                || a.getName().equals("储备主管")
                                || a.getName().equals("代主管"))
                    .count();
            if (count > 0) {
                reqVo.setGroupLeaderFlag(true);
            }
        }

        List<CxrEmployeePost> regionCxrEmployeePost =
            cxrEmployeePostMapper.selectList(new LambdaQueryWrapper<CxrEmployeePost>()
                .eq(CxrEmployeePost::getCxrEmployeeId, loginUser.getUserId())
                .eq(CxrEmployeePost::getChargeStatus, ChargeStatus.IN_PROGRESS.getValue())
                .eq(CxrEmployeePost::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .apply("JSON_OVERLAPS(JSON_EXTRACT(cxr_post_id,'$.value'),{0})", JSONUtil.toJsonStr(Arrays.asList(PostType.REGION_MANAGER.getValue(),
                    PostType.BIG_DISTRICT_MANAGER.getValue())))
            );
        if (CollectionUtils.isNotEmpty(regionCxrEmployeePost)) {
            List<Long> regionIds = regionCxrEmployeePost.stream().map(CxrEmployeePost::getCxrRegionId)
                .collect(Collectors.toList());
            List<CxrSite> cxrSites = cxrSiteMapper.selectList(new LambdaQueryWrapper<CxrSite>()
                .in(CxrSite::getCxrRegionId, regionIds));
            List<Long> siteIds = cxrSites.stream().map(CxrSite::getId)
                .collect(Collectors.toList());
            reqVo.getReturnSiteIds().addAll(siteIds);
            reqVo.setSiteSearch(true);
        }
        List<CxrEmployeePost> siteCxrEmployeePost =
            cxrEmployeePostMapper.selectList(new LambdaQueryWrapper<CxrEmployeePost>()
                .eq(CxrEmployeePost::getCxrEmployeeId, loginUser.getUserId())
                .eq(CxrEmployeePost::getChargeStatus, ChargeStatus.IN_PROGRESS.getValue())
                .eq(CxrEmployeePost::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .apply("JSON_OVERLAPS(JSON_EXTRACT(cxr_post_id,'$.value'),{0})", JSONUtil.toJsonStr(Arrays.asList(PostType.DIRECTOR.getValue(),
                    PostType.GENERATION_DIRECTOR.getValue()))));
        if (CollectionUtils.isNotEmpty(siteCxrEmployeePost)) {
            List<Long> siteIds = siteCxrEmployeePost.stream().map(CxrEmployeePost::getCxrSiteId)
                .collect(Collectors.toList());
            reqVo.getReturnSiteIds().addAll(siteIds);
            reqVo.setSiteSearch(true);
        }
        //站点条件
        if (StrUtil.isNotBlank(reqVo.getSiteName())) {
            List<CxrSite> cxrSites = cxrSiteMapper.selectList(new LambdaQueryWrapper<CxrSite>()
                .select(CxrSite::getId)
                .like(CxrSite::getName, reqVo.getSiteName()));
            List<Long> siteIds = cxrSites.stream().map(CxrSite::getId)
                .collect(Collectors.toList());
            if (CollectionUtil.isEmpty(siteIds)) siteIds = Arrays.asList(-1L);
            reqVo.setSiteIds(siteIds);
        }
        List<Long> allSiteIds = reqVo.getAllSiteIds();
        allSiteIds.addAll(reqVo.getSiteIds());
        allSiteIds.addAll(reqVo.getReturnSiteIds());
        Page<CxrUserOrder> idsPage = cxrUserOrderMapper.searchOrderIds(reqVo, page);
        List<CxrUserOrder> idsListOrder = idsPage.getRecords();
        if (CollectionUtil.isNotEmpty(idsListOrder)) {
            List<Long> ids = idsListOrder.stream().map(CxrUserOrder::getId).collect(Collectors.toList());
            OrderSearchReqVo orderSearchReqVo = new OrderSearchReqVo();
            orderSearchReqVo.setOrderIds(ids);
            List<CxrUserOrder> bos = cxrUserOrderMapper.searchOrder(orderSearchReqVo);
            idsPage.setRecords(bos);
        }
        List<ConsumerOrderVo> rlist = new ArrayList<>();
        List<CxrUserOrder> bos = idsPage.getRecords();
        if (page != null && CollectionUtils.isNotEmpty(bos)) {

            List<Long> orderIds = bos.stream().map(CxrUserOrder::getId).collect(Collectors.toList());
            List<DailyPerformanceDto> dailyPerformanceDtos =
                remoteEmployeeAchievementDetailService.selectSaleAchievement(
                    orderIds, loginUser.getUserId());
            //查询权限能看到的退订单业绩
            if (CollectionUtil.isNotEmpty(reqVo.getReturnSiteIds())) {
                List<Long> returnOrderIds = bos.stream().filter(e -> e.getOrderType().equals(OrderTypeEnums.RETURN_ORDER.getValue())).map(CxrUserOrder::getId).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(returnOrderIds)) {
                    List<DailyPerformanceDto> returenDailyPerformanceDtos = cxrUserOrderMapper.returenDailyPerformanceDtos(returnOrderIds, loginUser.getUserId(), reqVo.getReturnSiteIds());
                    dailyPerformanceDtos.addAll(returenDailyPerformanceDtos);
                }
            }

            Map<Long, List<DailyPerformanceDto>> employeeAchievementMap =
                dailyPerformanceDtos.stream()
                    .collect(Collectors.groupingBy(DailyPerformanceDto::getSouceId));
            LocalDate now = LocalDate.now();
            for (CxrUserOrder bo : bos) {
                ConsumerOrderVo vo = BeanCopyUtils.copy(bo, ConsumerOrderVo.class);

                List<BusinessAgent> agent = bo.getBusinessAgent();
                long count = 0;
                if (CollectionUtil.isNotEmpty(agent)) {
                    count =
                        agent.stream().filter(a -> a.getProxyName().equals(loginUser.getUserName())).count();
                }

                if (count > 0) {
                    vo.setProxyName(loginUser.getUserName()); // 销售代理
                    if (ObjectUtil.isNotEmpty(bo.getPerformanceMoney())) {

                        //                    BigDecimal performance=new BigDecimal(0);
                        BigDecimal amount = vo.getAmount();

                        if (bo.getBusinessAgent().size() > 1) { // 拆
                            BigDecimal decimal = NumberUtil.div(amount, agent.size(), 2);

                            vo.setPerformanceMoney(decimal + "");

                            BusinessAgent businessAgent1 = agent.get(agent.size() - 1);
                            String userName = loginUser.getUserName();
                            if (businessAgent1.getProxyName().equals(userName)) {

                                BigDecimal cai = NumberUtil.div(amount, agent.size(), 2);
                                for (int i = 1; i < agent.size(); i++) {
                                    amount = amount.subtract(cai);
                                }

                                if (amount.compareTo(new BigDecimal(0.00)) == 0) {
                                    vo.setPerformanceMoney(vo.getAmount().toString());
                                } else {
                                    vo.setPerformanceMoney(amount.toString());
                                }
                            }
                        }
                        //                    vo.get
                        if (bo.getOrderType().equals(OrderTypeEnums.RETURN_ORDER.getValue())) {
                            BigDecimal decimal =
                                new BigDecimal(bo.getPerformanceMoney()).setScale(2, BigDecimal.ROUND_DOWN);
                            String replace = decimal.toString().replace("-", "");
                            vo.setPerformanceMoney("-" + replace);
                        }
                    }
                } else {
                    vo.setProxyName(null); // 销售代理
                    vo.setPerformanceMoney(null);
                }
                vo.setPayStatus(bo.getPayStatus()); // 它这个字段的名字有点问题手动弄一下  PayState

                vo.setTerminalType(bo.getTerminalType());
                vo.setPerfectStatus(bo.getPerfectStatus());
                if (bo.getBusinessAgent().size() == 1 ||
                    ObjectUtil.equals(loginUser.getUserId(), bo.getCreateBy())
                ) {
                    vo.setPromotionalEditFlag(true);
                }
                // 合体订单名的“null,null" 需要做一些处理
                String str = vo.getCustomerName();
                if (StringUtils.isNotEmpty(str) && str.trim().equals("null,null")) {
                    vo.setCustomerName(null);
                }

                //                vo.setId(bo.getId());
                vo.setDailyPerformanceDto(employeeAchievementMap.get(bo.getId()));

                LocalDate orderTomorrow =
                    bo.getPayTime() == null
                        ? now
                        : DateUtils.getLocalDateFromDate(bo.getPayTime()).plusDays(1);
                ;
                // 编辑权限是创建人
                vo.setEditFlag(
                    now.isBefore(orderTomorrow) && bo.getCreateBy().equals(loginUser.getUserId()));

                Short orderTypeValue = vo.getOrderType();
                Short terminalType = vo.getTerminalType();

                if (orderTypeValue != null) {
                    short type = (short) vo.getOrderType().intValue();
                    OrderTypeHanderEnums currentType = OrderTypeHanderEnums.getType(type);

                    if (currentType == OrderTypeHanderEnums.return_order) {
                        // 退出奶总数 ,这个总数可能只是指鲜奶
                        //                    Integer
                        // count=vo.getFreshMilkGiveQuantity()+vo.getLongMilkGiveQuantity();
                        vo.setFreshMilkGiveQuantity(null);
                        vo.setLongMilkGiveQuantity(null);
                        vo.setFreshMilkReturnQuantity(bo.getFreshMilkReturnQuantity());
                        vo.setAmount(null);
                        vo.setReturnAmount(bo.getAmount());
                    } else if (currentType == OrderTypeHanderEnums.ORDER) {
                        vo.setReturnAmount(null);
                    }

                    //                    List<CustomerInfo> customerInfoList = bo.getCustomerInfoList();
                    List<CustomerInfo> customerInfoList =
                        JSONObject.parseArray(bo.getCustomerInfos(), CustomerInfo.class);
                    if (CollectionUtils.isNotEmpty(customerInfoList)) {
                        CustomerInfo customerInfo = customerInfoList.get(0);
                        vo.setProvince(customerInfo.getProvice());
                        vo.setCity(customerInfo.getCity());
                        vo.setArea(customerInfo.getArea());
                        vo.setCustomerAdress(customerInfo.getAdress());
                    }

                    if (terminalType != null && terminalType == TerminalTypeEnums.manager.getValue()) {
                        // 来自后台的单 不（需）显示状态，不显示状态
                        vo.setPayStatus(null);
                        vo.setAuditStatus(null);
                    }

                    if (NumberUtil.equals(OrderTypeEnums.CONTINUE_ORDER.getValue(), bo.getOrderType())
                        && NumberUtil.equals(
                        TerminalTypeEnums.disribution.getValue(), bo.getTerminalType())) {
                        if (NumberUtil.equals(PayStatusEnums.PAY_SUCCEEDED.getValue(), bo.getPayStatus())) {
                            Date payTime = bo.getPayTime();
                            LocalDate localDate = DateUtil.toLocalDateTime(payTime).toLocalDate();
                            if (now.compareTo(localDate) == 0) {
                                vo.setContinueEditFlag(Boolean.TRUE);
                            }
                        }
                    }

                    if (currentType == OrderTypeHanderEnums.return_order) {
                        CxrUserReturnOrder returnOrder = cxrUserReturnOrderMapper.selectOne(new LambdaQueryWrapper<CxrUserReturnOrder>()
                            .eq(CxrUserReturnOrder::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                            .eq(CxrUserReturnOrder::getUserOrderId, vo.getId()));
                        if (returnOrder != null && returnOrder.getAfterSalesId() != null) {
                            vo.setTerminalType(TerminalTypeEnums.xcx.getValue());
                        }
                    }
                    rlist.add(vo);
                }
            }
        }

        PageResult pageResult = new PageResult();

        pageResult.setRows(rlist);
        pageResult.setPageSize((int) idsPage.getSize());
        pageResult.setTotalRows((int) idsPage.getTotal());
        pageResult.setTotalPage((int) idsPage.getPages());
        pageResult.setPageNo((int) idsPage.getCurrent());
        return pageResult;
    }

    /**
     * 要求要不，通过审核，要不已经付款
     *
     * @param reqVo
     * @return
     */
    @Override
    public SummaryOrderVo summaryOrder(OrderSearchReqVo reqVo) {

        Date endDate = reqVo.getEndDate();
        if (endDate != null) {
            endDate.setHours(23);
            endDate.setMinutes(59);
            endDate.setSeconds(59);
        }

        List<SummaryOrderBo> list =
            cxrUserOrderMapper.summaryOrder(reqVo).stream()
                .filter(e -> e.getOrderType() != null)
                .collect(Collectors.toList());
        //        OrderTypeEnums.getType()

        SummaryOrderVo summaryOrderVo = SummaryOrderVo.createDefaultOne();

        for (OrderSummaryCalculator calculator : calculators) {
            calculator.prepare();
        }

        loop:
        for (SummaryOrderBo bo : list) {
            for (OrderSummaryCalculator calculator : calculators) {
                if (calculator.checkOrderType(bo.getOrdertypeEnums())) {
                    calculator.calculate(summaryOrderVo, bo);
                    continue loop;
                }
            }
        }

        for (OrderSummaryCalculator calculator : calculators) {
            calculator.done();
        }

        return summaryOrderVo;
    }

    /**
     * 查询详情
     *
     * @param id
     * @return
     */
    @Override
    public CxrUserOrder detial(Long id) {
        CxrUserOrder cxrUserOrder = cxrUserOrderMapper.selectById(id);
        return cxrUserOrder;
    }

    @Override
    public SummaryOrderVo summaryOrders(OrderSearchReqVo reqVo) {
        Integer pageNum = reqVo.getPageNo();
        Integer pageSize = reqVo.getPageSize();
        Page page = new Page(pageNum, pageSize);

        Date endDate = reqVo.getEndDate();
        if (endDate != null) {
            endDate.setHours(23);
            endDate.setMinutes(59);
            endDate.setSeconds(59);
        }

        LoginEmployee loginUser = StaffLoginHelper.getLoginUser();
        reqVo.setCreateBy(loginUser.getUserId());
        List<CxrPostId> cxrPostIds = loginUser.getCxrPostIds().stream().collect(Collectors.toList());
        if (cxrPostIds.size() > 0) {
            long count =
                cxrPostIds.stream()
                    .filter(
                        a ->
                            a.getName().equals("主管")
                                || a.getName().equals("储备主管")
                                || a.getName().equals("代主管"))
                    .count();
            if (count > 0) {
                reqVo.setGroupLeaderFlag(true);
            }
        }
        SummaryOrderVo total = cxrUserOrderMapper.summaryOrders(reqVo);
        if (total == null) {
            total = new SummaryOrderVo();
        }
        total.setAmount(
            ObjectUtil.isEmpty(total.getAmount())
                ? BigDecimal.valueOf(0)
                : total.getAmount().setScale(2, BigDecimal.ROUND_DOWN));
        total.setOrdersAchievementSummary(cxrUserOrderMapper.summaryOrdersAchievement(reqVo));
        return total;
    }

    @Override
    public int delete(Long id) {

        LoginInfo loginUser = LoginUtil.getLoginUser();
        Long userId = loginUser.getUserId();
        CxrUserOrder userOrder = cxrUserOrderMapper.selectById(id);

        if (userOrder == null
            || !userId.equals(userOrder.getCreateBy())
            || !DeleteStatus.NOT_DELETED.getValue().equals(userOrder.getDeleteStatus())) {
            throw new ServiceException("订单不存在");
        }

        if (userOrder.getPayStatus() == null
            || PayStatusEnums.WAIT_PAY.getValue() != userOrder.getPayStatus().intValue()) {
            throw new ServiceException("订单不是待支付，不可以删除");
        }
        RedisLockUtils.tryLockException(
            CacheConstants.ORDER_UPDATE + userOrder.getOrderNo(),
            () -> {
                CxrUserOrder finalUserOrder = cxrUserOrderMapper.selectById(id);
                if (finalUserOrder.getScanQrTimes() != null && finalUserOrder.getScanQrTimes() > 0) {
                    throw new ServiceException("客户已扫码，暂不能删除，两天后未支付订单系统自动删除。");
                }

                CxrUserOrder entity = new CxrUserOrder();
                entity.setId(id);
                entity.setDeleteStatus(DeleteStatus.DELETED.getValue());
                entity.setDeleteBy(userId);
                entity.setDeleteByName(loginUser.getUserName());
                entity.setDeleteTime(new Date());
                return cxrUserOrderMapper.updateById(entity);
            }
        );
        return 1;
    }

    @Override
    public IncreaseOrderDetailVo increaseOrderDetail(Long id) {
        CxrUserOrder userOrder = cxrUserOrderMapper.selectById(id);
        if (ObjectUtil.isNull(userOrder)) {
            return null;
        }
        IncreaseOrderDetailVo vo = BeanUtil.copyProperties(userOrder, IncreaseOrderDetailVo.class);
        vo.setType("增订通知");
        vo.setCustomerAddress(userOrder.getProvince() + userOrder.getCity() + userOrder.getArea() + userOrder.getCustomerAdress());
        vo.setBusinessAgents(userOrder.getBusinessAgent().stream()
            .map(BusinessAgent::getProxyName)
            .collect(Collectors.joining(",")));
        List<CxrCustomerAddress> customerAddresses = cxrCustomerAddressMapper.selectList(new LambdaQueryWrapper<CxrCustomerAddress>()
            .select(CxrCustomerAddress::getProvice, CxrCustomerAddress::getCity,
                CxrCustomerAddress::getArea, CxrCustomerAddress::getDetailDistributionAddress, CxrCustomerAddress::getCxrEmployeeId
            )
            .eq(CxrCustomerAddress::getCxrCustomerId, userOrder.getCustomerId())
            .orderByDesc(CxrCustomerAddress::getCreateTime)
        );
        if (CollUtil.isNotEmpty(customerAddresses)) {
            Set<Long> employeeIds = customerAddresses.stream()
                .map(CxrCustomerAddress::getCxrEmployeeId).collect(Collectors.toSet());
            List<CxrEmployee> cxrEmployees = employeeMapper.selectBatchIds(employeeIds);
            Map<Long, String> employeeMap = cxrEmployees.stream().collect(Collectors.toMap(a -> a.getId(),
                a -> a.getName(), (v1, v2) -> v1));
            CxrCustomerAddress customerAddress = customerAddresses.get(0);
            vo.setBusinessAgentsOne(employeeMap.get(customerAddress.getCxrEmployeeId()));
            vo.setCustomerAddressOne(customerAddress.getProvice() + customerAddress.getCity()
                + customerAddress.getArea() + customerAddress.getDetailDistributionAddress());
            if (customerAddresses.size() > 1) {
                CxrCustomerAddress customerAddressTow = customerAddresses.get(1);
                if (ObjectUtil.isNotEmpty(customerAddressTow)) {
                    vo.setBusinessAgentsTow(employeeMap.get(customerAddressTow.getCxrEmployeeId()));
                    vo.setCustomerAddressTow(customerAddressTow.getProvice() + customerAddressTow.getCity()
                        + customerAddressTow.getArea() + customerAddressTow.getDetailDistributionAddress());
                }
            }

        }
        return vo;
    }

    @Override
    public int delCancel(Long id) {

        LoginInfo loginUser = LoginUtil.getLoginUser();
        Long userId = loginUser.getUserId();
        CxrUserOrder userOrder = cxrUserOrderMapper.selectById(id);

        if (userOrder == null
            || !userId.equals(userOrder.getCreateBy())
            || !DeleteStatus.NOT_DELETED.getValue().equals(userOrder.getDeleteStatus())) {
            throw new ServiceException("订单不存在");
        }

        if (PayStatusEnums.PAY_CLOSE.getValue() != userOrder.getPayStatus().intValue()) {
            throw new ServiceException("订单不是取消类型订单，不可以删除");
        }

        RedisLockUtils.tryLockException(
            CacheConstants.ORDER_UPDATE + userOrder.getOrderNo(),
            () -> {
                CxrUserOrder finalUserOrder = cxrUserOrderMapper.selectById(id);
                if (finalUserOrder.getScanQrTimes() != null && finalUserOrder.getScanQrTimes() > 0) {
                    throw new ServiceException("客户已扫码，暂不能删除，两天后未支付订单系统自动删除。");
                }
                CxrUserOrder entity = new CxrUserOrder();
                entity.setId(id);
                entity.setDeleteStatus(DeleteStatus.DELETED.getValue());
                entity.setDeleteBy(userId);
                entity.setDeleteByName(loginUser.getUserName());
                entity.setDeleteTime(new Date());
                return cxrUserOrderMapper.updateById(entity);
            }
        );
        return 1;
    }
}
