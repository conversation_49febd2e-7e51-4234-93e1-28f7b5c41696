<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.calculate.orderCount.mapper.CxrRegionStaitemDailyReportMapper">

    <resultMap id="ReportVoResultMap" type="com.ruoyi.core.base.domain.vo.CxrRegionStaitemDailyReportVo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="reportDate" column="report_date" jdbcType="DATE"/>
        <result property="weekDay" column="week_day" jdbcType="VARCHAR"/>
        <result property="dimensionType" column="dimension_type" jdbcType="VARCHAR"/>
        <result property="companyId" column="company_id" jdbcType="BIGINT"/>
        <result property="companyName" column="company_name" jdbcType="VARCHAR"/>
        <result property="rootRegionId" column="root_region_id" jdbcType="BIGINT"/>
        <result property="rootRegionName" column="root_region_name" jdbcType="VARCHAR"/>
        <result property="regionId" column="region_id" jdbcType="BIGINT"/>
        <result property="regionName" column="region_name" jdbcType="VARCHAR"/>
        <result property="regionCode" column="region_code" jdbcType="VARCHAR"/>
        <result property="siteId" column="site_id" jdbcType="BIGINT"/>
        <result property="siteName" column="site_name" jdbcType="VARCHAR"/>
        <result property="siteCode" column="site_code" jdbcType="VARCHAR"/>
        <result property="agentId" column="agent_id" jdbcType="BIGINT"/>
        <result property="agentName" column="agent_name" jdbcType="VARCHAR"/>
        <result property="agentCode" column="agent_code" jdbcType="VARCHAR"/>
        <result property="agentLevel" column="agent_level" jdbcType="VARCHAR"/>
        <result property="newOrderCount" column="new_order_count" jdbcType="DECIMAL"/>
        <result property="continueOrderCount" column="continue_order_count" jdbcType="DECIMAL"/>
        <result property="increaseOrderCount" column="increase_order_count" jdbcType="DECIMAL"/>
        <result property="returnOrderCount" column="return_order_count" jdbcType="DECIMAL"/>
        <result property="totalOrderCount" column="total_order_count" jdbcType="DECIMAL"/>
        <result property="newOrderAchievement" column="new_order_achievement" jdbcType="DECIMAL"/>
        <result property="continueOrderAchievement" column="continue_order_achievement" jdbcType="DECIMAL"/>
        <result property="increaseOrderAchievement" column="increase_order_achievement" jdbcType="DECIMAL"/>
        <result property="returnOrderAchievement" column="return_order_achievement" jdbcType="DECIMAL"/>
        <result property="totalAchievement" column="total_achievement" jdbcType="DECIMAL"/>
        <result property="newOrderRatio" column="new_order_ratio" jdbcType="DECIMAL"/>
        <result property="continueOrderRatio" column="continue_order_ratio" jdbcType="DECIMAL"/>
        <result property="increaseOrderRatio" column="increase_order_ratio" jdbcType="DECIMAL"/>
        <result property="returnOrderRatio" column="return_order_ratio" jdbcType="DECIMAL"/>
        <result property="newOrderUnitPrice" column="new_order_unit_price" jdbcType="DECIMAL"/>
        <result property="continueOrderUnitPrice" column="continue_order_unit_price" jdbcType="DECIMAL"/>
        <result property="increaseOrderUnitPrice" column="increase_order_unit_price" jdbcType="DECIMAL"/>
        <result property="avgUnitPrice" column="avg_unit_price" jdbcType="DECIMAL"/>
        <result property="newOrderCountYoy" column="new_order_count_yoy" jdbcType="DECIMAL"/>
        <result property="continueOrderCountYoy" column="continue_order_count_yoy" jdbcType="DECIMAL"/>
        <result property="increaseOrderCountYoy" column="increase_order_count_yoy" jdbcType="DECIMAL"/>
        <result property="returnOrderCountYoy" column="return_order_count_yoy" jdbcType="DECIMAL"/>
        <result property="totalOrderCountYoy" column="total_order_count_yoy" jdbcType="DECIMAL"/>
        <result property="newOrderAchievementYoy" column="new_order_achievement_yoy" jdbcType="DECIMAL"/>
        <result property="continueOrderAchievementYoy" column="continue_order_achievement_yoy" jdbcType="DECIMAL"/>
        <result property="increaseOrderAchievementYoy" column="increase_order_achievement_yoy" jdbcType="DECIMAL"/>
        <result property="returnOrderAchievementYoy" column="return_order_achievement_yoy" jdbcType="DECIMAL"/>
        <result property="totalAchievementYoy" column="total_achievement_yoy" jdbcType="DECIMAL"/>
        <result property="newOrderCountMom" column="new_order_count_mom" jdbcType="DECIMAL"/>
        <result property="continueOrderCountMom" column="continue_order_count_mom" jdbcType="DECIMAL"/>
        <result property="increaseOrderCountMom" column="increase_order_count_mom" jdbcType="DECIMAL"/>
        <result property="returnOrderCountMom" column="return_order_count_mom" jdbcType="DECIMAL"/>
        <result property="totalOrderCountMom" column="total_order_count_mom" jdbcType="DECIMAL"/>
        <result property="newOrderAchievementMom" column="new_order_achievement_mom" jdbcType="DECIMAL"/>
        <result property="continueOrderAchievementMom" column="continue_order_achievement_mom" jdbcType="DECIMAL"/>
        <result property="increaseOrderAchievementMom" column="increase_order_achievement_mom" jdbcType="DECIMAL"/>
        <result property="returnOrderAchievementMom" column="return_order_achievement_mom" jdbcType="DECIMAL"/>
        <result property="totalAchievementMom" column="total_achievement_mom" jdbcType="DECIMAL"/>
    </resultMap>

    <!-- 共用的查询字段 -->
    <sql id="selectColumns">
        SELECT
            id,
            report_date,
            week_day,
            dimension_type,
            company_id,
            company_name,
            root_region_id,
            root_region_name,
            region_id,
            region_name,
            region_code,
            site_id,
            site_name,
            site_code,
            agent_id,
            agent_name,
            agent_code,
            agent_level,
            new_order_count,
            continue_order_count,
            increase_order_count,
            return_order_count,
            total_order_count,
            new_order_achievement,
            continue_order_achievement,
            increase_order_achievement,
            return_order_achievement,
            total_achievement,
            new_order_ratio,
            continue_order_ratio,
            increase_order_ratio,
            return_order_ratio,
            new_order_unit_price,
            continue_order_unit_price,
            increase_order_unit_price,
            avg_unit_price,
            new_order_count_yoy,
            continue_order_count_yoy,
            increase_order_count_yoy,
            return_order_count_yoy,
            total_order_count_yoy,
            new_order_achievement_yoy,
            continue_order_achievement_yoy,
            increase_order_achievement_yoy,
            return_order_achievement_yoy,
            total_achievement_yoy,
            new_order_count_mom,
            continue_order_count_mom,
            increase_order_count_mom,
            return_order_count_mom,
            total_order_count_mom,
            new_order_achievement_mom,
            continue_order_achievement_mom,
            increase_order_achievement_mom,
            return_order_achievement_mom,
            total_achievement_mom
        FROM cxr_region_staitem_daily_report
    </sql>

    <!-- 共用的基础查询条件 -->
    <sql id="baseWhereCondition">
        <where>
            delete_status = '0'
            <if test="bo.startDate != null">
                AND report_date >= #{bo.startDate}
            </if>
            <if test="bo.endDate != null">
                AND report_date &lt;= #{bo.endDate}
            </if>
            <if test="bo.companyId != null">
                AND company_id = #{bo.companyId}
            </if>
            <if test="bo.companyName != null and bo.companyName != ''">
                AND company_name LIKE CONCAT('%', #{bo.companyName}, '%')
            </if>
            <if test="bo.rootRegionId != null">
                AND root_region_id = #{bo.rootRegionId}
            </if>
            <if test="bo.rootRegionName != null and bo.rootRegionName != ''">
                AND root_region_name LIKE CONCAT('%', #{bo.rootRegionName}, '%')
            </if>
            <if test="bo.regionId != null">
                AND region_id = #{bo.regionId}
            </if>
            <if test="bo.regionName != null and bo.regionName != ''">
                AND region_name LIKE CONCAT('%', #{bo.regionName}, '%')
            </if>
            <if test="bo.siteId != null">
                AND site_id = #{bo.siteId}
            </if>
            <if test="bo.siteName != null and bo.siteName != ''">
                AND site_name LIKE CONCAT('%', #{bo.siteName}, '%')
            </if>
            <if test="bo.agentId != null">
                AND agent_id = #{bo.agentId}
            </if>
            <if test="bo.agentName != null and bo.agentName != ''">
                AND agent_name LIKE CONCAT('%', #{bo.agentName}, '%')
            </if>
            <if test="bo.agentCode != null and bo.agentCode != ''">
                AND agent_code LIKE CONCAT('%', #{bo.agentCode}, '%')
            </if>
            <if test="bo.agentLevel != null and bo.agentLevel != ''">
                AND agent_level = #{bo.agentLevel}
            </if>
            <if test="bo.weekDay != null and bo.weekDay != ''">
                AND week_day = #{bo.weekDay}
            </if>
        </where>
    </sql>

    <!-- 分页查询区域单数统计明细 - 公司维度 -->
    <select id="selectCompanyReportPage" resultMap="ReportVoResultMap">
        <include refid="selectColumns"/>
        <include refid="baseWhereCondition"/>
        AND dimension_type = 'COMPANY'
        <!-- 公司维度权限控制 -->
        <if test="bo.companyIds != null and bo.companyIds.size() > 0">
            AND company_id IN
            <foreach collection="bo.companyIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        ORDER BY report_date DESC, company_id
    </select>

    <!-- 分页查询区域单数统计明细 - 大区维度 -->
    <select id="selectRootRegionReportPage" resultMap="ReportVoResultMap">
        <include refid="selectColumns"/>
        <include refid="baseWhereCondition"/>
        AND dimension_type = 'ROOT_REGION'
        <!-- 大区维度权限控制 -->
        <if test="bo.companyIds != null and bo.companyIds.size() > 0">
            AND company_id IN
            <foreach collection="bo.companyIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="bo.rootRegionIds != null and bo.rootRegionIds.size() > 0">
            AND root_region_id IN
            <foreach collection="bo.rootRegionIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        ORDER BY report_date DESC, root_region_id
    </select>

    <!-- 分页查询区域单数统计明细 - 区域维度 -->
    <select id="selectRegionReportPage" resultMap="ReportVoResultMap">
        <include refid="selectColumns"/>
        <include refid="baseWhereCondition"/>
        AND dimension_type = 'REGION'
        <!-- 区域维度权限控制 -->
        <if test="bo.companyIds != null and bo.companyIds.size() > 0">
            AND company_id IN
            <foreach collection="bo.companyIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="bo.rootRegionIds != null and bo.rootRegionIds.size() > 0">
            AND root_region_id IN
            <foreach collection="bo.rootRegionIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="bo.regionIds != null and bo.regionIds.size() > 0">
            AND region_id IN
            <foreach collection="bo.regionIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        ORDER BY report_date DESC, region_id
    </select>

    <!-- 分页查询区域单数统计明细 - 站点维度 -->
    <select id="selectSiteReportPage" resultMap="ReportVoResultMap">
        <include refid="selectColumns"/>
        <include refid="baseWhereCondition"/>
        AND dimension_type = 'SITE'
        <!-- 站点维度权限控制 -->
        <if test="bo.companyIds != null and bo.companyIds.size() > 0">
            AND company_id IN
            <foreach collection="bo.companyIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="bo.rootRegionIds != null and bo.rootRegionIds.size() > 0">
            AND root_region_id IN
            <foreach collection="bo.rootRegionIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="bo.regionIds != null and bo.regionIds.size() > 0">
            AND region_id IN
            <foreach collection="bo.regionIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="bo.siteIds != null and bo.siteIds.size() > 0">
            AND site_id IN
            <foreach collection="bo.siteIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        ORDER BY report_date DESC, site_id
    </select>

    <!-- 分页查询区域单数统计明细 - 代理维度 -->
    <select id="selectAgentReportPage" resultMap="ReportVoResultMap">
        <include refid="selectColumns"/>
        <include refid="baseWhereCondition"/>
        AND dimension_type = 'AGENT'
        <!-- 代理维度权限控制 -->
        <if test="bo.companyIds != null and bo.companyIds.size() > 0">
            AND company_id IN
            <foreach collection="bo.companyIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="bo.rootRegionIds != null and bo.rootRegionIds.size() > 0">
            AND root_region_id IN
            <foreach collection="bo.rootRegionIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="bo.regionIds != null and bo.regionIds.size() > 0">
            AND region_id IN
            <foreach collection="bo.regionIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="bo.siteIds != null and bo.siteIds.size() > 0">
            AND site_id IN
            <foreach collection="bo.siteIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        ORDER BY report_date DESC, agent_id
    </select>

    <sql id="sumColumns">
        IFNULL(SUM(new_order_count), 0) as new_order_count,
        IFNULL(SUM(continue_order_count), 0) as continue_order_count,
        IFNULL(SUM(increase_order_count), 0) as increase_order_count,
        IFNULL(SUM(return_order_count), 0) as return_order_count,
        IFNULL(SUM(total_order_count), 0) as total_order_count,


        IFNULL(SUM(new_order_achievement), 0) as new_order_achievement,
        IFNULL(SUM(continue_order_achievement), 0) as continue_order_achievement,
        IFNULL(SUM(increase_order_achievement), 0) as increase_order_achievement,
        IFNULL(SUM(return_order_achievement), 0) as return_order_achievement,
        IFNULL(SUM(total_achievement), 0) as total_achievement
    </sql>

    <!-- 合计查询当期数据 - 公司维度 -->
    <select id="sumCompanyReport" resultMap="ReportVoResultMap">
        SELECT
            <include refid="sumColumns"/>
        FROM cxr_region_staitem_daily_report
        <include refid="baseWhereCondition"/>
        AND dimension_type = 'COMPANY'
        <!-- 公司维度权限控制 -->
        <if test="bo.companyIds != null and bo.companyIds.size() > 0">
            AND company_id IN
            <foreach collection="bo.companyIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>

    <!-- 合计查询当期数据 - 大区维度 -->
    <select id="sumRootRegionReport" resultMap="ReportVoResultMap">
        SELECT
            <include refid="sumColumns"/>
        FROM cxr_region_staitem_daily_report
        <include refid="baseWhereCondition"/>
        AND dimension_type = 'ROOT_REGION'
        <!-- 大区维度权限控制 -->
        <if test="bo.companyIds != null and bo.companyIds.size() > 0">
            AND company_id IN
            <foreach collection="bo.companyIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="bo.rootRegionIds != null and bo.rootRegionIds.size() > 0">
            AND root_region_id IN
            <foreach collection="bo.rootRegionIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>

    <!-- 合计查询当期数据 - 区域维度 -->
    <select id="sumRegionReport" resultMap="ReportVoResultMap">
        SELECT
            <include refid="sumColumns"/>
        FROM cxr_region_staitem_daily_report
        <include refid="baseWhereCondition"/>
        AND dimension_type = 'REGION'
        <!-- 区域维度权限控制 -->
        <if test="bo.companyIds != null and bo.companyIds.size() > 0">
            AND company_id IN
            <foreach collection="bo.companyIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="bo.rootRegionIds != null and bo.rootRegionIds.size() > 0">
            AND root_region_id IN
            <foreach collection="bo.rootRegionIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="bo.regionIds != null and bo.regionIds.size() > 0">
            AND region_id IN
            <foreach collection="bo.regionIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>

    <!-- 合计查询当期数据 - 站点维度 -->
    <select id="sumSiteReport" resultMap="ReportVoResultMap">
        SELECT
            <include refid="sumColumns"/>
        FROM cxr_region_staitem_daily_report
        <include refid="baseWhereCondition"/>
        AND dimension_type = 'SITE'
        <!-- 站点维度权限控制 -->
        <if test="bo.companyIds != null and bo.companyIds.size() > 0">
            AND company_id IN
            <foreach collection="bo.companyIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="bo.rootRegionIds != null and bo.rootRegionIds.size() > 0">
            AND root_region_id IN
            <foreach collection="bo.rootRegionIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="bo.regionIds != null and bo.regionIds.size() > 0">
            AND region_id IN
            <foreach collection="bo.regionIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="bo.siteIds != null and bo.siteIds.size() > 0">
            AND site_id IN
            <foreach collection="bo.siteIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>

    <!-- 合计查询当期数据 - 代理维度 -->
    <select id="sumAgentReport" resultMap="ReportVoResultMap">
        SELECT
            <include refid="sumColumns"/>
        FROM cxr_region_staitem_daily_report
        <include refid="baseWhereCondition"/>
        AND dimension_type = 'AGENT'
        <!-- 代理维度权限控制 -->
        <if test="bo.companyIds != null and bo.companyIds.size() > 0">
            AND company_id IN
            <foreach collection="bo.companyIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="bo.rootRegionIds != null and bo.rootRegionIds.size() > 0">
            AND root_region_id IN
            <foreach collection="bo.rootRegionIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="bo.regionIds != null and bo.regionIds.size() > 0">
            AND region_id IN
            <foreach collection="bo.regionIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="bo.siteIds != null and bo.siteIds.size() > 0">
            AND site_id IN
            <foreach collection="bo.siteIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>

</mapper>
