package com.ruoyi.order.common.domain.vo;

import com.ruoyi.business.base.api.domain.json.BusinessAgent;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/8/10 15:54
 */
@Data
public class LastUserOrderVo implements Serializable {


    /**
     * 订购数量
     */
    @ApiModelProperty("10天内订购数（不含活动订单）")
    private Integer orderQuantity;

    /**
     * 订购数量：鲜奶订购数量
     */
    @ApiModelProperty("10天内订购数（含活动订单）")
    private Integer orderQty;


    @ApiModelProperty("当日累计订购数量")
    private Integer daySumQty;

    /**
     * 鲜奶赠送数量
     */
    @ApiModelProperty("鲜奶赠送数量")
    private Integer freshMilkGiveQuantity;
    /**
     * 常温奶赠送数量
     */
    @ApiModelProperty("常温奶赠送数量")
    private Integer longMilkGiveQuantity;
    /**
     * 超送数量：根据订单套餐超送规则计算出来的超送数量;祥看原型
     */
    @ApiModelProperty("超送数量")
    private Integer excessQuantity;
    /**
     * 鲜奶已送数量
     */
    @ApiModelProperty("鲜奶已送数量")
    private Integer freshMilkSentQuantity;
    /**
     * 常温奶已送数量
     */
    @ApiModelProperty("常温奶已送数量")
    private Integer longMilkSentQuantity;

    @ApiModelProperty(value = "剩余数量")
    private Integer surplusQuantity;

    /**
     * 单价：鲜奶单价
     */
    @ApiModelProperty("单价：鲜奶单价")
    private BigDecimal unitPrice;
    /**
     * 金额：该笔订单的总金额
     */
    @ApiModelProperty("金额：该笔订单的总金额")
    private BigDecimal amount;
    /**
     * 刷卡金额
     */
    @ApiModelProperty("刷卡金额")
    private BigDecimal creditCardAmount;
    /**
     * 订单日期
     */
    @ApiModelProperty("订单日期")
    private Date orderDate;
    /**
     * 1 待审核 、 2 已审核 、 3.已拒绝
     */
    @ApiModelProperty("1 待审核 、 2 已审核 、 3.已拒绝")
    private Integer auditStatus;

    /**
     * 是否促销单;true 是 false 否
     */
    @ApiModelProperty("是否促销单;true 是 false 否")
    private Boolean promotionalOrderFlag;
    /**
     * 是否师徒单;true是 false 否
     */
    @ApiModelProperty("是否师徒单;true是 false 否")
    private Boolean apprenticeOrderFlag;

    private List<BusinessAgent> businessAgents;
}
