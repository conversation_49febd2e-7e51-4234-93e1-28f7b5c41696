package com.ruoyi.business.base.cxrRoomBoardAccounts.service.Impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.business.api.domain.EmployeeHistoryDate;
import com.ruoyi.business.base.api.config.RoomBoradAccountsDate;
import com.ruoyi.business.base.api.domain.CxrEmployee;
import com.ruoyi.business.base.api.domain.CxrEmployeePost;
import com.ruoyi.business.base.api.domain.CxrSite;
import com.ruoyi.business.base.api.domain.RoomBoradAccounts;
import com.ruoyi.business.base.api.domain.dto.ComputeDTO;
import com.ruoyi.business.base.api.dubbo.RemoteEmployeePostService;
import com.ruoyi.business.base.api.dubbo.RemoteEmployeeService;
import com.ruoyi.business.base.api.model.LoginEmployee;
import com.ruoyi.business.base.cxrRoomBoardAccounts.domain.bo.RoomBoradAccountAdd;
import com.ruoyi.business.base.cxrRoomBoardAccounts.domain.bo.RoomBoradAccountsBo;
import com.ruoyi.business.base.cxrRoomBoardAccounts.domain.vo.RoomBoradAccountsVo;
import com.ruoyi.business.base.cxrRoomBoardAccounts.mapper.RoomBoradAccountsMapper;
import com.ruoyi.business.base.cxrRoomBoardAccounts.service.ICxrRoomBoradAccountsService;
import com.ruoyi.business.base.employee.service.ICxrEmployeeService;
import com.ruoyi.business.base.employeePost.mapper.CxrEmployeePostMapper;
import com.ruoyi.business.base.site.mapper.CxrSiteMapper;
import com.ruoyi.common.core.domain.ExcelReportDTO;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.validator.AbstractAssert;
import com.ruoyi.common.excel.utils.ExcelExportObj;
import com.ruoyi.common.excel.utils.ExcelMqUtil;
import com.ruoyi.common.mybatis.core.page.PageQuery;
import com.ruoyi.common.mybatis.core.page.PageTableDataInfo;
import com.ruoyi.common.rocketmq.constant.biz.MqBizConst;
import com.ruoyi.common.rocketmq.utils.MqUtil;
import com.ruoyi.common.satoken.utils.helper.LoginHelper;
import com.ruoyi.common.satoken.utils.helper.StaffLoginHelper;
import com.ruoyi.system.api.model.LoginUser;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

@Slf4j
@RequiredArgsConstructor
@Service
@GlobalTransactional(rollbackFor = Exception.class)
public class CxrRoomBoardAccountsServiceImpl extends ServiceImpl<RoomBoradAccountsMapper, RoomBoradAccounts> implements
    ICxrRoomBoradAccountsService {

    private final RoomBoradAccountsMapper roomBoradAccountsMapper;
    private final CxrSiteMapper cxrSiteMapper;
    private final ICxrEmployeeService iCxrEmployeeService;

    private final CxrEmployeePostMapper cxrEmployeePostMapper;
    @Autowired
    private RoomBoradAccountsDate roomBoradAccountsDate;
    @DubboReference
    private RemoteEmployeeService remoteEmployeeService;
    @DubboReference
    private RemoteEmployeePostService remoteEmployeePostService;

    @Autowired
    private MqUtil mqUtil;

    @Override
    public PageTableDataInfo<RoomBoradAccountsVo> listRoomBoradAccounts(RoomBoradAccountsBo roomBoradAccountsBo,
        PageQuery pageQuery) {
        PageTableDataInfo pageTableDataInfo = new PageTableDataInfo();
        LoginEmployee loginUser = StaffLoginHelper.getLoginUser();
        Long userId = loginUser.getUserId();
        roomBoradAccountsBo.setCreateBy(userId);
        roomBoradAccountsBo.setCxrSiteId(roomBoradAccountsBo.getCxrSiteId());
        IPage<RoomBoradAccountsVo> listRoomBoradAccounts = roomBoradAccountsMapper.listRoomBoradAccounts(
            roomBoradAccountsBo, pageQuery.build());
        return pageTableDataInfo.build(listRoomBoradAccounts);
    }

    @Override
    public List<RoomBoradAccountsVo> getRoomBoradAccountsId(RoomBoradAccountsBo roomBoradAccountsBo) {
        return roomBoradAccountsMapper.getRoomBoradAccountsId(roomBoradAccountsBo);
    }

    @Override
    public Integer updateGetId(RoomBoradAccountAdd roomBoradAccountAdd) {
        roomBoradAccountsMapper
            .del(roomBoradAccountAdd.getTimeStr(), roomBoradAccountAdd.getCxrSiteId());
        LocalDate currentDate = LocalDate.now();
        int currentDayOfMonth = currentDate.getDayOfMonth();
        LocalDate lastDayOfPreviousMonth = currentDate.with(TemporalAdjusters.firstDayOfMonth())
            .minusDays(1);
        // 判断当前日期是否在时间范围内
        //需要考虑一种情况:一个财务管理两个站点

        if (currentDayOfMonth >= roomBoradAccountsDate.getStartDate() || currentDayOfMonth <= roomBoradAccountsDate
            .getEndDate()) {

            List<RoomBoradAccountsVo> roomBoradAccounts =
                roomBoradAccountsMapper.getTime(roomBoradAccountAdd.getCxrSiteId(), roomBoradAccountAdd.getTimeStr());
            RoomBoradAccountsBo accounts = new RoomBoradAccountsBo();
            if (CollUtil.isNotEmpty(roomBoradAccounts)) {
                accounts.setTimeStr(roomBoradAccountAdd.getTimeStr());
                accounts.setCxrSiteId(roomBoradAccountAdd.getCxrSiteId());
                accounts.setStatus(roomBoradAccounts.get(0).getStatus());
            }
            //当状态的提示
            List<RoomBoradAccountsVo> getCheckAdd = roomBoradAccountsMapper.getCheckAdd(accounts);
            if (CollectionUtil.isNotEmpty(getCheckAdd)) {
                if (getCheckAdd.get(0).getStatus().equals(RoomBoradAccountsStatus.CHECK_PEND.getValue())) {
                    return 1;
                }
                if (getCheckAdd.get(0).getStatus().equals(RoomBoradAccountsStatus.APPROV.getValue())) {
                    throw new ServiceException("已存在审核的记录,请勿重复提交!!!");
                }
                if (getCheckAdd.get(0).getStatus().equals(RoomBoradAccountsStatus.HAS_REFUSED_TO.getValue())) {
                    return 2;
                }
            } else {
                List<RoomBoradAccounts> list = new ArrayList<>();
                List<RoomBoradAccountsBo> roomBoradAccountsBo = roomBoradAccountAdd.getRoomBoradAccountsBo();
                Map<Long, RoomBoradAccountsBo> accountsBoMap = roomBoradAccountsBo.stream()
                    .collect(Collectors.toMap(a -> a.getEmployeeId(), a -> a));
                for (RoomBoradAccountsBo em : roomBoradAccountsBo) {
                    RoomBoradAccounts bo = new RoomBoradAccounts();
                    bo.setCxrSiteId(em.getCxrSiteId());
                    bo.setEmployeeId(accountsBoMap.get(em.getEmployeeId()).getEmployeeId());
                    bo.setCreateTime(new Date());
                    bo.setCreateBy(StaffLoginHelper.getLoginUser().getUserId());
                    bo.setCreateByType(StaffLoginHelper.getLoginUser().getUserType());
                    bo.setStatus(RoomBoradAccountsStatus.CHECK_PEND.getValue());
                    bo.setBlowDay(em.getBlowDay());
                    bo.setEatDay(em.getEatDay());
                    //判断是否是上个月的,还是本月的
                    DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM");
                    LocalDate beForeMouth = roomBoradAccountAdd.getTime();
                    LocalDate mouth = LocalDate.now();
                    String dateStr = beForeMouth.format(fmt);
                    String mouthStr = mouth.format(fmt);
                    //情况:现在是07-03  2023-07
                    // 1.提交上个月的 2023-06  时间:2023-06-01
                    if (dateStr.compareTo(mouthStr) < 0) {
                        bo.setTime(roomBoradAccountAdd.getTime());
                    } else {
                        if (currentDayOfMonth >= roomBoradAccountsDate.getStartDate()) {
                            bo.setTime(LocalDate.now());
                        } else {
                            if (currentDayOfMonth <= roomBoradAccountsDate.getEndDate()) {
                                bo.setTime(lastDayOfPreviousMonth);
                            }
                        }
                    }

                    bo.setLiveDay(em.getLiveDay());
                    bo.setEmployeeName(em.getEmployeeName());
                    CxrSite getCxrSite = cxrSiteMapper.getCxrSite(bo.getCxrSiteId());
                    bo.setCxrSiteName(getCxrSite.getName());
                    //添加的是人员在职状态
                    CxrEmployee getListRoomPeopele = iCxrEmployeeService.getListRoomPeopeleIds(
                        accountsBoMap.get(em.getEmployeeId()).getEmployeeId(), roomBoradAccountAdd.getTimeStr());

                    if (getListRoomPeopele.getOccupationStatus().equals(OccupationStatus.QUIT_ONESELF.getValue())) {
                        bo.setOccupationStatus(RoomOccupationStatus.LEAVE_OFFEICE.getValue());
                    }
                    if (getListRoomPeopele.getOccupationStatus()
                        .equals(OccupationStatus.QUIT_PROCEDURE.getValue())) {
                        bo.setOccupationStatus(RoomOccupationStatus.LEAVE_OFFEICE.getValue());
                    }
                    if (getListRoomPeopele.getOccupationStatus().equals(OccupationStatus.INDUCTION.getValue())) {
                        bo.setOccupationStatus(RoomOccupationStatus.INDUCTION.getValue());
                    }
                    list.add(bo);
                }
                roomBoradAccountsMapper.insertBatch(list);
            }
        }
        return 3;
    }

    @Override
    @Transactional
    public Integer add(RoomBoradAccountAdd roomBoradAccountAdd) {
        LocalDate currentDate = LocalDate.now();
        int currentDayOfMonth = currentDate.getDayOfMonth();
        LocalDate lastDayOfPreviousMonth = currentDate.with(TemporalAdjusters.firstDayOfMonth())
            .minusDays(1);
        // 判断当前日期是否在时间范围内
        //需要考虑一种情况:一个财务管理两个站点

        if (currentDayOfMonth >= roomBoradAccountsDate.getStartDate() || currentDayOfMonth <= roomBoradAccountsDate
            .getEndDate()) {

            List<RoomBoradAccountsVo> roomBoradAccounts =
                roomBoradAccountsMapper.getTime(roomBoradAccountAdd.getCxrSiteId(), roomBoradAccountAdd.getTimeStr());
            RoomBoradAccountsBo accounts = new RoomBoradAccountsBo();
            if (CollUtil.isNotEmpty(roomBoradAccounts)) {
                accounts.setTimeStr(roomBoradAccountAdd.getTimeStr());
                accounts.setCxrSiteId(roomBoradAccountAdd.getCxrSiteId());
                accounts.setStatus(roomBoradAccounts.get(0).getStatus());
            }
            //当状态的提示
            List<RoomBoradAccountsVo> getCheckAdd = roomBoradAccountsMapper.getCheckAdd(accounts);
            if (CollectionUtil.isNotEmpty(getCheckAdd)) {
                if (getCheckAdd.get(0).getStatus().equals(RoomBoradAccountsStatus.CHECK_PEND.getValue())) {
                    return 1;
                }
                if (getCheckAdd.get(0).getStatus().equals(RoomBoradAccountsStatus.APPROV.getValue())) {
                    throw new ServiceException("已存在审核的记录,请勿重复提交!!!");
                }
                if (getCheckAdd.get(0).getStatus().equals(RoomBoradAccountsStatus.HAS_REFUSED_TO.getValue())) {
                    return 2;
                }
            } else {
                List<RoomBoradAccounts> list = new ArrayList<>();
                List<RoomBoradAccountsBo> roomBoradAccountsBo = roomBoradAccountAdd.getRoomBoradAccountsBo();
                Map<Long, RoomBoradAccountsBo> accountsBoMap = roomBoradAccountsBo.stream()
                    .collect(Collectors.toMap(a -> a.getEmployeeId(), a -> a));
                for (RoomBoradAccountsBo em : roomBoradAccountsBo) {
                    RoomBoradAccounts bo = new RoomBoradAccounts();
                    bo.setCxrSiteId(em.getCxrSiteId());
                    bo.setEmployeeId(accountsBoMap.get(em.getEmployeeId()).getEmployeeId());
                    bo.setCreateTime(new Date());
                    bo.setCreateBy(StaffLoginHelper.getLoginUser().getUserId());
                    bo.setCreateByType(StaffLoginHelper.getLoginUser().getUserType());
                    bo.setStatus(RoomBoradAccountsStatus.CHECK_PEND.getValue());
                    bo.setBlowDay(em.getBlowDay());
                    bo.setEatDay(em.getEatDay());
                    //判断是否是上个月的,还是本月的
                    DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM");
                    LocalDate beForeMouth = roomBoradAccountAdd.getTime();
                    LocalDate mouth = LocalDate.now();
                    String dateStr = beForeMouth.format(fmt);
                    String mouthStr = mouth.format(fmt);
                    //情况:现在是07-03  2023-07
                    // 1.提交上个月的 2023-06  时间:2023-06-01
                    if (dateStr.compareTo(mouthStr) < 0) {
                        bo.setTime(roomBoradAccountAdd.getTime());
                    } else {
                        if (currentDayOfMonth >= roomBoradAccountsDate.getStartDate()) {
                            bo.setTime(LocalDate.now());
                        } else {
                            if (currentDayOfMonth <= roomBoradAccountsDate.getEndDate()) {
                                bo.setTime(lastDayOfPreviousMonth);
                            }
                        }
                    }

                    bo.setLiveDay(em.getLiveDay());
                    bo.setEmployeeName(em.getEmployeeName());
                    CxrSite getCxrSite = cxrSiteMapper.getCxrSite(bo.getCxrSiteId());
                    bo.setCxrSiteName(getCxrSite.getName());
                    //添加的是人员在职状态
                    CxrEmployee getListRoomPeopele = iCxrEmployeeService.getListRoomPeopeleIds(
                        accountsBoMap.get(em.getEmployeeId()).getEmployeeId(), roomBoradAccountAdd.getTimeStr());

                    if (getListRoomPeopele.getOccupationStatus().equals(OccupationStatus.QUIT_ONESELF.getValue())) {
                        bo.setOccupationStatus(RoomOccupationStatus.LEAVE_OFFEICE.getValue());
                    }
                    if (getListRoomPeopele.getOccupationStatus()
                        .equals(OccupationStatus.QUIT_PROCEDURE.getValue())) {
                        bo.setOccupationStatus(RoomOccupationStatus.LEAVE_OFFEICE.getValue());
                    }
                    if (getListRoomPeopele.getOccupationStatus().equals(OccupationStatus.INDUCTION.getValue())) {
                        bo.setOccupationStatus(RoomOccupationStatus.INDUCTION.getValue());
                    }
                    list.add(bo);
                }
                roomBoradAccountsMapper.insertBatch(list);
            }
        } else {
            throw new ServiceException("每月26号到次月3号可提交申请!");
        }
        return 3;
    }


    @Override
    public PageTableDataInfo<RoomBoradAccountsVo> autidRoomBoradAccounts(RoomBoradAccountsBo roomBoradAccountsBo,
        PageQuery pageQuery) {
        PageTableDataInfo pageTableDataInfo = new PageTableDataInfo();
        LoginEmployee loginUser = StaffLoginHelper.getLoginUser();
        Long userId = loginUser.getUserId();
        Long cxrSiteId = roomBoradAccountsBo.getCxrSiteId();
        CxrEmployeePost cxrEmployeePost = remoteEmployeePostService.getDeparetment(userId, cxrSiteId);
        CxrEmployeePost financePost = remoteEmployeePostService.getFinance(userId, cxrSiteId);
        //两者都是的
        if (cxrEmployeePost != null && financePost != null) {
            roomBoradAccountsBo.setCxrSiteId(cxrSiteId);
            IPage<RoomBoradAccountsVo> getBothChar = roomBoradAccountsMapper.getBothChar(roomBoradAccountsBo,
                pageQuery.build());
            return pageTableDataInfo.build(getBothChar);
        }
        //财务
        if (financePost != null) {
            roomBoradAccountsBo.setCxrSiteId(cxrSiteId);
            IPage<RoomBoradAccountsVo> getFinancePost = roomBoradAccountsMapper.getFinancePost(roomBoradAccountsBo,
                pageQuery.build());
            return pageTableDataInfo.build(getFinancePost);
        }
        //主管
        if (cxrEmployeePost != null) {
            roomBoradAccountsBo.setCxrSiteId(cxrSiteId);
            IPage<RoomBoradAccountsVo> getCxrEmployeePost = roomBoradAccountsMapper.getCxrEmployeePost(
                roomBoradAccountsBo, pageQuery.build());
            return pageTableDataInfo.build(getCxrEmployeePost);
        }
        IPage<RoomBoradAccountsVo> autidRoomBoradAccounts = roomBoradAccountsMapper.autidRoomBoradAccounts(
            roomBoradAccountsBo, pageQuery.build());
        return pageTableDataInfo.build(autidRoomBoradAccounts);
    }

    @Override
    public Integer autidUpdate(RoomBoradAccountsBo roomBoradAccountsBo) {
        if (roomBoradAccountsBo.getStatus() == 0) {
            roomBoradAccountsBo.setUpdateTime(new Date());
            roomBoradAccountsBo.setUpdateBy(StaffLoginHelper.getLoginUser().getUserId());
            roomBoradAccountsBo.setUpdateByName(StaffLoginHelper.getLoginUser().getUserName());
            roomBoradAccountsBo.setAuditTime(new Date());
            roomBoradAccountsBo.setAuditUserId(StaffLoginHelper.getLoginUser().getUserId());
            roomBoradAccountsBo.setAuditUserName(StaffLoginHelper.getLoginUser().getUserName());
            int num = roomBoradAccountsMapper.updateFail(roomBoradAccountsBo);
            if (num < 0) {
                return 0;
            }
        }
        if (roomBoradAccountsBo.getStatus() == 1) {

            String timeStr = roomBoradAccountsBo.getTimeStr();
            Integer endDay = roomBoradAccountsDate.getEndDate();
            LocalDate nextMonthDay3 = YearMonth.parse(timeStr).atDay(endDay).plusMonths(1);
            AbstractAssert.isTrue(!LocalDate.now().isBefore(nextMonthDay3), "审核时间必须在" + nextMonthDay3 + "之前");

            roomBoradAccountsBo.setUpdateTime(new Date());
            roomBoradAccountsBo.setUpdateBy(StaffLoginHelper.getLoginUser().getUserId());
            roomBoradAccountsBo.setUpdateByName(StaffLoginHelper.getLoginUser().getUserName());
            roomBoradAccountsBo.setAuditTime(new Date());
            roomBoradAccountsBo.setAuditUserId(StaffLoginHelper.getLoginUser().getUserId());
            roomBoradAccountsBo.setAuditUserName(StaffLoginHelper.getLoginUser().getUserName());
            int num = roomBoradAccountsMapper.updateSucessful(roomBoradAccountsBo);
            if (num < 0) {
                return 0;
            }
            //依据站点id把对应的金额查询出来
            Long siteId = StaffLoginHelper.getLoginUser().getSiteId();
            CxrSite getCxrSite = cxrSiteMapper.getCxrSite(siteId);
            List<RoomBoradAccounts> getPeople = this.getBaseMapper()
                .getPeople(roomBoradAccountsBo.getCxrSiteId(), timeStr);
            List<RoomBoradAccounts> list = new ArrayList<>();
            for (RoomBoradAccounts e : getPeople) {
                double eatDayMoney = 0;//吃的价格
                double liveDayMoney = 0;//住的价格
                double blowDayMoney = 0;//吹空调的价格
                if (e.getEatDay() > 15) {
                    eatDayMoney = getCxrSite.getMonthEatCost();
                } else if (e.getEatDay() == 0) {
                    eatDayMoney = 0;
                } else {
                    eatDayMoney = getCxrSite.getHalfAMonthEatCost();
                }

                if (e.getBlowDay() > 15) {
                    blowDayMoney = getCxrSite.getMonthBlowCost();
                } else if (e.getBlowDay() == 0) {
                    blowDayMoney = 0;
                } else {
                    blowDayMoney = getCxrSite.getHalfAMonthBlowCost();
                }

                if (e.getLiveDay() > 15) {
                    liveDayMoney = getCxrSite.getMonthLiveCost();
                } else if (e.getLiveDay() == 0) {
                    liveDayMoney = 0;
                } else {
                    liveDayMoney = getCxrSite.getHalfAMonthLiveCost();
                }
                e.setTotal(eatDayMoney + blowDayMoney + liveDayMoney);
                e.setEatDayMoney(eatDayMoney);
                e.setBlowDayMoney(blowDayMoney);
                e.setLiveDayMoney(liveDayMoney);
                setValue(e);
                list.add(e);
            }
            boolean isSuccess = roomBoradAccountsMapper.updateBatchById(list);
            if(isSuccess) {
                computeDivideEatLiveAc(list, timeStr);
            }

        }
        return 1;
    }

    @Override
    public PageTableDataInfo<RoomBoradAccountsVo> listGetRoomBoradAccounts(RoomBoradAccountsBo roomBoradAccountsBo,
        PageQuery pageQuery) {
        PageTableDataInfo pageTableDataInfo = new PageTableDataInfo();
        LoginUser loginUser = LoginHelper.getLoginUser();
        roomBoradAccountsBo.setEmployeeFlag(loginUser.getEmployeeFlag());
        roomBoradAccountsBo.setSiteIds(loginUser.getSiteIds());
        IPage<RoomBoradAccountsVo> listGetRoomBoradAccounts = roomBoradAccountsMapper.listGetRoomBoradAccounts(
            roomBoradAccountsBo, pageQuery.build());

        List<RoomBoradAccountsVo> records = listGetRoomBoradAccounts.getRecords();
        if (CollUtil.isNotEmpty(records)){
            for (RoomBoradAccountsVo r : records) {
                String postNo = r.getPostNo();
                if (StringUtils.isNotBlank(postNo)){
                    r.setPostNo(StrUtil.split(postNo, StrUtil.COMMA).stream().map(v -> {
                        PostType enumByValue = PostType.getEnumByValue(v);
                        if (PostType.COMMON_EMPLOYEE == enumByValue){
                            return "销售代理";
                        }
                        return enumByValue != null ? enumByValue.getName() : v;
                    }).collect(Collectors.joining(StrUtil.COMMA)));
                }
            }
        }

        return pageTableDataInfo.build(listGetRoomBoradAccounts);
    }

    @Override
    public List<CxrEmployeePost> getFinancial() {
        List<CxrEmployeePost> getFinancial = remoteEmployeePostService
            .getFinace(StaffLoginHelper.getLoginUser().getUserId());
        if (CollUtil.isNotEmpty(getFinancial)) {
            List<Long> getSitIds = getFinancial.stream().map(CxrEmployeePost::getCxrSiteId)
                .collect(Collectors.toList());
            List<CxrSite> siteList = cxrSiteMapper.selectList(new LambdaQueryWrapper<CxrSite>()
                .eq(CxrSite::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .in(CxrSite::getId, getSitIds));
            Map<Long, CxrSite> map = siteList.stream().collect(Collectors.toMap(CxrSite::getId, Function.identity(),
                (v1, v2) -> v2));

            for (CxrEmployeePost post : getFinancial) {
                CxrSite site = map.get(post.getCxrSiteId());
                if (ObjectUtil.isNotEmpty(site)) {
                    post.setCxrSiteName(site.getName());
                }
            }
        }
        return getFinancial;
    }

    @Override
    public List<CxrEmployeePost> getDirector() {
        List<CxrEmployeePost> getDirector = remoteEmployeePostService
            .getDirector(StaffLoginHelper.getLoginUser().getUserId());
        if (CollUtil.isNotEmpty(getDirector)) {
            List<Long> getSitIds = getDirector.stream().map(CxrEmployeePost::getCxrSiteId)
                .collect(Collectors.toList());
            List<CxrSite> siteList = cxrSiteMapper.selectList(new LambdaQueryWrapper<CxrSite>()
                .eq(CxrSite::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                .in(CxrSite::getId, getSitIds));
            Map<Long, CxrSite> map = siteList.stream().collect(Collectors.toMap(CxrSite::getId, Function.identity(),
                (v1, v2) -> v2));

            for (CxrEmployeePost post : getDirector) {
                CxrSite site = map.get(post.getCxrSiteId());
                if (ObjectUtil.isNotEmpty(site)) {
                    post.setCxrSiteName(site.getName());
                }
            }
        }
        return getDirector;
    }

    @Override
    public void export(RoomBoradAccountsBo bo, HttpServletResponse response) {
        int pageSize = 1000;
        long pageCount = 1;
        LoginUser loginUser = LoginHelper.getLoginUser();
        bo.setEmployeeFlag(loginUser.getEmployeeFlag());
        bo.setSiteIds(loginUser.getSiteIds());
        List<RoomBoradAccountsVo>records = new ArrayList<>();
        IPage<RoomBoradAccountsVo> totalPage = this.roomBoradAccountsMapper.listGetRoomBoradAccounts(
            bo, bo.build().setSize(1));
        Long splitTotal = totalPage.getTotal();
        ExcelReportDTO excelReportDTO = ExcelMqUtil. excelSendRecord(bo.getHtmlName(), splitTotal, loginUser.getUserId(),
            loginUser.getUserType(), loginUser.getUserName());
        for (int current = 1; current <= pageCount; current++) {
            PageQuery query = new PageQuery();
            query.setPageNum(current);
            query.setPageSize(pageSize);
            IPage<RoomBoradAccountsVo> roomBoradAccountsPage = roomBoradAccountsMapper.listGetRoomBoradAccounts(
                bo, query.build());
            long total = splitTotal;
            pageCount = total % pageSize > 0 ? (total / pageSize + 1) : total / pageSize;
            records.addAll(roomBoradAccountsPage.getRecords());
            ExcelMqUtil.excelSendIncrementData(excelReportDTO.getUniqueMark(),
                Long.valueOf(roomBoradAccountsPage.getSize()), null);
        }
        CompletableFuture.runAsync(() -> {
            excelExportmallRoomBoradAccounts(records, response, excelReportDTO);
        });
    }

    private void excelExportmallRoomBoradAccounts(List<RoomBoradAccountsVo> records, HttpServletResponse response, ExcelReportDTO excelReportDTO) {
        List<List<Object>> listList = records.stream().map(x -> {
            List<String> listStart = new ArrayList<>();
            List<String> listEnd = new ArrayList<>();
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            if(CollUtil.isNotEmpty(x.getHistoryDateList())) {
                for (EmployeeHistoryDate employeeHistoryDate : x.getHistoryDateList()) {
                    if (ObjectUtil.isNotEmpty(employeeHistoryDate.getStartDate())) {
                        String startDate = simpleDateFormat.format(employeeHistoryDate.getStartDate());
                        listStart.add(startDate);
                    }
                    if (ObjectUtil.isNotEmpty(employeeHistoryDate.getEndDate())) {
                        String endDate = simpleDateFormat.format(employeeHistoryDate.getEndDate());
                        listEnd.add(endDate);
                    }
                }
            }
            List<Object> list = new ArrayList<>();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
            String formattedDate = x.getTime().format(formatter);
            list.add(formattedDate);
            list.add(x.getProvice());
            list.add(x.getCity());
            list.add(x.getArea());
            list.add(x.getCxrSiteName());
            list.add(x.getEmployeeName());
            list.add(x.getJobNumber());
            list.add(x.getEmployeeLevel());
            list.add(StrUtil.split(x.getPostNo(), StrUtil.COMMA).stream().map(v -> {
                PostType enumByValue = PostType.getEnumByValue(v);
                return enumByValue != null ? enumByValue.getName() : v;
            }).collect(Collectors.joining(StrUtil.COMMA)));
            list.add(x.getEatDay());
            list.add(x.getEatDayMoney());
            list.add(x.getLiveDay());
            list.add(x.getLiveDayMoney());
            list.add(x.getBlowDay());
            list.add(x.getBlowDayMoney());
            list.add(x.getTotal());
            list.add(listStart);
            list.add(listEnd);
            Map<String, String> statusMap = new HashMap<>();
            statusMap.put("0","离职的状态");
            statusMap.put("1","在职的状态");
            list.add(statusMap.get(x.getOccupationStatus()));
            list.add(x.getCreateByName());
            LocalDate localDateCreateTime = x.getCreateTime().toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
            DateTimeFormatter formatterDate = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String createTime = localDateCreateTime.format(formatterDate);
            list.add(createTime);
            list.add(x.getAuditUserName());
            LocalDate localDateAudtime = x.getAuditTime().toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
            String auditTime = localDateAudtime.format(formatterDate);
            list.add(auditTime);
            return list;
        }).collect(toList());
        ExcelExportObj excelExportObj = new ExcelExportObj(excelReportDTO.getFileDir(), "吃住空调统计.xlsx",
            RoomBoradAccountsVo.class);
        excelExportObj.writeManySheet(listList);
        excelExportObj.pathClose();
    }

    public void setValue(RoomBoradAccounts e){
        try {
            CxrEmployee employee = iCxrEmployeeService.getById(e.getEmployeeId());
            e.setEmployeeLevel(employee.getEmployeeLevelType());
            setPost(e);
        }catch (Exception ex){
            ex.printStackTrace();
        }
    }

    public void setPost(RoomBoradAccounts e){

        try {
            Long employeeId = e.getEmployeeId();
            LocalDate time = e.getTime();
            LocalDate first = time.with(TemporalAdjusters.firstDayOfMonth());
            LocalDate nextFirst = first.plusMonths(1);
            LocalDate localDate = time.with(TemporalAdjusters.lastDayOfMonth());

            //查询职务
            String postValue = PostType.COMMON_EMPLOYEE.getValue();
            Set<String> postCode = new HashSet<>();
            List<CxrEmployeePost> cxrEmployeePosts = cxrEmployeePostMapper.selectList(new LambdaQueryWrapper<CxrEmployeePost>()
                .eq(CxrEmployeePost::getCxrEmployeeId, employeeId)
                .lt(CxrEmployeePost::getInChargeStartTime, nextFirst)
                .and(w -> w.ge(CxrEmployeePost::getInChargeEndTime, localDate).or().isNull(CxrEmployeePost::getInChargeEndTime))
                .eq(CxrEmployeePost::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));
            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(cxrEmployeePosts)){
                postCode = cxrEmployeePosts.stream().map(p -> p.getCxrPostId().getValue()).collect(Collectors.toSet());
            }

            List<String> employeePostCode = cxrEmployeePostMapper.getEmployeePostCode(employeeId);
            postCode.addAll(employeePostCode);
            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(postCode)){
                postValue = postCode.stream().collect(Collectors.joining(StrUtil.COMMA));
            }
            e.setPostNo(postValue);
        }catch (Exception ex){
            ex.printStackTrace();
        }
    }

    private void computeDivideEatLiveAc(List<RoomBoradAccounts> roomBoradAccountsList,String dateStr){
        try {
            LocalDate date = LocalDateTimeUtil.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM")).toLocalDate();
            LocalDate lastDayOfMonth = date.with(TemporalAdjusters.lastDayOfMonth());
            roomBoradAccountsList.forEach(r -> mqUtil.sendSyncMessage(
                MqBizConst.DIVIDE_COMPUTE_TOPIC,MqBizConst.DIVIDE_COMPUTE_EAT_LIVE_AC_TAG, ComputeDTO.builder().setEmployeeId(r.getEmployeeId()).setDate(lastDayOfMonth).toJSONString()));
        }catch (Exception e){
            log.error("审核通过后生成空调吃住 异常",e);
        }
    }
}
