package com.ruoyi.business.base.api.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 文件下载(CxrReportFormsExport)实体类
 *
 * <AUTHOR>
 * @since 2023-06-27 16:54:37
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("文件下载")
@TableName("cxr_report_forms_export")
public class CxrReportFormsExport implements Serializable {

    private static final long serialVersionUID = -71948308502719839L;

    @ApiModelProperty(value = "编号")
    @TableId("id")
    private Long id;


    @ApiModelProperty(value = "页面名称")
    @TableField("html_name")
    private String htmlName;


    @ApiModelProperty(value = "页面代码")
    @TableField("html_code")
    private String htmlCode;


    @ApiModelProperty(value = "文件存储目录")
    @TableField("file_path")
    private String filePath;


    @ApiModelProperty(value = "文件状态  1未过期,2过期")
    @TableField("file_status")
    private String fileStatus;


    @ApiModelProperty(value = "当前年月日+时间戳后6位")
    @TableField("file_name_suffix")
    private String fileNameSuffix;


    @ApiModelProperty(value = "导出文件数量")
    @TableField("data_total")
    private Long dataTotal;


    @ApiModelProperty(value = "已经导出数据量")
    @TableField("exported_total")
    private Long exportedTotal;


    @ApiModelProperty(value = "创建人id")
    @TableField("create_by")
    private Long createBy;

    @ApiModelProperty(value = "创建人id")
    @TableField("create_by_name")
    private String createByName;


    @ApiModelProperty(value = "创建人名称")
    @TableField("create_by_type")
    private String createByType;


    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "唯一标识")
    @TableField("unique_mark")
    private String uniqueMark;

    @ApiModelProperty(value = "错误消息")
    @TableField("message")
    private String message;

    @ApiModelProperty(value = "保留状态 1:保留 0:不保留")
    private Integer retainStatus;
}

