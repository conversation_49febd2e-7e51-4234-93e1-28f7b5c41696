package com.ruoyi.order.disribution.service;

import com.ruoyi.business.base.api.domain.json.CustomerInfo;
import com.ruoyi.business.base.api.enums.OrderTypeEnums;
import com.ruoyi.common.enums.TerminalTypeEnums;
import com.ruoyi.order.common.domain.vo.CxrUserOrderVO;
import com.ruoyi.order.common.domain.vo.LastUserOrderVo;
import com.ruoyi.order.disribution.domain.bo.UserFastOrderBo;

public interface FastOrderService {

    /**
     * 查询订单类型
     *
     * @param bo
     * @return
     */
    OrderTypeEnums queryOrderType(String phone);

    /**
     * 新增快单
     */
    String addFastOrder(UserFastOrderBo bo);

    /**
     * 校验快单
     *
     * @param bo
     */
    void verifyFastOrderParam(UserFastOrderBo userFastOrderBo, TerminalTypeEnums terminalTypeEnums);

    /**
     * 查询详情
     *
     * @param id
     * @return
     */
    CxrUserOrderVO detail(Long id);

    /**
     * 更新
     *
     * @param userFastOrderBo
     * @return
     */
    boolean updateNewOrder(UserFastOrderBo userFastOrderBo);

    LastUserOrderVo queryLastTenDayOrder(CustomerInfo bo);

    OrderTypeEnums queryOrderTypeNotInId(String phone, Long orderId);

    Object queryLastOrderBusinessAgent(CustomerInfo bo);

    Object queryLastTenDayOrderInfo(CustomerInfo bo);
}
