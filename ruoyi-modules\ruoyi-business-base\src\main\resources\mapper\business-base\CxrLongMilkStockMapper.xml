<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.business.base.cxrLongMilkStock.mapper.CxrLongMilkStockMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.business.base.api.domain.CxrLongMilkStock">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="cxrCustomerId" column="cxr_customer_id" jdbcType="BIGINT"/>
        <result property="cxrSiteId" column="cxr_site_id" jdbcType="BIGINT"/>
        <result property="cxrSiteName" column="cxr_site_name" jdbcType="VARCHAR"/>
        <result property="orderTime" column="order_time" jdbcType="DATE"/>
        <result property="orderType" column="order_type" jdbcType="CHAR"/>
        <result property="applyNum" column="apply_num" jdbcType="INTEGER"/>
        <result property="sentNum" column="sent_num" jdbcType="INTEGER"/>
        <result property="overdueNum" column="overdue_num" jdbcType="INTEGER"/>
        <result property="unregNum" column="unreg_num" jdbcType="INTEGER"/>
        <result property="surplusNum" column="surplus_num" jdbcType="INTEGER"/>
        <result property="expireTime" column="expire_time" jdbcType="DATE"/>
        <result property="longMilkStatus" column="long_milk_status" jdbcType="CHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="revision" column="revision" jdbcType="BIGINT"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="createByName" column="create_by_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="createByType" column="create_by_type" jdbcType="VARCHAR"/>
        <result property="updateByName" column="update_by_name" jdbcType="VARCHAR"/>
        <result property="updateByType" column="update_by_type" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleteBy" column="delete_by" jdbcType="BIGINT"/>
        <result property="deleteByType" column="delete_by_type" jdbcType="VARCHAR"/>
        <result property="deleteByName" column="delete_by_name" jdbcType="VARCHAR"/>
        <result property="deleteTime" column="delete_time" jdbcType="TIMESTAMP"/>
        <result property="deleteStatus" column="delete_status" jdbcType="CHAR"/>
        <result property="cxrUserOrderId" column="cxr_user_order_id" jdbcType="BIGINT"/>
        <result property="cxrUserOrderQuantity" column="cxr_user_order_quantity" jdbcType="BIGINT"/>
        <result property="provice" column="provice" jdbcType="VARCHAR"/>
        <result property="city" column="city" jdbcType="VARCHAR"/>
        <result property="area" column="area" jdbcType="VARCHAR"/>
        <result property="cxrRootRegionId" column="cxr_root_region_id"/>
        <result property="cxrRootRegionName" column="cxr_root_region_name"/>
        <result property="businessAgent" column="business_agent"
            typeHandler="com.ruoyi.business.base.api.typeHandler.TypeHandlerConstant$AgentTypeHandler"/>
    </resultMap>

    <resultMap id="VoResulMap" type="com.ruoyi.business.base.api.model.CxrLongMilkStockVO">
        <result property="businessAgent" column="business_agent"
            typeHandler="com.ruoyi.business.base.api.typeHandler.TypeHandlerConstant$AgentTypeHandler"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,cxr_customer_id,
       cxr_site_id,
        cxr_site_name,order_time,order_type,
        apply_num,sent_num,business_agent,
        overdue_num,unreg_num,surplus_num,
        expire_time,long_milk_status,remark,
        revision,create_by,create_by_name,
        create_time,update_by,create_by_type,
        update_by_name,update_by_type,update_time,
       delete_by,delete_by_type,
        delete_by_name,delete_time,delete_status,cxr_user_order_id,
        cxr_user_order_quantity,provice,city,area,cxr_root_region_id,cxr_root_region_name
        from  cxr_long_milk_stock
    </sql>
    <update id="updateLongMlikStockPast">
        UPDATE cxr_long_milk_stock
        <set>
            overdue_num=surplus_num,
            surplus_num=0,
            long_milk_status=#{expire}
        </set>
        <where>
            long_milk_status=#{status}
            and expire_time &lt;= #{now}
        </where>
    </update>
    <update id="updateByIdAndRevisionBatch">
        <foreach collection="stocks" open="" close="" item="item" separator=";" index="i">
            update cxr_long_milk_stock
            <set>
                unreg_num =#{item.unregNum},
                surplus_num=#{item.surplusNum},
                long_milk_status=#{item.longMilkStatus},
                update_by_name=#{item.updateByName},
                update_by_type=#{item.updateByType},
                update_time=#{item.updateTime},
                update_by=#{item.updateBy},
                revision=ifnull(#{item.revision},0)+1,
            </set>
            <where>
                id=#{item.id}
                and revision=#{item.revision}
            </where>
        </foreach>


    </update>
    <update id="updateLongMilkStockByTransfOrder">
        update cxr_long_milk_stock
        <set>
            surplus_num=surplus_num-#{quantity},
            revision=(ifnull(revision,0)+1),
            <if test="flag">
                apply_status='3'
            </if>
        </set>
        <where>
            id =#{id}
            and ifnull(revision,0)=#{rev}
        </where>


    </update>
    <update id="updateDeleteLongMilkStockByTransfOrder">

        update cxr_long_milk_stock
        <set>
            revision=(ifnull(revision,0)+1),
            delete_status='2'
        </set>
        <where>
            id =#{id}
            and revision=#{rev}
        </where>


    </update>
    <select id="userPage" resultMap="VoResulMap">
        select
        <include refid="Base_Column_List"/>
        <where>
            <if test="bo.cxrRootRegionName !=null and  bo.cxrRootRegionName!=''">
                and cxr_root_region_name like concat('%',#{bo.cxrRootRegionName},'%')
            </if>

            <if test="bo.cxrSiteName !=null and  bo.cxrSiteName!=''">
                and cxr_site_name like concat('%',#{bo.cxrSiteName},'%')
            </if>
            <if test="bo.cxrEmployeeName !=null and bo.cxrEmployeeName !=''">
                and JSON_EXTRACT(business_agent,'$[*].proxyName') like concat('%', #{bo.cxrEmployeeName},'%')
            </if>
            <if test="bo.cxrEmployeeJobNumber!=null  and bo.cxrEmployeeJobNumber !=''">
                and JSON_EXTRACT(business_agent,'$[*].proxyNo') like concat('%', #{bo.cxrEmployeeJobNumber},'%')
            </if>
            <if test="ids!=null and ids.size()>0">
                and cxr_customer_id in
                <foreach collection="ids" open="(" close=")" index="i" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="bo.surplus =='1'.toString() and  bo.surplus!='' ">
                and surplus_num &gt; 0
            </if>
            <if test="bo.surplus == '2'.toString() and  bo.surplus!='' ">
                and surplus_num = 0
            </if>
            <if test="bo.orderTime!=null and bo.orderTimeEnd==null">
                and order_time &gt;= #{bo.orderTime}
            </if>
            <if test="bo.orderTime==null and bo.orderTimeEnd!=null">
                and order_time &lt;= #{bo.orderTimeEnd}
            </if>
            <if test="bo.orderTime!=null and bo.orderTimeEnd!=null">
                and order_time between #{bo.orderTime} and #{bo.orderTimeEnd}
            </if>
            and delete_status='0'
        </where>
        order by create_time desc,id
    </select>
    <select id="sumApplyMilk" resultType="java.lang.Integer">
        select ifnull(sum(apply_num), 0)
        from cxr_long_milk_stock
        where cxr_customer_id = #{id}
    </select>
    <select id="sumNotApplyMilk" resultType="java.lang.Integer">
        select ifnull(sum(overdue_num), 0)
        from cxr_long_milk_stock
        where cxr_customer_id = #{id}
    </select>

    <select id="getCustomerGiveTotal" resultType="com.ruoyi.business.base.cxrLongMilkStock.domain.LongMilkStockTotal">
        select (select COUNT(*)
                from cxr_long_milk_stock
                where cxr_long_milk_stock.cxr_user_order_quantity >= 400
                  and surplus_num > 0
                  and cxr_customer_id = #{userId}
                  and delete_status = 0) as countNum,
               ifnull(sum(apply_num), 0)    applyNum,
               ifnull(sum(overdue_num), 0)  overdueNum,
               ifnull(sum(unreg_num), 0)    unregNum,
               ifnull(sum(surplus_num), 0)  surplusNum,
               ifnull(sum(sent_num), 0)     sentNum
        from cxr_long_milk_stock
        where 1 = 1
          and delete_status = 0
          and expire_time >= curdate()
          and cxr_customer_id = #{userId}
          and sent_num > 0
    </select>

    <select id="queryLongMilkStock"
        resultType="com.ruoyi.business.base.cxrLongMilkStock.domain.vo.CxrLongMilkStockJOBVo">
        select t1.id                                 customerId,
               t1.NAME                            AS customerName,
               t1.wx_open_id                      AS openId,
               t.expire_time                      as expireTime,
               (select ifnull(sum(t2.surplus_num), 0) surplusNum
                from cxr_long_milk_stock t2
                where t2.cxr_customer_id = t1.id) as surplusNum
        from (select *, row_number() over (partition by cxr_customer_id order by cxr_customer_id desc) num
              from cxr_long_milk_stock) t
                 LEFT JOIN cxr_customer t1 ON t1.id = t.cxr_customer_id
        where t.num = 1
          AND t.delete_status = 0
          AND t.expire_time > curdate()
          AND t.sent_num > 0
          AND t1.wx_open_id IS NOT NULL
    </select>

    <select id="queryLongMilkStockTotal"
        resultType="com.ruoyi.business.base.cxrLongMilkStock.domain.vo.CxrLongMilkStockJOBTotalVo">
        SELECT ifnull(sum(t.apply_num), 0)   applyNum,
               ifnull(sum(t.overdue_num), 0) overdueNum,
               ifnull(sum(t.unreg_num), 0)   unregNum,
               ifnull(sum(t.surplus_num), 0) surplusNum,
               ifnull(sum(t.sent_num), 0)    sentNum
        FROM cxr_long_milk_stock t
                 LEFT JOIN cxr_customer t1 ON t1.id = t.cxr_customer_id
        WHERE 1 = 1
          AND t.delete_status = 0
          AND t.expire_time >= curdate()
          AND t.sent_num > 0
          and cxr_customer_id = #{customerId}
          AND t1.wx_open_id IS NOT NULL
    </select>
    <select id="selectSumSurplusQuantity" resultType="java.lang.Integer">
        select ifnull(sum(surplus_num), 0)
        from cxr_long_milk_stock
        where cxr_customer_id = #{id}
          and long_milk_status = 1
    </select>
    <select id="transferLongMilkPage" resultType="com.ruoyi.business.base.api.domain.CxrLongMilkStock">

        select a.cxr_user_order_id,
        a.apply_num,
        a.sent_num,
        a.overdue_num,
        a.unreg_num,
        a.surplus_num,
        a.long_milk_status,
        a.apply_status,
        a.id
        from cxr_long_milk_stock a
        <where>
            a.delete_status='0'
            <if test="(bo.phone !=null and bo.phone!='') or   (bo.orderNo !=null and  bo.orderNo!='')">
                and a.cxr_user_order_id in
                (select id from cxr_user_order t
                <where>
                    <if test="bo.phone !=null and  bo.phone !=''">
                        t.customer_phone like concat(#{bo.phone},'%')
                    </if>
                    <if test="bo.orderNo !=null and  bo.orderNo !=''">
                        and t.order_no like concat(#{bo.orderNo},'%')
                    </if>
                    and t.order_type in
                    <foreach collection="types" open="(" close=")" item="i" separator=",">
                        #{i}
                    </foreach>
                </where>
                )
            </if>
        </where>
    </select>
    <select id="querySurplusNum" resultType="java.lang.Integer">

        select ifnull(sum(t.surplus_num), 0)
        from cxr_long_milk_stock t
        where t.long_milk_status = '1'
          and t.apply_status in ('1', '2')
          and t.cxr_customer_id = #{id}
          and t.expire_time >= #{localDate}
    </select>
    <select id="staffTransferLongMilkPage" resultType="com.ruoyi.business.base.api.model.CxrLongMilkStockVO">
        select
        apply_num,
        sent_num,
        overdue_num,
        unreg_num,
        cxr_customer_id,
        cxr_user_order_id,
        expire_time,
        order_time,
        long_milk_status,
        apply_status,
        surplus_num
        from cxr_long_milk_stock
        <where>
            cxr_customer_id in (select t.id
            from cxr_customer t
            <where>
                <if test="bo.phone != null and bo.phone!=''">
                    t.phone like concat(#{bo.phone},'%')
                </if>
                and t.id in (select tt.cxr_customer_id from cxr_customer_address tt
                <where>
                    <if test="employeeId!=null">
                        tt.cxr_employee_id =#{employeeId}
                    </if>
                    and tt.defalut_account_address='Y'
                </where>
                )
            </where>
            )
            <if test="bo.orderDate !=null">
                <![CDATA[and date_format(order_time, '%Y-%m-%d') >= #{bo.orderDate}]]>
            </if>
            <if test="bo.orderDateEnd != null">
                <![CDATA[and date_format(order_time, '%Y-%m-%d')<= #{bo.orderDateEnd}]]>
            </if>
            <if test="bo.expireTime !=null">
                <![CDATA[ and expire_time >= #{bo.expireTime}]]>
            </if>

            <if test="bo.expireTimeEnd !=null">
                <![CDATA[and expire_time<= #{bo.expireTimeEnd}]]>
            </if>
            <if test="bo.surplus == '1'.toString() ">
                <![CDATA[ and surplus_num > 0]]>
            </if>
            <if test="bo.surplus == '2'.toString() ">
                <![CDATA[ and surplus_num = 0]]>
            </if>
        </where>
    </select>
    <select id="staffTransferLongMilkSumQuantity"
        resultType="com.ruoyi.business.base.api.domain.dto.DailyIntergerDto">
        select
        ifnull(sum(surplus_num),0) as quantity,
        ifnull(sum(apply_num),0) as LongmilkQuantity,
        ifnull(sum(overdue_num),0) as dailyRoadCount
        from cxr_long_milk_stock
        <where>
            cxr_customer_id in (select t.id
            from cxr_customer t
            <where>
                <if test="bo.phone != null and bo.phone!=''">
                    t.phone like concat(#{bo.phone},'%')
                </if>
                and t.id in (select tt.cxr_customer_id from cxr_customer_address tt
                <where>
                    <if test="employeeId!=null">
                        tt.cxr_employee_id =#{employeeId}
                    </if>
                    and tt.defalut_account_address='Y'
                </where>
                )
            </where>
            )
            <if test="bo.orderDate !=null">
                <![CDATA[and date_format(order_time, '%Y-%m-%d') >= #{bo.orderDate}]]>
            </if>
            <if test="bo.orderDateEnd != null">
                <![CDATA[and date_format(order_time, '%Y-%m-%d')<= #{bo.orderDateEnd}]]>
            </if>
            <if test="bo.expireTime !=null">
                <![CDATA[ and expire_time >= #{bo.expireTime}]]>
            </if>

            <if test="bo.expireTimeEnd !=null">
                <![CDATA[and expire_time<= #{bo.expireTimeEnd}]]>
            </if>
            <if test="bo.surplus =='1' ">
                <![CDATA[ and surplus_num > 0]]>
            </if>
            <if test="bo.surplus =='2' ">
                <![CDATA[ and surplus_num = 0]]>
            </if>
        </where>
    </select>
    <select id="pageCount" resultType="java.lang.Long">
        select
            count(id)
                from cxr_long_milk_stock
        <where>
            <if test="bo.cxrRootRegionName !=null and  bo.cxrRootRegionName!=''">
                and cxr_root_region_name like concat('%',#{bo.cxrRootRegionName},'%')
            </if>

            <if test="bo.cxrSiteName !=null and  bo.cxrSiteName!=''">
                and cxr_site_name like concat('%',#{bo.cxrSiteName},'%')
            </if>
            <if test="bo.cxrEmployeeName !=null and bo.cxrEmployeeName !=''">
                and JSON_EXTRACT(business_agent,'$[*].proxyName') like concat('%', #{bo.cxrEmployeeName},'%')
            </if>
            <if test="bo.cxrEmployeeJobNumber!=null  and bo.cxrEmployeeJobNumber !=''">
                and JSON_EXTRACT(business_agent,'$[*].proxyNo') like concat('%', #{bo.cxrEmployeeJobNumber},'%')
            </if>
            <if test="ids!=null and ids.size()>0">
                and cxr_customer_id in
                <foreach collection="ids" open="(" close=")" index="i" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="bo.surplus =='1'.toString() and  bo.surplus!='' ">
                and surplus_num &gt; 0
            </if>
            <if test="bo.surplus == '2'.toString() and  bo.surplus!='' ">
                and surplus_num = 0
            </if>
            <if test="bo.orderTime!=null and bo.orderTimeEnd==null">
                and order_time &gt;= #{bo.orderTime}
            </if>
            <if test="bo.orderTime==null and bo.orderTimeEnd!=null">
                and order_time &lt;= #{bo.orderTimeEnd}
            </if>
            <if test="bo.orderTime!=null and bo.orderTimeEnd!=null">
                and order_time between #{bo.orderTime} and #{bo.orderTimeEnd}
            </if>
            and delete_status='0'
        </where>
    </select>
    <select id="userList" resultMap="VoResulMap">
        select
        <include refid="Base_Column_List"/>
        <where>
             id >#{lastId}
            <if test="bo.cxrRootRegionName !=null and  bo.cxrRootRegionName!=''">
                and cxr_root_region_name like concat('%',#{bo.cxrRootRegionName},'%')
            </if>

            <if test="bo.cxrSiteName !=null and  bo.cxrSiteName!=''">
                and cxr_site_name like concat('%',#{bo.cxrSiteName},'%')
            </if>
            <if test="bo.cxrEmployeeName !=null and bo.cxrEmployeeName !=''">
                and JSON_EXTRACT(business_agent,'$[*].proxyName') like concat('%', #{bo.cxrEmployeeName},'%')
            </if>
            <if test="bo.cxrEmployeeJobNumber!=null  and bo.cxrEmployeeJobNumber !=''">
                and JSON_EXTRACT(business_agent,'$[*].proxyNo') like concat('%', #{bo.cxrEmployeeJobNumber},'%')
            </if>
            <if test="ids!=null and ids.size()>0">
                and cxr_customer_id in
                <foreach collection="ids" open="(" close=")" index="i" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="bo.surplus =='1'.toString() and  bo.surplus!='' ">
                and surplus_num &gt; 0
            </if>
            <if test="bo.surplus == '2'.toString() and  bo.surplus!='' ">
                and surplus_num = 0
            </if>
            <if test="bo.orderTime!=null and bo.orderTimeEnd==null">
                and order_time &gt;= #{bo.orderTime}
            </if>
            <if test="bo.orderTime==null and bo.orderTimeEnd!=null">
                and order_time &lt;= #{bo.orderTimeEnd}
            </if>
            <if test="bo.orderTime!=null and bo.orderTimeEnd!=null">
                and order_time between #{bo.orderTime} and #{bo.orderTimeEnd}
            </if>
            and delete_status='0'
        </where>
        limit #{limit}

    </select>

</mapper>
