package com.ruoyi.core.base.domain.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.core.domain.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 区域单数统计明细Bo
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@ApiModel("区域单数统计明细Bo")
@Data
public class CxrRegionStaitemDailyReportBo extends PageQuery implements Serializable {

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间:YYYY-MM-dd", required = true)
    @NotNull(message = "开始时间不能为空")
    private LocalDate startDate;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间:YYYY-MM-dd", required = true)
    @NotNull(message = "结束时间不能为空")
    private LocalDate endDate;

    /**
     * 维度类型
     */
    @ApiModelProperty("维度类型:COMPANY-公司,ROOT_REGION-大区,REGION-区域,SITE-站点,AGENT-代理")
    @NotNull(message = "维度类型不能为空")
    private String dimensionType;

    /**
     * 星期 MONDAY-周一,TUESDAY-周二,WEDNESDAY-周三,THURSDAY-周四,FRIDAY-周五,SATURDAY-周六,SUNDAY-周日
     */
    @ApiModelProperty("星期 MONDAY-周一,TUESDAY-周二,WEDNESDAY-周三,THURSDAY-周四,FRIDAY-周五,SATURDAY-周六,SUNDAY-周日")
    private String weekDay;

    /**
     * 公司ID
     */
    @ApiModelProperty("公司ID")
    private Long companyId;

    /**
     * 公司名称
     */
    @ApiModelProperty("公司名称")
    private String companyName;

    /**
     * 大区ID
     */
    @ApiModelProperty("大区ID")
    private Long rootRegionId;

    /**
     * 大区名称
     */
    @ApiModelProperty("大区名称")
    private String rootRegionName;

    /**
     * 区域ID
     */
    @ApiModelProperty("区域ID")
    private Long regionId;

    /**
     * 区域名称
     */
    @ApiModelProperty("区域名称")
    private String regionName;

    /**
     * 站点ID
     */
    @ApiModelProperty("站点ID")
    private Long siteId;

    /**
     * 站点名称
     */
    @ApiModelProperty("站点名称")
    private String siteName;

    /**
     * 代理ID
     */
    @ApiModelProperty("代理ID")
    private Long agentId;

    /**
     * 代理姓名
     */
    @ApiModelProperty("代理姓名")
    private String agentName;

    /**
     * 代理编号
     */
    @ApiModelProperty("代理编号")
    private String agentCode;

    /**
     * 代理等级
     */
    @ApiModelProperty("代理等级")
    @ExcelProperty("代理等级")
    private String agentLevel;

    // 权限相关字段
    /**
     * 公司权限
     */
    private List<Long> companyIds;

    /**
     * 大区权限
     */
    private List<Long> rootRegionIds;

    /**
     * 区域权限
     */
    private List<Long> regionIds;

    /**
     * 站点权限
     */
    private List<Long> siteIds;
}
